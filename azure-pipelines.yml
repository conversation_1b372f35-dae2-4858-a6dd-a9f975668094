name: MyResaAPI - ${{  replace(variables['Build.SourceBranchName'], 'deploy/', '')  }}
trigger:
  branches:
    include:
      - deploy/*
lockBehavior: sequential
pool: AZSELFHOSTED_Agent_SDVIP-DEVOPS01
variables:
  ENV: $[replace(variables['Build.SourceBranchName'], 'deploy/', '')]
  PROFILE: resa
resources:
  containers:
    - container: myresaapi-cicd
      image: myresaapi-cicd
      endpoint: resaado.azurecr.io
      options: -u root
stages:
  - stage: Validate
    jobs:
      - job: Unittest
        workspace:
          clean: all
        container: myresaapi-cicd
        steps:
          - task: DownloadSecureFile@1
            name: AwsConfig
            displayName: 'Download AWS credentials'
            inputs:
              secureFile: 'aws_config'
          - script: |
              mkdir ~/.aws
              mv $(AwsConfig.secureFilePath) ~/.aws/config
            displayName: 'Set AWS credentials'
          - script: |
              python -m venv venv
              source venv/bin/activate
              for f in */*requirements.txt; do
                  pip install -r "$f";
              done
              pip install pytest pytest-azurepipelines pytest-cov pytest-xdist moto==4.2.14 boto3 flask flask_cors weasyprint==52.5
            displayName: 'Install dependencies'
          - script: |
              source venv/bin/activate
              set -a && source unittest.env && set +a
              export PYTHONPATH="$PWD"
              for dir in "$PWD"/*; do
                  if [ -d "$dir" ]; then
                      export PYTHONPATH="$PYTHONPATH:$dir";
                  fi;
              done
              pytest
            displayName: 'Run Python Unit Tests'
          - task: PublishCodeCoverageResults@2
            displayName: ‘Publish Coverage Report’
            inputs:
              summaryFileLocation: './coverage.xml'
  - stage: Build
    dependsOn: validate
    condition: succeeded()
    jobs:
      - job: Build_and_upload
        workspace:
          clean: all
        container: myresaapi-cicd
        steps:
          - task: DownloadSecureFile@1
            name: AwsConfig
            displayName: 'Download AWS credentials'
            inputs:
              secureFile: 'aws_config'
          - script: |
              mkdir ~/.aws
              mv $(AwsConfig.secureFilePath) ~/.aws/config
            displayName: 'Set AWS credentials'
          - script: |
              ./bin/updateResources.sh "$ENV"
            displayName: 'Upload ressources'
          - script: |
              ./bin/deploy-lambdas.py "$ENV"
            displayName: 'Build and upload lambda code'
  - stage: Deploy
    dependsOn: build
    condition: succeeded()
    jobs:
      - job: Deploy_API
        workspace:
          clean: all
        container: myresaapi-cicd
        steps:
          - task: DownloadSecureFile@1
            name: AwsConfig
            displayName: 'Download AWS credentials'
            inputs:
              secureFile: 'aws_config'
          - script: |
              mkdir ~/.aws
              mv $(AwsConfig.secureFilePath) ~/.aws/config
              cp ~/.aws/config ~/.aws/credentials
            displayName: 'Set AWS credentials'
          - script: |
              ./bin/terraform-only.sh "$PROFILE" "$ENV"
            displayName: 'Deploy'
          - script: |
              ./bin/terraform-only.sh "$PROFILE" "$ENV"
            displayName: 'Terraform second run'
          - script: |
              ./bin/eject-stage.sh "$ENV"
            displayName: 'Eject version stage'
            condition: and(succeeded(),or(eq(lower(variables.ENV), 'qla'), eq(lower(variables.ENV), 'production')))
          - script: |
              ./bin/send_release_mail.sh "$ENV"
            displayName: 'Send new version mail'
            condition: and(succeeded(),or(eq(lower(variables.ENV), 'qla'), eq(lower(variables.ENV), 'production')))
          - script: |
              python -m venv venv
              source venv/bin/activate
              pip install requests pyaml boto3
              export PYTHONPATH="${PYTHONPATH}:${PWD}/utils"
              ./bin/genOpenapi.py -e "$ENV"
              ./bin/gen_PostmanApi.py
            displayName: 'Generate Postman Collection'
      - job: Set_Sentry_release
        workspace:
          clean: all
        container: myresaapi-cicd
        variables:
          - group: sentry-cli
        steps:
          - script: |
              # Install sentry CLI
              curl -sL https://sentry.io/get-cli/ | sh
              
              # Set version
              if [ "$(echo "$ENV" | tr '[:upper:]' '[:lower:]')" = "qta" ]; then
                VERSION="MyResaAPI@$(sentry-cli releases propose-version)"
              else
                VERSION="MyResaAPI@$(./bin/get_version.sh "$ENV")"
              fi
              echo $VERSION
              
              # Create a release
              sentry-cli releases new -p myresa-api "$VERSION"
              
              # Associate commits with the release
              sentry-cli releases set-commits --auto "$VERSION"
              
              # Associate commits with the release
              sentry-cli deploys new --release "$VERSION" -e "$ENV"
            displayName: 'Send new version to Sentry'
  - stage: Cleanup
    dependsOn:
      - Validate
      - Build
      - Deploy
    condition: always()
    jobs:
      - job: Cleanup
        workspace:
          clean: all
        steps:
          - script: docker system prune -a --force
            displayName: 'Clean up docker container / volumes / images / caches'