AD_SECRET=MyResaAPI/ActiveDirectory/qta
ADFS_SECRET=MyResaAPI/ADFS/dev
ADFS_WEB=MyResaWeb/ADFS/Qual
API_REGION=eu-west-1
API_STAGE=v0
API_URL=https://127.0.0.1:8011
API_VERSION=v0
AsynchroneProcessesTable=AsynchroneProcesses_qta
AsyncProcessLambda=arn:aws:lambda:eu-west-1:204480941676:function:AsyncProcess-qta
BornesRechargeTable=BornesRechargeTable_qta
BUCKET=my-resa-api-qta
BUCKET_NAME=my-resa-api-qta-uploads
BUCKET_NAME_SHAREPOINT_UPLOADS=my-resa-api-qta-sharepoint-uploads
BUCKET_NAME_UPLOADS=my-resa-api-qta-uploads
CURL_CA_BUNDLE=
DIGACERT_STORE_CERT_LAMBDA_ARN=arn:aws:lambda:eu-west-1:204480941676:function:digacertStoreCert-qta
DOMAIN_NAME=127.0.0.1:8011
DossierConfActionsTable=DossierConfirmationActions_qta
DYNAMODB=MyResaUser_qta
DYNAMODB_TOKEN_INVALIDATION=TokenInvalidation
EmailSendsTable=EmailSends
ENV_FILE=resources/v0/env.qta.json
ENV_MESSAGE_LOG_BUCKET=env-message-logs-qta
FORMULAIRE=MyResaFormulaire_qta
FormulairesTable=FormulairesTable_qta
FunctionName=passThrough-qta
HTML_TO_PDF_LAMBDA=arn:aws:lambda:eu-west-1:204480941676:function:transformHtmlToPdf-qta
IS_PROD=true
JWK_BUCKET=my-resa-api-qta
JWK_KEY=resources/AD_publickey.json
LOCAL=true
MAPPING_BUCKET_FILE=resources/v0/linking.json
MAPPING_BUCKET_NAME=my-resa-api-qta
MD_TABLE=MyResaMD_qta
MOLLIE_TOKEN=MyResaAPI/Mollie/DEV
PANNE_DYNAMODB_TABLE=UserPanneSubscription_qta
PANNE_LOCATION_TABLE=PanneLocation_qta
PASSTHROUGH_LAMBDA_ARN=arn:aws:lambda:eu-west-1:204480941676:function:passThrough-qta:web-resa-proj
PayementTable=PayementTable_qta
PYTHONUNBUFFERED=1
SHAREPOINT_SECRET=MyResaAPI/Sharepoint/QTA
SharepointUpload_LAMBDA_ARN=arn:aws:lambda:eu-west-1:204480941676:function:sharepointUpload-qta:94
SMS_CODES=MyResaSMSCodes_qta
STAGE=qta
STAGE_TAG=QTA
USER_ACTIVATION_SQS_URL=https://sqs.eu-west-1.amazonaws.com/204480941676/userActivation-qta_queue
USER_SYNC_LAMBDA_ARN=arn:aws:lambda:eu-west-1:204480941676:function:userSynchronisation-qta
CACHE_BASE_DIR=/tmp/MyResa
UserEnergyProfiles=UserEnergyProfiles_dev
SmartConsoAlerts=SmartConsoAlerts_dev
HistoricAlerts=HistoricAlerts_QTA
LANG=FR