import json
import os

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file
from utils.token_utils import verify_token, checkForInvalidation, removeTokenType


@aws_lambda_handler
def handler(event, context):
    try:
        bucket = os.environ["BUCKET"]
        key = os.environ["KEY"]

        token = removeTokenType(event["authorizationToken"])
        checkForInvalidation(token)
        jsonwebkey = json.loads(load_s3_file(bucket, key))

        allow = verify_token(token, jsonwebkey)
    except:
        allow = False
    return {
        "principalId": "user",
        "policyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Action": "execute-api:Invoke",
                    "Effect": ("Allow" if allow else "Deny"),
                    "Resource": "*",
                }
            ],
        },
    }
