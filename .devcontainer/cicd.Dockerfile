FROM public.ecr.aws/lambda/python:3.11

# Install needed tools
RUN yum clean all && \
    yum install -y shadow-utils util-linux sudo tar git pango yum-utils rpmdevtools aws-cli git zip jq

RUN touch ~/.bashrc \
    && curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash \
    && source ~/.bashrc  \
    && nvm install 16

RUN curl https://releases.hashicorp.com/terraform/0.12.31/terraform_0.12.31_linux_amd64.zip > terraform.zip && \
    mkdir .bin && \
    unzip terraform.zip -d /bin && \
    chmod a+x /bin/terraform

RUN pip install pyyaml

COPY --from=public.ecr.aws/docker/library/docker /usr/local/bin/docker /usr/local/bin/

ENV PATH="$PATH:/usr/sbin"

ENTRYPOINT []