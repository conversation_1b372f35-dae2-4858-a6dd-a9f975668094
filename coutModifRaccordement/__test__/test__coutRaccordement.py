import unittest
from unittest.mock import patch

import coutModifRaccordement
from coutModifRaccordement import calcul_cout_raccordement
from utils.errors import InternalServerError, BadRequestError
from utils.mocking import mock_environnement


class TestcoutRaccordement(unittest.TestCase):
    puissance_actuelle = "11.5"
    puissance_demandee = "22.7"
    phase_actuelle = "1"
    phase_demandee = "4"
    amperage_actuelle = "50"
    amperage_demandee = "33"

    @mock_environnement
    @patch("coutModifRaccordement.api_caller")
    def test_calcul_cout_raccordement(self, mock_api_caller):
        mock_api_caller.side_effect = [
            {
                "Liste": [
                    {"IdCpt": 1, "LibIdCpt": "Confort", "PrixHtva": 1855.78},
                    {"IdCpt": 2, "LibIdCpt": "Power +", "PrixHtva": 6662.79},
                ],
                "MontantTotal": 8518.57,
            },
            [{"Id": "ERR0R", "Price": "181"}, {"Id": "ER015", "Price": "123.5"}],
        ]
        result_data = calcul_cout_raccordement(
            self.phase_actuelle,
            self.amperage_actuelle,
            self.puissance_actuelle,
            self.phase_demandee,
            self.amperage_demandee,
            self.puissance_demandee,
        )

        expected_data = {
            "isBase64Encoded": False,
            "statusCode": 200,
            "body": {
                "ForfaitsActuel": "Confort",
                "ForfaitsPropose": "Power +",
                "Prix": 4807.01,
            },
        }
        self.assertEqual(
            result_data,
            expected_data,
            "Le dictionnaire retourné ne correspond pas aux données attendues.",
        )

    @mock_environnement
    @patch("coutModifRaccordement.api_caller")
    def test_calcul_cout_raccordement_same_forfait(self, mock_api_caller):
        mock_api_caller.side_effect = [
            {
                "Liste": [
                    {"IdCpt": 1, "LibIdCpt": "Confort", "PrixHtva": 1855.78},
                    {"IdCpt": 2, "LibIdCpt": "Confort", "PrixHtva": 1855.78},
                ],
                "MontantTotal": 8518.57,
            },
            [{"Id": "ERR0R", "Price": "181"}, {"Id": "ER015", "Price": "123.5"}],
        ]
        result_data = calcul_cout_raccordement(
            self.phase_actuelle,
            self.amperage_actuelle,
            self.puissance_actuelle,
            self.phase_demandee,
            self.amperage_demandee,
            self.puissance_demandee,
        )

        expected_data = {
            "isBase64Encoded": False,
            "statusCode": 200,
            "body": {
                "ForfaitsActuel": "Confort",
                "ForfaitsPropose": "Confort",
                "Prix": 123.5,
            },
        }
        self.assertEqual(
            result_data,
            expected_data,
            "Le dictionnaire retourné ne correspond pas aux données attendues.",
        )

    @mock_environnement
    @patch("coutModifRaccordement.api_caller")
    def test_api_error(self, mock_api_caller):
        mock_api_caller.return_value = {"Liste": []}
        with self.assertRaises(InternalServerError) as e:
            calcul_cout_raccordement(1, 2, 3, 4, 5, 6)
        self.assertEqual(str(e.exception.message), "No response received from API")
        self.assertEqual(e.exception.error_code, "API_ERROR")

    @mock_environnement
    @patch("coutModifRaccordement.api_caller")
    def test_client_error(self, mock_api_caller):
        mock_api_caller.return_value = {"Liste": []}
        with self.assertRaises(BadRequestError) as e:
            coutModifRaccordement.handler(
                {
                    "queryStringParameters": {
                        "phase_actuelle": "1",
                        "amperage_actuelle": "2",
                        "puissance_actuelle": "3",
                        "phase_demandee": "4",
                        "amperage_demandee": "5",
                        "puissance_demandee": "aie",
                    }
                },
                {},
            )
        self.assertEqual(str(e.exception.message), "The field 'puissance_demandee' should be a float, or a string float representation")
        self.assertEqual(e.exception.error_code, "INVALID_FLOAT_FORMAT")
