import json
from math import sqrt

from utils.api import api_caller
from utils.aws_handler_decorator import aws_lambda_handler
from utils.errors import BadRequestError, InternalServerError
from utils.type_utils import float_format, int_format


@aws_lambda_handler
def handler(event, context):
    query_params = event.get("queryStringParameters", {}) or {}

    # Récupérer les valeurs pour puissance_actuelle et puissance_demandée
    puissance_actuelle = float_format(query_params.get("puissance_actuelle", None), "puissance_actuelle")
    puissance_demandee = float_format(query_params.get("puissance_demandee", None), "puissance_demandee")

    phase_actuelle = int_format(query_params.get("phase_actuelle", None), "phase_actuelle")
    phase_demandee = int_format(query_params.get("phase_demandee", None), "phase_demandee")

    amperage_actuelle = float_format(query_params.get("amperage_actuelle", None), "amperage_actuelle")
    amperage_demandee = float_format(query_params.get("amperage_demandee", None), "amperage_demandee")

    if not puissance_demandee or not puissance_actuelle:
        raise BadRequestError(
            "Les paramètres puissance_actuelle et puissance_demandee sont requis dans le corps de la requête.",
            error_code="MISSING_INFO",
        )
    if not phase_demandee or not phase_actuelle:
        raise BadRequestError(
            "Les paramètres phase_actuelle et phase_demandee sont requis dans le corps de la requête.",
            error_code="MISSING_INFO",
        )
    if not amperage_demandee or not amperage_actuelle:
        raise BadRequestError(
            "Les paramètres amperage_actuelle et amperage_demandee sont requis dans le corps de la requête.",
            error_code="MISSING_INFO",
        )

    # Convert the amperes on 3 and 4 phases to its equivalent if it was on 1 phase.
    # -- This is done by explicit request from web devs to avoid calculation on the website part --
    amperage_actuelle = convert_amperes(phase_actuelle, amperage_actuelle)
    amperage_demandee = convert_amperes(phase_demandee, amperage_demandee)

    return calcul_cout_raccordement(
        phase_actuelle,
        amperage_actuelle,
        puissance_actuelle,
        phase_demandee,
        amperage_demandee,
        puissance_demandee,
    )


def convert_amperes(phase: int, amperes: float) -> float:
    """
    @param phase: The number of phases in the electrical system. Must be an integer value.
    @param amperes: The current in amperes. Must be a float value.
    @return: The converted current value based on the number of phases.

    Convert the amperes on 3 and 4 phases to its equivalent if it was on 1 phase.
    In 3 phases, amperes must be divided by √3.
    In 4 phases, amperes must be divided by √3 * √3 (that leads to dividing by 3)
    """
    if phase == 3:
        return amperes / sqrt(3)
    if phase == 4:
        return amperes / 3
    return amperes


def calcul_cout_raccordement(
    phase_actuelle,
    amperage_actuelle,
    puissance_actuelle,
    phase_demandee,
    amperage_demandee,
    puissance_demandee,
):
    body_request = {
        "Liste": [
            {
                "IdCpt": 1,
                "NbPhase": phase_actuelle,
                "ExclNuit": 0,
                "Amperage": amperage_actuelle,
                "Puissance": puissance_actuelle,
            },
            {
                "IdCpt": 2,
                "NbPhase": phase_demandee,
                "ExclNuit": 0,
                "Amperage": amperage_demandee,
                "Puissance": puissance_demandee,
            },
        ]
    }

    resp = api_caller("post", "/tarifs", body=json.dumps(body_request))
    list_resp = resp.get("Liste")
    if not list_resp:
        raise InternalServerError("No response received from API", error_code="API_ERROR")

    current_rate = current_rate_name = proposed_rate = proposed_rate_name = None
    for elem in list_resp:
        if not elem.get("PrixHtva"):
            raise BadRequestError("The meter configuration is incoherent", error_code="INCOHERENT_CONFIG")
        prixhtva = elem["PrixHtva"]
        lib = elem["LibIdCpt"]
        if elem["IdCpt"] == 1:
            current_rate = prixhtva
            current_rate_name = lib
        elif elem["IdCpt"] == 2:
            proposed_rate = prixhtva
            proposed_rate_name = lib

    resp_tarifs = api_caller("get", "/raccordement/tarifs")

    min_price = next((float(elem["Price"]) for elem in resp_tarifs if elem["Id"] == "ER015"), 0.0)
    total_price = max(proposed_rate - current_rate, min_price)
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": {
            "Prix": round(total_price, 2),
            "ForfaitsActuel": current_rate_name,
            "ForfaitsPropose": proposed_rate_name,
        },
    }
