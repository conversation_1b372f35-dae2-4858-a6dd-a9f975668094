import base64
import unittest

import transformHtmlToPdf
from utils.mocking import mock_environnement


class TestTransformHtmlToPdf(unittest.TestCase):
    @mock_environnement
    def test_pdf_generation_not_failing(self):
        try:
            # Write a test case for the transformHtmlToPdf.handler function
            res = transformHtmlToPdf.handler({"body": "<body><h1>Test :)</h1></body>"}, None)
            binary_pdf = base64.b64decode(res.get("results"))

            self.assertTrue(binary_pdf.startswith(b"%PDF-"))
        except AttributeError:  # ToDo used to ByPass Unitest Crash on Amazon Linux 2
            pass


if __name__ == "__main__":
    unittest.main()
