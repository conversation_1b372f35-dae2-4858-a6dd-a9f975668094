#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import os

import requests

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_secret
from utils.dict_utils import capitalizeKeys
from utils.dict_utils import get
from utils.errors import BadRequestError, NotFoundError


@aws_lambda_handler
def handler(event, context):
    secret = get_secret(os.environ["ADFS_SECRET"])
    # ==================== Variables importantes ====================
    scope = "user.read openid profile offline_access"
    grant_type = "password"
    uri = secret["endpoint"]
    host = secret["host"]
    client_id = secret["client_id"]
    client_secret = secret["client_secret"]
    # ================================================================
    """
    Valide et transforme les parametres d une requete AWS Lambda
    :param event:
    :return:
    """
    print("event", event)
    body = json.loads(get(event, "body", "{}"))
    username = get(body, "Username", err=BadRequestError("No Username found in request body"))
    password = get(body, "Password", err=BadRequestError("No Password found in request body"))

    username = str(username)
    password = str(password)

    if not client_id or not scope or not client_secret or not username or not password or not grant_type:
        errmsg = "ERROR: 400 Request Error. Le parametres de username et password doivent etre non null. De meme que les variables environnement client_id et client_secret"
        raise NotFoundError(errmsg)

    request_payload = {}
    request_payload = {
        "client_id": client_id,
        "scope": scope,
        "client_secret": client_secret,
        "username": username,
        "password": password,
        "grant_type": grant_type,
    }

    headers = {"Host": host}
    r = requests.post(uri, data=request_payload, headers=headers, verify=False)

    response = {
        "statusCode": r.status_code,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys(r.json())),
        "isBase64Encoded": False,
    }
    return response
