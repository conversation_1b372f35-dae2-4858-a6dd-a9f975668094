import json
import os

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file
from utils.errors import BadRequestError


@aws_lambda_handler
def handler(event, context):
    try:
        json_str = load_s3_file(os.environ["BUCKET"], "evdb.json")
        json_data = json.loads(json_str)  # Conversion de la chaîne UTF-8 en JSON
        return {"statusCode": 200, "body": json.dumps(json_data)}
    except Exception as e:
        raise BadRequestError(e, error_code="BUCKET_NOT_ACCESSIBLE")
