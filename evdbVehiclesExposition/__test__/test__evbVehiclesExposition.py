import json
import os
import unittest
from multiprocessing import Process
from time import sleep

from tools.local_flask.gateway import start_local_flask
from utils.api import api_caller
from utils.aws_utils import upload_s3_file
from utils.mocking import mock_environnement


class TestSqlDrivenFunction(unittest.TestCase):
    @mock_environnement
    def test_SqlDriven_handler(self):
        data = [
            {
                "Marque": "Tesla",
                "Modele": "Model 3",
                "Version": "Long Range Dual Motor",
                "CapaciteBatterie": 78.8,
                "Type": "Electrique",
                "Autonomie": 455,
                "Consommation": 162,
                "PuissanceChargeMax": 11,
                "CarImages": "https://ev-database.org/img/auto/Tesla_Model_3/<EMAIL>",
            }
        ]

        upload_s3_file(os.environ["BUCKET"], "evdb.json", json.dumps(data))
        # Start local API with flask to receive request
        flask = Process(target=start_local_flask, args=(8011, "./resources"))
        flask.start()
        sleep(5)

        # Appeler la fonction Lambda
        response = api_caller("GET", "/vehicules", raw=True)
        # Vérifier le code de statut HTTP
        self.assertEqual(response.status_code, 200)

        # Stop flask
        flask.terminate()
