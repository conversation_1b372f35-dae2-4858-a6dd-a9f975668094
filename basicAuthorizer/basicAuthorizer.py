import json
import traceback
from os import environ

from utils.auth_utils import check_basic_credentials
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file, get_resource_key
from utils.dict_utils import case_insensitive_dict
from utils.errors import UnauthorizedError
from utils.log_utils import log_info_json
from utils.parallel import concurrently


@aws_lambda_handler
def handler(event, context):
    log_info_json(event)
    try:
        authorization_header = case_insensitive_dict(event["headers"])["authorization"]
        if not authorization_header:
            raise UnauthorizedError(message="Authorization credentials not found in header")

        linking_file = json.loads(load_s3_file(environ["BUCKET"], get_resource_key("linking.json")))
        method = event["httpMethod"]
        path = event["resource"]
        linking = linking_file[path][method.upper()]

        check_secrets = []
        for secret in linking["authorizerSecrets"]:
            check_secrets.append(lambda _secret=secret: check_basic_credentials(authorization_header, secret_name=_secret))
        allow = any(concurrently(*check_secrets))
    except Exception as e:
        print("basicAuth failed with an error ", e)
        traceback.print_exc()
        allow = False
    return {
        "principalId": "user",
        "policyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Action": "execute-api:Invoke",
                    "Effect": ("Allow" if allow else "Deny"),
                    "Resource": "*",
                }
            ],
        },
    }


if __name__ == "__main__":
    print(
        handler(
            {
                "resource": "/utilisateurs/envMessage",
                "httpMethod": "POST",
                "headers": {"Authorization": "Basic "},  # +  token
            },
            None,
        )
    )
