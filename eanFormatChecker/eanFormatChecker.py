import json

from utils.aws_handler_decorator import aws_lambda_handler


def ean_checksum(ean_digits) -> bool:
    """
    Verify the EAN checksum
    """
    first_step = sum(ean_digits[0::2]) * 3
    second_step = sum(ean_digits[1::2])
    third_step = first_step + second_step

    return third_step % 10 == 0


@aws_lambda_handler
def handler(event, context):
    """
    This lambda check the format and validity of a given EAN number

    @return bool: the validity of the given EAN
    """
    valid = False
    ean: str = event.get("queryStringParameters", {}).get("Ean", "")

    if ean.isdecimal():
        # Convert ean to a list of digits
        ean_digits = [int(digit) for digit in str(ean)]

        # Check the ean length and the checksum
        valid = len(ean_digits) == 18 and ean_checksum(ean_digits)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": json.dumps({"Valide": valid}),
    }
