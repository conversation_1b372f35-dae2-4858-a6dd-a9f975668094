import os
from collections import defaultdict
from typing import List

import boto3

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_dynamodb_table
from utils.bornes_utils import (
    get_borne_recharge_from_db,
    verify_ean_access,
    process_bornes,
    process_and_putDb,
    upload_borne_file,
)
from utils.dict_utils import get
from utils.errors import BadRequestError
from utils.models.borne_recharge_model import BorneRecharge, BorneRechargeInstant
from utils.models.user import User


@aws_lambda_handler
def handler(event, context):
    http_method = event["httpMethod"].lower()  # Convertir en minuscule pour garantir la correspondance
    dic_methods_func = {"put": put_lambda, "get": get_lambda, "delete": delete_lambda}

    if http_method in dic_methods_func:
        return dic_methods_func[http_method](event)
    else:
        raise BadRequestError("Methode non reconnue", error_code="BAD_REQUEST_ERROR")


def put_lambda(event):
    body = get(event, "body", "{}")
    borne_recharge_instant: BorneRechargeInstant = BorneRechargeInstant.from_json(body)
    table = get_dynamodb_table(os.environ["BornesRechargeTable"])
    uuid_from_url = event["pathParameters"]["uuid"]

    # Check if UUID in the URL and body are consistent
    if uuid_from_url != borne_recharge_instant.uuid:
        raise BadRequestError("UUID Can't be modified", error_code="UUID_URL_DIFF_UUID_BODY")

    # Retrieve the corresponding BorneRecharge record from DynamoDB using the UUID
    borne_recharge_db: BorneRecharge = get_borne_recharge_from_db(borne_recharge_instant.uuid, table)

    # Ensure that the creation date in the body matches the one in the database, as it shouldn't be modified
    if borne_recharge_db.date_creation != borne_recharge_instant.date_creation:
        raise BadRequestError("Date Creation Can't be modified", error_code="CREATION_DATE_NOT_MODIFIABLE")

    # Create a User object from the incoming event
    user = User.from_event(event)

    new_uuid = False
    if verify_ean_access(user, borne_recharge_instant.borne.ean, borne_recharge_db.borne.ean):
        if borne_recharge_instant.borne.ean != borne_recharge_db.borne.ean:
            new_uuid = True
        if borne_recharge_instant.borne.active is True:
            type_demande = {
                "Valeur": "ACTIVATE",
                "Libelle": "La mise en service d'une borne de recharge",
            }
            date = borne_recharge_instant.borne.date_activation
            if not date:
                raise BadRequestError(
                    "Borne.DateActivation can't be null when activating",
                    error_code="DATE_ACTIVATION_NULL",
                )
            if borne_recharge_db.type_demande.valeur != "ACTIVATE":
                new_uuid = True
        elif borne_recharge_instant.borne.active is False:
            type_demande = {
                "Valeur": "DEACTIVATE",
                "Libelle": "La mise hors service d'une borne de recharge",
            }
            date = borne_recharge_instant.borne.date_desactivation
            if not date:
                raise BadRequestError(
                    "Borne.DateDesactivation can't be null when deactivating",
                    error_code="DATE_DESACTIVATION_NULL",
                )
            if borne_recharge_db.type_demande.valeur != "DEACTIVATE":
                new_uuid = True
        else:
            raise BadRequestError("Active can't be null", error_code="ACTIVE_NULL")

        # Create a BorneRecharge object based on the BorneRechargeInstant
        borne_recharge: BorneRecharge = BorneRecharge.from_dict(
            {
                **borne_recharge_instant.to_dict(),
                "TypeDemande": type_demande,
                "Borne": {**borne_recharge_instant.borne.to_dict(), "Date": date},
            }
        )

        if new_uuid:
            # Put new entry into DB
            borne_recharge = process_and_putDb(borne_recharge)
        else:
            # Upload file if changed
            upload_borne_file(borne_recharge)
            # Update existing entry in DB
            response = table.put_item(Item=borne_recharge.to_dict())
            if response["ResponseMetadata"]["HTTPStatusCode"] != 200:
                raise BadRequestError("PUT PROBLEME", error_code="PROBLEME_PUT")
        return {"statusCode": 200, "body": borne_recharge.to_dict()}


def get_lambda(event):
    table = get_dynamodb_table(os.environ["BornesRechargeTable"])

    list_bornes = []

    my_user = User.from_event(event)
    for elem in my_user.ean_ids:
        try:
            response = table.query(KeyConditionExpression=boto3.dynamodb.conditions.Key("Ean").eq(str(elem)))
        except ValueError:
            raise BadRequestError(ValueError, error_code="DB PROBLEM")

        borne_items: List[BorneRecharge] = BorneRecharge.schema().load(response.get("Items", []), many=True)

        # Trier les éléments par DateCreation, du plus récent au plus ancien
        borne_items.sort(key=lambda x: x.date_creation, reverse=True)

        if borne_items:
            grouped_by_serial = defaultdict(list)
            for item in borne_items:
                grouped_by_serial[item.borne.serial].append(item)
            list_bornes.extend(grouped_by_serial.values())

    bornes = process_bornes(list_bornes)
    return {
        "statusCode": 200,
        "body": BorneRechargeInstant.schema().dump(bornes, many=True),
    }


def delete_lambda(event):
    table = get_dynamodb_table(os.environ["BornesRechargeTable"])
    uuid_from_url = event["pathParameters"]["uuid"]
    borne_recharge_db: BorneRecharge = get_borne_recharge_from_db(uuid_from_url, table)
    user = User.from_event(event)
    # todo : remove str conversion when ean str fix is live
    if borne_recharge_db.borne.ean not in [str(ean) for ean in user.ean_ids]:
        raise BadRequestError("EAN ACCESS DENIED", error_code="NOT_AUTHORIZED")
    borne_recharge_db.supprimer = True

    response = table.put_item(Item=borne_recharge_db.to_dict())
    if response["ResponseMetadata"]["HTTPStatusCode"] != 200:
        raise BadRequestError("Can't delete previous version", error_code="EAN_NOT_DELETABLE")
    return {"statusCode": 204}
