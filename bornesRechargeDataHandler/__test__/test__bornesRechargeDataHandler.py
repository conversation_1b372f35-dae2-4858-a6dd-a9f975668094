import unittest
from datetime import datetime, timedelta
from typing import List

from utils.api import api_caller
from utils.mocking import get_mock_user_token
from utils.mocking import mock_api
from utils.models.borne_recharge_model import BorneRecharge, BorneRechargeInstant


@mock_api
class TestLambdaFunction(unittest.TestCase):
    token = None

    def setUp(self):
        self.token = get_mock_user_token()

    def test_get_borne(self):
        borne = _add_test_borne_to_db(self.token)
        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        self.assertEqual(response_get.status_code, 200)

        bornes_get: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        self.assertEqual(1, len(bornes_get), "Invalid number of element")

        borne_get = bornes_get[0]
        self.assertTrue(borne.uuid == borne_get.uuid)

    def test_edit_borne(self):
        borne = _add_test_borne_to_db(self.token)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)

        borne_get = bornes_get[0]
        borne_get.borne.marque = "Teslo"
        response_put = api_caller(
            "PUT",
            f"/me/bornes_recharge/{borne.uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=borne_get.to_json(),
        )
        self.assertEqual(response_put.status_code, 200)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get_after: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        self.assertEqual(1, len(bornes_get_after), "Invalid number of element")

        borne_get_after = bornes_get_after[0]
        self.assertTrue(borne_get.uuid == borne_get_after.uuid)
        self.assertTrue(borne_get.borne.marque == borne_get_after.borne.marque == "Teslo")

    def test_edit_borne_missing_or_invalid_date(self):
        borne = _add_test_borne_to_db(self.token)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        borne_get = bornes_get[0]

        borne_get.borne.date_activation = "Youhou"
        borne_get.borne.date_desactivation = None
        response_put = api_caller(
            "PUT",
            f"/me/bornes_recharge/{borne.uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=borne_get.to_json(),
        )
        self.assertEqual(response_put.status_code, 400)
        self.assertTrue("BORNE_DATE_ACTIVATION_BAD_VALUE" in response_put.text)
        self.assertTrue("DateActivation" in response_put.text)

        borne_get.borne.date_activation = None
        borne_get.borne.date_desactivation = "Youhou"
        response_put = api_caller(
            "PUT",
            f"/me/bornes_recharge/{borne.uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=borne_get.to_json(),
        )
        self.assertEqual(response_put.status_code, 400)
        self.assertTrue("BORNE_DATE_DESACTIVATION_BAD_VALUE" in response_put.text)
        self.assertTrue("DateDesactivation" in response_put.text)

    def test_edit_borne_active_to_inactive(self):
        test_data = get_test_borne_data()
        test_data["TypeDemande"] = {"Libelle": "Test", "Valeur": "ACTIVATE"}
        test_data["Borne"]["Date"] = "2023-01-01"
        response = api_caller(
            "POST",
            "/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=test_data,
        )
        borne: BorneRecharge = BorneRecharge.from_json(response.text)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        borne_get = bornes_get[0]
        self.assertTrue(borne_get.borne.active is True)
        self.assertTrue(borne_get.borne.date_activation == "2023-01-01")

        borne_get.borne.active = False
        borne_get.borne.date_desactivation = "2023-01-31"
        response_put = api_caller(
            "PUT",
            f"/me/bornes_recharge/{borne.uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=borne_get.to_json(),
        )
        self.assertEqual(response_put.status_code, 200)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get_after: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        self.assertEqual(1, len(bornes_get_after), "Invalid number of element")

        borne_get_after = bornes_get_after[0]
        self.assertTrue(borne_get_after.uuid != borne_get.uuid)
        self.assertTrue(borne_get_after.borne.active is False)
        self.assertTrue(borne_get_after.borne.date_desactivation == "2023-01-31")

    def test_edit_borne_inactive_to_active(self):
        test_data = get_test_borne_data()
        test_data["TypeDemande"] = {"Libelle": "Test", "Valeur": "DEACTIVATE"}
        test_data["Borne"]["Date"] = "2023-01-01"
        response = api_caller(
            "POST",
            "/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=test_data,
        )
        borne: BorneRecharge = BorneRecharge.from_json(response.text)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        borne_get = bornes_get[0]
        self.assertTrue(borne_get.borne.active is False)
        self.assertTrue(borne_get.borne.date_desactivation == "2023-01-01")

        borne_get.borne.active = True
        borne_get.borne.date_activation = "2023-01-31"
        response_put = api_caller(
            "PUT",
            f"/me/bornes_recharge/{borne.uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=borne_get.to_json(),
        )
        self.assertEqual(response_put.status_code, 200)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get_after: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        self.assertEqual(1, len(bornes_get_after), "Invalid number of element")

        borne_get_after = bornes_get_after[0]
        self.assertTrue(borne_get_after.uuid != borne_get.uuid)
        self.assertTrue(borne_get_after.borne.active is True)
        self.assertTrue(borne_get_after.borne.date_activation == "2023-01-31")

    def test_edit_borne_active_to_inactive_in_future(self):
        future_date = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")
        test_data = get_test_borne_data()
        test_data["TypeDemande"] = {"Libelle": "Test", "Valeur": "ACTIVATE"}
        test_data["Borne"]["Date"] = "2023-01-01"
        response = api_caller(
            "POST",
            "/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=test_data,
        )
        borne: BorneRecharge = BorneRecharge.from_json(response.text)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        borne_get = bornes_get[0]
        self.assertTrue(borne_get.borne.active is True)
        self.assertTrue(borne_get.borne.date_activation == "2023-01-01")

        borne_get.borne.active = False
        borne_get.borne.date_desactivation = future_date
        response_put = api_caller(
            "PUT",
            f"/me/bornes_recharge/{borne.uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=borne_get.to_json(),
        )
        self.assertEqual(response_put.status_code, 200)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get_after: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        self.assertEqual(1, len(bornes_get_after), "Invalid number of element")

        borne_get_after = bornes_get_after[0]
        self.assertTrue(borne_get_after.uuid != borne_get.uuid)
        self.assertTrue(borne_get_after.borne.active is True)
        self.assertTrue(borne_get_after.borne.date_desactivation == future_date)

    def test_edit_borne_inactive_to_active_in_future(self):
        future_date = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")
        test_data = get_test_borne_data()
        test_data["TypeDemande"] = {"Libelle": "Test", "Valeur": "DEACTIVATE"}
        test_data["Borne"]["Date"] = "2023-01-01"
        response = api_caller(
            "POST",
            "/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=test_data,
        )
        borne: BorneRecharge = BorneRecharge.from_json(response.text)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        borne_get = bornes_get[0]
        self.assertTrue(borne_get.borne.active is False)
        self.assertTrue(borne_get.borne.date_desactivation == "2023-01-01")

        borne_get.borne.active = True
        borne_get.borne.date_activation = future_date
        response_put = api_caller(
            "PUT",
            f"/me/bornes_recharge/{borne.uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=borne_get.to_json(),
        )
        self.assertEqual(response_put.status_code, 200)

        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        bornes_get_after: List[BorneRechargeInstant] = BorneRechargeInstant.schema().loads(response_get.text, many=True)
        self.assertEqual(1, len(bornes_get_after), "Invalid number of element")

        borne_get_after = bornes_get_after[0]
        self.assertTrue(borne_get_after.uuid != borne_get.uuid)
        self.assertTrue(borne_get_after.borne.active is False)
        self.assertTrue(borne_get_after.borne.date_activation == future_date)

    def test_delete_borne(self):
        borne = _add_test_borne_to_db(self.token)
        response_delete = api_caller(
            "DELETE",
            f"/me/bornes_recharge/{borne.uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        self.assertEqual(response_delete.status_code, 204)
        response_get = api_caller(
            "GET",
            "/me/bornes_recharge",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        self.assertTrue(response_get.text == "[]")


def get_test_borne_data():
    return {
        "Ean": "541456700002569958",
        "Borne": {
            "Ean": "541456700002569958",
            "Adresse": {
                "CodePostal": "4020",
                "Commune": "BRUXELLES",
                "Numero": "25",
                "Pays": "Belgique",
                "Rue": "Avenue de la Liberté",
            },
            "Bidirectionnelle": False,
            "Date": "2023-04-21",
            "Marque": "BrandB",
            "Modele": "ModelY",
            "Photo": None,
            "Puissance": 15,
            "Serial": "WXYZ5678",
            "Utilisation": {"Libelle": "Privée", "Valeur": "PRIVATE"},
        },
        "Demandeur": {
            "Email": "<EMAIL>",
            "Nom": "Doe",
            "Prenom": "Jane",
            "Telephone": "496969696",
        },
        "Entreprise": {
            "Acronyme": "JEC",
            "FormeJuridique": "SA",
            "Nom": "Jane Enterprises",
            "Numero": "78901",
        },
        "Installateur": None,
        "TypeDemande": {"Libelle": "Test", "Valeur": "ACTIVATE"},
    }


def _add_test_borne_to_db(user_token: str) -> BorneRecharge:
    test_data = get_test_borne_data()
    response = api_caller(
        "POST",
        "/bornes_recharge",
        raw=True,
        headers={"Authorization": f"Bearer {user_token}"},
        body=test_data,
    )
    borne = BorneRecharge.from_json(response.text)
    return borne
