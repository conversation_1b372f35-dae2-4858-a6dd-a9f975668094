import json
import os
import traceback
from time import sleep

from psycopg2.extensions import AsIs

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_secret, load_s3_file
from utils.db_connector import make_connection
from utils.geo_utils import Address
from utils.log_utils import log_err


def represents_int(string_representation):
    try:
        int(string_representation)
        return True
    except ValueError:
        return False


def convert_string(string_representation, type_name, field_name):
    if type_name == "int":
        try:
            string_as_int = int(string_representation)
            return string_as_int
        except Exception:
            log_err(traceback.format_exc())
            raise ValueError("Erreur 400 Request Error: Donne de type int incorrecte dans le champ " + field_name)

    else:
        if type_name == "zipcode":
            try:
                zipcode_int = int(string_representation)
            except Exception:
                log_err(traceback.format_exc())
                raise ValueError("Erreur 400 Request Error: Donne de type zipcode non numerique dans le champ " + field_name)

            if 9999 < zipcode_int or zipcode_int < 1000:
                log_err(traceback.format_exc())
                raise ValueError("Erreur 400 Request Error: Zipcode inexistant dans le champ " + field_name)
            return zipcode_int


def process_new_store(store_resource):
    try:
        print("Geocoding store")
        sleep(10)
        address = Address(
            road=store_resource["rue"],
            city=store_resource["ville"],
            zip_code=store_resource["codepostal"],
        )
        address.geocode()
        store_resource["latitude"] = address.lat
        store_resource["longitude"] = address.lng
    except Exception:
        log_err(traceback.format_exc())
        store_resource["latitude"] = 0.0
        store_resource["longitude"] = 0.0
    finally:
        return store_resource


def to_row(text_line, sep):
    try:
        table = text_line.split(sep)
    except Exception:
        errmsg = "ERROR 500: separateur incorrect dans le fichier requis"
        log_err(traceback.format_exc())
        raise Exception(errmsg)
    return table


def to_resource(store_row, stores_in_db):
    store_resource = {
        "id": convert_string(store_row[0], "int", "id"),
        "modele": store_row[1],
        "nbr": convert_string(store_row[2], "int", "nbr"),
        "marque": store_row[3],
        "nom": store_row[4],
        "rue": store_row[5],
        "codepostal": convert_string(store_row[6], "zipcode", "codepostal"),
        "ville": store_row[7],
        "contactname": store_row[8],
        "contact": store_row[9],
        "horaire": store_row[10],
        "active": True,
    }

    if store_resource["id"] in stores_in_db:
        result = store_resource
    else:
        result = process_new_store(store_resource)

    return result


def execute_query(cursor, store_resource, stores_in_db, table_name):
    columns = store_resource.keys()
    values = store_resource.values()
    if store_resource["id"] in stores_in_db:
        print("Update existing store")
        try:
            update_template = "update " + table_name + " set ({}) = %s where id = {}"
            update_statement = update_template.format(", ".join(columns), store_resource["id"])
            cursor.execute(update_statement, (tuple(values),))
            store_resource["sync_status"] = True
        except BaseException:
            errmsg = "ERROR 500: erreur lors de la requete d actualisation du magasin " + str(store_resource["id"])
            log_err(traceback.format_exc())
            raise Exception(errmsg)

    else:
        print("Insert new store")
        try:
            insert_statement = "insert into " + table_name + " (%s) values %s returning id"
            cursor.execute(insert_statement, (AsIs(",".join(columns)), tuple(values)))
            store_resource["sync_status"] = cursor.fetchone()[0] == store_resource["id"]
        except BaseException:
            errmsg = "ERROR 500: erreur lors de la requete d enregistrement du magasin " + str(store_resource["id"])
            log_err(traceback.format_exc())
            raise Exception(errmsg)

    return store_resource


def process_active_store(cursor, store, stores_in_db, table_name):
    try:
        if store and len(store) >= 2 and store[1] == "CMS":
            store_sync_resource = {"id": None, "sync_status": True}
        elif len(store) == 11:
            print("Process active store")
            store_resource = to_resource(store, stores_in_db)
            store_sync_resource = execute_query(cursor, store_resource, stores_in_db, table_name)
            store_resource["is_new_store"] = store_resource["id"] not in stores_in_db
        else:
            errmsg = "ERROR 500: le nombre d attributs du point de recharge est incorrect dans le fichier requis"
            log_err(traceback.format_exc())
            store_sync_resource = {
                "id": store[0],
                "sync_status": False,
                "error_message": errmsg,
            }
    except BaseException as e:
        store_sync_resource = {
            "id": store[0] if store else None,
            "sync_status": False,
            "error_message": str(e),
        }

    if store_sync_resource is None:
        store_sync_resource = {
            "id": None,
            "sync_status": False,
            "error_message": str(store),
        }

    return store_sync_resource


def process_inactive_store(cursor, store_id, table_name):
    try:
        print("Process inactive store")
        update_statement = "update " + table_name + " set active = False where id = %s"
        cursor.execute(update_statement, (store_id,))
        store_sync_resource = {"id": store_id, "active": False, "sync": True}
    except BaseException as e:
        store_sync_resource = {
            "id": store_id,
            "sync_status": False,
            "error_message": str(e),
        }
    return store_sync_resource


@aws_lambda_handler
def handler(event, context):
    headers = {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": True,
    }

    # Magasins dans fichier SFTP
    try:
        sftp_filename = event["sftp_filename"]
    except BaseException:
        errormsg = "Error 400 bad request. Un nom de fichier requis doit etre fourni"
        response = {
            "statusCode": 400,
            "isBase64Encoded": False,
            "headers": json.dumps(headers),
            "body": json.dumps({"errorMessage": errormsg}),
        }
        return response

    try:
        sftp_bucket_name = os.environ["sftp_bucket_name"]
        store_location_file = load_s3_file(sftp_bucket_name, sftp_filename)
        store_location_list = store_location_file.splitlines()
        store_table = [to_row(store_line, ";") for store_line in store_location_list]
    except BaseException as e:
        response = {
            "statusCode": 500,
            "isBase64Encoded": False,
            "headers": json.dumps(headers),
            "body": json.dumps({"errorMessage": str(e)}),
        }
        return response

    # Se connecter a la DB
    try:
        db_credentials = get_secret(os.environ["storelocation_db_secret"])
        storelocation_db_table = db_credentials["TABLE"]
        cnx = make_connection(db_credentials)
        cursor = cnx.cursor()
    except BaseException as e:
        response = {
            "statusCode": 500,
            "isBase64Encoded": False,
            "headers": json.dumps(headers),
            "body": json.dumps({"errorMessage": str(e)}),
        }
        return response

    # Liste des magasins en DB
    try:
        cursor.execute("SELECT id FROM " + storelocation_db_table)
        store_ids = cursor.fetchall()
        stores_in_db = [x[0] for x in store_ids]
    except Exception:
        log_err(traceback.format_exc())
        error_message = "Erreur lors de l execution de la requete." + "Il na pas ete possible d obtenir la liste des points de recharge existants"
        response = {
            "statusCode": 500,
            "isBase64Encoded": False,
            "headers": json.dumps(headers),
            "body": json.dumps({"errorMessage": error_message}),
        }
        return response

    # Synchroniser chaque magasin du fichier SFTP
    active_store_sync = [process_active_store(cursor, store, stores_in_db, storelocation_db_table) for store in store_table]

    # Flag magasin inactif si dans DB mais pas dans SFTP
    store_sync_ids = [int(store[0]) for store in store_table if represents_int(store[0])]
    inactive_store_ids = list(set(stores_in_db).difference(store_sync_ids))
    if len(inactive_store_ids) > 0:
        inactive_store_sync = [process_inactive_store(cursor, store_id, storelocation_db_table) for store_id in inactive_store_ids]
        store_sync_results = active_store_sync + inactive_store_sync
    else:
        store_sync_results = active_store_sync

    error_messages = [store["error_message"] for store in store_sync_results if "error_message" in store]

    response = {
        "statusCode": 200 if not error_messages else 500,
        "isBase64Encoded": False,
        "headers": json.dumps(headers),
        "body": json.dumps(store_sync_results),
    }
    print(response)

    # Close connection
    try:
        cnx.close()
    except:
        pass

    return response


if __name__ == "__main__":
    print(handler(None, None))
