import datetime as dt
import json
import os
from uuid import uuid4

import boto3
from boto3.dynamodb.conditions import Key
from unidecode import unidecode

from utils.auth_utils import getUserData
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import create_s3_presigned_url
from utils.dict_utils import capitalizeKeys, get
from utils.errors import BadRequestError, NotFoundError

BUCKET = os.environ["BUCKET_NAME_UPLOADS"]
REGION_NAME = os.environ["API_REGION"]
MD_TABLE = os.environ["MD_TABLE"]


@aws_lambda_handler
def handler(event, context):
    if event["resource"] == "/fichiers/liste":
        response = fichier_liste(event)
    elif event["resource"] == "/fichiers/upload":
        response = fichier_upload(event)
    else:
        response = {
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": "application/json",
            },
            "isBase64Encoded": False,
            "statusCode": 404,
            "body": '{"Error": "Endpoint Not Found"}',
        }

    return response


def fichier_liste(event):
    result = list_documents(event)
    response = {
        "headers": {
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/json",
        },
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": json.dumps(result),
    }

    return response


def fichier_upload(event):
    document_full_name = event["queryStringParameters"]["DocumentName"]
    submit = get(event["queryStringParameters"], "Submit", "true").lower() == "true"
    document_extension = document_full_name.split(".")[-1]
    document_name = document_full_name.replace(f".{document_extension}", "")

    uid = get_uid_from_user(event)
    dossier_id = get_dossier_id(event)

    existing_document_id = find_existing_md(uid, dossier_id, document_name, document_extension)
    document_id = str(uuid4()) if not existing_document_id else existing_document_id

    if not dossier_id:
        raise BadRequestError("Aucun numéro de dossier fourni, merci d'ajouter IdDossier en queryStringParameters.")

    file_path = f"{document_id}.{document_extension}"

    try:
        resource = f"{dossier_id}/{file_path}"
        url = create_s3_presigned_url(BUCKET, resource, method="put")
        metadata_response = store_md(
            uid,
            dossier_id,
            document_id,
            document_name,
            document_extension,
            submit,
            resource,
            url,
        )
    except Exception as e:
        raise IOError(e)

    response = {
        "headers": {
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/json",
        },
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": json.dumps(capitalizeKeys(metadata_response)),
    }

    return response


def get_uid_from_user(event):
    user_data = getUserData(event)

    return user_data["uid"]


def get_dossier_id(event):
    if event.get("queryStringParameters", {}).get("IdDossier"):
        # Use unidecode to be able to strip \xe2\x80\x8b out of IdDossier
        val = unidecode(event["queryStringParameters"]["IdDossier"]).strip()
        try:
            # Cast to int to filter trailing 0
            val = int(val)
        except ValueError:
            pass
        return str(val)
    else:
        return None


def find_existing_md(uid, dossier_id, document_name, document_extension):
    dynamodb = boto3.resource("dynamodb")
    table = dynamodb.Table(MD_TABLE)

    metatdata = table.query(
        IndexName="uid-index",
        KeyConditionExpression=Key("uid").eq(uid),
        FilterExpression=Key("dossier_id").eq(dossier_id),
    )["Items"]

    for item in metatdata:
        if not get(item, "sharepoint") and get(item, "file_name") == document_name and get(item, "file_format") == document_extension:
            return item["document_id"]


def store_md(
    uid,
    dossier_id,
    document_id,
    document_name,
    document_extension,
    submitted,
    resource,
    url,
):
    dynamodb = boto3.resource("dynamodb")
    table = dynamodb.Table(MD_TABLE)

    file_md = {
        "uid": uid,
        "dossier_id": str(dossier_id),
        "document_id": document_id,
        "file_name": document_name,
        "file_format": document_extension,
        "original_file_format": document_extension,
        "resource": resource,
        "submitted": submitted,
        "sharepoint": False,
        "s3_backup": False,
        "s3_backup_date": None,
        "sharepoint_date": None,
        "put_url": url,
        "url_creation_date": dt.datetime.utcnow().isoformat(),
    }

    table.put_item(Item=file_md)

    return file_md


def list_documents(event):
    uid = get_uid_from_user(event)
    dossier_id = get_dossier_id(event)

    dynamodb = boto3.resource("dynamodb")
    table = dynamodb.Table(MD_TABLE)

    if dossier_id:
        response = table.query(
            IndexName="dossier_id-index",
            KeyConditionExpression=Key("dossier_id").eq(dossier_id),
            FilterExpression=Key("uid").eq(uid),
        )
    else:
        # If no dossier_id found return list of items for client
        response = table.query(IndexName="uid-index", KeyConditionExpression=Key("uid").eq(uid))

    if not response:
        raise NotFoundError(f"Le dossier '{dossier_id}' n'a aucun document uploader pour cet utilisateur.")

    return [capitalizeKeys(item) for item in response.get("Items", [])]
