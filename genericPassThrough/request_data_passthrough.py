from base64 import b64encode
from dataclasses import dataclass, field
from typing import Optional

from dataclasses_json import dataclass_json, LetterCase
from requests import request

from patch_ssl_requests import patch_requests
from utils.errors import BadRequestError


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class RequestDataSecurity:
    cert: Optional[str] = None
    key: Optional[str] = None
    id: Optional[str] = None

    @property
    def tuple(self) -> tuple:
        return self.cert, self.key


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class RequestDataPassthrough:
    endpoint: str
    method: str
    body: Optional[any] = None
    headers: Optional[dict] = field(default_factory=dict)
    security: Optional[RequestDataSecurity] = None

    def __post_init__(self):
        if not self.endpoint.startswith("/"):
            raise BadRequestError('Invalid endpoint, should start with "/"', error_code="INVALID_ENDPOINT")

    def send_request(self, base_url: str):
        patch_requests()
        response = request(
            method=self.method,
            url=f"{base_url}{self.endpoint}",
            headers=self.headers,
            data=(self.body if self.headers.get("Content-Type") != "application/json" else None),
            json=(self.body if self.headers.get("Content-Type") == "application/json" else None),
            cert=self.security.tuple if self.security else None,
        )

        content_type = response.headers.get("Content-Type", "")
        binary_type = (
            content_type.startswith("image/")
            or content_type.startswith("audio/")
            or content_type.startswith("video/")
            or content_type in ("application/pdf", "application/octet-stream")
        )

        response.headers.pop("content-encoding", None)
        response.headers.pop("transfer-encoding", None)

        return {
            "statusCode": response.status_code,
            "headers": dict(response.headers),
            "isBase64Encoded": binary_type,
            "body": (b64encode(response.content).decode() if binary_type else response.text),
        }
