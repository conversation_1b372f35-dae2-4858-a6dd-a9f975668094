import json
import os

from request_data_passthrough import RequestDataPassthrough, RequestDataSecurity
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file, get_resource_key, get_secret_string
from utils.dict_utils import get


@aws_lambda_handler
def handler(event, context):
    linking = json.loads(load_s3_file(os.environ["BUCKET"], get_resource_key("linking.json")))[event["resource"]][event["httpMethod"].upper()]

    request_data: RequestDataPassthrough = RequestDataPassthrough.from_json(get(event, "body", "{}", default_on_empty=True))

    if linking.get("security"):
        if not request_data.security:
            request_data.security = RequestDataSecurity(
                cert=get_secret_string(linking["security"]["default"]["cert"]),
                key=get_secret_string(linking["security"]["default"]["key"]),
            )
        elif request_data.security.id and not request_data.security.key and not request_data.security.cert:
            request_data.security = RequestDataSecurity(
                cert=get_secret_string(linking["security"][request_data.security.id]["cert"]),
                key=get_secret_string(linking["security"][request_data.security.id]["key"]),
            )

    response = request_data.send_request(linking["base_url"])

    return response
