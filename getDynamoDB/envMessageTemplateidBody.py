import json
import os
import re

import boto3
import sib_api_v3_sdk
from sib_api_v3_sdk.rest import ApiException

from utils.aws_utils import get_secret_string
from utils.errors import BadRequestError, NotFoundError


def handler(event, config):
    configuration = sib_api_v3_sdk.Configuration()
    configuration.api_key["api-key"] = get_secret_string("MyResaApi/sendinblueApiKey")
    api_instance = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))
    path_params = event.get("pathParameters")
    if path_params is None:
        raise BadRequestError("Invalid path, should have TemplateId", error_code="INVALID_PATH")
    template_id = get_templateId_fr(path_params["TemplateId"])

    try:
        # 1 : Get the HTML
        api_response = api_instance.get_smtp_template(int(template_id))
        html_content = api_response.html_content + api_response.subject
    except ApiException as e:
        raise Exception(f"Exception when calling SMTPApi->get_smtp_template: {e}")
    # 2 : GET the {{}} {% %} elem in the html and remove the params.
    elements_without_params = get_html_variables_without_params(html_content)
    # 3 : Make a list with the elem inside the for loop and get rid of the loop logic naming.
    # In the process get some easy part to delete and the part that should be a list in the end.
    replaced_elements, index_to_del, to_transform_in_list = replace_in_loop_name(elements_without_params)

    # 4 : Delete the easy part
    for i in range(len(index_to_del)):
        del replaced_elements[index_to_del[i] - i]
    # 5 : Delete the elem that are not some variables
    cleaned_elements = [clean_element(elem) for elem in replaced_elements]
    cleaned_elements = [elem for elem in cleaned_elements if elem]
    # 6 : make a dictionnary out of the remaining elem (that should only be used variables)
    hierarchical_dict = build_hierarchical_dict(cleaned_elements)
    # 7 : Convert the elem in the dict to list with the list you had in the step 3
    transformed_dict = transform_dict_to_list_dict(hierarchical_dict, to_transform_in_list)
    # 8 : Specific transformation , remove contact, split dict that has space as key, and ADD informations
    final_transformed_dict = merge_keys_remove_contact_add_fields(transformed_dict, path_params["TemplateId"])
    # 9 : Tranform it into json and return it
    json_data = json.dumps(final_transformed_dict)

    response = {"statusCode": 200, "body": json_data, "headers": {"Content-Type": "application/json"}}

    return response


def merge_keys_remove_contact_add_fields(input_dict, template_id):
    new_dict = {}
    processed_keys = set()  # Ensemble pour suivre les clés déjà traitées

    for key in input_dict:
        if key == "contact":  # Ignorer et ne pas ajouter la clé "contact" au nouveau dictionnaire
            continue
        base_key, _, extended_key_part = key.partition(" ")
        if base_key in input_dict and key != base_key and base_key not in processed_keys:
            # Fusionner les contenus si la clé de base et la clé "étendue" existent
            merged_content = {**input_dict[base_key], **input_dict[key]}
            new_dict[base_key] = merged_content
            processed_keys.add(base_key)
        elif base_key not in processed_keys:
            # Ajouter directement les clés qui n'ont pas de version "étendue"
            new_dict[key] = input_dict[key]
        # Marquer la clé "étendue" comme traitée
        processed_keys.add(key)
    header_fields = {
        "TEMPLATE_ID": template_id,
        "EMAIL": "EMAIL",
        "MOBILE_PHONE": "+32123456789",
        "PARTNER_ID": "00000000",
    }

    # Mise à jour ou création de la clé "Header" dans new_dict
    new_dict["Header"] = {**new_dict.get("Header", {}), **header_fields}

    return new_dict


def transform_dict_to_list_dict(base_dict, to_transform_paths):
    """
    Transforme les éléments spécifiés d'un dictionnaire en listes de dictionnaires.
    """
    # Convertir les chemins en un ensemble pour une recherche plus rapide.
    to_transform_paths_set = set(to_transform_paths)

    def recursive_transform(current_dict, path=""):
        for key, value in list(current_dict.items()):
            # Construire le chemin actuel.
            new_path = f"{path}.{key}" if path else key

            # Si le chemin actuel doit être transformé et n'est pas déjà une liste, en utilisant l'ensemble pour vérification.
            if new_path in to_transform_paths_set and not isinstance(value, list):
                # Transformer l'élément en liste de dictionnaires.
                current_dict[key] = [value]

            # Si la valeur est un dictionnaire, appliquer récursivement la transformation.
            if isinstance(value, dict):
                recursive_transform(value, new_path)
            elif isinstance(value, list):
                # Appliquer la transformation à chaque dictionnaire dans la liste.
                for item in value:
                    if isinstance(item, dict):
                        recursive_transform(item, new_path)

    recursive_transform(base_dict)

    return base_dict


def build_hierarchical_dict(list_strings):
    root = {}
    for string in list_strings:
        parts = string.split(".")
        current_level = root
        for part in parts[:-1]:
            try:
                part = int(part)
            except ValueError:
                if (part not in current_level) or (part in current_level and current_level[part] == parts[-2]):
                    current_level[part] = {}
                current_level = current_level[part]
        if parts[-1] not in current_level:
            try:
                parts[-1] = int(parts[-1])
                current_level[parts[-1]] = []
            except ValueError:
                current_level[parts[-1]] = parts[-1]
    return root


def to_del_list():
    return [
        "if",
        "not",
        "loop.first",
        "forloop.Last",
        "endif",
        "mirror",
        "==",
        "in",
        "else",
        "time_now|date:",
        "unsubscribe",
        "elif",
        "env",
        "params",
        "and",
        "DomainName",
        "Unsubscribe",
    ]


def clean_element(elem):
    to_del = to_del_list()
    # Suppression du texte entre guillemets
    regex_quotes = r"(['\"])(?:(?=(\\?))\2.)*?\1"
    elem_cleaned = re.sub(regex_quotes, "", elem)

    for word in to_del:
        if word.isalpha():  # Pour les mots alphabétiques, utilisez les limites de mots
            word_regex = r"\b" + re.escape(word) + r"\b"
        else:  # Pour les symboles, ne pas utiliser les limites de mots
            word_regex = re.escape(word)
        elem_cleaned = re.sub(word_regex, "", elem_cleaned)

    # Nettoyer les espaces supplémentaires qui peuvent être créés et assigner le résultat
    return re.sub(r"\s+", " ", elem_cleaned).strip()


def get_html_variables_without_params(html_content):
    all_fields_pattern = re.compile(r"\{\{\s*(.*?)\s*\}\}|\{\%\s*(.*?)\s*\%\}|\{%\s*(endfor)\s*%\}")

    matches = all_fields_pattern.findall(html_content)
    all_fields_ordered = []
    for match in matches:
        for field in match:
            if field:
                all_fields_ordered.append(field)
    elements_without_params = [elem.replace("params.", "") for elem in all_fields_ordered]
    return elements_without_params


def replace_in_loop_name(elements_without_params):
    for_loop_elem_list = []
    pattern = re.compile(r"for\s+(\w+)\s+in\s+([\w\.]+)")
    index_to_del = []
    to_transform_in_list = []
    for index, elem in enumerate(elements_without_params):
        if elem.startswith("for") and "in" in elem:
            loop_name, variable_name = pattern.findall(elem)[0]
            variable_name_parts = variable_name.split(".")
            # Recherche si le nom de la variable fait partie d'une boucle précédente
            for i, (prev_loop_name, prev_variable_name) in enumerate(for_loop_elem_list):
                if prev_loop_name == variable_name_parts[0]:
                    variable_name_parts[0] = prev_variable_name
                    variable_name = ".".join(variable_name_parts)
                    break
            for_loop_elem_list.append((loop_name, variable_name))
            index_to_del.append(index)
            if variable_name not in to_transform_in_list:
                to_transform_in_list.append(variable_name)
        elif "endfor" in elem:
            del for_loop_elem_list[-1]
            index_to_del.append(index)
        elif len(for_loop_elem_list) >= 1:
            for loop_elem in for_loop_elem_list:
                loop_name, variable_name = loop_elem
                elements_without_params[index] = elem.replace(loop_name, variable_name)
    return elements_without_params, index_to_del, to_transform_in_list


def get_templateId_fr(item_id):
    dynamodb = boto3.resource("dynamodb")
    table_name = f"WS68_TEMPLATE_{os.environ.get('STAGE_TAG', 'DEV')}"
    table = dynamodb.Table(table_name)
    item_dynamodb = table.get_item(Key={"Id": item_id}).get("Item")
    if item_dynamodb is None or "EmailId" not in item_dynamodb or "FR" not in item_dynamodb["EmailId"]:
        raise NotFoundError(f"No template found with ID {item_id} for lang FR or invalid structure.")
    return item_dynamodb.get("EmailId").get("FR")
