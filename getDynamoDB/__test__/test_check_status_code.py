import unittest


class TestFunctionCheckStatus(unittest.TestCase):
    def test_comm_info_uid_status_code_404(self):
        from compte import check_status

        self.assertEqual(check_status(404, 200, 200), 404)
        self.assertEqual(check_status(404, 404, 404), 404)
        self.assertEqual(check_status(404, 404, 200), 404)
        self.assertEqual(check_status(404, 200, 404), 404)

    def test_a_and_b_not_404(self):
        from compte import check_status

        self.assertEqual(check_status(200, 200, 200), 200)
        self.assertEqual(check_status(200, 201, 200), 200)
        self.assertEqual(check_status(200, 200, 201), 200)
        self.assertEqual(check_status(200, 201, 202), 201)

    def test_a_not_404_b_404(self):
        from compte import check_status

        self.assertEqual(check_status(200, 200, 404), 200)
        self.assertEqual(check_status(200, 201, 404), 201)

    def test_a_404_b_not_404(self):
        from compte import check_status

        self.assertEqual(check_status(200, 404, 200), 200)
        self.assertEqual(check_status(200, 404, 201), 201)

    def test_both_a_and_b_404(self):
        from compte import check_status

        self.assertEqual(check_status(200, 404, 404), 404)
