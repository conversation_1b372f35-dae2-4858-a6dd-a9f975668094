# import json
# import os
#
# from boto3.dynamodb.conditions import Key
#
# from compte import creer
# from utils.aws_utils import get_dynamodb_table
# from utils.mocking import mock_environnement
#
#
# @mock_environnement
# def test_ghost_user_insert():
#     table_name = os.environ['DYNAMODB']
#     ret = creer({}, None)
#     uid = json.loads(ret['body'])['SessionId']
#     table = get_dynamodb_table(table_name)
#     response = table.query(KeyConditionExpression=Key('uid').eq(uid))
#     assert len(response['Items']) > 0
