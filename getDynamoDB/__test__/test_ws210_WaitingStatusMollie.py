import unittest
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, Mock

from utils.api import api_caller
from utils.mocking import mock_api


@mock_api
class TestWS210(unittest.TestCase):
    def test_add_waiting_data(self):
        response_post = api_caller("POST", "/payements/mollie/status?PaymentId=PAYMENT_TEST", raw=True)
        self.assertEqual(response_post.status_code, 200)
        response_body = response_post.json()
        self.assertEqual(response_body["MollieStatus"], "waiting")
        self.assertEqual(response_body["PaymentId"], "PAYMENT_TEST")
        self.assertEqual(response_body["OrderId"], "UNKNOWN")
        self.assertAlmostEqual(
            datetime.fromisoformat(response_body["CreationDate"]),
            datetime.now(timezone.utc),
            delta=timedelta(seconds=10),
        )
        self.assertEqual(response_body["PaymentType"], "UNKNOWN")

    @patch("mollie.get_secret_string", new=Mock(return_value="mocked_secret_token"))
    @patch(
        "mollie.requests.get",
        new=Mock(
            return_value=Mock(
                json=Mock(
                    return_value={
                        "status": "test_example",  # Use a test status to prevent triggering ws109
                        "metadata": {"dossier": "example_order", "type": "example_type"},
                        "createdAt": "2023-01-01T00:00:00Z",
                    }
                )
            )
        ),
    )
    def test_add_waiting_but_already_finished(self):
        # Sent paiement update to get and save status
        response_post = api_caller(
            "POST",
            "/payements/mollie/update",
            raw=True,
            headers={"Content-Type": "text/plain"},
            body="id=test_mollie_id",
        )
        self.assertEqual(response_post.status_code, 200)

        # Try to put in waiting (should not)
        response_post = api_caller("POST", "/payements/mollie/status?PaymentId=test_mollie_id", raw=True)
        self.assertEqual(response_post.status_code, 200)
        response_body = response_post.json()
        self.assertEqual(response_body["MollieStatus"], "test_example")
        self.assertEqual(response_body["PaymentId"], "test_mollie_id")
        self.assertEqual(response_body["OrderId"], "example_order")
        self.assertEqual(response_body["CreationDate"], "2023-01-01T00:00:00Z")
        self.assertEqual(response_body["PaymentType"], "EXAMPLE_TYPE")

    def test_missing_query_params(self):
        response_post = api_caller("POST", "/payements/mollie/status", raw=True)
        self.assertEqual(response_post.status_code, 400)
        self.assertIn("MISSING_QUERY_PARAMS", response_post.text)

    def test_missing_payment_id(self):
        response_post = api_caller("POST", "/payements/mollie/status?PaymentId=", raw=True)

        self.assertEqual(response_post.status_code, 400)
        self.assertIn("MISSING_QUERY_PARAMS", response_post.text)
