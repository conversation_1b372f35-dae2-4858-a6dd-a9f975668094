import unittest
from unittest.mock import patch, Mock

from utils.api import api_caller
from utils.mocking import mock_api

mock_csv_file = """HEADPOINT;LockStatus;Datum;Date
541460900002869475;LOCKED;1/12/2024;12-01-24
541460900002891735;BLOCKED;10/24/2023;24-10-23
541449011000008579;;10/21/2023;21-10-23"""


@patch("check_ean_status.load_s3_file", new=Mock(return_value=mock_csv_file))
@mock_api
class TestCheckEanStatus(unittest.TestCase):
    def test_locked(self):
        response = api_caller("GET", "/ean/541460900002869475/lockStatus", raw=True)
        self.assertEqual(response.status_code, 200)
        self.assertDictEqual(response.json(), {"LockStatus": "LOCKED", "Date": "2024-01-12"})

    def test_blocked(self):
        response = api_caller("GET", "/ean/541460900002891735/lockStatus", raw=True)
        self.assertEqual(response.status_code, 200)
        self.assertDictEqual(response.json(), {"LockStatus": "BLOCKED", "Date": "2023-10-24"})

    def test_not_locked(self):
        response = api_caller("GET", "/ean/541449011000008579/lockStatus", raw=True)
        self.assertEqual(response.status_code, 404)
        self.assertDictEqual(
            response.json(),
            {"Error": 404, "Message": "EAN not found", "ErrorCode": "EAN_NOT_FOUND"},
        )

    def test_not_in_list(self):
        response = api_caller("GET", "/ean/541401234567891011/lockStatus", raw=True)
        self.assertEqual(response.status_code, 404)
        self.assertDictEqual(
            response.json(),
            {"Error": 404, "Message": "EAN not found", "ErrorCode": "EAN_NOT_FOUND"},
        )


if __name__ == "__main__":
    unittest.main()
