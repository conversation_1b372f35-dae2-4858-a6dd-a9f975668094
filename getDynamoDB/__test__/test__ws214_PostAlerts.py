import unittest

from utils.api import api_caller
from utils.errors import HttpError
from utils.mocking import get_mock_user_token, mock_api


@mock_api
class TestWS214AlertsConso(unittest.TestCase):
    def setUp(self):
        self.user_token = get_mock_user_token()
        self.headers = {
            "Authorization": f"Bearer {self.user_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        self.valid_querry_params = "Ean=541456700002569958&Meter=1SAG1105183360"

    def test_valid_data(self):
        valid_body = {"Consent": True, "Daily": 7, "Monthly": 200}
        response = api_caller("POST", f"/me/alerts?{self.valid_querry_params}", body=valid_body, headers=self.headers)
        expected_response = {"Uid": "Acc.Test.Dae9z", "Ean": "541456700002569958", "Meter": "1SAG1105183360", "Daily": 7.0, "Monthly": 200.0, "Consent": True}
        self.assertDictEqual(response, expected_response)

    def test_no_alerts(self):
        no_alerts_body = {"Consent": True}
        response = api_caller("POST", f"/me/alerts?{self.valid_querry_params}", body=no_alerts_body, headers=self.headers)
        expected_response = {"Uid": "Acc.Test.Dae9z", "Ean": "541456700002569958", "Meter": "1SAG1105183360", "Daily": None, "Monthly": None, "Consent": True}
        self.assertDictEqual(response, expected_response)

    def test_no_consent(self):
        no_alerts_body = {"Consent": False}
        with self.assertRaises(HttpError) as context:
            api_caller("POST", f"/me/alerts?{self.valid_querry_params}", body=no_alerts_body, headers=self.headers)

        error_message = str(context.exception)

        self.assertIn("Error 400", error_message)
        self.assertIn("Some required fields are missing or have an incorrect type", error_message)
        self.assertIn("ErrorCode : INVALID_FIELDS", error_message)

    def test_one_alerts(self):
        one_alert_body = {"Consent": True, "Daily": 7}
        response = api_caller("POST", f"/me/alerts?{self.valid_querry_params}", body=one_alert_body, headers=self.headers)
        expected_response = {"Uid": "Acc.Test.Dae9z", "Ean": "541456700002569958", "Meter": "1SAG1105183360", "Daily": 7.0, "Monthly": None, "Consent": True}
        self.assertDictEqual(response, expected_response)

    def test_negative_alerts(self):
        negative_alerts_body = {"Consent": True, "Daily": -7, "Monthly": 200}
        with self.assertRaises(HttpError) as context:
            api_caller("POST", f"/me/alerts?{self.valid_querry_params}", body=negative_alerts_body, headers=self.headers)

        error_message = str(context.exception)

        self.assertIn("Error 400", error_message)
        self.assertIn("Some required fields are missing or have an incorrect type", error_message)
        self.assertIn("ErrorCode : INVALID_FIELDS", error_message)
