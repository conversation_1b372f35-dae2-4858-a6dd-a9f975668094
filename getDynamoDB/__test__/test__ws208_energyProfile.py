import os
import unittest

from utils.api import api_caller
from utils.aws_utils import get_dynamodb_table
from utils.mocking import get_mock_user_token, mock_api


@mock_api
class TestWS208EnergyProfile(unittest.TestCase):
    def setUp(self):
        self.user_token = get_mock_user_token()
        self.headers = {
            "Authorization": f"Bearer {self.user_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        self.mock_energy_data = {
            "Uid": "Acc.Test.Dae9z",
            "Address": {"Street": "Rue de la test", "Number": "45", "Postcode": "4000", "City": "Liege", "Box": None, "Country": "BE"},
            "HashAddress": "cf39f7109c351d5ff834238ad576b18616d9bb01a8a691ba8092090a7e0be8f2",
            "EnergyAsset": {
                "NumberPeople": 3,
                "HabitationType": "HOUSE",
                "DomesticHotWater": True,
                "PrimaryHeatingSource": "PAC",
                "SwimmingPool": True,
                "Jacuzzi": False,
                "ElectricVehiclesChargedHome": 1,
            },
            "ConsentBilan": True,
            "ConsentEnergyAsset": True,
        }

        # Mock consumption parameters for misc_table
        self.mock_consumption_params = {
            "PrimaryHeatingSource": {
                "Gas": "0",
                "Oil": "0",
                "PAC": "0",
                "Electricity": {"APARTMENT": "10000", "HOUSE": "16500"},
            },
            "DomesticHotWater": "1000",
            "Jacuzzi": "3000",
            "SwimmingPool": "3500",
            "ElectricVehicles": "3000",
            "NumberOfPeopleInHousehold": {
                "1": "1845",
                "2": "2844",
                "3": "4093",
                "4": "4999",
                "5+": "800",
            },
        }

        # Retrieve the DynamoDB tables
        self.energy_profile_table = get_dynamodb_table(os.environ["UserEnergyProfiles"])
        self.misc_table = get_dynamodb_table(f"MyResaAPI_MISC_{os.environ['STAGE_TAG']}")

        self.valid_querry_params = "City=Liege&Street=Rue de la test&Number=45&Postcode=4000"

    def test_valid_data(self):
        self.energy_profile_table.put_item(Item=self.mock_energy_data)
        self.misc_table.put_item(Item={"Key": "EnergyConsumptionParameters", "Value": self.mock_consumption_params})
        response = api_caller("GET", f"/me/energy_profile?{self.valid_querry_params}", headers=self.headers)
        expected_response = {
            "EnergyAsset": {
                "NumberPeople": 3,
                "HabitationType": "HOUSE",
                "DomesticHotWater": True,
                "PrimaryHeatingSource": "PAC",
                "SwimmingPool": True,
                "Jacuzzi": False,
                "ElectricVehiclesChargedHome": 1,
            },
            "Bilan": None,
            "TotalConsumption": 13593.0,
            "ConsentBilan": True,
            "ConsentEnergyAsset": True,
        }
        self.assertDictEqual(response, expected_response)

    def test_total_consumption_with_different_data(self):
        test_cases = [
            {
                "energy_data": {
                    "Uid": "Acc.Test.Dae9z",
                    "Address": {"Street": "Rue de la test", "Number": "45", "Postcode": "4000", "City": "Liege", "Box": None, "Country": "BE"},
                    "HashAddress": "cf39f7109c351d5ff834238ad576b18616d9bb01a8a691ba8092090a7e0be8f2",
                    "EnergyAsset": {
                        "DomesticHotWater": True,
                        "ElectricVehiclesChargedHome": 2,
                        "HabitationType": "HOUSE",
                        "Jacuzzi": True,
                        "NumberPeople": 4,
                        "PrimaryHeatingSource": "ELECTRICITY",
                        "SwimmingPool": True,
                    },
                    "ConsentBilan": True,
                    "ConsentEnergyAsset": True,
                },
                "expected_total_consumption": 37999.0,
            },
            {
                "energy_data": {
                    "Uid": "Acc.Test.Dae9z",
                    "Address": {"Street": "Rue de la test", "Number": "45", "Postcode": "4000", "City": "Liege", "Box": None, "Country": "BE"},
                    "HashAddress": "cf39f7109c351d5ff834238ad576b18616d9bb01a8a691ba8092090a7e0be8f2",
                    "EnergyAsset": {
                        "DomesticHotWater": False,
                        "ElectricVehiclesChargedHome": 0,
                        "HabitationType": "APARTMENT",
                        "Jacuzzi": False,
                        "NumberPeople": 2,
                        "PrimaryHeatingSource": "PAC",
                        "SwimmingPool": False,
                    },
                    "ConsentBilan": True,
                    "ConsentEnergyAsset": True,
                },
                "expected_total_consumption": 2844.0,
            },
        ]

        for case in test_cases:
            with self.subTest(case=case):
                # Mock energy data
                self.energy_profile_table.put_item(Item=case["energy_data"])
                self.misc_table.put_item(Item={"Key": "EnergyConsumptionParameters", "Value": self.mock_consumption_params})

                response = api_caller("GET", f"/me/energy_profile?{self.valid_querry_params}", headers=self.headers)
                self.assertIn("TotalConsumption", response)
                self.assertAlmostEqual(response["TotalConsumption"], case["expected_total_consumption"])
