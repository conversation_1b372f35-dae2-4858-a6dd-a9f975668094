import unittest
from os import environ

from consentements import run_special_action

from utils.api import api_caller
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import convert_float_to_decimal
from utils.errors import HttpError
from utils.mocking import get_mock_user_token, mock_api
from utils.models.energy_profile_model import EnergyAsset, EnergyProfile, HabitationType, PrimaryHeatingSource
from utils.models.smartConso_alerts_model import SmartConsoAlertModel
from utils.models.user import User


@mock_api
class TestWS112ConsentHistory(unittest.TestCase):
    def setUp(self):
        self.user_token = get_mock_user_token()
        self.headers = {
            "Authorization": f"Bearer {self.user_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        self.valid_body = [{"Key": "test.1", "Value": True}, {"Key": "test.2", "Value": False}]

    def test_valid_body(self):
        response = api_caller("POST", "/me/consentements", body=self.valid_body, headers=self.headers)
        self.assertGreater(len(response), 0, "La liste est vide alors qu'elle ne devrait pas l'être")
        for item in response:
            self.assertIn("Key", item, "Clé 'Key' manquante dans la réponse")
            self.assertIn("Value", item, "Clé 'Value' manquante dans la réponse")
            self.assertIn("DateTime", item, "Clé 'DateTime' manquante dans la réponse")

    def test_invalid_multiple_consent(self):
        invalid_body = []
        invalid_body.extend(self.valid_body)
        invalid_body.append({"Key": "test.2", "Value": False})
        try:
            response = api_caller("POST", "/me/consentements", body=invalid_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "NOT_UNIQUE_CONSENT")

    def test_invalid_consent_format(self):
        invalid_body = [{"Keya": "test.2", "Valuea": False}]
        try:
            response = api_caller("POST", "/me/consentements", body=invalid_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "INVALID_CONSENT_FORMAT")


@mock_api
class TestRunSpecialAction(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.user_token = get_mock_user_token()
        cls.user = User.from_event({"headers": {"Authorization": f"Bearer {cls.user_token}"}})

        cls.table_alerts = get_dynamodb_table(environ["SmartConsoAlerts"])
        cls.table_energy_profiles = get_dynamodb_table(environ["UserEnergyProfiles"])
        cls.table_users = get_dynamodb_table(environ["DYNAMODB"])

    def setUp(self):
        self.table_alerts.put_item(
            Item=convert_float_to_decimal(
                SmartConsoAlertModel(
                    consent=True,
                    uid=self.user.uid,
                    ean=self.user.ean[0].ean,
                    meter=self.user.ean[0].meterid[0],
                    daily=5,
                    monthly=10,
                ).model_dump(exclude={"consent"}),
            ),
        )

        self.table_energy_profiles.put_item(
            Item=EnergyProfile(
                uid=self.user.uid,
                bilan=True,
                energy_asset=EnergyAsset(
                    number_people=1,
                    habitation_type=HabitationType.HOUSE,
                    domestic_hot_water=False,
                    primary_heating_source=PrimaryHeatingSource.GAZ,
                    swimming_pool=False,
                    jacuzzi=False,
                    electric_vehicles_charged_home=0,
                ),
                hash_address=self.user.ean[0].address_hash,
                consent_bilan=True,
                consent_energy_asset=True,
            ).model_dump_dynamodb(by_alias=True),
        )

        self.table_users.update_item(
            Key={"uid": self.user.uid},
            UpdateExpression="SET #preference1 = :consent_value, #preference2 = :consent_value, #preference3 = :consent_value",
            ExpressionAttributeNames={
                "#preference1": "preferences.com_smartportal_bilan_mail",
                "#preference2": "preferences.com_smartportal_alert_mail",
                "#preference3": "preferences.com_smartportal_alert_sms",
            },
            ExpressionAttributeValues={":consent_value": True},
        )

    def test_run_special_action_bilan_false(self):
        consents = [{"Key": f"maconso.{self.user.ean[0].address_hash}.bilan", "Value": False}]

        run_special_action(self.user, consents)

        energy_profile = self.table_energy_profiles.get_item(Key={"Uid": self.user.uid, "HashAddress": self.user.ean[0].address_hash})["Item"]

        self.assertEqual(energy_profile["Bilan"], False)
        self.assertEqual(energy_profile["ConsentBilan"], False)

        user_data = self.table_users.get_item(Key={"uid": self.user.uid})["Item"]

        self.assertEqual(user_data["preferences"]["com_smartportal_bilan_mail"], False)

    def test_run_special_action_alert_mail_false(self):
        consents = [{"Key": f"maconso.{self.user.ean[0].address_hash}.alertEmail", "Value": False}]

        run_special_action(self.user, consents)

        user_data = self.table_users.get_item(Key={"uid": self.user.uid})["Item"]
        self.assertEqual(user_data["preferences"]["com_smartportal_alert_mail"], False)

    def test_run_special_action_alert_sms_false(self):
        consents = [{"Key": f"maconso.{self.user.ean[0].address_hash}.alertSms", "Value": False}]

        run_special_action(self.user, consents)

        user_data = self.table_users.get_item(Key={"uid": self.user.uid})["Item"]
        self.assertEqual(user_data["preferences"]["com_smartportal_alert_sms"], False)

    def test_run_special_action_delete_alerts(self):
        consents = [
            {"Key": f"maconso.{self.user.ean[0].address_hash}.alertEmail", "Value": False},
            {"Key": f"maconso.{self.user.ean[0].address_hash}.alertSms", "Value": False},
        ]

        run_special_action(self.user, consents)

        response = self.table_alerts.get_item(Key={"Uid": self.user.uid, "Meter": self.user.ean[0].meterid[0]})
        self.assertIsNone(response.get("Item"))

        user_data = self.table_users.get_item(Key={"uid": self.user.uid})["Item"]
        self.assertEqual(user_data["preferences"]["com_smartportal_alert_mail"], False)
        self.assertEqual(user_data["preferences"]["com_smartportal_alert_sms"], False)

    def test_run_special_action_energy_asset_false(self):
        consents = [{"Key": f"maconso.{self.user.ean[0].address_hash}.energyAsset", "Value": False}]

        run_special_action(self.user, consents)

        energy_profile = self.table_energy_profiles.get_item(Key={"Uid": self.user.uid, "HashAddress": self.user.ean[0].address_hash})["Item"]

        self.assertEqual(energy_profile["EnergyAsset"], None)
        self.assertEqual(energy_profile["ConsentEnergyAsset"], False)
