# import json
# import os
# from datetime import datetime, timedelta
#
# from boto3.dynamodb.conditions import Key
#
# from compte import activer
# from utils.aws_utils import get_dynamodb_table
# from utils.mocking import samples, mock_environnement
# from utils.token_utils import encode_simple_jwt
#
#
# @mock_environnement
# def test_user_activate_response():
#     table_name = os.environ['DYNAMODB']
#     user = samples['ghost_user']
#     user['email'] = "<EMAIL>"
#     get_dynamodb_table(table_name).put_item(Item=user)
#     token = encode_simple_jwt({
#         'uid':
#             user['uid'],
#         'email':
#             user['email'],
#         'expirationTimestamp':
#             (datetime.now() + timedelta(hours=3)).timestamp()
#     })
#     ret = activer(
#         {
#             "headers": {
#                 "SessionId": user['uid']
#             },
#             'queryStringParameters': {
#                 'Token': token
#             },
#             'body':
#                 json.dumps({
#                     "Password": "Welcome@2020",
#                     "Firstname": "Mini",
#                     "Lastname": "Mouse",
#                     "Phone": "0000",
#                     "Adresse": {
#                         "Rue": "mock street",
#                         "NumRue": "5",
#                         "Localite": "mock city",
#                         "Cdpostal": "4000"
#                     }
#                 })
#         }, None)
#     response = json.loads(ret['body'])
#     assert not response['Uid'].startswith('RESACLIENTS\\')
#     assert user['email'] == response['Email']
#
#
# @mock_environnement
# def test_user_activate_insert():
#     table_name = os.environ['DYNAMODB']
#     user = samples['ghost_user']
#     user['email'] = "<EMAIL>"
#     get_dynamodb_table(table_name).put_item(Item=user)
#     token = encode_simple_jwt({
#         'uid':
#             user['uid'],
#         'email':
#             user['email'],
#         'expirationTimestamp':
#             (datetime.now() + timedelta(hours=3)).timestamp()
#     })
#     ret = activer(
#         {
#             "headers": {
#                 "SessionId": user['uid']
#             },
#             'queryStringParameters': {
#                 'Token': token
#             },
#             'body':
#                 json.dumps({
#                     "Password": "Welcome@2020",
#                     "Firstname": "Mini",
#                     "Lastname": "Mouse",
#                     "Phone": "0000",
#                     "Adresse": {
#                         "Rue": "mock street",
#                         "NumRue": "5",
#                         "Localite": "mock city",
#                         "Cdpostal": "4000"
#                     }
#                 })
#         }, None)
#     uid = json.loads(ret['body'])['Uid']
#     table = get_dynamodb_table(table_name)
#     response = table.query(KeyConditionExpression=Key('uid').eq(uid))
#     assert len(response['Items']) > 0
