import unittest
from unittest.mock import patch, Mock

from utils.api import api_caller
from utils.mocking import mock_api


@patch("mollie.get_secret_string", new=<PERSON><PERSON>(return_value="mocked_secret_token"))
@patch(
    "mollie.requests.get",
    new=Mock(
        return_value=Mock(
            json=Mock(
                return_value={
                    "status": "test_example",  # Use a test status to prevent triggering ws109
                    "metadata": {"dossier": "000000000123", "type": "example_type"},
                    "createdAt": "2023-01-01T00:00:00Z",
                }
            )
        )
    ),
)
@mock_api
class TestUpdateFunction(unittest.TestCase):
    def test_update_with_mocked_requests_get(self):
        # Sent paiement update to get and save status
        response_post = api_caller(
            "POST",
            "/payements/mollie/update",
            raw=True,
            headers={"Content-Type": "text/plain"},
            body="id=test_mollie_id",
        )
        self.assertEqual(response_post.status_code, 200)

        # Fetch result by OrderId and PaymentType
        response_get = api_caller("GET", "/payements/mollie/status?OrderId=000000000123&PaymentType=EXAMPLE_TYPE", raw=True)
        self.assertEqual(response_get.status_code, 200)

        body_status = response_get.json()
        self.assertDictEqual(
            body_status,
            {"MollieStatus": "test_example", "PaymentId": "test_mollie_id", "OrderId": "000000000123", "CreationDate": "2023-01-01T00:00:00Z", "PaymentType": "EXAMPLE_TYPE"},
        )

        # Fetch result by PaymentId
        response_get = api_caller("GET", "/payements/mollie/status?PaymentId=test_mollie_id", raw=True)
        self.assertEqual(response_get.status_code, 200)

        body_status = response_get.json()
        self.assertDictEqual(
            body_status,
            {"MollieStatus": "test_example", "PaymentId": "test_mollie_id", "OrderId": "000000000123", "CreationDate": "2023-01-01T00:00:00Z", "PaymentType": "EXAMPLE_TYPE"},
        )
