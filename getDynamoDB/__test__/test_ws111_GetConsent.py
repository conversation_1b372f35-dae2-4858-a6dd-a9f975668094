import unittest

from utils.api import api_caller
from utils.mocking import mock_api, get_mock_user_token


@mock_api
class TestWS111ConsentHistory(unittest.TestCase):
    def setUp(self):
        self.user_token = get_mock_user_token()
        self.headers = {
            "Authorization": f"Bearer {self.user_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    def test_valid_body(self):
        response = api_caller("GET", "/me/consentements", headers=self.headers)
        self.assertGreater(len(response), 0, "La liste est vide alors qu'elle ne devrait pas l'être")
        for item in response:
            self.assertIn("Key", item, "Clé 'Key' manquante dans la réponse")
            self.assertIn("Value", item, "Clé 'Value' manquante dans la réponse")
            self.assertIn("DateTime", item, "Clé 'DateTime' manquante dans la réponse")
