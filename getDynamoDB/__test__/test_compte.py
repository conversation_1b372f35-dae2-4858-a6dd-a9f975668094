import unittest
from unittest.mock import patch, Mock

from utils.api import api_caller
from utils.mocking import mock_api, get_mock_user_token


def mock_to_response(self, fake_cursor, params=None):
    data = {
        "dossiers": [],
        "eans": [],
        "preferences": [],
        "user_data": [
            {
                "Bp": 3100487064,
                "Cdpostal": "4367",
                "CodePays": "BE",
                "ContactEmail": "<EMAIL>",
                "Email": "<EMAIL>",
                "Firstname": "<PERSON>",
                "Lastname": "Franco<PERSON>",
                "Localite": "Fize-le-Marsal",
                "NumRue": "29",
                "Phone": "+32498629932",
                "PhoneFixe": None,
                "Rue": "Rue Nestor Royer",
            }
        ],
    }
    data = self.layer(data, None)
    return data


@patch(
    "compte.sap_edit_user",
    new=Mock(),
)
@patch(
    "controllers.ws14.ws14.fetch_master",
    new=Mock(return_value=None),
)
@patch(
    "controllers.ws14.ws14.fetch_is_smart_portal_enabled",
    new=Mock(return_value=None),
)
@patch(
    "controllers.ws14.ws14.syncMaster",
    new=Mock(),
)
@mock_api
class TestSmartPortalConsent(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        from controllers.parallel_controller import ParallelController

        cls.original_to_response = ParallelController.to_response
        ParallelController.to_response = mock_to_response
        cls.token = get_mock_user_token()

    @classmethod
    def tearDownClass(cls):
        from controllers.parallel_controller import ParallelController

        ParallelController.to_response = cls.original_to_response

    def test_should_be_null_by_default(self):
        response = api_caller("GET", "/me/dashboard?quick=true", headers={"Authorization": f"Bearer {self.token}"}, raw=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn("SmartPortalConsent", response.json())
        self.assertIsNone(response.json()["SmartPortalConsent"])

    def test_correctly_assigned_true(self):
        # Set "SmartPortalConsent" to True using WS97
        patch_response = api_caller("PATCH", "/utilisateurs", headers={"Authorization": f"Bearer {self.token}"}, body={"SmartPortalConsent": True}, raw=True)
        self.assertEqual(patch_response.status_code, 200)

        # Verify that "SmartPortalConsent" is True in WS14
        get_response = api_caller("GET", "/me/dashboard?quick=true", headers={"Authorization": f"Bearer {self.token}"}, raw=True)
        self.assertEqual(get_response.status_code, 200)
        self.assertTrue(get_response.json().get("SmartPortalConsent"))

    def test_correctly_assigned_false(self):
        # Set "SmartPortalConsent" to False using WS97
        patch_response = api_caller("PATCH", "/utilisateurs", headers={"Authorization": f"Bearer {self.token}"}, body={"SmartPortalConsent": False}, raw=True)
        self.assertEqual(patch_response.status_code, 200)

        # Verify that "SmartPortalConsent" is False in WS14
        get_response = api_caller("GET", "/me/dashboard?quick=true", headers={"Authorization": f"Bearer {self.token}"}, raw=True)
        self.assertEqual(get_response.status_code, 200)
        self.assertFalse(get_response.json().get("SmartPortalConsent"))

    def test_invalid_type_smart_portal_consent(self):
        # Send SmartPortalConsent as a string value
        patch_response_str = api_caller("PATCH", "/utilisateurs", headers={"Authorization": f"Bearer {self.token}"}, body={"SmartPortalConsent": "invalid_type_string"}, raw=True)
        self.assertEqual(patch_response_str.status_code, 400)
        self.assertIn("ErrorCode", patch_response_str.json())
        self.assertEqual(patch_response_str.json().get("ErrorCode"), "INVALID_TYPE")

        # Send SmartPortalConsent as a numeric value
        patch_response_num = api_caller("PATCH", "/utilisateurs", headers={"Authorization": f"Bearer {self.token}"}, body={"SmartPortalConsent": 12345}, raw=True)
        self.assertEqual(patch_response_num.status_code, 400)
        self.assertIn("ErrorCode", patch_response_num.json())
        self.assertEqual(patch_response_num.json().get("ErrorCode"), "INVALID_TYPE")


if __name__ == "__main__":
    unittest.main()
