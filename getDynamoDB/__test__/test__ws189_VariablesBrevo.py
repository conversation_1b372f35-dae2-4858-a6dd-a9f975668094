import unittest

from envMessageTemplateidBody import (
    get_html_variables_without_params,
    replace_in_loop_name,
    clean_element,
    to_del_list,
    build_hierarchical_dict,
    transform_dict_to_list_dict,
    merge_keys_remove_contact_add_fields,
)
from utils.mocking import mock_environnement

test_template = """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="format-detection" content="telephone=no"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Fluides : Rectifications {% for element in params.ContentA %} {% if not loop.first %} - {% endif %} EAN {{ element.ENERGY_TYPE }} : {{ element.EAN }}{% endfor %}</title><style type="text/css" emogrify="no">#outlook a { padding:0; } .ExternalClass { width:100%; } .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div { line-height: 100%; } table td { border-collapse: collapse; mso-line-height-rule: exactly; } .editable.image { font-size: 0 !important; line-height: 0 !important; } .nl2go_preheader { display: none !important; mso-hide:all !important; mso-line-height-rule: exactly; visibility: hidden !important; line-height: 0px !important; font-size: 0px !important; } body { width:100% !important; -webkit-text-size-adjust:100%; -ms-text-size-adjust:100%; margin:0; padding:0; } img { outline:none; text-decoration:none; -ms-interpolation-mode: bicubic; } a img { border:none; } table { border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt; } th { font-weight: normal; text-align: left; } *[class="gmail-fix"] { display: none !important; } </style><style type="text/css" emogrify="no"> @media (max-width: 600px) { .gmx-killpill { content: ' \03D1';} } </style><style type="text/css" emogrify="no">@media (max-width: 600px) { .gmx-killpill { content: ' \03D1';} .r0-o { border-style: solid !important; margin: 0 auto 0 auto !important; width: 320px !important } .r1-i { background-color: #f6f6f6 !important } .r2-o { border-style: solid !important; margin: 0 auto 0 auto !important; width: 100% !important } .r3-c { box-sizing: border-box !important; display: block !important; valign: top !important; width: 100% !important } .r4-o { border-style: solid !important; width: 100% !important } .r5-i { background-color: #f6f6f6 !important; padding-bottom: 10px !important; padding-left: 10px !important; padding-right: 10px !important; padding-top: 10px !important; text-align: center !important } .r6-c { box-sizing: border-box !important; text-align: center !important; valign: top !important; width: 100% !important } .r7-o { background-image: url('https://img.mailinblue.com/3628247/images/rnb/original/633d5899f0284346ea764714.jpg') !important; background-size: cover !important; border-style: solid !important; margin: 0 auto 0 auto !important; width: 100% !important } .r8-i { padding-bottom: 20px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 20px !important } .r9-i { padding-left: 0px !important; padding-right: 0px !important } .r10-c { box-sizing: border-box !important; text-align: center !important; valign: top !important; width: 250px !important } .r11-o { border-style: solid !important; margin: 0 auto 0 auto !important; width: 250px !important } .r12-i { padding-bottom: 0px !important; padding-top: 0px !important } .r13-i { background-color: #e84e0f !important; padding-bottom: 10px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 10px !important } .r14-o { border-style: solid !important; margin: 0 auto 0 0 !important; width: 100% !important } .r15-i { text-align: center !important } .r16-i { background-color: #ffffff !important; padding-bottom: 10px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 10px !important } .r17-i { text-align: left !important } .r18-o { border-style: solid !important; margin: 0 auto 0 auto !important; margin-bottom: 0px !important; margin-top: 0px !important; width: 100% !important } .r19-i { background-color: #ffffff !important; padding-left: 15px !important; padding-right: 15px !important } .r20-o { border-style: solid !important; margin: 0 auto 0 auto !important; width: 150px !important } .r21-i { background-color: #ffffff !important; padding-bottom: 5px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 10px !important } .r22-c { box-sizing: border-box !important; text-align: left !important; valign: top !important; width: 100% !important } .r23-i { padding-bottom: 10px !important; padding-top: 10px !important; text-align: center !important } .r24-i { background-color: #ffffff !important; padding-bottom: 0px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 0px !important } .r25-i { background-color: #ffffff !important; padding-bottom: 5px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 5px !important } .r26-i { background-color: #ffffff !important; padding-bottom: 20px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 20px !important } .r27-i { background-color: #ffffff !important; padding-bottom: 0px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 10px !important } .r28-i { padding-left: 0px !important; padding-right: 20px !important } .r29-c { box-sizing: border-box !important; text-align: center !important; valign: top !important; width: 180px !important } .r30-o { border-style: solid !important; margin: 0 auto 0 auto !important; width: 180px !important } .r31-i { padding-bottom: 10px !important; text-align: center !important } .r32-o { border-style: solid !important; margin: 0 auto 0 auto !important; margin-bottom: 2px !important; width: 100% !important } .r33-i { background-color: #e84e0f !important; padding-bottom: 20px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 20px !important } .r34-i { padding-bottom: 20px !important; padding-left: 20px !important; padding-right: 20px !important; text-align: center !important } .r35-c { box-sizing: border-box !important; text-align: center !important; width: 100% !important } .r36-i { font-size: 0px !important; padding-bottom: 2px !important; padding-left: 105px !important; padding-right: 105px !important } .r37-c { box-sizing: border-box !important; width: 32px !important } .r38-o { border-style: solid !important; margin-right: 8px !important; width: 32px !important } .r39-i { padding-bottom: 5px !important; padding-top: 5px !important } .r40-o { border-style: solid !important; margin-right: 0px !important; width: 32px !important } .r41-i { background-color: #f6f6f6 !important; padding-bottom: 0px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 10px !important } .r42-i { background-color: #f6f6f6 !important; padding-bottom: 20px !important; padding-left: 15px !important; padding-right: 15px !important; padding-top: 10px !important } body { -webkit-text-size-adjust: none } .nl2go-responsive-hide { display: none } .nl2go-body-table { min-width: unset !important } .mobshow { height: auto !important; overflow: visible !important; max-height: unset !important; visibility: visible !important; border: none !important } .resp-table { display: inline-table !important } .magic-resp { display: table-cell !important } } </style><!--[if !mso]><!--><style type="text/css" emogrify="no">@import url("https://fonts.googleapis.com/css2?family=Poppins&family=Roboto"); </style><!--<![endif]--><style type="text/css">p, h1, h2, h3, h4, ol, ul, li { margin: 0; } a, a:link { color: #e84e0f; text-decoration: underline } .nl2go-default-textstyle { color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word } .default-button { color: #ffffff; font-family: arial,helvetica,sans-serif; font-size: 16px; font-style: normal; font-weight: normal; line-height: 1.15; text-decoration: none; word-break: break-word } .default-heading1 { color: #1F2D3D; font-family: arial,helvetica,sans-serif; font-size: 36px; word-break: break-word } .default-heading2 { color: #1F2D3D; font-family: arial,helvetica,sans-serif; font-size: 32px; word-break: break-word } .default-heading3 { color: #1F2D3D; font-family: arial,helvetica,sans-serif; font-size: 24px; word-break: break-word } .default-heading4 { color: #1F2D3D; font-family: arial,helvetica,sans-serif; font-size: 18px; word-break: break-word } a[x-apple-data-detectors] { color: inherit !important; text-decoration: inherit !important; font-size: inherit !important; font-family: inherit !important; font-weight: inherit !important; line-height: inherit !important; } .no-show-for-you { border: none; display: none; float: none; font-size: 0; height: 0; line-height: 0; max-height: 0; mso-hide: all; overflow: hidden; table-layout: fixed; visibility: hidden; width: 0; } </style><!--[if mso]><xml> <o:OfficeDocumentSettings> <o:AllowPNG/> <o:PixelsPerInch>96</o:PixelsPerInch> </o:OfficeDocumentSettings> </xml><![endif]--><style type="text/css">a:link{color: #e84e0f; text-decoration: underline;}</style></head><body bgcolor="#f6f6f6" text="#3b3f44" link="#e84e0f" yahoo="fix" style="background-color: #f6f6f6; padding-top: 38px;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" class="nl2go-body-table" width="100%" style="background-color: #f6f6f6; width: 100%;"><tr><td> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="590" align="center" class="r0-o" style="table-layout: fixed; width: 590px;"><tr><td valign="top" class="r1-i" style="background-color: #f6f6f6;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="background-color: #ffffff; table-layout: fixed;"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" class="r5-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; background-color: #f6f6f6; padding-bottom: 10px; padding-left: 30px; padding-right: 30px; padding-top: 10px; text-align: center;"> <div><p style="margin: 0;"><a href="{{ mirror }}" style="color: #e84e0f; text-decoration: underline;"><span style="color: #e84e0f; font-family: Arial,Helvetica,sans-serif; font-size: 13px;"><u>Voir la version en ligne</u></span></a></p></div> </td> </tr></table></th> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r7-o" style="background-color: #e84e0f; background-image: url('https://img.mailinblue.com/3628247/images/rnb/original/633d5899f0284346ea764714.jpg'); background-position: top; background-repeat: no-repeat; background-size: cover; font-size: 0; table-layout: fixed; width: 100%;"><tr><td class="r8-i" style="padding-bottom: 20px; padding-left: 20px; padding-right: 20px; padding-top: 20px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r4-o" style="table-layout: fixed; width: 100%;"><tr><td valign="top" class="r9-i" style="padding-left: 5px; padding-right: 5px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><td class="r10-c" align="center"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="250" class="r11-o" style="table-layout: fixed; width: 250px;"><tr><td class="r12-i"> <a href="https://www.resa.be/fr/?utm_source=brevo&utm_campaign=Nouvel diteur CLIENT - Relevs dindex V7 ELEC  GAZ FR - Communication aprs estimation&utm_medium=email" target="_blank" style="color: #e84e0f; text-decoration: underline;"> <img src="https://img.mailinblue.com/3628247/images/rnb/original/611f7210fac2e965f914ee96.png?t=1630932501324" width="250" alt="Resa" border="0" style="display: block; width: 100%;"></a> </td> </tr></table></td> </tr></table></td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r13-i" style="background-color: #e84e0f; padding-bottom: 10px; padding-left: 20px; padding-right: 20px; padding-top: 10px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r15-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center;"> <div><p style="margin: 0; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 14px; color: #3c4858;"><span><span style="color: #FFFFFF;">Votre adresse de consommation : </span></span><br><span><span style="color: #FFFFFF;">{{params.Header.STREET}} {{params.Header.POST_CODE}} {{params.Header.CITY}}</span></span></p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r16-i" style="background-color: #ffffff; padding-bottom: 10px; padding-left: 20px; padding-right: 20px; padding-top: 10px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="left" valign="top" class="r17-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: left;"> <div><p style="margin: 0; font-family: Arial,Helvetica,sans-serif; font-size: 14px; color: #3c4858;"></p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r18-o" style="table-layout: fixed; width: 100%;"><tr><td class="r19-i" style="background-color: #ffffff;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="150" align="center" class="r20-o" style="border-collapse: separate; border-radius: 5px; table-layout: fixed; width: 150px;"><tr><td class="r12-i" style="border-radius: 5px; font-size: 0px; line-height: 0px;"> <img src="https://img.mailinblue.com/3628247/images/rnb/original/62de4572458ca507ef36c9be.png?t=1664973686897" width="150" border="0" style="display: block; width: 100%; border-radius: 5px;"></td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r21-i" style="background-color: #ffffff; padding-bottom: 5px; padding-left: 20px; padding-right: 20px; padding-top: 10px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r4-o" style="table-layout: fixed; width: 100%;"><tr><td valign="top" class="r9-i" style="padding-left: 5px; padding-right: 5px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><td class="r22-c" align="left"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r23-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; word-break: break-word; line-height: 32px; padding-bottom: 10px; padding-top: 10px; text-align: center;"> <div><p style="margin: 0; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 24px; color: #3c4858;"><span><span style="color: #e84e0f;"><span style="font-size: 26px;"><strong>Estimation des index pour l’électricité et le gaz</strong></span></span></span></p></div> </td> </tr></table></td> </tr></table></td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r24-i" style="background-color: #ffffff; padding-left: 20px; padding-right: 20px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r4-o" style="table-layout: fixed; width: 100%;"><tr><td valign="top" class="r9-i" style="padding-left: 5px; padding-right: 5px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><td class="r22-c" align="left"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r23-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; padding-bottom: 10px; padding-top: 10px; text-align: center;"> <div><p style="margin: 0; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 24px; color: #3c4858;"><span style="color: #e84e0f;"><span style="font-size: 18px;">Un relevé d’index correct = une facture correcte !</span></span></p></div> </td> </tr></table></td> </tr></table></td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r25-i" style="background-color: #ffffff; padding-bottom: 5px; padding-left: 20px; padding-right: 20px; padding-top: 5px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="left" valign="top" class="r17-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: left;"> <div><p style="margin: 0; font-family: Arial,Helvetica,sans-serif; font-size: 14px; color: #3c4858;"></p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r26-i" style="background-color: #ffffff; padding-bottom: 20px; padding-left: 20px; padding-right: 20px; padding-top: 20px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r15-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center;"> <div><p style="margin: 0; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 14px; color: #3c4858;"><span>Dans le cadre de la relève annuelle de votre compteur ou suite à un changement de fournisseur, nous venons d’estimer vos index.</span></p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r27-i" style="background-color: #ffffff; padding-left: 20px; padding-right: 20px; padding-top: 10px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="40%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r4-o" style="table-layout: fixed; width: 100%;"><tr><td valign="top" class="r28-i" style="padding-right: 20px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><td class="r29-c" align="center"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="180" class="r30-o" style="table-layout: fixed; width: 180px;"><tr><td class="r12-i"> <img src="https://img.mailinblue.com/3628247/images/rnb/original/60a366abb50fde4b164e1f87.png?t=1621323655747" width="180" alt="Encodez vous-même votre index" border="0" style="display: block; width: 100%;"></td> </tr></table></td> </tr></table></td> </tr></table></th> <th width="60%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r31-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; padding-bottom: 10px; text-align: center;"> <div><p style="margin: 0; color: #3c4858; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 14px;"><span style="color: #3c4858;">Nous vous informons qu’il est toujours possible d’obtenir une rectification de cette estimation.</span><span style="color: #e84e0f;"> <strong>Envoyez-nous vos index et une photo de votre compteur en répondant à cet e-mail.</strong></span><br><br>Cela nous permettra de revoir, si nécessaire, notre estimation afin que la facturation de votre fournisseur d’énergie reflète au mieux votre utilisation d’énergie.<br> </p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r26-i" style="background-color: #ffffff; padding-bottom: 20px; padding-left: 20px; padding-right: 20px; padding-top: 20px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r15-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center;"> <div><p style="margin: 0; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 14px; color: #3c4858;"><span>Merci pour votre collaboration,</span><br><span><br>Votre gestionnaire de réseau RESA</span></p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r32-o" style="table-layout: fixed; width: 100%;"><tr><td class="r25-i" style="background-color: #ffffff; padding-bottom: 5px; padding-left: 20px; padding-right: 20px; padding-top: 5px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="left" valign="top" class="r17-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: left;"> <div><p style="margin: 0; font-family: Arial,Helvetica,sans-serif; font-size: 14px; color: #3c4858;"></p></div> </td> </tr></table></th> </tr></table></td> </tr><tr class="nl2go-responsive-hide"><td height="2" style="font-size: 2px; line-height: 2px;">­</td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r25-i" style="background-color: #ffffff; padding-bottom: 5px; padding-left: 20px; padding-right: 20px; padding-top: 5px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="left" valign="top" class="r17-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: left;"> <div><p style="margin: 0; font-family: Arial,Helvetica,sans-serif; font-size: 14px; color: #3c4858;"></p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r16-i" style="background-color: #ffffff; padding-bottom: 10px; padding-left: 20px; padding-right: 20px; padding-top: 10px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r15-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center;"> <div><p style="margin: 0; color: #3c4858; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 14px;"><strong>INFORMATIONS CLIENT</strong><br><br><span style="color: #e84e0f;"><strong>VOS RÉFÉRENCES POUR L'ÉLECTRICITÉ :</strong></span><br>{% for item in params.ContentA %}{% if item.ENERGY_TYPE == "Elec" %}<br>Référence EAN : <span style="color: #999999;">{{item.EAN}}</span><br>Numéro(s) de compteur(s) :<span style="color: #D3D3D3;"> </span><span style="color: #999999;">{% if 'SERIAL_NUMBER' in item.ContentB %}{{item.ContentB.SERIAL_NUMBER}}{% else %}{% for itemB in item.ContentB %}{{itemB.SERIAL_NUMBER}}{% if not forloop.Last %} - {% endif %}{% endfor %}{% endif %}</span><br>Adresse de consommation : <span style="color: #999999;">{{params.Header.STREET}} {{params.Header.POST_CODE}} {{params.Header.CITY}}</span><br>Type d’index : <i>Relevé d’index annuels</i>{% endif %}{% endfor %}<br><span style="color: #e84e0f;">***</span><br><br><span style="color: #e84e0f;"><strong>VOS RÉFÉRENCES POUR LE GAZ :</strong></span><br>{% for item in params.ContentA %}{% if item.ENERGY_TYPE == "Gaz" %}<br>Référence EAN :<span style="color: #999999;"> {{item.EAN}}</span><br>Numéro(s) de compteur(s) :<span style="color: #999999;"> {% if 'SERIAL_NUMBER' in item.ContentB %}{{item.ContentB.SERIAL_NUMBER}}{% else %}{% for itemB in item.ContentB %}{{itemB.SERIAL_NUMBER}}{% if not forloop.Last %} - {% endif %}{% endfor %}{% endif %}</span><br>Adresse de consommation : <span style="color: #999999;">{{params.Header.STREET}} {{params.Header.POST_CODE}} {{params.Header.CITY}}</span><br>Type d’index : <i>Relevé d’index annuel</i>{% endif %}{% endfor %}</p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r21-i" style="background-color: #ffffff; padding-bottom: 5px; padding-left: 20px; padding-right: 20px; padding-top: 10px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="left" valign="top" class="r17-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: left;"> <div><p style="margin: 0; font-family: Arial,Helvetica,sans-serif; font-size: 14px; color: #3c4858;"></p></div> </td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r33-i" style="background-color: #e84e0f; padding-bottom: 20px; padding-top: 20px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r4-o" style="table-layout: fixed; width: 100%;"><tr><td valign="top" class="r9-i" style="padding-left: 5px; padding-right: 5px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><td class="r22-c" align="left"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r34-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; padding-bottom: 20px; padding-left: 20px; padding-right: 20px; text-align: center;"> <div><p style="margin: 0; color: #888888; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 14px;"><span style="color: #FFFFFF; font-size: 13px;">Pour toute question, n’hésitez pas à contacter le service clientèle au <strong>04/220.12.11</strong> du lundi au samedi de 8h à 21h ou à l’adresse </span><a href="mailto:<EMAIL>?subject=Fluides%20%3A%20Rectifications%20EAN%20{{ params.ContentA.0.ENERGY_TYPE }}%20%3A%20{{ params.ContentA.0.EAN }}%20%20-%20%20EAN%20{{ params.ContentA.1.ENERGY_TYPE }}%20%3A%20{{ params.ContentA.1.EAN }}%20" title="Fluides : Rectifications EAN {{ params.ContentA.0.ENERGY_TYPE }} : {{ params.ContentA.0.EAN }} - EAN {{ params.ContentA.1.ENERGY_TYPE }} : {{ params.ContentA.1.EAN }} " target="_blank" style="color: #e84e0f; text-decoration: underline;"><span style="color: #FFFFFF; font-size: 13px;"><strong><EMAIL></strong></span></a></p></div> </td> </tr></table></td> </tr><tr><td class="r35-c" align="center"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="580" align="center" class="r2-o" style="table-layout: fixed; width: 580px;"><tr><td valign="top"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><td class="r35-c" align="center" style="display: inline-block;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="580" align="center" class="r2-o" style="table-layout: fixed; width: 580px;"><tr><td class="r36-i" style="padding-bottom: 2px; padding-left: 254px; padding-right: 254px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="40" class="r37-c mobshow resp-table" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r38-o" style="table-layout: fixed; width: 100%;"><tr><td class="r39-i" style="font-size: 0px; line-height: 0px; padding-bottom: 5px; padding-top: 5px;"> <a href="https://www.facebook.com/Resa-1683122401912471/?utm_source=brevo&utm_campaign=Nouvel diteur CLIENT - Relevs dindex V7 ELEC  GAZ FR - Communication aprs estimation&utm_medium=email" target="_blank" style="color: #e84e0f; text-decoration: underline;"> <img src="https://creative-assets.mailinblue.com/editor/social-icons/original_light/facebook_32px.png" width="32" border="0" style="display: block; width: 100%;"></a> </td> <td class="nl2go-responsive-hide" width="8" style="font-size: 0px; line-height: 1px;">­ </td> </tr></table></th> <th width="32" class="r37-c mobshow resp-table" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r40-o" style="table-layout: fixed; width: 100%;"><tr><td class="r39-i" style="font-size: 0px; line-height: 0px; padding-bottom: 5px; padding-top: 5px;"> <a href="https://www.linkedin.com/company/resa-grd/?utm_source=brevo&utm_campaign=Nouvel diteur CLIENT - Relevs dindex V7 ELEC  GAZ FR - Communication aprs estimation&utm_medium=email" target="_blank" style="color: #e84e0f; text-decoration: underline;"> <img src="https://creative-assets.mailinblue.com/editor/social-icons/original_light/linkedin_32px.png" width="32" border="0" style="display: block; width: 100%;"></a> </td> </tr></table></th> </tr></table></td> </tr></table></td> </tr></table></td> </tr></table></td> </tr></table></td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r41-i" style="background-color: #f6f6f6; padding-top: 10px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r4-o" style="table-layout: fixed; width: 100%;"><tr><td valign="top" class="r9-i" style="padding-left: 15px; padding-right: 15px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><td class="r22-c" align="left"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r15-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center;"> <div><p style="margin: 0; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 14px; color: #888888;"><span><span style="font-size: 11px;">Cet e-mail a été envoyé à {{ contact.EMAIL }} </span><span><span style="font-size: 11px;">Vous avez reçu cet email dans le cadre de votre relevé d'index en ligne.</span></span></span></p></div> </td> </tr></table></td> </tr><tr><td class="r22-c" align="left"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r15-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center;"> <div><p style="margin: 0;"><a href="{{ unsubscribe }}" style="color: #e84e0f; text-decoration: underline;"><span style="font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 12px; color: #666666; text-decoration: underline;">Se désinscrire</span></a></p></div> </td> </tr></table></td> </tr></table></td> </tr></table></th> </tr></table></td> </tr></table><table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="center" class="r2-o" style="table-layout: fixed; width: 100%;"><tr><td class="r42-i" style="background-color: #f6f6f6; padding-bottom: 20px; padding-left: 20px; padding-right: 20px; padding-top: 10px;"> <table width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation"><tr><th width="100%" valign="top" class="r3-c" style="font-weight: normal;"> <table cellspacing="0" cellpadding="0" border="0" role="presentation" width="100%" align="left" class="r14-o" style="table-layout: fixed; width: 100%;"><tr><td align="center" valign="top" class="r15-i nl2go-default-textstyle" style="color: #3b3f44; font-family: arial,helvetica,sans-serif; font-size: 16px; word-break: break-word; line-height: 16px; text-align: center;"> <div><p style="margin: 0; color: #3c4858; font-family: 'Proxima Nova','Arial',Helvetica,sans-serif; font-size: 14px;"><span style="color: #A9A9A9; font-size: 11px;">Consultez notre </span><a href="https://www.resa.be/fr/politique-dutilisation-des-cookies/?utm_source=brevo&utm_campaign=Nouvel diteur CLIENT - Relevs dindex V7 ELEC  GAZ FR - Communication aprs estimation&utm_medium=email" style="color: #e84e0f; text-decoration: underline;"><span style="color: #A9A9A9; font-size: 11px;"><u>politique de confidentialité</u></span></a><span style="color: #A9A9A9; font-size: 11px;">. </span><br><span style="color: #A9A9A9; font-size: 11px;">Tous droits réservés © {{ time_now|date:"2006" }} RESA</span></p></div> </td> </tr></table></th> </tr></table></td> </tr></table></td> </tr></table></td> </tr></table></body></html>
Fluides : Rectifications {% for element in params.ContentA %} {% if not loop.first %} - {% endif %} EAN {{ element.ENERGY_TYPE }} : {{ element.EAN }}{% endfor %}"""


def verifier_elements(liste_a, liste_b):
    for element_a in liste_a:
        for element_b in liste_b:
            if element_b in element_a:
                return False
    return True


def check_conditions(elements, indices):
    for index in indices:
        element = elements[index]
        if not (element == "endfor" or (element.startswith("for") and "in" in element)):
            return False
    return True


class TestVariablesBrevo(unittest.TestCase):
    @mock_environnement
    def test_html_without_params(self):
        variables_without_params = get_html_variables_without_params(test_template)
        self.assertIsInstance(variables_without_params, list, "Le résultat n'est pas une liste")
        for element in variables_without_params:
            self.assertNotIn("params", element, f"L'élément '{element}' contient 'params'")

    def test_for_loop_replacement(self):
        variables_without_params = [
            "for element in ContentA",
            "if not loop.first",
            "endif",
            "element.ENERGY_TYPE",
            "element.EAN",
            "endfor",
            "mirror",
            "Header.STREET",
            "Header.POST_CODE",
            "Header.CITY",
            "for item in ContentA",
            'if item.ENERGY_TYPE == "Elec"',
            "item.EAN",
            "if 'SERIAL_NUMBER' in item.ContentB",
            "item.ContentB.SERIAL_NUMBER",
            "else",
            "for itemB in item.ContentB",
            "itemB.SERIAL_NUMBER",
            "if not forloop.Last",
            "endif",
            "endfor",
            "endif",
            "Header.STREET",
            "Header.POST_CODE",
            "Header.CITY",
            "endif",
            "endfor",
            "for item in ContentA",
            'if item.ENERGY_TYPE == "Gaz"',
            "item.EAN",
            "if 'SERIAL_NUMBER' in item.ContentB",
            "item.ContentB.SERIAL_NUMBER",
            "else",
            "for itemB in item.ContentB",
            "itemB.SERIAL_NUMBER",
            "if not forloop.Last",
            "endif",
            "endfor",
            "endif",
            "Header.STREET",
            "Header.POST_CODE",
            "Header.CITY",
            "endif",
            "endfor",
            "ContentA.0.ENERGY_TYPE",
            "ContentA.0.EAN",
            "ContentA.1.ENERGY_TYPE",
            "ContentA.1.EAN",
            "ContentA.0.ENERGY_TYPE",
            "ContentA.0.EAN",
            "ContentA.1.ENERGY_TYPE",
            "ContentA.1.EAN",
            "contact.EMAIL",
            "unsubscribe",
            'time_now|date:"2006"',
            "for element in ContentA",
            "if not loop.first",
            "endif",
            "element.ENERGY_TYPE",
            "element.EAN",
            "endfor",
        ]
        replaced_elements, index_to_del, to_transform_in_list = replace_in_loop_name(variables_without_params)
        self.assertEqual(to_transform_in_list, ["ContentA", "ContentA.ContentB"])
        self.assertEqual(index_to_del, [0, 5, 10, 16, 20, 26, 27, 33, 37, 43, 55, 60])
        result = check_conditions(replaced_elements, index_to_del)
        self.assertTrue(result, "Les éléments aux indices spécifiés ne remplissent pas les conditions requises")

    def test_cleaned_elements_exclude_unwanted(self):
        replaced_elements, index_to_del, to_transform_in_list = (
            [
                "for element in ContentA",
                "if not loop.first",
                "endif",
                "ContentA.ENERGY_TYPE",
                "ContentA.EAN",
                "endfor",
                "mirror",
                "Header.STREET",
                "Header.POST_CODE",
                "Header.CITY",
                "for item in ContentA",
                'if ContentA.ENERGY_TYPE == "Elec"',
                "ContentA.EAN",
                "if 'SERIAL_NUMBER' in ContentA.ContentB",
                "ContentA.ContentB.SERIAL_NUMBER",
                "else",
                "for itemB in item.ContentB",
                "ContentA.ContentB.SERIAL_NUMBER",
                "if not forloop.Last",
                "endif",
                "endfor",
                "endif",
                "Header.STREET",
                "Header.POST_CODE",
                "Header.CITY",
                "endif",
                "endfor",
                "for item in ContentA",
                'if ContentA.ENERGY_TYPE == "Gaz"',
                "ContentA.EAN",
                "if 'SERIAL_NUMBER' in ContentA.ContentB",
                "ContentA.ContentB.SERIAL_NUMBER",
                "else",
                "for itemB in item.ContentB",
                "ContentA.ContentB.SERIAL_NUMBER",
                "if not forloop.Last",
                "endif",
                "endfor",
                "endif",
                "Header.STREET",
                "Header.POST_CODE",
                "Header.CITY",
                "endif",
                "endfor",
                "ContentA.0.ENERGY_TYPE",
                "ContentA.0.EAN",
                "ContentA.1.ENERGY_TYPE",
                "ContentA.1.EAN",
                "ContentA.0.ENERGY_TYPE",
                "ContentA.0.EAN",
                "ContentA.1.ENERGY_TYPE",
                "ContentA.1.EAN",
                "contact.EMAIL",
                "unsubscribe",
                'time_now|date:"2006"',
                "for element in ContentA",
                "if not loop.first",
                "endif",
                "ContentA.ENERGY_TYPE",
                "ContentA.EAN",
                "endfor",
            ],
            [0, 5, 10, 16, 20, 26, 27, 33, 37, 43, 55, 60],
            ["ContentA", "ContentA.ContentB"],
        )
        for i in range(len(index_to_del)):
            del replaced_elements[index_to_del[i] - i]
        cleaned_elements = [clean_element(elem) for elem in replaced_elements]
        cleaned_elements = [elem for elem in cleaned_elements if elem]
        self.assertTrue(verifier_elements(cleaned_elements, to_del_list()), "La liste A contient des éléments de la liste B")

    def test_hierarchical_dict(self):
        cleaned_elements = [
            "ContentA.ENERGY_TYPE",
            "ContentA.EAN",
            "Header.STREET",
            "Header.POST_CODE",
            "Header.CITY",
            "ContentA.ENERGY_TYPE",
            "ContentA.EAN",
            "ContentA.ContentB",
            "ContentA.ContentB.SERIAL_NUMBER",
            "ContentA.ContentB.SERIAL_NUMBER",
            "Header.STREET",
            "Header.POST_CODE",
            "Header.CITY",
            "ContentA.ENERGY_TYPE",
            "ContentA.EAN",
            "ContentA.ContentB",
            "ContentA.ContentB.SERIAL_NUMBER",
            "ContentA.ContentB.SERIAL_NUMBER",
            "Header.STREET",
            "Header.POST_CODE",
            "Header.CITY",
            "ContentA.0.ENERGY_TYPE",
            "ContentA.0.EAN",
            "ContentA.1.ENERGY_TYPE",
            "ContentA.1.EAN",
            "ContentA.0.ENERGY_TYPE",
            "ContentA.0.EAN",
            "ContentA.1.ENERGY_TYPE",
            "ContentA.1.EAN",
            "contact.EMAIL",
            "ContentA.ENERGY_TYPE",
            "ContentA.EAN",
        ]
        hierarchical_dict = build_hierarchical_dict(cleaned_elements)
        self.assertEqual(
            hierarchical_dict,
            {
                "ContentA": {"ENERGY_TYPE": "ENERGY_TYPE", "EAN": "EAN", "ContentB": {"SERIAL_NUMBER": "SERIAL_NUMBER"}},
                "Header": {"STREET": "STREET", "POST_CODE": "POST_CODE", "CITY": "CITY"},
                "contact": {"EMAIL": "EMAIL"},
            },
        )

    def test_added_elements(self):
        hierarchical_dict = {
            "ContentA": {"ENERGY_TYPE": "", "EAN": "", "ContentB": {"SERIAL_NUMBER": ""}},
            "Header": {"STREET": "", "POST_CODE": "", "CITY": ""},
            "contact": {"EMAIL": ""},
        }
        to_transform_in_list = ["ContentA", "ContentA.ContentB"]
        transformed_dict = transform_dict_to_list_dict(hierarchical_dict, to_transform_in_list)
        final_transformed_dict = merge_keys_remove_contact_add_fields(transformed_dict, "test")
        added_key = [
            "TEMPLATE_ID",
            "EMAIL",
            "MOBILE_PHONE",
            "PARTNER_ID",
        ]
        for key in added_key:
            with self.subTest(key=key):
                self.assertIn(key, final_transformed_dict["Header"], f"La clé '{key}' est absente dans les clés de 'Header'")
