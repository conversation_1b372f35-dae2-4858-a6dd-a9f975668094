import json
import unittest

from utils.api import api_caller
from utils.mocking import mock_environnement, mock_api
from utils.models.borne_recharge_model import Born<PERSON><PERSON><PERSON><PERSON><PERSON>


@mock_api
class TestLambdaFunction(unittest.TestCase):
    def test_add_one_borne_missing_date(self):
        test_data = _get_test_borne_data()
        test_data["Borne"].pop("Date")
        response_post = api_caller("POST", "/bornes_recharge", raw=True, body=test_data)
        print(response_post.status_code)
        print(response_post.text)
        self.assertEqual(response_post.status_code, 400)
        self.assertTrue("INVALID_JSON_OBJECT" in response_post.text)
        self.assertTrue("Borne.Date" in response_post.text)

    def test_add_one_borne_invalid_date(self):
        test_data = _get_test_borne_data()
        test_data["Borne"]["Date"] = "Youhou"
        response_post = api_caller("POST", "/bornes_recharge", raw=True, body=test_data)
        print(response_post.status_code)
        print(response_post.text)
        self.assertEqual(response_post.status_code, 400)
        self.assertTrue("BORNE_DATE_BAD_VALUE" in response_post.text)
        self.assertTrue("Date" in response_post.text)

    def test_add_one_borne_invalid_date_type(self):
        test_data = _get_test_borne_data()
        test_data["Borne"]["Date"] = True
        response_post = api_caller("POST", "/bornes_recharge", raw=True, body=test_data)
        print(response_post.status_code)
        print(response_post.text)
        self.assertEqual(response_post.status_code, 400)
        self.assertTrue("INVALID_JSON_OBJECT" in response_post.text)
        # depend on the dataclass-json version
        self.assertTrue("Borne.Date" in response_post.text or "date" in response_post.text)

    def test_add_one_borne(self):
        test_data = _get_test_borne_data()
        response_post = api_caller("POST", "/bornes_recharge", raw=True, body=test_data)
        self.assertEqual(response_post.status_code, 200)
        borne = BorneRecharge.from_json(response_post.text)
        self.assertIsNotNone(borne)

    @mock_environnement
    def test_add_multi_borne(self):
        test_data = _get_test_borne_data()
        response_post = api_caller("POST", "/bornes_recharge", raw=True, body=[test_data, test_data])
        self.assertEqual(response_post.status_code, 200)
        multi_data = json.loads(response_post.text)
        for data in multi_data:
            self.assertIsNotNone(data)
            BorneRecharge.from_dict(data)


def _get_test_borne_data():
    return {
        "Borne": {
            "Ean": "541448484848484849",
            "Adresse": {
                "CodePostal": "4020",
                "Commune": "BRUXELLES",
                "Numero": "25",
                "Pays": "Belgique",
                "Rue": "Avenue de la Liberté",
            },
            "Bidirectionnelle": False,
            "Date": "2023-04-21",
            "Marque": "BrandB",
            "Modele": "ModelY",
            "Photo": None,
            "Puissance": 15,
            "Serial": "WXYZ5678",
            "Utilisation": {"Libelle": "Privée", "Valeur": "PRIVATE"},
        },
        "Demandeur": {
            "Email": "<EMAIL>",
            "Nom": "Doe",
            "Prenom": "Jane",
            "Telephone": "496969696",
        },
        "Entreprise": {
            "Acronyme": "JEC",
            "FormeJuridique": "SA",
            "Nom": "Jane Enterprises",
            "Numero": "78901",
        },
        "Installateur": None,
        "TypeDemande": {"Libelle": "Test", "Valeur": "ACTIVATE"},
    }
