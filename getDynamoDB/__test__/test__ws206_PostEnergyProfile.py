import unittest

from utils.api import api_caller
from utils.errors import HttpError
from utils.mocking import get_mock_user_token, mock_api


@mock_api
class TestWS206EnergyProfile(unittest.TestCase):
    def setUp(self):
        self.user_token = get_mock_user_token()
        self.headers = {
            "Authorization": f"Bearer {self.user_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        self.valid_querry_params = "City=Liege&Street=Rue de la test&Number=45&Postcode=4000"
        self.expected_response = {
            "EnergyAsset": {
                "NumberPeople": 3,
                "HabitationType": "HOUSE",
                "DomesticHotWater": True,
                "PrimaryHeatingSource": "PAC",
                "SwimmingPool": True,
                "Jacuzzi": False,
                "ElectricVehiclesChargedHome": 1,
            },
            "Bilan": None,
            "ConsentBilan": None,
            "ConsentEnergyAsset": True,
        }

    def test_valid_data(self):
        dict_body = {
            "EnergyAsset": {
                "NumberPeople": 3,
                "HabitationType": "HOUSE",
                "DomesticHotWater": True,
                "PrimaryHeatingSource": "PAC",
                "SwimmingPool": True,
                "Jacuzzi": False,
                "ElectricVehiclesChargedHome": 1,
            },
            "ConsentEnergyAsset": True,
        }
        response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
        self.assertDictEqual(response, self.expected_response)

    def test_invalid_request_body(self):
        dict_body = {
            "Consent": "INVALID_BOOL",
            "ContactPref": "INVALID_PREF",
        }
        try:
            response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "INVALID_FIELDS")

    def test_energy_profile_with_zero_number(self):
        dict_body = {
            "EnergyAsset": {
                "NumberPeople": 0,
                "HabitationType": "HOUSE",
                "PrimaryHeatingSource": "PAC",
                "ElectricVehiclesChargedHome": 0,
            },
            "ConsentEnergyAsset": True,
        }
        response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
        excepted_answer = {
            "Bilan": None,
            "EnergyAsset": {
                "NumberPeople": 0,
                "HabitationType": "HOUSE",
                "DomesticHotWater": False,
                "PrimaryHeatingSource": "PAC",
                "SwimmingPool": False,
                "Jacuzzi": False,
                "ElectricVehiclesChargedHome": 0,
            },
            "ConsentBilan": None,
            "ConsentEnergyAsset": True,
        }
        self.assertDictEqual(response, excepted_answer)

    def test_energy_profile_with_negative_number(self):
        dict_body = {
            "EnergyAsset": {
                "NumberPeople": -1,
                "HabitationType": "HOUSE",
                "PrimaryHeatingSource": "PAC",
                "ElectricVehiclesChargedHome": -1,
            },
        }
        try:
            response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "INVALID_FIELDS")

    def test_missing_consent(self):
        dict_body = {
            "EnergyAsset": {
                "NumberPeople": 0,
                "HabitationType": "HOUSE",
                "PrimaryHeatingSource": "PAC",
                "ElectricVehiclesChargedHome": 0,
            },
        }
        try:
            response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "INVALID_FIELDS")

    def test_missing_consent_with_bilan(self):
        dict_body = {
            "ConsentEnergyAsset": True,
            "Bilan": True,
            "EnergyAsset": {
                "NumberPeople": 0,
                "HabitationType": "HOUSE",
                "PrimaryHeatingSource": "PAC",
                "ElectricVehiclesChargedHome": 0,
            },
        }
        try:
            response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "INVALID_FIELDS")

    def test_missing_consent_with_energy_asset(self):
        dict_body = {
            "ConsentBilan": True,
            "Bilan": True,
            "EnergyAsset": {
                "NumberPeople": 0,
                "HabitationType": "HOUSE",
                "PrimaryHeatingSource": "PAC",
                "ElectricVehiclesChargedHome": 0,
            },
        }
        try:
            response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "INVALID_FIELDS")

    def test_bilan_alone(self):
        dict_body = {"ConsentBilan": True, "Bilan": True}
        response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
        excepted_answer = {
            "Bilan": True,
            "EnergyAsset": None,
            "ConsentBilan": True,
            "ConsentEnergyAsset": None,
        }
        self.assertDictEqual(response, excepted_answer)

    def test_bilan_alone_no_consent(self):
        dict_body = {"ConsentBilan": False, "Bilan": True}
        try:
            response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "INVALID_FIELDS")

    def test_energy_asset_alone(self):
        dict_body = {
            "ConsentEnergyAsset": True,
            "EnergyAsset": {
                "NumberPeople": 0,
                "HabitationType": "HOUSE",
                "PrimaryHeatingSource": "PAC",
                "ElectricVehiclesChargedHome": 0,
            },
        }
        response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
        excepted_answer = {
            "EnergyAsset": {
                "NumberPeople": 0,
                "HabitationType": "HOUSE",
                "DomesticHotWater": False,
                "PrimaryHeatingSource": "PAC",
                "SwimmingPool": False,
                "Jacuzzi": False,
                "ElectricVehiclesChargedHome": 0,
            },
            "Bilan": None,
            "ConsentBilan": None,
            "ConsentEnergyAsset": True,
        }
        self.assertDictEqual(response, excepted_answer)

    def test_energy_asset_alone_no_consent(self):
        dict_body = {
            "ConsentEnergyAsset": False,
            "EnergyAsset": {
                "NumberPeople": 0,
                "HabitationType": "HOUSE",
                "PrimaryHeatingSource": "PAC",
                "ElectricVehiclesChargedHome": 0,
            },
        }
        try:
            response = api_caller("POST", f"/me/energy_profile?{self.valid_querry_params}", body=dict_body, headers=self.headers)
            self.fail("Expected a HttpError for invalid request body, but no error was raised.")
        except HttpError as e:
            self.assertEqual(e.status, 400)
            self.assertEqual(e.error_code, "INVALID_FIELDS")
