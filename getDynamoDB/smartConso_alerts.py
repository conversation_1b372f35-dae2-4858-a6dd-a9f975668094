import json
import os

from utils.api import api_caller
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import convert_float_to_decimal, get
from utils.errors import INVALID_EAN_FOR_USER, NotFoundError
from utils.models.smartConso_alerts_model import SmartConsoAlertModel
from utils.models.user import User

table = get_dynamodb_table(os.environ["SmartConsoAlerts"])


def ws214(event, context):
    body = json.loads(get(event, "body", "{}"))
    user = User.from_event(event, allow_ghost=False)
    query_params = get(event, "queryStringParameters", "{}")
    input_ean = get(query_params, "Ean")
    input_meter = get(query_params, "Meter")

    if str(input_ean) not in user.ean_ids:
        raise INVALID_EAN_FOR_USER

    address_hash = next((ean_data.address_hash for ean_data in user.ean if ean_data.ean == input_ean), None)
    if not address_hash:
        raise NotFoundError("Address hash not found in user account.", error_code="ADDRESS_NOT_FOUND")

    smart_conso = SmartConsoAlertModel(
        consent=get(body, "Consent"),
        uid=user.uid,
        ean=input_ean,
        meter=input_meter,
        daily=get(body, "Daily"),
        monthly=get(body, "Monthly"),
    )

    if smart_conso.is_alert_empty():
        table.delete_item(
            Key={
                "Uid": smart_conso.uid,
                "Meter": smart_conso.meter,
            },
        )
    else:
        table.put_item(Item=convert_float_to_decimal(smart_conso.model_dump(exclude={"consent"})))

    valid_body = [{"Key": f"maconso.{address_hash}.alertEmail", "Value": True}]
    headers = get(event, "headers", {})
    api_caller("POST", "/me/consentements", body=valid_body, headers=headers)

    return {
        "statusCode": 200,
        "body": smart_conso.model_dump_json(),
    }


def ws215(event, config):
    user = User.from_event(event, allow_ghost=False)
    query_params = get(event, "queryStringParameters", "{}")
    input_ean = get(query_params, "Ean")
    input_meter = get(query_params, "Meter")

    if str(input_ean) not in user.ean_ids:
        raise INVALID_EAN_FOR_USER
    alert_data = table.get_item(Key={"Uid": user.uid, "Meter": input_meter}).get("Item")

    if not alert_data:
        raise NotFoundError("Alerts Data not found", error_code="DATA_NOT_FOUND")

    smart_conso = SmartConsoAlertModel.model_construct(**alert_data)

    response_body = smart_conso.model_dump_json()

    return {
        "statusCode": 200,
        "body": response_body,
    }
