import json

from utils.DecimalEncoder import DecimalEncoder
from utils.api import APICaller, concurrent_execution
from utils.auth_utils import getUserData, load_env_file, get_basic_credentials
from utils.dict_utils import get
from utils.userdata import Ean


def preparer_encodage(event, config):
    user_data = getUserData(event)
    dict_ean = Ean(user_data).getAsDict(enrich_data=True)
    result = concurrent_execution(
        f=enrich_ean_cpt,
        attribute_name="Ean",
        iterable=list(dict_ean.keys()),
        init_iterable=[],
        dict_ean=dict_ean,
    )

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps({"ListeEan": result}, cls=DecimalEncoder),
    }


def enrich_ean_cpt(ean, params):
    ean_result = enrich_ean(ean)
    if ean_result:
        ean_result["Ean"] = ean
        if "Compteurs" in ean_result and ean_result["Compteurs"]:
            cpt_result = concurrent_execution(
                f=enrich_cpt,
                attribute_name="NumCompteur",
                iterable=ean_result["Compteurs"],
                init_iterable=[],
                ean_info=ean_result,
            )
        else:
            cpt_result = {"Message": "Aucun compteur connu pour cet EAN"}

        attributes_of_interest = [
            "SectActivite",
            "DateDebutReleve",
            "DateFinReleve",
            "DateDebutPassage",
            "DateFinPassage",
        ]
        ean_result_filtered = {k: ean_result[k] for k in attributes_of_interest}
        result = {**ean_result_filtered, **{"Compteurs": cpt_result}}
    else:
        ean_result["Ean"] = ean
        result = ean_result
    return result


def enrich_ean(ean):
    result_dict = {}

    ws27_call = APICaller(
        job_name=str(ean),
        method="get",
        path="/ean",
        params={"Ean": ean},
        body=None,
        headers={"Authorization": get_basic_credentials(load_env_file()["BASICAUTH_MYRESAAPI"])},
        throw=False,
        result_dict=result_dict,
    )

    ws27_call.start()
    ws27_call.join()

    liste_ean = get(get(result_dict, str(ean), {}), "ListeEan", [])
    ean_elem = [elem for elem in liste_ean if str(get(elem, "Ean", "")) == str(ean)]

    if not ean_elem:
        return {}
    else:
        ean_result = ean_elem[0]
        compteurs = [ean_result[k] for k in ean_result if k[0:6] == "NumCpt"]
        return {
            "Nom": get(ean_result, "Nom", ean),
            "Prenom": get(ean_result, "Prenom", ean),
            "SectActivite": str(get(ean_result, "SectActivite")),
            "DateDebutPassage": get(ean_result, "DateDebut", ""),
            "DateFinPassage": get(ean_result, "DateFin", ""),
            "DateDebutReleve": get(ean_result, "DtLimiDebut", ""),
            "DateFinReleve": get(ean_result, "DtLimiFin", ""),
            "Compteurs": compteurs,
        }


def enrich_cpt(cpt, params):
    result_dict = {}
    ean_info = get(params, "ean_info", {})
    ean = get(ean_info, "Ean", "")

    threads_list = []

    if cpt:
        cpt_call = APICaller(
            job_name="get_index",
            method="get",
            path="/index",
            params={"Ean": ean, "NumCompteur": cpt},
            body=None,
            headers={"Langue": "FR"},
            throw=False,
            propagate=True,
            result_dict=result_dict,
        )
        threads_list.append(cpt_call)

    threads_list.append(
        APICaller(
            job_name="get_historique",
            method="get",
            path="/index/historique",
            params={"Ean": ean, "NumCompteur": cpt},
            body=None,
            headers={"Langue": "FR"},
            throw=False,
            propagate=True,
            result_dict=result_dict,
        )
    )

    for thread in threads_list:
        thread.start()

    for thread in threads_list:
        thread.join()

    if "get_index" in result_dict:
        registres_compteurs = get(result_dict["get_index"], "Compteurs", [])
        registres = get(registres_compteurs[0], "Registres", {}) if registres_compteurs else {}
        registres_message = get(result_dict["get_index"], "Message", None)
        registres_result = {"Registres": registres} if registres else {"RegistresErrorMessage": registres_message}
    else:
        registres_result = {"Registres": None}

    historique = get(result_dict["get_historique"], "Contrat", None)
    historique_message = get(result_dict["get_historique"], "Message", None)
    historique_result = {"Historique": historique} if historique else {"HistoriqueErrorMessage": historique_message}

    return {**registres_result, **historique_result}
