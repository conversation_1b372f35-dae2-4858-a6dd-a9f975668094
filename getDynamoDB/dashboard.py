import json
import os
from datetime import datetime, date

from utils.DecimalEncoder import DecimalEncoder
from utils.auth_utils import getUserData
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import capitalizeKeys, get
from utils.errors import BadRequestError, UnauthorizedError
from utils.notification import create_notification


def notifications(event, config):
    user_data = getUserData(event, allow_ghost=False)
    if not user_data:
        raise UnauthorizedError("No Token or SessionId")
    uid = user_data["uid"]
    delaisJours = int(get(event["queryStringParameters"] or {}, "DelaisJour", -1))
    readStatus = int(get(event["queryStringParameters"] or {}, "ReadStatus", -1))

    if int(readStatus) != 0 and int(readStatus) != 1 and int(readStatus) != -1:
        raise Exception("Invalid ReadStatus value, value must be 0 (unread) or 1 (read) ")
    if int(delaisJours) < -1:
        raise Exception("Invalid DelaisJours value, value must be a positive integer ")

    notifications = get(user_data, "notifications", [])

    result = []

    for notif in notifications:
        dt_notif = datetime.fromtimestamp(float(notif["timestamp"]))
        my_date = date.today()
        today = datetime(my_date.year, my_date.month, my_date.day)
        diff = today - dt_notif
        diff = diff.days
        is_notif_read = get(notif, "read_timestamp") is not None
        should_display_read = True if readStatus == -1 else ((readStatus == 1) == is_notif_read)
        should_display_delais = delaisJours == -1 or diff <= delaisJours
        if should_display_delais and should_display_read:
            result.append(notif)
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys(result), cls=DecimalEncoder),
    }


def create_notifications(event, config):
    user_data = getUserData(event, allow_ghost=False)
    if not user_data:
        raise UnauthorizedError("No Token or SessionId")

    body = json.loads(get(event, "body", "{}"))
    message = str(get(body, "Message", err=BadRequestError("No Message found in Body")))
    priority = str(get(body, "Priority", "medium")).lower()
    if priority not in ("low", "medium", "high"):
        raise BadRequestError("Invalid priority (low, medium, high)")
    type_ = get(body, "Type")
    link = get(body, "Link")

    notifItem = create_notification(user_data, type_, priority, message, link)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys(notifItem), cls=DecimalEncoder),
    }


"""pass in querystring uid of notification and read status to set (1 (true) or 0 (false))"""


def notifications_status(event, config):
    # ID generation for dynamoDB (don't use increment as it can be shared on multiple servers) :
    user_data = getUserData(event, allow_ghost=False)
    if not user_data:
        raise Exception("No Token found")
    uid = user_data["uid"]
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    body = json.loads(get(event, "body", "{}"))
    uidEvent = get(
        body,
        "NotificationUid",
        err=BadRequestError("No NotificationUid found in queryStringParamters"),
    )
    read = get(
        body,
        "Read",
        err=BadRequestError("No read (true, false)) found in queryStringParamters"),
    )
    if read != "false" and read != "true":
        raise Exception("Invalid Read value, value must be true (read) or false (unread) ")
    if read == "false":
        read = 0
    if read == "true":
        read = 1
    notifications = get(user_data, "notifications", [])
    newNotifications = []

    for item in notifications:
        if str(uidEvent) == str(item["uid"]):
            item["read"] = read
        newNotifications.append(item)

    response = table.update_item(
        Key={"uid": uid},
        UpdateExpression="SET #notifications = :notificationsValue",
        # ConditionExpression=Attr('version').eq('1.0'),
        ExpressionAttributeNames={"#notifications": "notifications"},
        ExpressionAttributeValues={":notificationsValue": newNotifications},
    )
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys(response), cls=DecimalEncoder),
    }
