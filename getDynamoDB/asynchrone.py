import base64
import io
import json
import os
from datetime import datetime
from json import JSONDecodeError
from os import environ

from boto3.dynamodb.conditions import Key
from openpyxl import load_workbook
from requests import request

from utils.aws_utils import get_dynamodb_table, invoke_lambda, load_s3_file
from utils.DecimalEncoder import DecimalEncoder
from utils.dict_utils import capitalizeKeys, convert_float_to_decimal, get
from utils.errors import BadRequestError, HttpError, NotFoundError
from utils.log_utils import log_err_json, log_info
from utils.message_utils import get_template_config, send_in_blue_mail
from utils.sentry.sentry_utils import capture_message_in_sentry
from utils.sharepoint import submit_document_to_sharepoint
from utils.string_utils import capitalizeFirstLetter, randomString


def getAvailableProcessId():
    id_ = randomString(50)
    table = get_dynamodb_table(os.environ["AsynchroneProcessesTable"])
    response = table.query(KeyConditionExpression=Key("id").eq(id_))
    if len(response["Items"]) > 0:
        return getAvailableProcessId()
    return id_


def post(event, config):
    body = json.loads(get(event, "body", "{}"))
    method = get(body, "Method", err=BadRequestError("Method not found in request body"))
    path = get(body, "Path", err=BadRequestError("Path not found in request body"))
    type = body.get("Type", "internal")
    pathParameters = get(body, "PathParameters", {})
    queryStringParameters = get(body, "Params", {})
    extra_data = get(body, "ExtraData")
    request_body = get(body, "Data")
    given_process_id = get(body, "ProcessId")
    headers = get(event, "headers", {})
    headers.pop("Content-Length", None)
    headers.pop("Host", None)

    if request_body and not isinstance(request_body, str):
        request_body = json.dumps(request_body)

    for key, val in queryStringParameters.items():
        queryStringParameters[key] = str(val)

    input_ = {
        "method": method,
        "path": path,
        "pathParameters": pathParameters,
        "queryStringParameters": queryStringParameters,
        "body": request_body if len(request_body) < 10000 else request_body[:10000] + "--body truncated, too big for DynamoDB",
        "headers": headers,
    }
    startAt = int(datetime.now().timestamp() * 1000)

    id_ = given_process_id or getAvailableProcessId()
    table = get_dynamodb_table(os.environ["AsynchroneProcessesTable"])
    table.put_item(
        Item=convert_float_to_decimal(
            {
                "id": id_,
                "status": "IN_PROGRESS",
                "startAt": startAt,
                "input": input_,
                "expirationTime": int(datetime.now().timestamp() + 60 * 60 * 24 * 90),  # expire after 90 days
                "extraData": extra_data,
            },
        ),
    )

    response = {"ProcessId": id_}
    if type == "external":
        # Call an external already async process
        queryStringParameters["MessageId"] = id_

        result = request(
            method=method,
            url=os.environ["EXTERNAL_API_URL"] + path.format(pathParameters),
            params=queryStringParameters,
            data=request_body,
            headers=headers,
            verify=os.environ.get("LOCAL") != "true",
        )

        response["ExternalStatusCode"] = result.status_code
        try:
            response["ExternalResponse"] = result.json()
        except ValueError:
            response["ExternalResponse"] = result.text

        if not 200 <= result.status_code < 300:
            callback(
                {
                    "body": json.dumps(
                        {
                            "StatusCode": response["ExternalStatusCode"],
                            "Response": response["ExternalResponse"],
                        },
                    ),
                    "queryStringParameters": {"MessageId": id_},
                },
                None,
            )
    elif type == "internal":
        # Run an internal async process
        invocation = invoke_lambda(
            FunctionName=os.environ["AsyncProcessLambda"],
            InvocationType="Event",
            Payload=json.dumps({"process_id": id_, "event": event}).encode("utf8"),
        )
        print(invocation)
        if not (200 <= invocation["StatusCode"] < 300):
            raise HttpError(500, "Error while launching async process")
    else:
        raise HttpError(400, "Invalid 'Type' field")

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys(response)),
    }


def callback(event, config):
    body = json.loads(get(event, "body", "{}"))
    process_id = event.get("queryStringParameters", {}).get("MessageId")

    if not process_id:
        raise HttpError(400, "Path parameters 'MessageId' is missing")

    table = get_dynamodb_table(os.environ["AsynchroneProcessesTable"])
    table.update_item(
        Key={"id": process_id},
        UpdateExpression="SET #content = :content, #headers = :headers, #finishAt = :finishAt, #status = :status, #statusCode = :statusCode, #errorMessage = :errorMessage",
        ExpressionAttributeNames={
            "#content": "content",
            "#headers": "headers",
            "#status": "status",
            "#finishAt": "finishAt",
            "#statusCode": "statusCode",
            "#errorMessage": "error_message",
        },
        ExpressionAttributeValues=convert_float_to_decimal(
            {
                ":content": body.get("Response") if len(body.get("Response")) < 10000 else body.get("Response")[:10000] + "--body truncated, too big for DynamoDB",
                ":headers": None,
                ":statusCode": body.get("StatusCode"),
                ":status": "SUCCEED" if 200 <= body.get("StatusCode") < 300 else "FAILED",
                ":errorMessage": body.get("Response") if not 200 <= body.get("StatusCode") < 300 else None,
                ":finishAt": int(datetime.now().timestamp() * 1000),
            },
        ),
    )

    if 200 <= body.get("StatusCode") < 300:
        # Specials callback actions
        process_info = table.get_item(Key={"id": process_id})["Item"]
        process_info_input = process_info.get("input", {})
        process_info_input_path = process_info_input.get("path")

        log_info(process_info_input_path)
        if process_info_input_path == "/demande_travaux/demande":
            ws35_callback_actions(process_id, body.get("Response"))
        elif process_info_input_path == "/index/bulk":
            extra_data = process_info.get("extraData", {})
            callback_upload_index_masse(process_id, extra_data, body.get("Response"))
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
    }


def callback_upload_index_masse(message_id, extra_data, body_response):
    mail = extra_data.get("Mail")
    commune_name = extra_data.get("CommuneName")
    file_dir_key = extra_data.get("FileDirKey")
    excel_from_s3 = load_s3_file(
        os.environ["BUCKET"],
        f"{file_dir_key}/{message_id}.xlsx",
        decode_utf=False,
    )
    file = io.BytesIO(excel_from_s3)

    workbook = load_workbook(filename=file)
    sheet = workbook.active
    numcompteur_col = "G"
    numcadran_col = "K"
    resultat_col = "S"

    for row in range(2, sheet.max_row + 1):
        numcompteur_value = sheet[f"{numcompteur_col}{row}"].value
        resultat_value = sheet[f"{resultat_col}{row}"].value
        numcadran_value = sheet[f"{numcadran_col}{row}"].value.lstrip("0")

        if resultat_value.lower().startswith("en attente"):
            record_message = None
            for response in body_response:
                if not response.get("Messages"):
                    log_info("No messages found in response")
                    capture_message_in_sentry("No messages found in response", level="error")
                    raise BadRequestError(
                        "No messages found in response",
                        error_code="NO_MESSAGE_IN_RESPONSE",
                    )

                else:
                    for message in response["Messages"]:
                        if message["NumCompteur"].endswith(numcompteur_value) and message["NumCadran"].endswith(numcadran_value):
                            record_message = message["Message"]
                            break
                if record_message:
                    sheet[f"{resultat_col}{row}"].value = record_message
                    log_info(f"Compteur : {numcompteur_value} with Cadran : {numcadran_value} message : {record_message}")
                    break
            else:
                sheet[f"{resultat_col}{row}"].value = "Relevé du compteur et cadran introuvable"
                log_info(f"Compteur : {numcompteur_value} with Cadran : {numcadran_value} not found")

    output = io.BytesIO()
    workbook.save(output)
    output.seek(0)
    send_mail_uploads_index(output, mail, commune_name)


def send_mail_uploads_index(file, user_email, commune_name):
    file.seek(0)
    file_content = file.getvalue()
    attachments = [
        {
            "content": base64.b64encode(file_content).decode(),
            "name": "Resultat_enregistrement_des_index.xlsx",
        },
    ]
    send_in_blue_mail(
        template_id=get_template_config("COMMUNE_ENREGISTREMENT_INDEX")["EmailId"][os.environ["LANG"]],
        template_data={"commune_name": commune_name},
        email=user_email,
        attachments=attachments,
    )


def hide_authorization_header(headers):
    headers = {capitalizeFirstLetter(k): v for k, v in headers.items()}
    keys = ["Authorization"]
    for key in keys:
        if key in headers:
            headers[key] = "[hidden]"
    return headers


def get_result(event, config):
    process_id = get(event["pathParameters"], "Id")
    table = get_dynamodb_table(os.environ["AsynchroneProcessesTable"])
    query = table.query(KeyConditionExpression=Key("id").eq(process_id))["Items"]
    if len(query) < 1:
        raise NotFoundError("Unknown ProcessId")
    response = query[0]
    status = get(response, "status")
    TIMEOUT = 15 * 60 * 1000
    if get(response, "finishAt", int(datetime.now().timestamp() * 1000)) - get(response, "startAt") > TIMEOUT:
        if status == "IN_PROGRESS":
            status = "FAILED"
    input = get(response, "input")
    input["headers"] = hide_authorization_header(get(input, "headers", {}))
    ret = {
        "ProcessId": response["id"],
        "status": status,
        "startAt": get(response, "startAt"),
        "input": input,
        "finishAt": get(response, "finishAt"),
        "statusCode": get(response, "statusCode"),
        "content": get(response, "content"),
        "headers": hide_authorization_header(get(response, "headers", {})),
    }
    if status == "FAILED":
        ret = {**ret, "ErrorMessage": get(response, "error_message")}

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys(ret), cls=DecimalEncoder),
    }


def ws35_callback_actions(process_id: str, response: any):
    try:
        if isinstance(response, str):
            try:
                response = json.loads(response)
            except JSONDecodeError:
                return

        if isinstance(response, list):
            for resp in response:
                for dossier in resp.get("Dossiers", []):
                    ws35_send_documents_to_sharepoint(
                        fake_dossier_id=f"{process_id}_{dossier.get('SectActivite')}",
                        real_dossier_id=str(int(dossier.get("NumDossier"))),
                    )

    except Exception as e:
        log_err_json({"Exception ws35_callback": e})


def ws35_send_documents_to_sharepoint(fake_dossier_id: str, real_dossier_id: str):
    table = get_dynamodb_table(environ["MD_TABLE"])
    documents = get(
        table.query(
            IndexName="dossier_id-index",
            KeyConditionExpression=Key("dossier_id").eq(fake_dossier_id),
        ),
        "Items",
        [],
    )

    for document in documents:
        table.update_item(
            Key={"document_id": document["document_id"]},
            UpdateExpression="SET dossier_id=:s",
            ExpressionAttributeValues={":s": real_dossier_id},
        )
        submit_document_to_sharepoint(document["document_id"])
