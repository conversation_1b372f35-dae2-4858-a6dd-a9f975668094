import json
import os
from datetime import timedelta, datetime

from boto3.dynamodb.conditions import Key

from utils.DecimalEncoder import DecimalEncoder
from utils.api import basic_auth_headers, api_caller
from utils.auth_utils import getUserData
from utils.aws_utils import (
    get_dynamodb_table,
    check_s3_file_exist,
    create_presigned_url,
    scanAll,
    create_presigned_post,
    delete_s3_file,
)
from utils.dict_utils import first, capitalizeKeys, get, merge
from utils.email_utils import Email
from utils.errors import ForbiddenError, BadRequestError, HttpError, NotFoundError
from utils.ldap_utils import LDAP
from utils.log_utils import log_err
from utils.sap_user import generate_or_find_bp
from utils.string_utils import randomString
from utils.token_utils import getToken
from utils.type_utils import windowsToUnixTimestamp
from utils.userdata import Permissions
from utils.validation import alnum_only, validateAdresse


def generate_random_id(file_type):
    return randomString(stringLength=8) + "_" + file_type


def creer_compte(event, config):
    getUserData(event, allow_ghost=False, require_permission="admin")
    token = getToken(get(event, "headers", {}))
    body = json.loads(get(event, "body", "{}"))
    email = get(body, "Email", err=BadRequestError("No Email found in body"))
    call_entrepreneur = api_caller(
        "get",
        "/powalco/entrepreneurs",
        params={"PageSize": 10000},
        body=None,
        headers={"Authorization": "Bearer " + token},
    )
    entrepreneur = first([entrepreneur for entrepreneur in get(call_entrepreneur, "Data", []) if get(entrepreneur, "Email") == email])
    if not entrepreneur:
        raise BadRequestError("`{}` is not an entrepreneur".format(email))
    if entrepreneur["Status"] != "sans compte":
        raise HttpError(409, "entrepreneur `{}` already exist".format(email))
    entrepreneurId = entrepreneur["Id"]

    firstname = "Entreprise"
    lastname = entrepreneur["Name"]
    adresse = {
        "NumRue": "0",
        "Rue": "UNKNOWN",
        "Localite": "UNKNOWN",
        "Cdpostal": "4000",
    }
    adresse = validateAdresse(adresse)
    phone = None
    password = "8aA@" + randomString(15)
    table = get_dynamodb_table(os.environ["DYNAMODB"])

    username = alnum_only(firstname[:3]) + "." + alnum_only(lastname[:9]) + "." + randomString(5)
    fullname = firstname + " " + lastname
    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    uid = ldap.addUser(
        {
            "fullname": fullname,
            "password": password,
            "firstname": firstname,
            "lastname": lastname,
            "email": email,
            "phone": phone,
            "username": username,
        }
    )
    try:
        user = ldap.findUser("mail", email)
        if not user:
            raise NotFoundError("Mail `{}` not found in AD".format(email))
        cn = user["cn"]
        ldap.setUserAttribute(cn, "shadowLastChange", 0)
        ldap.setUserAttribute(cn, "pwdLastSet", 0)
        user_data = {
            "uid": uid,
            "valide": True,
            "ean": [],
            "preferences": {},
            "email": email,
            "dossiers": [],
            "pannes": [],
            "notifications": [],
            "phone": phone,
            "firstname": firstname,
            "lastname": lastname,
            "adresse": adresse,
            "entrepreneur_id": entrepreneurId,
            "createdAt": int(datetime.now().timestamp() * 1000),
        }
        bp = generate_or_find_bp(user_data)
        table.put_item(Item=user_data)
    except Exception as e:
        ldap.deleteUserByUid(uid)
        raise e
    finally:
        del ldap
    Permissions(user_data).addPermission("entrepreneur")
    table.update_item(
        Key={"uid": uid},
        UpdateExpression="SET bp = :bp",
        ExpressionAttributeValues={
            ":bp": bp,
        },
    )

    emailUtils = Email()
    emailUtils.send(
        "Invitation sur la Plateforme Resa Entrepreneur",
        email,
        "invitation_powalco.html",
        {"password": password, "email": email},
    )
    del emailUtils
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({"Uid": uid}),
    }


def switch_account_enabled_state(event, config):
    getUserData(event, allow_ghost=False, require_permission="admin")
    body = json.loads(get(event, "body", "{}"))
    email = get(body, "Email", err=BadRequestError("No Email found in body"))
    enabled = get(body, "Enabled", err=BadRequestError("No Enabled found in body"))

    table = get_dynamodb_table(os.environ["DYNAMODB"])
    response = table.query(KeyConditionExpression=Key("email").eq(email), IndexName="email-index")
    user_data = first(response["Items"])
    if not user_data:
        raise ForbiddenError("user `{}` don't extist".format(email))
    if "entrepreneur" not in Permissions(user_data).get():
        raise ForbiddenError("`{}` is not an entrepreneur account".format(email))
    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    user = ldap.findUser("mail", email)
    if not user:
        raise NotFoundError("Mail `{}` not found in AD".format(email))
    cn = user["cn"]
    ADS_UF_ACCOUNTDISABLE = 0x00000002
    userAccountControl = int(user["userAccountControl"])
    if enabled:
        userAccountControl = userAccountControl & ~ADS_UF_ACCOUNTDISABLE
    else:
        userAccountControl = userAccountControl | ADS_UF_ACCOUNTDISABLE
    ldap.setUserAttribute(cn, "userAccountControl", userAccountControl)
    del ldap
    table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression="SET lastEnabledStatusChange = :lastEnabledStatusChange",
        ExpressionAttributeValues={":lastEnabledStatusChange": int(datetime.now().timestamp() * 1000)},
    )
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({}),
    }


def generate_file_name(powalco_id, chantier_id, entrepreneur_id, upload_type):
    return f"{powalco_id}_{chantier_id}_{entrepreneur_id}_{upload_type}.pdf"


def upload_file(event, config):
    user_data = getUserData(event, allow_ghost=False, require_permission="entrepreneur")
    body = json.loads(get(event, "body", "{}"))
    fileName = get(body, "FileName", err=BadRequestError("No FileName found in body"))
    if not fileName.endswith(".pdf"):
        raise BadRequestError("Uploaded file should be a pdf document!")
    chantier = get(body, "Chantier", err=BadRequestError("No Chantier found in body"))
    type_ = get(body, "Type", err=BadRequestError("No Type found in body")).upper()

    if type_ not in ["EDLE", "EDLS"]:
        raise BadRequestError("Type should be EDLE or ELDS")

    chantier_data = api_caller("get", f"/powalco/chantiers/{chantier}", headers=basic_auth_headers())
    entrepreneur_id = user_data["entrepreneur_id"]
    if not entrepreneur_id or entrepreneur_id != get(chantier_data, "EntrepreneurId"):
        raise ForbiddenError("Acces non authorizé")
    bucket = os.environ["PowalcoBucket"]
    powalco_id = get(chantier_data, "PowalcoId", err=HttpError(500, "No PowalcoId for this Chantier"))
    reseau_ami = get(chantier_data, "ReseauAMI", err=HttpError(500, "No ReseauAMI for this Chantier"))
    renamed_file = generate_file_name(powalco_id, chantier, entrepreneur_id, type_)
    identifier = str(hash(renamed_file))
    key = "files/" + identifier + ".pdf"
    resource = "s3://" + bucket + "/" + key
    table = get_dynamodb_table(os.environ["PowalcoFiles"])
    existing = get(table.get_item(Key={"resource": resource}) or {}, "Item")
    db_item = {
        "resource": resource,
        "entrepreneur_id": entrepreneur_id,
        "fileName": fileName,
        "chantier": chantier,
        "type": type_,
        "powalco_id": powalco_id,
        "reseau_ami": reseau_ami,
        "renamed_file": renamed_file,
        "uploadedAt": int(datetime.now().timestamp() * 1000),
        "powalcoUpload": get(existing or {}, "powalcoUpload"),
        "sharepointUpload": None,
        "powalco_random_identifier": identifier,
    }
    table.put_item(Item=db_item)
    expiration = 3600
    expiration_timestamp = int((datetime.now() + timedelta(seconds=expiration)).timestamp())
    presigned_url = create_presigned_post(
        bucket,
        key,
        expiration=expiration,
        conditions=[["content-length-range", 1, 1024 * 1024 * 100]],
    )
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps(
            {
                "Item": capitalizeKeys(db_item),
                "Upload": merge(presigned_url, {"ExpirationTimestamp": expiration_timestamp}),
            },
            cls=DecimalEncoder,
        ),
    }


def delete_file(event, config):
    user_data = getUserData(event, allow_ghost=False, require_permission="entrepreneur")
    body = json.loads(get(event, "body", "{}"))
    resource = get(body, "Resource", err=BadRequestError("No Resource found in body"))
    entrepreneur_id = user_data["entrepreneur_id"]
    table = get_dynamodb_table(os.environ["PowalcoFiles"])
    try:
        q = table.query(KeyConditionExpression=Key("resource").eq(resource))
        item = first(q["Items"])
    except Exception:
        raise NotFoundError("resource `{}` don't exist".format(resource))
    if user_data["entrepreneur_id"] != get(item, "entrepreneur_id"):
        raise ForbiddenError("Not Allowed")
    resource_ = resource.replace("s3://", "")
    bucket, key = resource_.split("/", 1)
    table.delete_item(Key={"resource": resource})
    try:
        delete_s3_file(bucket, key)
    except Exception as e:
        log_err("cannot delete `{}` : ".format(resource) + str(e))
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({}),
    }


def modify_upload(event, config):
    user_data = getUserData(event, allow_ghost=False, require_permission="entrepreneur")
    body = json.loads(get(event, "body", "{}"))
    resource = get(body, "Resource", err=BadRequestError("No Resource found in body"))
    fileName = get(body, "FileName", err=BadRequestError("No FileName found in body"))

    entrepreneur_id = user_data["entrepreneur_id"]
    table = get_dynamodb_table(os.environ["PowalcoFiles"])
    try:
        q = table.query(KeyConditionExpression=Key("resource").eq(resource))
        item = first(q["Items"])
    except Exception:
        raise NotFoundError("resource `{}` don't exist".format(resource))
    if user_data["entrepreneur_id"] != get(item, "entrepreneur_id"):
        raise ForbiddenError("Not Allowed")
    fileName = fileName or item["fileName"]
    resource_ = resource.replace("s3://", "")
    bucket, key = resource_.split("/", 1)
    table.update_item(
        Key={"resource": resource},
        UpdateExpression="SET fileName = :fileName, uploadedAt = :uploadedAt",
        ExpressionAttributeValues={
            ":fileName": fileName,
            ":uploadedAt": int(datetime.now().timestamp() * 1000),
        },
    )
    expiration = 3600
    expiration_timestamp = int((datetime.now() + timedelta(seconds=expiration)).timestamp())
    presigned_url = create_presigned_post(
        bucket,
        key,
        expiration=expiration,
        conditions=[["content-length-range", 1, 1024 * 1024 * 100]],
    )
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({"Upload": merge(presigned_url, {"ExpirationTimestamp": expiration_timestamp})}),
    }


def list_files(event, config):
    user_data = getUserData(event, allow_ghost=False, require_permission="entrepreneur")
    files = scanAll(
        os.environ["PowalcoFiles"],
        {
            "FilterExpression": "entrepreneur_id = :entrepreneur_id",
            "ExpressionAttributeValues": {":entrepreneur_id": user_data["entrepreneur_id"]},
        },
    )

    def treat_file(file):
        file_ = file.copy()
        resource = file_["resource"]
        fileName = file_["fileName"]
        file_id = file_["powalco_random_identifier"]
        file_ = capitalizeKeys(file_)
        resource = resource.replace("s3://", "")
        bucket, key = resource.split("/", 1)
        key = "files/" + file_id + ".pdf"
        expiration = 3600
        presigned_url = create_presigned_url(bucket, key, fileName, expiration)
        file_["URL"] = presigned_url
        return file_

    def exist(resource_):
        resource = resource_.replace("s3://", "")
        bucket, key = resource.split("/", 1)
        print(resource_, check_s3_file_exist(bucket, key))
        return check_s3_file_exist(bucket, key)

    files = [treat_file(file) for file in files if get(file, "entrepreneur_id") == user_data["entrepreneur_id"] and exist(get(file, "resource"))]

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({"Files": files}, cls=DecimalEncoder),
    }


def get_entrepreneur(event, config):
    entrepreneurId = get(event["pathParameters"], "EntrepreneurId")
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    entrepreneur = first(
        get(
            table.query(
                KeyConditionExpression=Key("entrepreneur_id").eq(entrepreneurId),
                IndexName="entrepreneur_id-index",
            ),
            "Items",
            [],
        )
    )
    if not entrepreneur:
        raise NotFoundError("entrepreneur `{}` not found".format(entrepreneurId))
    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    user = ldap.findUser("uid", entrepreneur["uid"])
    if not user:
        raise NotFoundError("User not found in AD")
    cn = user["cn"]
    ADS_UF_ACCOUNTDISABLE = 0x00000002
    userAccountControl = int(user["userAccountControl"])
    disabled = userAccountControl & ADS_UF_ACCOUNTDISABLE
    lastLogon = int(get(user, "lastLogon", 0))
    del ldap

    status = "inactif" if disabled else ("en attente" if not lastLogon else "actif")

    ret = {
        "Id": entrepreneurId,
        "Firstname": entrepreneur["firstname"],
        "Lastname": entrepreneur["lastname"],
        "Email": entrepreneur["email"],
        "Status": status,
        "CreationDate": get(entrepreneur, "createdAt"),
        "LastActivityDate": windowsToUnixTimestamp(lastLogon) if lastLogon else None,
        "DeactivationDate": (get(entrepreneur, "lastEnabledStatusChange") if disabled else None),
    }

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps(ret, cls=DecimalEncoder),
    }
