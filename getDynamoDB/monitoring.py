import json

from utils.DecimalEncoder import DecimalEncoder
from utils.aws_utils import get_last_log
from utils.dict_utils import get, merge
from utils.errors import BadRequestError


def monitoring(event, config):
    env = get(get(event, "queryStringParameters", {}), "Env", "production")
    envs = ["production", "qta", "dev", "qla"]
    if env not in envs:
        raise BadRequestError("env must be one of " + str(envs))
    logs = get_last_log("monitoring-" + str(env))
    structuredLogs = structureLogs(logs)
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps(structuredLogs, cls=DecimalEncoder),
    }


def structureLogs(logs):
    ret = {"Dependencies": {}, "Endpoints": {}}
    for log in [log for log in logs if "[DEPENDENCY]" in log]:
        status = "[SUCCEED]" in log
        tags, object_, scenario = log.split(" ")
        scenario = scenario.replace("(", "").replace(")", "")
        ret["Dependencies"][object_] = merge(get(ret["Dependencies"], object_, {}), {scenario: status})

    for log in [log for log in logs if "[ENDPOINT]" in log]:
        status = "[SUCCEED]" in log
        endpoint = log.split('"')[1]
        current_status = get(ret["Endpoints"], endpoint, True)
        ret["Endpoints"][endpoint] = current_status and status

    return ret
