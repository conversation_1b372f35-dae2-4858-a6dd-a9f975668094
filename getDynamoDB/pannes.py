import json
import os
from os import environ

import boto3

from utils.api import api_caller
from utils.aws_utils import get_secret
from utils.dict_utils import get
from utils.errors import BadRequestError, NotFoundError
from utils.log_utils import log_err_json
from utils.message_utils import send_in_blue_mail, get_template_config, send_sms
from utils.sentry.sentry_utils import capture_exception_in_sentry
from utils.type_utils import assertType
from utils.url_sign_utils import generate_salted_signed_url
from utils.validation import is_valid_email, isPhone

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["PANNE_DYNAMODB_TABLE"])


def _get_panne_info(panne_id: str, adresse: str):
    try:
        resp = api_caller("GET", f"/pannes/{panne_id}")
    except Exception as e:
        if "404" in str(e):
            raise NotFoundError("No panne found with given PanneId")
        else:
            raise e
    panne_info = get(resp, "Data", {})

    if not panne_info:
        raise NotFoundError("No panne found with given PanneId")

    panne_info_address = get(panne_info, "Adresse", {})

    return {
        "Header": {
            "PANNE_ID": panne_id,
            "STREET": get(panne_info_address, "Rue", ""),
            "POST_CODE": get(panne_info_address, "Zipcode", ""),
            "CITY": get(panne_info_address, "Ville", ""),
            "NUMERO": get(panne_info_address, "Numero", ""),
        },
        "Unsubscribe": generate_salted_signed_url(
            "GET",
            f"{os.environ['API_URL']}/panne_subscription/{panne_id}/remove",
            {"Adresse": adresse},
        ),
        "EP": get(panne_info, "Ep"),
    }


def _send_subscription_confirmation(panne_id: str, type: str, adresse: str):
    template_data = _get_panne_info(panne_id, adresse)

    if template_data["EP"]:
        template_config = get_template_config("PANNE_SUBSCRIPTION_EP")
    else:
        template_config = get_template_config("PANNE_SUBSCRIPTION")

    if type == "EMAIL":
        email_templates = template_config["EmailId"]
        template_id = email_templates.get(environ["LANG"]) or email_templates.get("fr")
        send_in_blue_mail(template_id=template_id, template_data=template_data, email=adresse)
    elif type == "SMS":
        sms_templates = template_config["SmsId"]
        template_id = sms_templates.get(environ["LANG"]) or sms_templates.get("FR")
        # remove trailing '0' and '41'
        template_data["Header"]["PANNE_ID"] = template_data["Header"]["PANNE_ID"].lstrip("0")[2:]
        send_sms(
            input_phone=adresse,
            template_name=template_id,
            template_content=template_data,
            api_config={"sandbox": False},
            options=None,
        )


def ajouter(event, config):
    body = json.loads(get(event, "body", "{}"))

    panne_id = assertType(
        "PanneId",
        str,
        get(body, "PanneId", err=BadRequestError("PanneId is mandatory in request body")),
    ).zfill(12)
    type = assertType(
        "Email",
        str,
        get(body, "Type", err=BadRequestError("Type is mandatory in request body")),
    )
    adresse = assertType(
        "Sms",
        str,
        get(body, "Adresse", err=BadRequestError("Adresse is mandatory in request body")),
    )

    if type == "EMAIL":
        if not is_valid_email(adresse):
            raise BadRequestError("Invalid email address")
    elif type == "SMS":
        if not isPhone(adresse):
            raise BadRequestError("Invalid phone number")
    else:
        raise BadRequestError("Type should be either EMAIL or SMS")

    _send_subscription_confirmation(panne_id, type, adresse)

    table.put_item(
        Item={
            "panne": panne_id,
            "type": type,
            "adresse": adresse,
            "langue": environ["LANG"],
        }
    )

    return {"isBase64Encoded": False, "statusCode": 204}


def enlever(event, config):
    path_params = get(event, "pathParameters", {})
    query_params = get(event, "queryStringParameters", {})

    address = get(
        query_params,
        "Adresse",
        err=BadRequestError("Adresse is mandatory in query parameters"),
    )
    if "@" not in address:  # if no @, assume it's a phone number and add missing trailing + (auto removed by AWS)
        address = "+" + address

    table.delete_item(
        Key={
            "panne": get(
                path_params,
                "PanneId",
                err=BadRequestError("PanneId is mandatory in path parameters"),
            ),
            "adresse": address,
        }
    )

    return {"isBase64Encoded": False, "statusCode": 204}


def unsubscribe(event, config):
    lang = "FR"

    try:
        path_params = get(event, "pathParameters", {})
        query_params = get(event, "queryStringParameters", {})

        address = get(
            query_params,
            "Adresse",
            err=BadRequestError("Adresse is mandatory in query parameters"),
        )
        if "@" not in address:  # if no @, assume it's a phone number and add missing trailing + (auto removed by AWS)
            address = "+" + address

        item = table.get_item(
            Key={
                "panne": get(
                    path_params,
                    "PanneId",
                    err=BadRequestError("PanneId is mandatory in path parameters"),
                ),
                "adresse": address,
            }
        ).get("Item")
        if not item:
            raise BadRequestError(
                message="Panne subscriptions not found",
                error_code="SUBSCRIPTIONS_NOT_FOUND",
            )
        lang = item.get("langue") or "FR"

        enlever(event, config)
    except Exception as e:  # avoid returning any error to client
        log_err_json(e)
        capture_exception_in_sentry(e)

    return {
        "isBase64Encoded": False,
        "statusCode": 303,
        "headers": {
            "Location": f"https://{get_secret(os.environ['ADFS_WEB'])['redirect_domain']}/{lang.lower()}/confirmation-desinscription/",
            "Cache-Control": "no-cache",
        },
    }
