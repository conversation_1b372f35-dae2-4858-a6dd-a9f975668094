import base64
import json
import os
import string
import uuid
from datetime import datetime, timedelta, UTC
from math import ceil
from random import randint, choice
from typing import Union, List, Optional
from urllib.parse import unquote

import requests
from boto3.dynamodb.conditions import Key

from utils.DecimalEncoder import DecimalEncoder
from utils.api import api_caller, basic_auth_headers
from utils.auth_utils import (
    getUserData,
    loadUserByUID,
    matches_user,
    load_env_file,
    get_user_data_from_query_string,
    get_token_data_from_query_string,
)
from utils.aws_utils import get_dynamodb_table, send_sqs, scanAll
from utils.dict_utils import get, capitalizeKeys, merge, pascal_to_snake, first
from utils.errors import (
    BadRequestError,
    ForbiddenError,
    HttpError,
    NotFoundError,
    EmailAlreadyUsedError,
)
from utils.ldap_utils import LDAP, check_if_mail_already_used
from utils.log_utils import LogTime, log_info
from utils.log_utils import log_err_json
from utils.message_utils import (
    get_template_config,
    check_comm_requirements,
    send_sms,
    send_in_blue_mail,
    save_send_in_blue_mail_to_sharepoint,
    save_sms_to_sharepoint,
)
from utils.models.user import User
from utils.sap_user import sap_edit_user
from utils.sharepoint import SharepointHandler
from utils.token_utils import (
    checkForInvalidation,
    decode_simple_jwt,
    encode_simple_jwt,
    createTokenInvalidation,
    getToken,
)
from utils.type_utils import to_list
from utils.url_sign_utils import generate_salted_signed_url
from utils.validation import (
    validateAdresse,
    is_valid_email,
    isPhone,
    minLength,
    check_password_policy,
    check_callback,
    check,
)

user_table = get_dynamodb_table(os.environ["DYNAMODB"])


def getAvailableGhostUserName():
    DIGIT = 8
    random = randint(10**DIGIT, (10 ** (DIGIT + 1)) - 1)
    name = "ghost_" + str(random)
    response = user_table.query(KeyConditionExpression=Key("uid").eq(name))
    if len(response["Items"]) > 0:
        return getAvailableGhostUserName()
    return name


def sort_user_list(users: list):
    users.sort(key=lambda x: x.get("createdAt"), reverse=True)
    users.sort(key=lambda x: x.get("valide"), reverse=True)
    return users


def find_user_by_bp(bp: int):
    users = get_dynamodb_table(os.environ["DYNAMODB"]).query(
        ProjectionExpression="bp, uid, email, contact_email, phone, ean, preferences, valide, firstname, lastname, createdAt",
        KeyConditionExpression=Key("bp").eq(bp),
        IndexName="bp-index",
    )["Items"]
    return sort_user_list(users)


def find_user_by_email(email: str):
    users = user_table.query(
        ProjectionExpression="uid, email, contact_email, phone, ean, preferences, valide, firstname, lastname, createdAt",
        KeyConditionExpression=Key("email").eq(email.lower()),
        IndexName="email-index",
    )["Items"]
    users.extend(
        user_table.query(
            ProjectionExpression="uid, email, contact_email, phone, ean, preferences, valide, firstname, lastname, createdAt",
            KeyConditionExpression=Key("email").eq(email),
            IndexName="email-index",
        )["Items"]
    )
    return sort_user_list(users)


def find_user_by_phone(phone):
    users = user_table.query(
        ProjectionExpression="uid, email, contact_email, phone, ean, preferences, valide, firstname, lastname, createdAt",
        KeyConditionExpression=Key("phone").eq(phone),
        IndexName="phone-index",
    )["Items"]
    return sort_user_list(users)


def find_user_by_ean(ean):
    users = scanAll(
        os.environ["DYNAMODB"],
        {
            "ProjectionExpression": "uid, email, contact_email, phone, ean, preferences, valide, firstname, lastname, createdAt",
            "FilterExpression": "ean <> :ean",
            "ExpressionAttributeValues": {":ean": []},
        },
    )
    return sort_user_list([user for user in users if matches_user("ean", ean, user)])


def find_user_by_panne(panne_id):
    users = get_dynamodb_table(os.environ["PANNE_DYNAMODB_TABLE"]).query(
        ProjectionExpression="panne, #t, adresse",
        ExpressionAttributeNames={"#t": "type"},
        KeyConditionExpression=Key("panne").eq(panne_id),
    )["Items"]

    return [
        {
            "uid": f"panne_{user['panne']}_{i}",
            "panne": user["panne"],
            "email": user["adresse"] if user["type"] == "EMAIL" else None,
            "contact_email": None,
            "phone": user["adresse"] if user["type"] == "SMS" else None,
            "ean": None,
            "preferences": {},
            "valide": False,
            "firstname": "",
            "lastname": "",
        }
        for i, user in enumerate(users)
    ]


def get_existing_preferences_by_mail(email):
    merged_preferences = {}

    for user in find_user_by_email(email):
        for pref, val in get(user, "preferences", {}).items():
            merged_preferences[pref] = merged_preferences.get(pref) or val

    return merged_preferences


def creer_structure(data: dict) -> dict:
    ghost_uid = getAvailableGhostUserName()

    adresse = get(data, "Adresse")
    adresse = validateAdresse(adresse) if adresse else None

    email = get(data, "Email")
    if email:
        email = email.lower()
        if not is_valid_email(email):
            raise BadRequestError("Invalid email")

    contact_email = get(data, "ContactEmail") or email
    if contact_email and not is_valid_email(contact_email):
        raise BadRequestError("Invalid contact email")

    phone = get(data, "Phone")
    if phone == "+32":
        phone = None
    if phone and not isPhone(phone):
        raise BadRequestError("Invalid Phone")

    phonefixe = get(data, "PhoneFixe")
    if phonefixe == "+32":
        phonefixe = None
    if phonefixe and not isPhone(phonefixe):
        raise BadRequestError("Invalid PhoneFixe")

    firstname = get(data, "Firstname")
    if firstname and not minLength(firstname, 1):
        raise BadRequestError("Firstname cannot be empty")

    lastname = get(data, "Lastname")
    if lastname and not minLength(lastname, 1):
        raise BadRequestError("Lastname cannot be empty")

    user_data = {
        "uid": ghost_uid,
        "valide": False,
        "ean": [],
        "preferences": {
            "com_global": "mail",
            "com_encod_index_mail": False,
            "com_encod_index_sms": False,
            "com_encod_index_postal": False,
            "com_pass_index_mail": False,
            "com_pass_index_sms": False,
            "com_pass_index_postal": False,
            "com_dossier_racc_mail": True,
            "com_dossier_racc_sms": False,
            "com_dossier_racc_postal": False,
            "com_rappel_paiement_mail": False,
            "com_rappel_paiement_sms": False,
            "com_rappel_paiement_postal": False,
            "com_conf_paiement_mail": False,
            "com_conf_paiement_sms": False,
            "com_conf_paiement_postal": False,
            "com_conf_sepa_mail": False,
            "com_conf_sepa_sms": False,
            "com_conf_sepa_postal": False,
            "com_panne_mail": False,
            "com_panne_sms": False,
            "com_panne_evo_mail": False,
            "com_panne_evo_sms": False,
            "com_panne_fin_mail": False,
            "com_panne_fin_sms": False,
            "com_marketing_mail": False,
            "com_ppp_mail": True,
            "com_ppp_sms": True,
            "com_smartportal_invitation_mail": False,
            "com_smartportal_bilan_mail": False,
            "com_smartportal_alert_mail": False,
            "com_smartportal_alert_sms": False,
            "Langue": os.environ["LANG"],
        },
        "email": email,
        "contact_email": contact_email,
        "dossiers": [],
        "pannes": [],
        "notifications": [],
        "phone": phone,
        "phone_fixe": phonefixe,
        "firstname": firstname,
        "lastname": lastname,
        "adresse": adresse,
        "createdAt": int(datetime.now().timestamp() * 1000),
    }
    if not email:
        del user_data["email"]
    if not phone:
        del user_data["phone"]

    return user_data


def creer(event, config):
    try:
        body = json.loads(get(event, "body", "{}"))
    except ValueError:
        raise BadRequestError(
            "Unable to load body; body should be in JSON format.",
            error_code="UNABLE_TO_LOAD_BODY",
        )
    user_data = creer_structure(body)

    user_table.put_item(Item=user_data)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({"SessionId": user_data["uid"]}, cls=DecimalEncoder),
    }


def edit(event, config):
    user_data = getUserData(event)
    body = json.loads(get(event, "body", "{}"))
    uid = get(user_data, "uid")

    adresse = get(body, "Adresse")
    adresse = validateAdresse(adresse) if adresse else get(user_data, "adresse")

    current_email = get(user_data, "email")
    if isinstance(current_email, str):
        current_email = current_email.lower()
    email = get(body, "Email", current_email)
    if isinstance(email, str):
        email = email.lower()

    if user_data["valide"] and email != current_email:
        raise ForbiddenError(
            "Le changement d’adresse e-mail d’un utilisateur actif n’est pas autorisé. Merci d’utiliser la méthode updateMail/sendMail pour effectuer ce changement."
        )
    if email and not is_valid_email(email):
        raise BadRequestError("Invalid email")
    email = email if email else current_email

    contact_email = get(body, "ContactEmail")
    if contact_email and not is_valid_email(contact_email):
        raise BadRequestError("Invalid ContactEmail")
    if contact_email != get(user_data, "contact_email"):
        user_data["valid_contact_email"] = False
    contact_email = contact_email or get(user_data, "contact_email") or email

    phone = get(body, "Phone")
    if phone == "+32":
        phone = None
    if phone and not isPhone(phone):
        raise BadRequestError("Invalid Phone")
    if phone != get(user_data, "phone"):
        user_data["valid_phone"] = False
    phone = phone if phone else get(user_data, "phone")

    phonefixe = get(body, "PhoneFixe")
    if phonefixe == "+32":
        phonefixe = None
    if phonefixe and not isPhone(phonefixe):
        raise BadRequestError("Invalid PhoneFixe")
    phonefixe = phonefixe if phonefixe else get(user_data, "phone_fixe")

    firstname = get(body, "Firstname")
    if firstname and not minLength(firstname, 1):
        raise BadRequestError("Firstname cannot be empty")
    firstname = firstname if firstname else get(user_data, "firstname")

    lastname = get(body, "Lastname")
    if lastname and not minLength(lastname, 1):
        raise BadRequestError("Lastname cannot be empty")
    lastname = lastname if lastname else get(user_data, "lastname")

    smart_portal_consent = get(body, "SmartPortalConsent", default=user_data.get("SmartPortalConsent"))
    if smart_portal_consent not in (True, False, None):
        raise BadRequestError("Invalid value for SmartPortalConsent. Must be a boolean.", error_code="INVALID_TYPE")

    preferences = body.get("Preferences", {})
    preferences = {
        **user_data.get("preferences", {}),
        **capitalizeKeys(preferences, pascal_to_snake),
    }

    user_data["email"] = email
    user_data["contact_email"] = contact_email
    user_data["phone"] = phone
    user_data["phone_fixe"] = phonefixe
    user_data["adresse"] = adresse
    user_data["firstname"] = firstname
    user_data["lastname"] = lastname
    user_data["preferences"] = preferences
    user_data["SmartPortalConsent"] = smart_portal_consent

    if not email:
        del user_data["email"]
    if not phone:
        del user_data["phone"]

    sap_edit_user(user_data)

    user_table.put_item(Item=user_data)

    if user_data["valide"]:
        ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
        user = ldap.findUser("uid", uid)
        if firstname:
            ldap.setUserAttribute(user["cn"], "givenName", firstname)
        if lastname:
            ldap.setUserAttribute(user["cn"], "sn", lastname)
        if phone:
            ldap.setUserAttribute(user["cn"], "telephoneNumber", phone)
        if phonefixe:
            ldap.setUserAttribute(user["cn"], "homePhone", phonefixe)
        del ldap

    ret = {
        "uid": uid,
        "firstname": firstname,
        "lastname": lastname,
        "email": email,
        "contact_email": contact_email,
        "phone": phone,
        "phone_fixe": phonefixe,
        "adresse": adresse,
        "preferences": preferences,
    }

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys(ret), cls=DecimalEncoder),
    }


def activer(event, config):
    token_ = get(
        event["queryStringParameters"],
        "Token",
        err=BadRequestError("No Token found in queryStrings"),
    )
    checkForInvalidation(token_)
    token = decode_simple_jwt(token_)
    if token["expirationTimestamp"] < datetime.now().timestamp():
        raise ForbiddenError(
            "Votre demande d’activation est expirée. Merci de recommencer votre inscription.",
            error_code="TOKEN_EXPIRED",
        )
    uid = token["uid"]

    user_data = loadUserByUID(uid)

    # Check if user already started the activation process
    if user_data.get("launched_activation", False):
        # Abort if it is the case
        return {
            "statusCode": 409,
            "body": '{"Message": "This user already started its activation process."}',
        }

    email = user_data["email"]
    if token["email"] != email:
        raise ForbiddenError("Token invalide")

    if user_data["email"] == user_data.get("contact_email"):
        user_data["valid_contact_email"] = True

    if user_data["valide"]:
        raise HttpError(
            409,
            "Votre compte est déjà activé. Merci de vous identifier.",
        )

    body = json.loads(get(event, "body", "{}"))

    password = get(body, "Password", err=BadRequestError("No Password found in body"))
    encryptedPassword = encode_simple_jwt({"password": password})

    adresse = get(body, "Adresse")
    adresse = validateAdresse(adresse) if adresse else get(user_data, "adresse")

    phone = get(body, "Phone")
    if phone and not isPhone(phone):
        raise BadRequestError("Invalid Phone")
    if phone != get(user_data, "phone"):
        user_data["valid_phone"] = False
    phone = phone if phone else get(user_data, "phone")

    phonefixe = get(body, "PhoneFixe")
    if phonefixe and not isPhone(phonefixe):
        raise BadRequestError("Invalid PhoneFixe")
    phonefixe = phonefixe if phonefixe else get(user_data, "phone_fixe")

    firstname = get(body, "Firstname")
    if firstname and not minLength(firstname, 1):
        raise BadRequestError("Firstname cannot be empty")
    firstname = firstname if firstname else get(user_data, "firstname")

    lastname = get(body, "Lastname")
    if lastname and not minLength(lastname, 1):
        raise BadRequestError("Lastname cannot be empty")
    lastname = lastname if lastname else get(user_data, "lastname")

    if not firstname:
        raise ForbiddenError("Firstname is mandatory to get activated")
    if not lastname:
        raise ForbiddenError("Lastname is mandatory to get activated")

    if not check_password_policy(password):
        raise HttpError(
            409,
            """Le mot de passe doit contenir au minimum 8 caractères et être composé d'au moins :\n - 1 lettre minuscule\n - 1 lettre majuscule\n - 1 caractère spécial parmi ~!@#$%^&€£*_-+=`|\\()\{\}[]:;"'<>,.?/\n - 1 chiffre""",
        )
    check_if_mail_already_used(email)

    createTokenInvalidation(token_)

    pref = get_existing_preferences_by_mail(email)

    user_data = merge(
        user_data,
        {
            "phone": phone,
            "phone_fixe": phonefixe,
            "firstname": firstname,
            "lastname": lastname,
            "adresse": adresse,
            "preferences": pref,
        },
    )

    update_expression = (
        "SET phone_fixe = :phone_fixe, adresse = :adresse, firstname = :firstname, lastname = :lastname, preferences = :preferences, valid_contact_email = :valid_contact_email"
    )
    expression_attributes = {
        ":phone_fixe": get(user_data, "phone_fixe"),
        ":adresse": get(user_data, "adresse"),
        ":firstname": get(user_data, "firstname"),
        ":lastname": get(user_data, "lastname"),
        ":preferences": get(user_data, "preferences"),
        ":valid_contact_email": get(user_data, "valid_contact_email"),
    }

    if user_data["phone"]:
        update_expression += ", phone = :phone"
        expression_attributes[":phone"] = get(user_data, "phone")

    user_table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression=update_expression,
        ExpressionAttributeValues=expression_attributes,
    )

    send_sqs(
        os.environ["USER_ACTIVATION_SQS_URL"],
        {
            "log_id": str(uuid.uuid4()),
            "step": "GET_BP",
            "API_URL": os.environ["API_URL"],
            "USER_ACTIVATION_SQS_URL": os.environ["USER_ACTIVATION_SQS_URL"],
            "uid": user_data["uid"],
            "encryptedPassword": encryptedPassword,
        },
    )

    # Set that the user started its activation process
    user_table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression="SET launched_activation = :launched_activation",
        ExpressionAttributeValues={":launched_activation": True},
    )

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({"Uid": user_data["uid"], "Email": email}, cls=DecimalEncoder),
    }


def reset(event, config):
    user_mail = get(
        event["queryStringParameters"],
        "Email",
        err=BadRequestError("No email found in queryStringParamters"),
    )
    callback = get(
        event["queryStringParameters"],
        "Callback",
        err=BadRequestError("No callback found in queryStringParameters"),
    )
    check_callback(callback)
    sandbox = get(event["queryStringParameters"], "Sandbox", default=False)
    sandbox = True if sandbox == "true" else False

    # check if user exists
    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    user = ldap.findUser("mail", user_mail)
    del ldap

    if not user:
        raise NotFoundError(
            f"Aucun compte avec l'email {user_mail} n'a été trouvé.",
            error_code="UNKNOWN_USER_MAIL",
        )
    else:
        user_data: User = User.from_dict(
            first(
                get(
                    user_table.query(KeyConditionExpression=Key("uid").eq(user["cn"])),
                    "Items",
                    [],
                )
            )
        )

        expirationTimestamp = (datetime.now() + timedelta(days=1)).timestamp()
        token = encode_simple_jwt(
            {
                "email": user_mail,
                "cn": user["cn"],
                "expirationTimestamp": expirationTimestamp,
            }
        )
        # create callback url
        callback = callback + ("&" if "?" in callback else "?") + "Token=" + token
        api_caller(
            method="post",
            path=f"/envMessage?Sandbox={sandbox}",
            body={
                "Langue": os.environ["LANG"],
                "Header": {
                    "TEMPLATE_ID": ("MYRE_COMMUNE_PWD_RESET" if user_data.commune_id else "MYRE_PWD_RESET"),
                    "EMAIL": user_mail,
                },
                "Link": callback,
            },
            headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
        )
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({"Email": user_mail}, cls=DecimalEncoder),
    }


def resetChangePassword(event, config):
    token_ = get(
        event["queryStringParameters"],
        "Token",
        err=BadRequestError("No token found in queryStringParamters"),
    )
    body = json.loads(get(event, "body", "{}"))
    password = get(body, "Password", err=BadRequestError("No Password found in request body"))

    token = decode_simple_jwt(token_)
    if token["expirationTimestamp"] < datetime.now().timestamp():
        raise ForbiddenError(
            "Votre demande de changement de mot de passe est expirée. Merci de recommencer votre demande de changement de mot de passe.",
            error_code="TOKEN_EXPIRED",
        )
    checkForInvalidation(token_)

    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    mail = token["email"]
    user = ldap.findUser("mail", mail)
    if not user:
        raise NotFoundError("Votre email `{}` n'est pas reconnu par notre système. Veuillez vérifier vos données.".format(mail))
    user = user["cn"]

    ldap.changePassword(user, password)
    del ldap

    createTokenInvalidation(token_, 3 * 60 * 60)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({}),
    }


def updateMailSendMail(event, config):
    user_mail = get(
        event["queryStringParameters"],
        "Email",
        err=BadRequestError("No email found in queryStringParamters"),
    )
    callback = get(
        event["queryStringParameters"],
        "Callback",
        err=BadRequestError("No callback found in queryStringParameters"),
    )
    check_callback(callback)
    check(is_valid_email(user_mail), BadRequestError("Invalid email address"))
    sandbox = get(event["queryStringParameters"], "Sandbox", default=False)
    sandbox = True if sandbox == "true" else False

    user_data = getUserData(event)
    if not user_data:
        raise Exception("No Token or SessionId")

    uid = user_data["uid"]
    oldEmail = user_data["email"]

    # check if mail exists
    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    user = ldap.findUser("mail", user_mail)
    if user:
        raise HttpError(409, "Cet adresse e-mail a déjà été utilisée pour la création d'un compte.")
    del ldap

    expirationTimestamp = (datetime.now() + timedelta(days=1)).timestamp()
    token = encode_simple_jwt(
        {
            "email": user_mail,
            "oldEmail": oldEmail,
            "uid": uid,
            "expirationTimestamp": expirationTimestamp,
        }
    )
    # create callback url
    callback = callback + ("&" if "?" in callback else "?") + "Token=" + token
    api_caller(
        method="post",
        path=f"/envMessage?Sandbox={sandbox}",
        body={
            "Langue": os.environ["LANG"],
            "Header": {"TEMPLATE_ID": "MYRE_MAIL_UPDATE", "EMAIL": user_mail},
            "Link": callback,
        },
        headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
    )
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({"Email": user_mail}, cls=DecimalEncoder),
    }


def updateMail(event, config):
    authUser = None
    try:
        authUser = getUserData(event, allow_ghost=False)
    except:
        pass
    token_ = get(
        event["queryStringParameters"],
        "Token",
        err=BadRequestError("No token found in queryStringParamters"),
    )

    checkForInvalidation(token_)

    # token decode to get mail and validate timestamp
    token = decode_simple_jwt(token_)
    if token["expirationTimestamp"] < datetime.now().timestamp():
        raise ForbiddenError(
            "Votre demande de changement d'email est expirée. Merci de recommencer votre demande.",
            error_code="TOKEN_EXPIRED",
        )

    uid = token["uid"]
    if authUser and authUser["uid"] != uid:
        raise ForbiddenError("Token not emitted by logged user")
    user_data = loadUserByUID(uid)
    oldEmail = token["oldEmail"]
    email = token["email"]
    if oldEmail != user_data["email"]:
        raise ForbiddenError("Token invalide")

    # find user based on mail
    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    # check if mail exists
    user = ldap.findUser("mail", email)
    if user:
        raise HttpError(409, "Cet adresse e-mail a déjà été utilisée pour la création d'un compte.")

    user = ldap.findUser("mail", oldEmail)
    if not user:
        raise NotFoundError("Votre email `{}` n'est pas reconnu par notre système. Veuillez vérifier vos données.".format(email))
    user = user["cn"]

    ldap.setUserAttribute(user, "mail", email)
    ldap.setUserAttribute(user, "userPrincipalName", email)
    del ldap

    # change mail in DynamoDB
    user_table.update_item(
        Key={"uid": uid},
        UpdateExpression="SET #email = :email",
        ExpressionAttributeNames={"#email": "email"},
        ExpressionAttributeValues={":email": email},
    )
    createTokenInvalidation(token_)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({"Uid": uid, "Email": email}, cls=DecimalEncoder),
    }


def check_status(comm_info_uid_status_code, a, b):
    if comm_info_uid_status_code == 404:
        message_status = 404
    elif a != 404 and b != 404:
        message_status = min(a, b)
    elif a != 404:
        message_status = a
    elif b != 404:
        message_status = b
    else:
        message_status = 404
    return message_status


def env_message(event, context):
    """
    Sends a message to a MyResa user. The user is identified by a single input: e-mail or phone or ean or haugazelID.
    Message should be sent if 5 conditions are matched:
         (1) The user data matches the input provided
         (2) The user level of credentials (allowGhost) is compatible with the template
         (3) The user accepts to receive messages via the communication channel, or has no communication preferences
         (4) The template is defined for this communication channel
         (5) The contact info (email or phone) has a valid format
         If one condition is not met the error message specifies which one failed

    The content of the message is defined by NomTemplate, EmailContenu, EmailTitre, and SmsContenu. NomTemplate is
    mandatory, the rest of parameters are mandatory only if the template requires user specific customizations.

    If multiple records are found satisfying all conditions (could happen in case of duplicate ghost users)
    a single message will be sent for each contact info value (no multiple messages to same email or phone)
    """

    def _list_to_dict(_list: list):
        """
        Convert list of [{'type' : xxx, 'valeur' : xxx}, ...] to dict {'type' : 'valeur', ...}
        """
        _dict = {}
        duplicated_key_cpt = {}

        for item in _list:
            key = item["Type"]
            val = get(item, "Valeur", "")
            if key in _dict:
                if f"{key}_LIST" not in _dict:
                    _dict[f"{key}_LIST"] = [_dict[key]]
                _dict[f"{key}_LIST"].append(val)

                i = duplicated_key_cpt.get(key, 1)
                duplicated_key_cpt[key] = i + 1
                key = f"{key}_{i}"

            # Special case, split string with \n into list of string
            if isinstance(val, str) and "\n" in val:
                _dict[f"{key.strip()}_LIST"] = [str(v).strip() for v in val.split("\n")]

            if isinstance(val, list):
                val = [str(v).strip() for v in val]
            elif isinstance(val, dict):
                val = {str(k).strip(): str(v).strip() for k, v in val.items()}
            else:
                val = str(val).strip()
            _dict[key.strip()] = val

        return _dict

    def _flatten_dict(_dict: dict):
        """
        Flatten dict containing list of {'type': xxx, 'valeur': xxx}
        """
        if not isinstance(_dict, (list, dict, set)):
            return str(_dict).strip()

        flat_dict = {}

        if "Type" in _dict and len(_dict) <= 2:
            flat_dict[_dict["Type"].strip()] = str(_dict.get("Valeur", "")).strip()
        else:
            for key, val in _dict.items():
                if not val:
                    flat_dict[key.strip()] = val
                elif isinstance(val, list):
                    if key == "Data":
                        flat_dict = {**flat_dict, **_list_to_dict(val)}
                    elif "Type" in val[0]:  # Special case for Header or ContentC
                        flat_dict[key.strip()] = _list_to_dict(val)
                    else:
                        flat_dict[key.strip()] = [_flatten_dict(item) for item in val]
                elif isinstance(val, dict):
                    if key == "Data":
                        flat_dict[val["Type"].strip()] = str(val["Valeur"]).strip()
                    else:
                        flat_dict[key.strip()] = _flatten_dict(val)
                else:
                    flat_dict[key.strip()] = str(val).strip()

        return flat_dict

    def _custom_body_actions(_dict: dict):
        """
        Execute some custom action for certain communication
        """
        # Always set 'ContentA' as a list
        if _dict.get("ContentA"):
            _dict["ContentA"] = to_list(_dict["ContentA"])

        # Set 'ContentB' elements as dict if empty string (fix error with racc MODI_SMART)
        for content_a in get(_dict, "ContentA", []):
            contents_b = get(content_a, "ContentB", [])
            if isinstance(contents_b, list):
                for i, content_b in enumerate(contents_b):
                    if content_b == "":
                        contents_b[i] = {"null": None}
            elif contents_b == "":
                content_a["ContentB"] = {"null": None}

        # Always set 'DOC_MANQUANT' as a list
        for content_a in get(_dict, "ContentA", []):
            for content_b in to_list(get(content_a, "ContentB", [])):
                if not content_b.get("DOC_MANQUANT_LIST") and content_b.get("DOC_MANQUANT"):
                    content_b["DOC_MANQUANT_LIST"] = to_list(content_b["DOC_MANQUANT"])

        # Set 'ReplyMail' in dict
        # _dict['ReplyMail'] = '<EMAIL>'  # Default reply_to mailbox
        for content_a in get(_dict, "ContentA", []):
            for content_b in to_list(get(content_a, "ContentB", [])):
                if content_b.get("EMAIL_RESA"):
                    _dict["ReplyMail"] = content_b["EMAIL_RESA"]

        # Add current year
        _dict["year"] = str(datetime.now(UTC).year)

        # Add current environment
        _dict["env"] = os.environ["STAGE"]

        # If EMAIL_LIST exist (list of EMAIL from SAP format) then apply it to EMAIL
        if "EMAIL_LIST" in _dict.get("Header", {}):
            _dict["Header"]["EMAIL"] = _dict["Header"]["EMAIL_LIST"]

        # If CCI_LIST exist (list of CCI from SAP format) then apply it to CCI
        if "CCI_LIST" in _dict.get("Header", {}):
            _dict["Header"]["CCI"] = _dict["Header"]["CCI_LIST"]

        # If CC_LIST exist (list of CC from SAP format) then apply it to CC
        if "CC_LIST" in _dict.get("Header", {}):
            _dict["Header"]["CC"] = _dict["Header"]["CC_LIST"]

    # Force UTF-8 encoding to avoid error when sending to SendInBlue
    body = json.loads(get(event, "body", "{}").encode("utf-8"))
    flat_body = _flatten_dict(body)
    _custom_body_actions(flat_body)
    body_header = get(flat_body, "Header", {})
    allow_unknown_user = body_header.get("MYRESA_INCONNU", "N").upper() == "Y"
    no_user_check = body_header.get("NO_USER_CHECK", "N").upper() == "Y"

    # Retrieve template config info
    template_id = body_header.get("TEMPLATE_ID")
    template_config = get_template_config(template_id)
    if "SmsId" not in template_config and "EmailId" not in template_config:
        raise BadRequestError("Ce type de message n'est disponible ni pour Email ni pour Sms")

    # Check inputs identifying the user
    field_values = {}
    only_one = ("email",)
    if "PARTNER_ID" in body_header and body_header["PARTNER_ID"]:
        field_values["bp"] = int(body_header.get("PARTNER_ID"))
    if "EMAIL" in body_header and body_header["EMAIL"]:
        field_values["email"] = body_header.get("EMAIL")
    if "MOBILE_PHONE" in body_header and body_header["MOBILE_PHONE"]:
        field_values["phone"] = body_header.get("MOBILE_PHONE")
    if "EAN" in body_header and body_header["EAN"]:
        field_values["ean"] = body_header.get("EAN")
    if "PANNE_ID" in body_header and body_header["PANNE_ID"]:
        field_values["panne"] = body_header.get("PANNE_ID").zfill(12)
        # remove trailing '0'
        body_header["PANNE_ID"] = body_header["PANNE_ID"].lstrip("0")
    if not field_values:
        raise BadRequestError("Au moins un paramètre parmi PARTNER_ID, EMAIL, MOBILE_PHONE, EAN, ou PANNE_ID doit être fourni")

    # Don't allow using multiple email when checking users
    if isinstance(field_values.get("email"), list) and not no_user_check:
        raise BadRequestError("Multiple email is only allowed when using NO_USER_CHECK = Y", error_code="MULTIPLE_EMAIL_USER_CHECK")

    # Find users
    get_users_fn = {
        "bp": find_user_by_bp,
        "email": find_user_by_email,
        "phone": find_user_by_phone,
        "ean": find_user_by_ean,
        "panne": find_user_by_panne,
    }

    comm_info = {}
    if not no_user_check:
        # Try to find a matching user
        for field_name, field_value in field_values.items():
            with LogTime(f"User search by {field_name} = {field_value}"):
                query = get_users_fn[field_name](field_value)

                for item in query:
                    comm_info[item["uid"]] = check_comm_requirements(item, template_config, flat_body.get("Langue"))
                    if comm_info and field_name in only_one:
                        break

                if comm_info:
                    break

    if allow_unknown_user or no_user_check:
        if not comm_info or all(uid.startswith("ghost") for uid in comm_info):
            language_mail = language_sms = get(flat_body, "Langue", "FR", default_on_empty=True).upper()
            if template_config.get("EmailId") and language_mail not in template_config["EmailId"]:
                language_mail = "FR"
            if template_config.get("SmsId") and language_sms not in template_config["SmsId"]:
                language_sms = "FR"
            comm_info = {
                "unknown_user": {
                    "RecipientName": "",
                    "StatusCode": (200 if field_values.get("email") or field_values.get("phone") else 404),
                    "Email": field_values.get("email"),
                    "EmailStatusCode": (200 if field_values.get("email") and get(template_config, "EmailId", {}).get(language_mail) else 404),
                    "EmailErrorMessage": "",
                    "Phone": field_values.get("phone"),
                    "SmsStatusCode": (200 if field_values.get("phone") and get(template_config, "SmsId", {}).get(language_sms) else 404),
                    "SmsErrorMessage": "",
                    "PrefLangMail": language_mail,
                    "PrefLangSms": language_sms,
                }
            }

    if not comm_info:
        raise NotFoundError(f"Aucun utilisateur trouvé pour les valeurs {field_values}")

    api_config = {
        "apiId": os.environ["API_URL"],
        "apiRegion": os.environ["API_REGION"],
        "apiStage": os.environ["API_STAGE"],
        "sandbox": (event.get("queryStringParameters") or {}).get("Sandbox", "false").lower() != "false",
    }

    # Prepare attachments
    valise_url = body_header.get("VALISE", "")
    valise_url = unquote(valise_url)  # sometimes the valise url use %20 instead of space, this function replace them
    valise = valise_url.split("/")[-1]

    attachments = []
    for document_name in to_list(body_header.get("DOCUMENT_LIST") or body_header.get("DOCUMENT", [])):
        if body_header.get("DOCUMENT") and valise:
            document = SharepointHandler.download_valise_file_from_sharepoint(f"{valise_url}/{document_name}")
            if document:  # Avoid empty data becouse of missing file or SHP API error
                attachments.append(
                    {
                        "content": base64.b64encode(document).decode(),
                        "name": document_name,
                    }
                )
    if "ATTACHMENTS" in body_header:
        for file_info in body_header["ATTACHMENTS"]:
            url = file_info.get("URL")
            filename = file_info.get("FILENAME")

            if url and filename:
                try:
                    response = requests.get(url)
                    if response.status_code == 200:
                        document = response.content
                        attachments.append(
                            {
                                "content": base64.b64encode(document).decode(),
                                "name": filename,
                            }
                        )
                    else:
                        log_info(f"Erreur lors du téléchargement du fichier : {response.status_code}")
                except Exception as e:
                    log_info(f"Erreur lors de l'accès au fichier : {e}")
    # Send message (return error message if check_comm_requirements are not ok)
    email_queue = {}
    sms_queue = {}
    sent_devices = {}

    for uid in comm_info:
        comm_info_user = comm_info[uid]

        # generate unsubscribe link
        flat_body["Unsubscribe"] = generate_unsubscribe_link(uid, comm_info_user, template_config)

        # If list, convert to tuple, because tuple are hashable (hash used to check if a mail is alreaduy sent)
        if isinstance(comm_info_user["Email"], list):
            comm_info_user["Email"] = tuple(comm_info_user["Email"])

        if comm_info_user["EmailStatusCode"] == 200 and comm_info_user["Email"] not in sent_devices:
            if template_config.get("ComPref") == "ComEncodIndex":
                # Save index mail to sharepoint
                for energy in get(flat_body, "ContentA", [], default_on_empty=True):
                    ean = energy.get("EAN")
                    if ean:
                        save_send_in_blue_mail_to_sharepoint(
                            template_id=template_config["EmailId"][comm_info_user["PrefLangMail"]],
                            template_data=flat_body,
                            name="index",
                            desc="Mail index client",
                            valise=f"Dossier EAN-{ean}",
                            email=comm_info_user["Email"],
                            reply_mail=flat_body.get("ReplyMail"),
                            attachments=attachments,
                            bcc=body_header.get("CCI"),
                            cc=body_header.get("CC"),
                        )

            if template_id.startswith("RACC_"):
                # Save racc mail to sharepoint
                for doss_id in {item.get("NUM_DOSSIER") for item in get(flat_body, "ContentA", [], default_on_empty=True)}:
                    if doss_id:
                        save_send_in_blue_mail_to_sharepoint(
                            template_id=template_config["EmailId"][comm_info_user["PrefLangMail"]],
                            template_data=flat_body,
                            desc="Mail racc",
                            valise=f"Dossier Client-{doss_id}",
                            email=comm_info_user["Email"],
                            reply_mail=flat_body.get("ReplyMail"),
                            attachments=attachments,
                            bcc=body_header.get("CCI"),
                            cc=body_header.get("CC"),
                        )
                # Send mail also to contact
                for partenaire in get(flat_body, "ContentA", [], default_on_empty=True):
                    if partenaire.get("TYPE_PARTNER") in ("DEMANDEUR", "CONTACT"):
                        email = partenaire.get("ContentB", {}).get("EMAIL")
                        if email and email not in sent_devices:
                            email_queue[uid] = send_in_blue_mail(
                                template_id=template_config["EmailId"][comm_info_user["PrefLangMail"]],
                                template_data=flat_body,
                                email=email,
                                reply_mail=flat_body.get("ReplyMail"),
                                name=comm_info_user.get("RecipientName"),
                                sandbox=api_config.get("sandbox", False),
                                attachments=attachments,
                                bcc=body_header.get("CCI"),
                                cc=body_header.get("CC"),
                            )
                            sent_devices[email] = email_queue[uid]

            if comm_info_user["Email"] not in sent_devices:
                email_queue[uid] = send_in_blue_mail(
                    template_id=template_config["EmailId"][comm_info_user["PrefLangMail"]],
                    template_data=flat_body,
                    email=comm_info_user["Email"],
                    reply_mail=flat_body.get("ReplyMail"),
                    name=comm_info_user.get("RecipientName"),
                    sandbox=api_config.get("sandbox", False),
                    attachments=attachments,
                    bcc=body_header.get("CCI"),
                    cc=body_header.get("CC"),
                )
                sent_devices[comm_info_user["Email"]] = email_queue[uid]
        elif comm_info_user["EmailStatusCode"] != 200:
            email_queue[uid] = {
                "EmailStatusCode": comm_info_user["EmailStatusCode"],
                "Email": comm_info_user["Email"],
                "EmailErrorMessage": comm_info_user["EmailErrorMessage"],
            }
        else:
            email_queue[uid] = {
                **(sent_devices[comm_info_user["Email"]]),
                **{"SharedEmail": True},
            }

        if comm_info_user["SmsStatusCode"] == 200 and comm_info_user["Phone"] not in sent_devices:
            if template_config.get("ComPref") == "ComEncodIndex":
                # Save index sms to sharepoint
                for energy in get(flat_body, "ContentA", [], default_on_empty=True):
                    ean = energy.get("EAN")
                    if ean:
                        save_sms_to_sharepoint(
                            template_id=template_config["SmsId"][comm_info_user["PrefLangSms"]],
                            template_data=flat_body,
                            name="index",
                            desc="SMS index client",
                            valise=f"Dossier EAN-{ean}",
                            phone=comm_info_user["Phone"],
                        )

            sms_queue[uid] = send_sms(
                input_phone=comm_info_user["Phone"],
                template_name=template_config["SmsId"][comm_info_user["PrefLangSms"]],
                template_content=flat_body,
                api_config=api_config,
                options=None,
            )
            sent_devices[comm_info_user["Phone"]] = sms_queue[uid]
        elif comm_info_user["SmsStatusCode"] != 200:
            sms_queue[uid] = {
                "SmsStatusCode": comm_info_user["SmsStatusCode"],
                "Phone": comm_info_user["Phone"],
                "SmsErrorMessage": comm_info_user["SmsErrorMessage"],
            }
        else:
            sms_queue[uid] = {
                **(sent_devices[comm_info_user["Phone"]]),
                **{"SharedPhone": True},
            }

    if body_header.get("VALISE"):
        _save_comm_to_sharepoint(
            template_config,
            comm_info,
            valise,
            flat_body,
            attachments,
            bcc=body_header.get("CCI"),
            cc=body_header.get("CC"),
        )

    # Format results
    def response_uid(uid):
        email_status_code = get(get(email_queue, uid, {}), "EmailStatusCode", 500)
        sms_status_code = get(get(sms_queue, uid, {}), "SmsStatusCode", 500)
        comm_info_uid_status_code = get(comm_info[uid], "StatusCode", None)

        message_status = check_status(comm_info_uid_status_code, email_status_code, sms_status_code)

        return {
            "Uid": uid,
            "MessageStatus": message_status,
            "MessageResponse": {
                **(email_queue[uid] if uid in email_queue else {}),
                **(sms_queue[uid] if uid in sms_queue else {}),
            },
        }

    response_content = [response_uid(uid) for uid in comm_info]
    global_status = min([uid_response["MessageStatus"] for uid_response in response_content])

    response = {
        "isBase64Encoded": False,
        "headers": {},
        "body": json.dumps(
            {"StatusCode": global_status, "Messages": response_content},
            cls=DecimalEncoder,
        ),
    }

    return response


def generate_unsubscribe_link(uid: str, comm_info_user: dict, template_config: dict) -> str:
    link = ""

    if uid != "unknown_user":
        if template_config.get("ComPref"):
            link = generate_salted_signed_url(
                "GET",
                f"{os.environ['EXTERNAL_API_URL']}/utilisateurs/edit/{uid}/preferences",
                {
                    "Preference": pascal_to_snake(template_config["ComPref"] + "Mail"),
                    "Value": "false",
                },
            )
        elif uid.startswith("panne_"):
            link = generate_salted_signed_url(
                "GET",
                f"{os.environ['EXTERNAL_API_URL']}/panne_subscription/{uid.split('_')[1]}/remove",
                {"Adresse": comm_info_user["Email"] or comm_info_user["Phone"]},
            )
    return link


def _save_comm_to_sharepoint(
    template_config: dict,
    comm_info: dict,
    valise: str,
    flat_body: dict,
    attachments: Optional[list] = None,
    bcc: Optional[Union[str, List[str]]] = None,
    cc: Optional[Union[str, List[str]]] = None,
):
    comm_saved = []
    for info in comm_info.values():
        if info.get("Email") and template_config.get("EmailId") and info["Email"] not in comm_saved:
            save_send_in_blue_mail_to_sharepoint(
                template_id=template_config["EmailId"][info.get("PrefLangMail", "FR")],
                template_data=flat_body,
                desc="Mail client",
                valise=valise,
                email=info["Email"],
                reply_mail=flat_body.get("ReplyMail"),
                not_sent=info.get("EmailStatusCode", 200) != 200,
                attachments=attachments,
                bcc=bcc,
                cc=cc,
            )
            comm_saved.append(info["Email"])
        if info.get("Phone") and template_config.get("SmsId") and info["Phone"] not in comm_saved:
            save_sms_to_sharepoint(
                template_id=template_config["SmsId"][info.get("PrefLangSms", "FR")],
                template_data=flat_body,
                desc="SMS client",
                valise=valise,
                phone=info["Phone"],
                not_sent=info.get("SmsStatusCode", 200) != 200,
            )
            comm_saved.append(info["Phone"])


def send_verification_email(event, config):
    user_data = getUserData(event)
    body = json.loads(get(event, "body", "{}"))
    query_string = get(event, "queryStringParameters", {})
    contact_email = get(query_string, "ContactEmail", default="false").lower() == "true"
    uid = user_data["uid"]

    if contact_email:
        email = get(body, "Email") or user_data["contact_email"]
        if not email:
            raise BadRequestError(
                message="Aucun mail dans le body / dans le compte user",
                error_code="MAIL_NOT_FOUND",
            )
        email = email.lower()
    else:
        if user_data["valide"]:
            raise HttpError(409, "Votre compte est déjà activé. Merci de vous identifier.")
        email = (get(body, "Email") or user_data["email"]).lower()
    if not email:
        raise BadRequestError("No Email found in body", error_code="BAD_EMAIL")

    callback = get(
        query_string,
        "Callback",
        err=BadRequestError("No callback found in queryStringParameters", error_code="NO_CALLBACK"),
    )
    callback_2fa = get(query_string, "Callback2FA")
    error_callback = get(query_string, "ErrorCallback")
    check_callback(callback)
    check_callback(callback_2fa)
    check_callback(error_callback)
    check(
        is_valid_email(email),
        BadRequestError("Invalid email address", error_code="BAD_EMAIL"),
    )
    sandbox = get(query_string, "Sandbox", default="false").lower() == "true"

    try:
        expiration_timestamp = (datetime.now() + timedelta(days=1)).timestamp()

        if contact_email:
            token = encode_simple_jwt(
                {
                    "uid": uid,
                    "email": email,
                    "callback": callback,
                    "errorCallback": error_callback,
                    "expirationTimestamp": expiration_timestamp,
                }
            )

            template = "MYRE_ACCOUNT_VALIDATION_CONTACT_MAIL"
            callback = f"{os.environ['EXTERNAL_API_URL']}/utilisateurs/activate/checkMail"
        else:
            check_if_mail_already_used(email)
            token = encode_simple_jwt(
                {
                    "uid": uid,
                    "email": email,
                    "expirationTimestamp": expiration_timestamp,
                }
            )

            template = "MYRE_ACCOUNT_ACTIVATION"
            if callback_2fa:
                callback = f"{callback_2fa}{'&' if '?' in callback else '?'}Phone={get(user_data, 'phone', '', default_on_empty=True).replace('+', '')}"
                template = "MYRE_ACCOUNT_ACTIVATION_2FA"

            template = "MYRE_COMMUNE_ACCOUNT_ACTIVATION" if user_data.get("commune_id") else template

        user_table.update_item(
            Key={"uid": uid},
            UpdateExpression="SET #email = :email",
            ExpressionAttributeNames={"#email": "contact_email" if contact_email else "email"},
            ExpressionAttributeValues={":email": email},
        )
        api_caller(
            method="post",
            path=f"/envMessage?Sandbox={sandbox}",
            body={
                "Langue": os.environ["LANG"],
                "Header": {
                    "TEMPLATE_ID": template,
                    "EMAIL": email,
                    "NO_USER_CHECK": "Y",
                },
                "Link": f"{callback}{'&' if '?' in callback else '?'}Token={token}",
            },
            headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
        )
    except EmailAlreadyUsedError:
        api_caller(
            method="post",
            path="/envMessage",
            body={
                "Langue": os.environ["LANG"],
                "Header": {"TEMPLATE_ID": "MYRE_ACCOUNT_DUPLICATE", "EMAIL": email},
            },
            headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
            raw=True,
        )

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": {"SessionId": uid, "Email": email},
    }


def send_verification_sms(event, config):
    if get(event, "queryStringParameters", {}, default_on_empty=True).get("Token"):
        user_data = get_user_data_from_query_string(event)
    else:
        user_data = getUserData(event, allow_ghost=False)

    body = json.loads(get(event, "body", "{}", default_on_empty=True))

    phone = get(
        body,
        "Phone",
        err=BadRequestError("Phone is mandatory in body", error_code="MISSING_PHONE"),
    )
    existing_codes = set()

    for validation in get(user_data, "sms_validation", [], default_on_empty=True):
        existing_codes.add(get(validation, "code"))
        sent = get(validation, "sent")
        if sent and datetime.fromisoformat(sent) + timedelta(seconds=50) > datetime.now():
            raise BadRequestError(
                "Rate exceeded, max one code by minute by user",
                error_code="SEND_RATE_LIMIT_EXCEED",
            )

    sms_code = None
    while sms_code is None or sms_code in existing_codes:  # Avoid generating already existing code
        sms_code = "".join(choice(string.digits) for _ in range(6))

    if not isPhone(phone):
        raise BadRequestError("Invalid Phone number", error_code="BAD_PHONE")

    send_sms(
        input_phone=phone,
        template_name=get_template_config("SMS_VALIDATION")["SmsId"][os.environ.get("LANG", "fr").upper()],
        template_content={"Code": sms_code},
        api_config={"sandbox": "false"},
        options=None,
    )

    user_table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression="SET #sms_val = list_append(if_not_exists(#sms_val, :empty_list), :validation)",
        ExpressionAttributeNames={"#sms_val": "sms_validation"},
        ExpressionAttributeValues={
            ":validation": [
                {
                    "phone": phone,
                    "code": sms_code,
                    "expire": (datetime.now() + timedelta(minutes=10)).isoformat(),
                    "sent": datetime.now().isoformat(),
                    "type": body.get("Type"),
                }
            ],
            ":empty_list": [],
        },
    )

    return {"isBase64Encoded": False, "statusCode": 200, "headers": {}}


def check_verification_sms(event, config):
    if get(event, "queryStringParameters", {}, default_on_empty=True).get("Token"):
        user_data = get_user_data_from_query_string(event)
    else:
        user_data = getUserData(event, allow_ghost=False)

    body = json.loads(get(event, "body", "{}", default_on_empty=True))
    sms_code = str(body.get("Code"))
    valid_phone = None
    expired = False
    validation_type = None

    for validation in user_data.get("sms_validation", []):
        if validation["code"] == sms_code:
            expired = datetime.fromisoformat(validation["expire"]) < datetime.now()
            if not expired:
                valid_phone = validation["phone"]
                validation_type = validation["type"]
                break

    if user_data.get("sms_validation_last_try"):
        next_possible_try = datetime.fromisoformat(user_data.get("sms_validation_last_try")) + timedelta(seconds=10)
        remaining = (next_possible_try - datetime.now()).total_seconds()
        if remaining > 0:
            raise HttpError(
                429,
                "Too many validation code requests",
                error_code="TOO_MANY_VALIDATION_REQUESTS",
                headers={"Retry-After": ceil(remaining)},
            )

    user_table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression="SET #last_try = :last_try",
        ExpressionAttributeNames={"#last_try": "sms_validation_last_try"},
        ExpressionAttributeValues={":last_try": datetime.now().isoformat()},
    )

    if expired:
        raise BadRequestError("Expired SMS validation code", error_code="EXP_CODE")
    elif not valid_phone:
        raise BadRequestError("Invalid SMS validation code", error_code="BAD_CODE")
    else:
        user_data["valid_phone"] = True
        user_data["phone"] = valid_phone
        user_table.update_item(
            Key={"uid": user_data["uid"]},
            UpdateExpression="SET #valid = :valid, #phone = :phone",
            ExpressionAttributeNames={"#valid": "valid_phone", "#phone": "phone"},
            ExpressionAttributeValues={":valid": True, ":phone": valid_phone},
        )
        sap_edit_user(user_data)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": {"Type": validation_type},
    }


def check_verification_mail(event, config):
    token = get_token_data_from_query_string(event)

    try:
        if token["expirationTimestamp"] < datetime.now().timestamp():
            raise ForbiddenError("Votre demande de validation est expirée.", error_code="TOKEN_EXPIRED")

        user_data = get_user_data_from_query_string(event)
        user_data["valid_contact_email"] = True
        user_data["contact_email"] = token["email"]

        user_table.update_item(
            Key={"uid": token["uid"]},
            UpdateExpression="SET #valid = :valid, #mail = :mail",
            ExpressionAttributeNames={
                "#valid": "valid_contact_email",
                "#mail": "contact_email",
            },
            ExpressionAttributeValues={":valid": True, ":mail": token["email"]},
        )

        sap_edit_user(user_data)
        callback = token["callback"]
    except HttpError as e:
        callback = f"{token['errorCallback']}{'&' if '?' in token['errorCallback'] else '?'}ErrorCode={e.error_code or 'INTERNAL_SERVER_ERROR'}"
    except Exception as e:
        # Unhandled error
        log_err_json(e)
        callback = f"{token['errorCallback']}{'&' if '?' in token['errorCallback'] else '?'}ErrorCode=INTERNAL_SERVER_ERROR"

    return {
        "isBase64Encoded": False,
        "statusCode": 303,
        "headers": {"Location": callback, "Cache-Control": "no-cache"},
    }


def updatePassword(event, config):
    body = json.loads(get(event, "body", "{}"))
    password = get(body, "Password", err=BadRequestError("No Password found in body"))
    oldPassword = get(body, "OldPassword", err=BadRequestError("No OldPassword found in body"))

    user_data = getUserData(event, allow_ghost=False)

    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    uid = user_data["uid"]
    user = ldap.findUser("uid", uid)
    if not user:
        raise NotFoundError("Uid `{}` not found in AD".format(uid))
    user = user["cn"]
    ldap.changePassword(user, password, old_password=oldPassword)
    del ldap

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({}),
    }


def logout(event, config):
    token = getToken(event["headers"])
    createTokenInvalidation(token)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps({}),
    }


def send_delete_account_mail(event, config):
    # get user data
    user_data = getUserData(event)

    callback = get(
        event.get("queryStringParameters"),
        "Callback",
        err=BadRequestError("No callback found in queryStringParameters", error_code="MISSING_CALLBACK"),
    )
    check_callback(callback)

    # create callback url
    expiration_timestamp = (datetime.now() + timedelta(days=1)).timestamp()
    token = encode_simple_jwt(
        {
            "email": user_data["email"],
            "uid": user_data["uid"],
            "expirationTimestamp": expiration_timestamp,
        }
    )
    callback = callback + ("&" if "?" in callback else "?") + "Token=" + token

    api_caller(
        method="post",
        path="/envMessage",
        body={
            "Langue": os.environ["LANG"],
            "Header": {
                "TEMPLATE_ID": "MYRE_ACCOUNT_DELETE",
                "EMAIL": user_data["email"],
            },
            "Link": callback,
        },
        headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
    )

    return {"isBase64Encoded": False, "statusCode": 204, "headers": {}}


def delete_account(event: dict, config: dict) -> dict:
    """
    Delete user account from DynamoDB and ADFS, then disconnect it from SAP.
    @param event: given by AWS APIGateway
    @param config: given by AWS APIGateway
    @return: AWS APIGateway http dict
    """
    # get user data
    token = get(
        event.get("queryStringParameters"),
        "Token",
        err=BadRequestError("No token found in queryStringParameters", error_code="MISSING_TOKEN"),
    )
    token_content = decode_simple_jwt(token)
    user_data = first(get_dynamodb_table(os.environ["DYNAMODB"]).query(KeyConditionExpression=Key("uid").eq(token_content["uid"]))["Items"])

    if not user_data:
        raise NotFoundError("No account linked to the given token", error_code="NO_ACCOUNT_FOUND")

    # Delete from LDAP
    if user_data["valide"]:
        ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
        uid = user_data["uid"]
        ldap.deleteUserByUid(uid)
        del ldap

        token = getToken(event["headers"])
        if token:
            createTokenInvalidation(token)

    # Flag as disconnected in SAP
    if user_data.get("bp"):
        # Only if not multiple account on same BP (shouldn't happen for account created after 27/04/2023)
        if len(find_user_by_bp(user_data["bp"])) == 1:
            user_data["preferences"]["BP_CONNECTE"] = False
            sap_edit_user(user_data)

    # Delete account from DynamoDB
    user_table.delete_item(Key={"uid": user_data["uid"]})

    # Send confirmation mail
    api_caller(
        method="post",
        path="/envMessage",
        body={
            "Langue": os.environ["LANG"],
            "Header": {
                "TEMPLATE_ID": "MYRE_ACCOUNT_DELETE_CONFIRMATION",
                "EMAIL": user_data.get("contact_email") or user_data["email"],
                "NO_USER_CHECK": "Y",
            },
        },
        headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
    )

    return {"isBase64Encoded": False, "statusCode": 204, "headers": {}}
