import json
import os
from datetime import datetime

from utils.api import api_caller, basic_auth_headers
from utils.auth_utils import load_env_file
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import get
from utils.errors import BadRequestError

misc_table = get_dynamodb_table(f"MyResaAPI_MISC_{os.environ['STAGE_TAG']}")
PANNE_SMART_KEY = "PANNE_SMART"


def get_panne_smart(event, config):
    panne_smart = misc_table.get_item(Key={"Key": PANNE_SMART_KEY}).get("Item")

    if panne_smart:
        result = {"Actif": True, "Id": panne_smart["Id"], "Date": panne_smart["Date"]}
    else:
        result = {"Actif": False}

    return {"isBase64Encoded": False, "statusCode": 200, "body": result}


def post_panne_smart(event, config):
    body = json.loads(get(event, "body", "{}"))
    panne_id = get(
        body,
        "Id",
        err=BadRequestError('No "Id" found in body', error_code="ID_MISSING"),
    )
    actif = get(
        body,
        "Actif",
        err=BadRequestError('No "Actif" found in body', error_code="ACTIF_MISSING"),
    )

    if actif:
        misc_table.put_item(
            Item={
                "Key": PANNE_SMART_KEY,
                "Id": panne_id,
                "Date": datetime.now().isoformat(),
            }
        )
    else:
        api_caller(
            method="post",
            path="/envMessage",
            body={"Header": {"TEMPLATE_ID": "PANNE_RESOLUE", "PANNE_ID": panne_id}},
            headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
            raw=True,
        )
        misc_table.delete_item(Key={"Key": PANNE_SMART_KEY})

    return {"isBase64Encoded": False, "statusCode": 204}
