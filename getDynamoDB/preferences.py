import json
import os

import boto3

from utils.auth_utils import getUserData, loadUserByUID
from utils.aws_utils import get_secret
from utils.dict_utils import get, merge
from utils.errors import BadRequestError
from utils.log_utils import log_err_json
from utils.sap_user import sap_edit_user
from utils.sentry.sentry_utils import capture_exception_in_sentry
from utils.userdata import Preferences

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])


def lire(event, config):
    user_data = getUserData(event)
    user_data["preferences"] = Preferences(user_data).get()

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(user_data["preferences"]),
    }


def ajouter(event, config):
    user_data = getUserData(event)
    body = json.loads(get(event, "body", "{}"))
    user_data["preferences"] = merge(user_data["preferences"], body)
    sap_edit_user(user_data)
    table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression="SET #preferences = :preferences",
        ExpressionAttributeNames={"#preferences": "preferences"},
        ExpressionAttributeValues={":preferences": user_data["preferences"]},
    )

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(user_data["preferences"]),
    }


def modifier(event, config):
    user_data = getUserData(event)
    if not user_data:
        raise Exception("No Token or SessionId")
    key = event["pathParameters"]["Key"]
    user_data["preferences"].pop(key, None)
    body = json.loads(get(event, "body", "{}"))
    user_data["preferences"] = merge(user_data["preferences"], body)
    sap_edit_user(user_data)
    table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression="SET #preferences = :preferences",
        ExpressionAttributeNames={"#preferences": "preferences"},
        ExpressionAttributeValues={":preferences": user_data["preferences"]},
    )

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(user_data["preferences"]),
    }


def enlever(event, config):
    user_data = getUserData(event)
    if not user_data:
        raise Exception("No Token or SessionId")
    key = event["pathParameters"]["Key"]
    user_data["preferences"].pop(key, None)
    sap_edit_user(user_data)
    table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression="SET #preferences = :preferences",
        ExpressionAttributeNames={"#preferences": "preferences"},
        ExpressionAttributeValues={":preferences": user_data["preferences"]},
    )

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(user_data["preferences"]),
    }


def set_by_uid(event, config):
    lang = "FR"
    try:
        uid = get(event["pathParameters"], "Uid")
        user_data = loadUserByUID(uid)

        query_params = get(event, "queryStringParameters", {})
        key = get(
            query_params,
            "Preference",
            err=BadRequestError("Preference query parameter is mandatory."),
        )
        value = get(
            query_params,
            "Value",
            err=BadRequestError("Value query parameter is mandatory."),
        )

        if value.lower() == "true":
            value = True
        elif value.lower() == "false":
            value = False
        else:
            raise BadRequestError('Value should be "true" or "false".')

        user_data["preferences"][key] = value
        table.update_item(
            Key={"uid": user_data["uid"]},
            UpdateExpression="SET #preferences = :preferences",
            ExpressionAttributeNames={"#preferences": "preferences"},
            ExpressionAttributeValues={":preferences": user_data["preferences"]},
        )
        sap_edit_user(user_data)
        lang = get(user_data["preferences"], "Langue", "FR", default_on_empty=True)
    except Exception as e:  # avoid returning any error to client
        log_err_json(e)
        capture_exception_in_sentry(e)

    return {
        "isBase64Encoded": False,
        "statusCode": 303,
        "headers": {
            "Location": f"https://{get_secret(os.environ['ADFS_WEB'])['redirect_domain']}/{lang.lower()}/confirmation-desinscription/",
            "Cache-Control": "no-cache",
        },
    }
