import json
from os import environ

from marshmallow import ValidationError

from utils.DecimalEncoder import DecimalEncoder
from utils.api import api_caller, basic_auth_headers
from utils.bornes_utils import process_and_putDb
from utils.dict_utils import get
from utils.errors import BadRequestError, format_validation_error
from utils.models.borne_recharge_model import BorneRecharge


def post(event, config):
    body = get(event, "body", "{}")

    try:
        # code that could raise ValidationError
        if body.startswith("[") and body.endswith("]"):
            bornes = [process_and_putDb(borne) for borne in BorneRecharge.schema().loads(body, many=True)]
            result_json_items = BorneRecharge.schema().dump(bornes, many=True)
            mail_borne = bornes[0]
            mail_borne.borne.serial = ", ".join([elem.borne.serial for elem in bornes])
            mail_borne.borne.photo = any([elem.borne.photo for elem in bornes])
        else:
            mail_borne = process_and_putDb(BorneRecharge.schema().loads(body))
            result_json_items = mail_borne.to_dict()

        # Send confirmation email
        api_caller(
            method="post",
            path="/envMessage",
            body={
                "Header": {
                    "TEMPLATE_ID": "BORNE_RECHARGE_CLIENT",
                    "EMAIL": mail_borne.demandeur.email,
                    "NO_USER_CHECK": "Y",
                },
                **mail_borne.to_dict(),
            },
            headers=basic_auth_headers(environ["BASICAUTH_AWS"]),
        )
    except ValidationError as err:
        raise BadRequestError(format_validation_error(err.messages), error_code="INVALID_JSON_OBJECT")
    except KeyError as err:
        raise BadRequestError(f"Missing field {err.args[0]}", error_code="INVALID_JSON_OBJECT")

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(result_json_items, cls=DecimalEncoder),
    }
