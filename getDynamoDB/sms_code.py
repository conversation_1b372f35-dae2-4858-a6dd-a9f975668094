import json
import os
import random
from datetime import date

from boto3.dynamodb.conditions import Key

from utils.api import api_caller, basic_auth_headers
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import capitalizeKeys, get
from utils.errors import UnauthorizedError
from utils.validation import validate_type_bool


def envoyer(event, config):
    body = json.loads(get(event, "body", "{}"))
    mobile = get(body, "Mobile")
    langue = get(get(event, "headers", {}), "Langue", "Fr")
    sandbox = validate_type_bool(
        get(get(event, "queryStringParameters", {}), "Sandbox", True),
        err="Valeur du paramètre Sandbox invalide pour type booléen",
    )
    nb_codes_sent_today = count_codes_sent(mobile)
    if not sandbox and nb_codes_sent_today > 3:
        raise UnauthorizedError("On ne peut pas envoyer plus de 3 SMS au même téléphone dans la journée")
    code = generer_code()
    message = generate_message(langue, code)
    envoyer_message(mobile, message, sandbox)
    sauver_code(code, mobile, langue)
    ret = {}
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys(ret)),
    }


def generate_message(langue, code):
    lower_case_langue = langue.lower()
    if lower_case_langue == "fr":
        return "Votre code de vérification MyResa est {}.".format(code)
    elif lower_case_langue == "en":
        return "Your MyResa validation code is {}".format(code)
    else:
        return "Uw MyResa validatie code is {}".format(code)


def verifier(event, config):
    mobile = event["queryStringParameters"]["Mobile"]
    validation_code = event["queryStringParameters"]["Code"]
    table = get_dynamodb_table(os.environ["SMS_CODES"])
    response = table.get_item(Key={"code": validation_code})
    print("response")
    print(response)
    if not response or "Item" not in response:
        raise UnauthorizedError("Code de validation invalide")
    item = response["Item"]
    if item["mobile"] != mobile:
        raise UnauthorizedError("Code de validation invalide")
    if item["validated"]:
        raise UnauthorizedError("Code de validation déjà utilisé")
    table.update_item(
        Key={"code": validation_code},
        UpdateExpression="SET #validated = :validated",
        ExpressionAttributeNames={
            "#validated": "validated",
        },
        ExpressionAttributeValues={":validated": True},
    )
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(capitalizeKeys({})),
    }


def generer_code():
    return "".join([str(random.randint(0, 999)).zfill(3) for _ in range(2)])


def count_codes_sent(mobile):
    table = get_dynamodb_table(os.environ["SMS_CODES"])
    codes_sent = table.query(
        ProjectionExpression="mobile, #d",
        ExpressionAttributeNames={"#d": "date"},
        KeyConditionExpression=Key("mobile").eq(mobile),
        IndexName="mobile-index",
    )["Items"]
    nb_codes_sent_today = len([c for c in codes_sent if get(c, "date", "") == str(date.today())])
    return nb_codes_sent_today


def sauver_code(code, numero, langue):
    table = get_dynamodb_table(os.environ["SMS_CODES"])
    item = {
        "code": code,
        "mobile": numero,
        "validated": False,
        "date": str(date.today()),
        "langue": langue,
    }
    table.put_item(Item=item)


def envoyer_message(to, message, sandbox):
    api_caller(
        "POST",
        "/sms",
        {"Sandbox": sandbox},
        json.dumps({"to": to, "message": message}),
        headers=basic_auth_headers("MyResaAPI/chatbot/BasicAuthPassword"),
    )
