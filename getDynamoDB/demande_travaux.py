import json
import os

from utils.DecimalEncoder import DecimalEncoder
from utils.aws_utils import load_s3_file, get_resource_key


def puissance_forains(event, config):
    file = load_s3_file(os.environ["BUCKET"], get_resource_key("puissance_forains.json"))

    data = json.loads(file)
    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(data, cls=DecimalEncoder),
    }
