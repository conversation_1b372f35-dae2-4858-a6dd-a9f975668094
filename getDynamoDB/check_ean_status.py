import io
import os
from csv import Dict<PERSON>eader, excel
from datetime import datetime

from _csv import register_dialect

from utils.aws_utils import load_s3_file
from utils.dict_utils import get
from utils.errors import BadRequestError, NotFoundError


class excel_csv(excel):
    delimiter = ";"


register_dialect("excel-csv", excel_csv)


def get_ean_lock_status(event, config):
    path_params = get(event, "pathParameters", {})
    ean = get(
        path_params,
        "Ean",
        err=BadRequestError("EAN query params is mandatory", error_code="EAN_MISSING"),
        err_on_empty=True,
    )

    locked_ean = load_s3_file(os.environ["BUCKET"], "locked_ean.csv")
    locked_ean_file = io.StringIO(locked_ean)

    result = None
    for locked_ean in DictReader(locked_ean_file, dialect="excel-csv"):
        if locked_ean["HEADPOINT"] == ean and locked_ean["LockStatus"]:
            result = {
                "LockStatus": locked_ean["LockStatus"],
                "Date": datetime.strptime(locked_ean["Date"], "%d-%m-%y").date().isoformat(),
            }

    if not result:
        raise NotFoundError("EAN not found", error_code="EAN_NOT_FOUND")

    return {"isBase64Encoded": False, "statusCode": 200, "headers": {}, "body": result}
