import json
from datetime import datetime
from decimal import Decimal
from os import environ

import pytz

from utils.aws_utils import get_dynamodb_table
from utils.errors import BadRequestError
from utils.models.user import User
from utils.sap_user import sap_edit_user

CONSENT_TO_USER_PREFERENCE_MAP = {
    "bilan": "com_smartportal_bilan_mail",
    "alertEmail": "com_smartportal_alert_mail",
    "alertSms": "com_smartportal_alert_sms",
}


def check_consent_format(consent: dict) -> bool:
    # Check keys
    valid_key = "Key" in consent and "Value" in consent
    if not valid_key:
        return valid_key

    # Check values types
    valid_types = isinstance(consent["Key"], str) and isinstance(consent["Value"], bool)

    return valid_key and valid_types


def convert_consent_date_time(consent: dict) -> dict:
    consent["DateTime"] = datetime.fromtimestamp(float(consent["DateTime"]), pytz.utc).isoformat()
    return consent


def run_special_action(user: User, consents: list[dict]) -> None:
    """Execute a special action based on the consent key and value."""
    table_alerts = get_dynamodb_table(environ["SmartConsoAlerts"])
    table_energy_profiles = get_dynamodb_table(environ["UserEnergyProfiles"])
    table_users = get_dynamodb_table(environ["DYNAMODB"])

    consents_dict = {}
    address_hash_set = set()

    for consent in consents:
        key = consent["Key"]
        value = consent["Value"]
        consents_dict[key] = value

        if key.startswith("maconso."):
            key_part = key.split(".")
            # TODO : If in future we use a consent that start with 'maconso.' and follow with something else than an address hash, this line could be problematic
            address_hash = key_part[1]
            key_last_part = key_part[-1]

            # Add to the address hash to set for check on multiple consent change under the same address
            address_hash_set.add(address_hash)

            # Sync generic preference value with the given consent
            preference = CONSENT_TO_USER_PREFERENCE_MAP.get(key_last_part)
            if preference:
                table_users.update_item(
                    Key={"uid": user.uid},
                    UpdateExpression="SET preferences.#preference = :consent_value",
                    ExpressionAttributeNames={"#preference": preference},
                    ExpressionAttributeValues={":consent_value": value},
                )

            # Specific action other than preference sync
            if key.endswith(".bilan") and value is False:
                # Sync the bilan config
                table_energy_profiles.update_item(
                    Key={"Uid": user.uid, "HashAddress": address_hash},
                    UpdateExpression="SET #bilan = :consent_value, #consent_bilan = :consent_value",
                    ExpressionAttributeNames={"#bilan": "Bilan", "#consent_bilan": "ConsentBilan"},
                    ExpressionAttributeValues={":consent_value": value},
                )
            if key.endswith(".energyAsset") and value is False:
                # Sync and delete the energy assets config
                table_energy_profiles.update_item(
                    Key={"Uid": user.uid, "HashAddress": address_hash},
                    UpdateExpression="SET #energy_asset = :energy_asset, #consent_energy_asset = :consent_value",
                    ExpressionAttributeNames={"#energy_asset": "EnergyAsset", "#consent_energy_asset": "ConsentEnergyAsset"},
                    ExpressionAttributeValues={":consent_value": value, ":energy_asset": None},
                )

    # loop over all unique address_hash
    for address_hash in address_hash_set:
        # if both alertEmail and alertSms are pushed and both are false, we delete alert config
        if consents_dict.get("maconso." + address_hash + ".alertEmail") is False and consents_dict.get("maconso." + address_hash + ".alertSms") is False:
            for meters in (ean_data.meterid for ean_data in user.ean if ean_data.address_hash == address_hash):
                for meter in meters:
                    table_alerts.delete_item(Key={"Uid": user.uid, "Meter": meter})


def ajouter(event, config):
    """
    Adds a list of consents after verifying their format.
    Also executes special actions associated with each consent.
    """
    user = User.from_event(event)
    consents = json.loads(event.get("body") or "[]")

    # Check values
    if not consents or not isinstance(consents, list) or not all(check_consent_format(consent) for consent in consents):
        raise BadRequestError(
            "Le body doit être une liste de consentement. Avec pour chaque, un élément Key de type string et Value de type boolean.",
            error_code="INVALID_CONSENT_FORMAT",
        )

    # Retrieve the latest consents for comparison
    existing_consents = {c["Key"]: c["Value"] for c in get_latest_consents(user.consentements or [])}

    # Filter consents to keep only changed values
    consents = [c for c in consents if existing_consents.get(c["Key"]) != c["Value"]]

    if consents:
        # Add timestamps
        timestamp = Decimal(datetime.now(pytz.utc).timestamp())
        unique_consent = []
        for consent in consents:
            consent["DateTime"] = timestamp
            consent_key = consent["Key"]
            if consent_key in unique_consent:
                raise BadRequestError(f"Les consentements doivent etre unique : {consent_key} fournis plusieurs fois.", error_code="NOT_UNIQUE_CONSENT")
            unique_consent.append(consent_key)

        # Run special action
        run_special_action(user, consents)

        # Save to DynamoDB
        table_users = get_dynamodb_table(environ["DYNAMODB"])
        user_data = table_users.update_item(
            Key={"uid": user.uid},
            UpdateExpression="SET #consent = list_append(if_not_exists(#consent, :empty_list), :consent)",
            ExpressionAttributeNames={"#consent": "consentements"},
            ExpressionAttributeValues={":consent": consents, ":empty_list": []},
            ReturnValues="ALL_NEW",
        )["Attributes"]

        # Save to SAP
        sap_edit_user(user_data)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps([convert_consent_date_time(consent) for consent in consents]),
    }


def get_latest_consents(consents):
    """Filtre pour ne garder que la dernière valeur de chaque Key sans convertir DateTime."""
    if not consents:
        return []

    consents.sort(key=lambda x: x["DateTime"], reverse=True)

    latest_consents = {}
    for consent in consents:
        if consent["Key"] not in latest_consents:
            latest_consents[consent["Key"]] = consent

    return list(latest_consents.values())


def lire(event, config):
    user = User.from_event(event)
    consents = user.consentements

    latest_consents = get_latest_consents(consents)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps([convert_consent_date_time(consent) for consent in latest_consents]),
    }
