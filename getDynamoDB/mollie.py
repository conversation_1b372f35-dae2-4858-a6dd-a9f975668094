import os
import time
from datetime import datetime, timezone
from os import environ
from urllib.parse import parse_qs

import requests
from boto3.dynamodb.conditions import Key, Attr

from utils.api import api_caller
from utils.aws_utils import get_secret_string, get_dynamodb_table
from utils.dict_utils import first, capitalizeKeys, get
from utils.errors import BadRequestError, NotFoundError
from utils.log_utils import log_warning_json, log_info_json

mollie_table = get_dynamodb_table(f"MollieStatus_{os.environ['STAGE_TAG']}")


# Not as useless as you think, used so I can mock the TTL in testing
def create_ttl_year():
    current_time = int(time.time())

    # TTL = Today + 365 days
    return current_time + (365 * 24 * 60 * 60)  # 365days in seconds


def update(event, config):
    payment_id = next(iter(parse_qs(event.get("body") or "").get("id")))

    # Id is mandatory
    if not payment_id:
        log_warning_json({"error": "Mollie callback without ID", "request": event})
        raise BadRequestError("L'id est obligatoire dans la requête")

    resp = requests.get(
        url=f"https://api.mollie.com/v2/payments/{payment_id}",
        headers={"Authorization": f"Bearer {get_secret_string(environ['MOLLIE_TOKEN'])}"},
    )
    resp_data = resp.json()

    dossier_id = (resp_data.get("metadata") or {}).get("dossier")
    payment_type = (resp_data.get("metadata") or {}).get("type")
    status = resp_data.get("status")

    # OrderId and Status are mandatory
    if not dossier_id or not status or not isinstance(status, str) or not payment_type:
        log_warning_json(
            {
                "error": "Mollie Invalid DossierId, Status, or Type",
                "request": event,
                "response": resp,
                "response_data": resp_data,
            }
        )
        raise BadRequestError("Le dossier, status, ou type n'est pas présent ou invalide")
    else:
        log_info_json({"mollie_status": resp.status_code, "mollie_reponse": resp_data})

    # Send paid status to SAP
    if status == "paid":
        resp_ws109 = api_caller(
            "get",
            "/demande_travaux/confirmation",
            params={
                "NumDossier": dossier_id,
                "TypeAction": f"{payment_type.upper()}_PAYE",
                "DateAccord": datetime.now().strftime("%d/%m/%Y"),
            },
            raw=True,
        )
        try:
            WS109_reponse = resp_ws109.json()
        except:
            WS109_reponse = resp_ws109.text
        log_info_json({"WS109_status": resp_ws109.status_code, "WS109_reponse": WS109_reponse})

    mollie_data = {
        "payment_id": payment_id,
        "order_id": dossier_id,
        "payment_type": payment_type.upper(),
        "mollie_status": status,
        "ttl": create_ttl_year(),
        "creation_date": resp_data.get("createdAt"),
    }
    mollie_table.put_item(Item=mollie_data)
    return {"isBase64Encoded": False, "statusCode": 200, "body": "{}"}


def retrieve_mollie_data(event, config):
    query_str_params = get(event, "queryStringParameters", {})
    payment_type = query_str_params.get("PaymentType")
    order_id = query_str_params.get("OrderId")
    payment_id = query_str_params.get("PaymentId")

    if not (payment_id or (payment_type and order_id)):
        raise BadRequestError(
            "PaymentId or OrderId + PaymentType is required",
            error_code="MISSING_QUERY_PARAMS",
        )

    if payment_id:
        response = mollie_table.get_item(
            Key={"payment_id": payment_id},
            ProjectionExpression="payment_id, creation_date, order_id, mollie_status, payment_type",
        )
        data = response.get("Item")
    else:
        response = mollie_table.query(
            IndexName="order_id-index",
            KeyConditionExpression=Key("order_id").eq(order_id.zfill(12)),
            FilterExpression=Attr("payment_type").eq(payment_type),
            ProjectionExpression="payment_id, creation_date, order_id, mollie_status, payment_type",
        )["Items"]

        data = first(sorted(response, key=lambda x: x["creation_date"], reverse=True))

    if data is None:
        raise NotFoundError("No data with given ID", error_code="NO_DATA_FROM_ID")

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": capitalizeKeys(data),
    }


def post_mollie_waiting(event, config):
    query_str_params = get(event, "queryStringParameters", {})
    payment_id = query_str_params.get("PaymentId")

    if not payment_id:
        raise BadRequestError(
            "PaymentId is required",
            error_code="MISSING_QUERY_PARAMS",
        )

    response = mollie_table.get_item(Key={"payment_id": payment_id})
    item = response.get("Item")

    current_time = datetime.now(timezone.utc)

    if not item:
        item = {
            "payment_id": payment_id,
            "order_id": "UNKNOWN",
            "payment_type": "UNKNOWN",
            "mollie_status": "waiting",
            "ttl": create_ttl_year(),
            "creation_date": current_time.isoformat(),
        }

        mollie_table.put_item(Item=item)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": capitalizeKeys(item),
    }
