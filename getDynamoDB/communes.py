import json
import os

from boto3.dynamodb.conditions import Attr, Key

from compte import creer_structure, find_user_by_bp
from utils.aws_handler_decorator import commune_handler
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import get, first
from utils.errors import BadRequestError, NotFoundError
from utils.ldap_utils import LDAP
from utils.models.user import User
from utils.sap_user import sap_edit_user

user_table = get_dynamodb_table(os.environ["DYNAMODB"])


@commune_handler(admin=True)
def get_users(event, config, logged_user: User) -> dict:
    users = get(
        user_table.query(
            IndexName="commune_id-index",
            KeyConditionExpression=Key("commune_id").eq(logged_user.commune.id),
            FilterExpression=Attr("uid").ne(logged_user.uid),
        ),
        "Items",
        [],
    )

    users = [User.from_dict(user) for user in users]

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps(
            [
                {
                    "Uid": user.uid,
                    "Nom": user.lastname,
                    "Prenom": user.firstname,
                    "Departement": user.commune.departement,
                    "Actif": user.commune.actif,
                    "Valide": user.valide,
                    "Roles": user.commune.roles,
                }
                for user in users
            ]
        ),
    }


def _get_user(uid: str, commune_id: str) -> User:
    user_data = first(
        get(
            user_table.query(
                KeyConditionExpression=Key("uid").eq(uid),
                FilterExpression=Attr("Commune.Id").eq(commune_id),
            ),
            "Items",
            [],
        )
    )

    if not user_data:
        raise NotFoundError(f'User with Uid "{uid}" not found.', error_code="USER_NOT_FOUND")

    return User.from_dict(user_data)


def _get_user_by_mail(mail: str) -> User:
    user_data = first(
        get(
            user_table.query(
                IndexName="email-index",
                KeyConditionExpression=Key("email").eq(mail),
                FilterExpression=Attr("Commune.Id").exists(),
            ),
            "Items",
            [],
        )
    )

    if not user_data:
        raise NotFoundError(f'User with Mail "{mail}" not found.', error_code="USER_NOT_FOUND")

    return User.from_dict(user_data)


@commune_handler(admin=True)
def get_user(event, config, logged_user: User) -> dict:
    path_params = get(event, "pathParameters", {}, default_on_empty=True)
    uid = get(
        path_params,
        "Uid",
        err_on_empty=True,
        err=BadRequestError("Uid is mandatory in path parameters", error_code="UID_MANDATORY"),
    )

    user = _get_user(uid, logged_user.commune.id)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps(
            {
                "Uid": user.uid,
                "Nom": user.lastname,
                "Prenom": user.firstname,
                "Departement": user.commune.departement,
                "Fonction": user.commune.fonction,
                "Email": user.email,
                "Tel": user.phone,
                "Actif": user.commune.actif,
                "Valide": user.valide,
                "Roles": user.commune.roles,
            }
        ),
    }


@commune_handler(admin=True)
def create_user(event, config, logged_user: User) -> dict:
    body = json.loads(get(event, "body", "{}"))

    user_data = creer_structure(body)
    user_data["firstname"] = get(body, "Prenom")
    user_data["lastname"] = get(body, "Nom")
    user_data["phone"] = get(body, "Tel")
    user_data["Commune"] = {
        "Id": logged_user.commune.id,
        "Localite": logged_user.commune.localite,
        "CodePostaux": logged_user.commune.code_postaux,
        "Fonction": get(body, "Fonction"),
        "Departement": get(body, "Departement"),
        "Roles": get(body, "Roles"),
    }
    user: User = User.from_dict(user_data)
    user_data["commune_id"] = user.commune_id

    try:
        _get_user_by_mail(user.email)
    except NotFoundError:
        if not user_data["phone"]:
            del user_data["phone"]
        user_table.put_item(Item=user_data)
    else:
        raise NotFoundError(
            f'User with Mail "{user.email}" already exist.',
            error_code="USER_ALREDY_EXIST",
        )

    return {
        "isBase64Encoded": False,
        "statusCode": 201,
        "headers": {},
        "body": json.dumps(
            {
                "Uid": user.uid,
                "Nom": user.lastname,
                "Prenom": user.firstname,
                "Departement": user.commune.departement,
                "Fonction": user.commune.fonction,
                "Email": user.email,
                "Tel": user.phone,
                "Actif": user.commune.actif,
                "Roles": user.commune.roles,
            }
        ),
    }


@commune_handler(admin=True)
def patch_user(event, config, logged_user: User) -> dict:
    body = json.loads(get(event, "body", "{}", default_on_empty=True))
    path_params = get(event, "pathParameters", {}, default_on_empty=True)
    uid = get(
        path_params,
        "Uid",
        err_on_empty=True,
        err=BadRequestError("Uid is mandatory in path parameters", error_code="UID_MANDATORY"),
    )

    user = _get_user(uid, logged_user.commune.id)
    minimal_len = len(user.commune.roles)

    user.firstname = get(body, "Prenom", user.firstname)
    user.lastname = get(body, "Nom", user.lastname)
    user.phone = get(body, "Tel", user.phone)
    user.commune.fonction = get(body, "Fonction", user.commune.fonction)
    user.commune.departement = get(body, "Departement", user.commune.departement)
    user.commune.roles = get(body, "Roles", user.commune.roles)

    if len(user.commune.roles) < minimal_len:
        user.commune.roles += [""] * (minimal_len - len(user.commune.roles))

    if get(body, "Actif", user.commune.actif) != user.commune.actif:
        user.commune.actif = get(body, "Actif", user.commune.actif)
        adfs_activate_user(user.uid, user.commune.actif)

    user_dict = user.to_dict()
    if not user.email:
        del user_dict["email"]
    if not user.bp:
        del user_dict["bp"]
    if not user.phone:
        del user_dict["phone"]

    sap_edit_user(user_dict)

    user_dict["Commune"]["Roles"] = [item for item in user_dict["Commune"]["Roles"] if item != ""]

    user_table.put_item(Item=user_dict)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "headers": {},
        "body": json.dumps(
            {
                "Uid": user.uid,
                "Nom": user.lastname,
                "Prenom": user.firstname,
                "Departement": user.commune.departement,
                "Fonction": user.commune.fonction,
                "Email": user.email,
                "Tel": user.phone,
                "Actif": user.commune.actif,
                "Roles": user.commune.roles,
            }
        ),
    }


def adfs_activate_user(uid: str, activate: bool):
    with LDAP.loadFromSecret(os.environ["AD_SECRET"]) as ldap:
        ldap.setUserAttribute(uid, "userAccountControl", "512" if activate else "514")


@commune_handler(admin=True)
def delete_user(event, config, logged_user: User) -> dict:
    path_params = get(event, "pathParameters", {}, default_on_empty=True)
    uid = get(
        path_params,
        "Uid",
        err_on_empty=True,
        err=BadRequestError("Uid is mandatory in path parameters", error_code="UID_MANDATORY"),
    )
    user = _get_user(uid, logged_user.commune.id)

    if user.valide:
        ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
        ldap.deleteUserByUid(uid)
        del ldap

    # Flag as disconnected in SAP
    if user.bp:
        # Only if not multiple account on same BP (shouldn't happen for account created after 27/04/2023)
        user_data = user.to_dict()
        if len(find_user_by_bp(user_data["bp"])) == 1:
            user_data["preferences"]["BP_CONNECTE"] = False
            sap_edit_user(user_data)

    user_table.delete_item(Key={"uid": user.uid})

    return {"isBase64Encoded": False, "statusCode": 204, "headers": {}}
