#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import os

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file
from utils.dict_utils import merge


def import_(elt):
    part = elt.split(":")
    mod = __import__(part[0])
    return getattr(mod, part[1])


@aws_lambda_handler
def handler(event, context):
    method = event["httpMethod"]
    path = event["resource"]

    linking_file = json.loads(load_s3_file(os.environ["MAPPING_BUCKET_NAME"], os.environ["MAPPING_BUCKET_FILE"]))  # pylint: disable=no-member
    linking = linking_file[path][method.upper()]

    result = import_(linking["function"])(event, linking)

    result["headers"] = merge(
        {"Access-Control-Allow-Origin": "*", "Content-Type": "application/json"},
        result.get("headers", {}),
    )

    return result
