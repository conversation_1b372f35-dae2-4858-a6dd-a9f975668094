import os

from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import get
from utils.errors import NotFoundError
from utils.models.histo_alert_model import HistoricAlert
from utils.models.user import User

table = get_dynamodb_table(os.environ["HistoricAlerts"])


def remove_expiration_time(data):
    if isinstance(data, dict):
        return {k: remove_expiration_time(v) for k, v in data.items() if k != "expirationTime"}
    elif isinstance(data, list):
        return [remove_expiration_time(v) for v in data]
    return data


def ws218(event, config):
    user = User.from_event(event, allow_ghost=False)
    query_params = get(event, "queryStringParameters", "{}")
    input_address_hash = get(query_params, "AddressHash")

    response = table.query(
        IndexName="address_hash-index",
        KeyConditionExpression="address_hash = :address_hash",
        FilterExpression="uid = :uid",
        ExpressionAttributeValues={":address_hash": input_address_hash, ":uid": user.uid},
        ScanIndexForward=False,
    )

    items = response.get("Items", [])
    if not items:
        raise NotFoundError("No alerts found for this user.", error_code="DATA_NOT_FOUND")

    user_alerts = sorted(
        [HistoricAlert.model_construct(**item) for item in items],
        key=lambda alert: alert.date,
        reverse=True,
    )

    return {
        "statusCode": 200,
        "body": user_alerts,  # capitalizeKeys([alert.model_dump() for alert in user_alerts]),
    }
