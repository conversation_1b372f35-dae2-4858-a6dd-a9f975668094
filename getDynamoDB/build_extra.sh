#!/bin/sh

# install extra binary dependencies
yum install -y yum-utils rpmdevtools
mkdir tmp_lib tmp_install
(cd tmp_install; yumdownloader libffi libffi-devel cairo pango libthai fribidi harfbuzz graphite2 libXft pixman \
  fontconfig freetype libEGL libpng libxcb libXrender libXext libGL libX11 hwdata libXdamage libXfixes libXxf86vm \
  libdrm libglvnd libpciaccess libxshmfence mesa-libGL mesa-libglapi libXau && rpmdev-extract *rpm)
cp -P -R tmp_install/*/usr/lib64/* tmp_lib
(cd tmp_lib; ln -s libpango-1.0.so.0 pango-1.0)
(cd tmp_lib; ln -s libpangocairo-1.0.so.0 pangocairo-1.0)
mv tmp_lib/* bundle
rm -rf tmp_lib tmp_install