import json
import os

from utils.api import api_caller
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import convert_float_to_decimal, get
from utils.errors import NotFoundError
from utils.models.address import PydanticAddress
from utils.models.energy_profile_model import EnergyProfile
from utils.models.user import User

energy_profile_table = get_dynamodb_table(os.environ["UserEnergyProfiles"])
misc_table = get_dynamodb_table(f"MyResaAPI_MISC_{os.environ['STAGE_TAG']}")


def get_adresse_from_qp(query_params) -> PydanticAddress:
    return PydanticAddress.model_validate(query_params)


def register(event, context):
    user = User.from_event(event, allow_ghost=False)

    body = json.loads(get(event, "body", "{}"))
    query_params = event.get("queryStringParameters", {}) or {}

    hash_address = query_params.get("AddressHash") or get_adresse_from_qp(query_params).stable_hash()

    bilan = get(body, "Bilan")
    energy_asset = get(body, "EnergyAsset")
    bilan_consent = get(body, "ConsentBilan")
    consent_energy_asset = get(body, "ConsentEnergyAsset")

    energy_profile = EnergyProfile(
        uid=user.uid,
        bilan=bilan,
        energy_asset=energy_asset,
        hash_address=hash_address,
        consent_bilan=bilan_consent,
        consent_energy_asset=consent_energy_asset,
    )
    response = update_profile(energy_profile)
    valid_body = []

    if bilan is not None:
        valid_body.append({"Key": f"maconso.{hash_address}.bilan", "Value": True})
    if energy_asset:
        valid_body.append({"Key": f"maconso.{hash_address}.energyAsset", "Value": True})

    headers = get(event, "headers", {})
    api_caller("POST", "/me/consentements", body=valid_body, headers=headers)

    response_body = response["Attributes"]
    for key in ["Uid", "HashAddress"]:
        response_body.pop(key, None)

    for key in ["Bilan", "EnergyAsset", "ConsentBilan", "ConsentEnergyAsset"]:
        if key not in response_body:
            response_body[key] = None

    return {
        "statusCode": 200,
        "body": response_body,
    }


def update_profile(energy_profile: EnergyProfile):
    key = {"Uid": energy_profile.uid, "HashAddress": energy_profile.hash_address}
    update_expr = []
    expr_attr_names = {}
    expr_attr_values = {}

    if energy_profile.bilan is not None:
        update_expr.append("#bilan = :bilan")
        expr_attr_names["#bilan"] = "Bilan"
        expr_attr_values[":bilan"] = energy_profile.bilan

        update_expr.append("#consent_bilan = :consent_bilan")
        expr_attr_names["#consent_bilan"] = "ConsentBilan"
        expr_attr_values[":consent_bilan"] = energy_profile.consent_bilan

    if energy_profile.energy_asset is not None:
        update_expr.append("#energy_asset = :energy_asset")
        expr_attr_names["#energy_asset"] = "EnergyAsset"
        expr_attr_values[":energy_asset"] = energy_profile.energy_asset.model_dump_dynamodb(by_alias=True)

        update_expr.append("#consent_energy_asset = :consent_energy_asset")
        expr_attr_names["#consent_energy_asset"] = "ConsentEnergyAsset"
        expr_attr_values[":consent_energy_asset"] = energy_profile.consent_energy_asset

    update_expression = "SET " + ", ".join(update_expr)

    return energy_profile_table.update_item(
        Key=key,
        UpdateExpression=update_expression,
        ExpressionAttributeNames=expr_attr_names,
        ExpressionAttributeValues=convert_float_to_decimal(expr_attr_values),
        ReturnValues="ALL_NEW",
    )


def calculation_energy_consumption(energy_model: EnergyProfile) -> float:
    consumption_params = misc_table.get_item(Key={"Key": "EnergyConsumptionParameters"}).get("Item")["Value"]
    total_consumption = 0

    if energy_model.energy_asset:
        num_people = str(energy_model.energy_asset.number_people)
        if num_people in consumption_params["NumberOfPeopleInHousehold"]:
            total_consumption += float(consumption_params["NumberOfPeopleInHousehold"][num_people])
        else:
            total_consumption += float(consumption_params["NumberOfPeopleInHousehold"]["4"]) + float(consumption_params["NumberOfPeopleInHousehold"]["5+"]) * (
                energy_model.energy_asset.number_people - 4
            )

        if energy_model.energy_asset.domestic_hot_water:
            total_consumption += float(consumption_params["DomesticHotWater"]) * energy_model.energy_asset.number_people

        primary_heating = energy_model.energy_asset.primary_heating_source.value
        habitation_type = energy_model.energy_asset.habitation_type.value
        if primary_heating == "ELECTRICITY":
            total_consumption += float(consumption_params["PrimaryHeatingSource"]["Electricity"][habitation_type])
        else:
            total_consumption += float(consumption_params["PrimaryHeatingSource"].get(primary_heating, 0))

        if energy_model.energy_asset.swimming_pool:
            total_consumption += float(consumption_params["SwimmingPool"])

        if energy_model.energy_asset.jacuzzi:
            total_consumption += float(consumption_params["Jacuzzi"])

        total_consumption += float(consumption_params["ElectricVehicles"]) * energy_model.energy_asset.electric_vehicles_charged_home

    return total_consumption


def fetch(event, config):
    user = User.from_event(event, allow_ghost=False)
    query_params = event.get("queryStringParameters") or {}
    hash_address = query_params.get("AddressHash", None)
    if not hash_address:
        address = get_adresse_from_qp(query_params)
        hash_address = address.stable_hash()

    energy_data = energy_profile_table.get_item(Key={"Uid": user.uid, "HashAddress": hash_address}).get("Item")
    if not energy_data:
        raise NotFoundError("Energy Data not found", error_code="DATA_NOT_FOUND")

    energy_profile = EnergyProfile.recursive_construct(**energy_data)
    total_energy_consumption = calculation_energy_consumption(energy_profile)

    response_body = energy_profile.model_dump()
    response_body["TotalConsumption"] = total_energy_consumption if total_energy_consumption > 0 else None
    for key in ["Uid", "HashAddress"]:
        del response_body[key]

    return {
        "statusCode": 200,
        "body": response_body,
    }
