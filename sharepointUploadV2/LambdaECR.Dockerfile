FROM public.ecr.aws/lambda/python:3.11

# Install ImageMagick
RUN yum install -y ImageMagick-devel

# Copy lambda code
COPY ./sharepointUploadV2 ${LAMBDA_TASK_ROOT}/tmp_build
COPY ./utils ${LAMBDA_TASK_ROOT}/tmp_build/utils
COPY ./global_requirements.txt ${LAMBDA_TASK_ROOT}

# Install python packages
WORKDIR ${LAMBDA_TASK_ROOT}/tmp_build
RUN pip install -r requirements.txt

# Move everything to the lambda task root
WORKDIR ${LAMBDA_TASK_ROOT}
RUN mv ./tmp_build/* ./ &&\
 rm -rf ./tmp_build

# Set the CMD to your handler
CMD [ "sharepointUpload.handler" ]