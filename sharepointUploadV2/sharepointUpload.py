import base64
import datetime as dt
import json
import os
from urllib.parse import unquote_plus

import boto3

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import upload_s3_file
from utils.dict_utils import get
from utils.image_utils import convert_image_to_pdf
from utils.log_utils import log_info_json, log_err_json
from utils.sharepoint import SharepointHandler

REGION_NAME = os.environ["API_REGION"]
MD_TABLE = os.environ["MD_TABLE"]
BUCKET = os.environ["BUCKET_NAME_SHAREPOINT_UPLOADS"]
DYNAMO_DB = os.environ["DYNAMODB"]

MAX_WRITE_ATTEMPTS = 3
s3 = boto3.client("s3", region_name=REGION_NAME)


@aws_lambda_handler
def handler(event, context):
    log_info_json(event)

    for record in event["Records"]:
        bucket = record["s3"]["bucket"]["name"]
        key = unquote_plus(record["s3"]["object"]["key"])

        key_part = key.split("/")
        dossier_id = key_part[0]
        document = key_part[1]
        document_id = document.split(".")[0]

        # Get metadata
        metadata = get_file_meta(document_id)

        if not get(metadata, "submitted"):
            return {
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Content-Type": "application/json",
                },
                "isBase64Encoded": False,
                "statusCode": 200,
                "body": "",
            }

        # Get file from S3 bucket
        file_data = get_file_data(bucket, key)

        # convert images to pdf
        extension = metadata["original_file_format"]
        if extension in (
            "bmp",
            "eps",
            "gif",
            "ico",
            "jpeg",
            "jpg",
            "jp2",
            "pcx",
            "png",
            "ppm",
            "tiff",
        ):
            try:
                file_data = convert_image_to_pdf(file_data)
                extension = "pdf"
            except Exception as e:
                # In case on failure, log error but still upload the file
                log_err_json({"ImageToPdfFailed": e})

        # Upload to final S3
        s3_uploaded = upload_s3_file(
            BUCKET,
            f"{metadata.get('dossier_id', dossier_id)}/{document_id}.{extension}",
            file_data,
        )
        s3_upload_ts = dt.datetime.utcnow().isoformat() if s3_uploaded else None

        # Send PDF file to S3 and upload to Sharepoint
        sharepoint_response = SharepointHandler.send_valise_file_to_sharepoint(
            metadata["file_name"],
            extension,
            base64.b64encode(file_data).decode("utf-8"),
            f"Dossier Client-{metadata.get('dossier_id', dossier_id)}",
            metadata={
                "ContentType": "autres",
                "Type_Autres": "Document",
                "Description_document": "Import Client SiteWeb",
                "VALISE_OPERATIONNELLE": "0",
            },
            timeout=(60, 60 * 8),
        )
        sharepoint_upload_ts = dt.datetime.utcnow().isoformat() if sharepoint_response else None

        # Update Metadata
        metadata_response = store_file_meta(
            document_id,
            bool(sharepoint_response),
            bool(s3_uploaded),
            sharepoint_upload_ts,
            s3_upload_ts,
        )
        status_code = 200
        body = json.dumps(
            {
                "file_path": document,
                "sharepoint_response": sharepoint_response,
                "metadata_response": metadata_response["ResponseMetadata"],
            }
        )

        response = {
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": "application/json",
            },
            "isBase64Encoded": False,
            "statusCode": status_code,
            "body": body,
        }

        return response


def get_file_extension(document_name):
    file_extension = document_name.split(".")[-1]
    return file_extension


def remove_file(file_path):
    try:
        os.remove(file_path)
    except OSError:
        pass

    return True


def get_file_data(bucket, key):
    """
    Get file from S3 bucket
    """

    try:
        obj = s3.get_object(Bucket=bucket, Key=key)
        file = obj["Body"].read()

        return file
    except Exception as e:
        raise IOError(e)


def store_file_meta(document_id, sharepoint_uploaded, s3_uploaded, sharepoint_upload_ts, s3_upload_ts):
    dynamodb = boto3.resource("dynamodb")
    table = dynamodb.Table(MD_TABLE)

    try:
        response = table.update_item(
            Key={"document_id": document_id},
            UpdateExpression="SET sharepoint=:s, s3_backup=:b, s3_backup_date=:sbt, sharepoint_date=:spt, file_format=:ff",
            ExpressionAttributeValues={
                ":s": sharepoint_uploaded,
                ":b": s3_uploaded,
                ":sbt": s3_upload_ts,
                ":spt": sharepoint_upload_ts,
                ":ff": "pdf",
            },
            ReturnValues="UPDATED_NEW",
        )

        return response
    except Exception as e:
        raise e


def get_file_meta(document_id):
    dynamodb = boto3.resource("dynamodb")
    table = dynamodb.Table(MD_TABLE)

    try:
        response = table.get_item(Key={"document_id": document_id})

        return response.get("Item", {})
    except Exception as e:
        raise e
