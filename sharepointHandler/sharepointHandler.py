import base64
import json
import os
from uuid import uuid4

import requests
import xmltodict
from sharepointOnline import SharepointHandlerOnline
from sharepointPremise import SharepointHandlerPremise

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import create_s3_presigned_url, upload_s3_file
from utils.errors import BadRequestError, InternalServerError, NotImplementedError
from utils.sentry.sentry_utils import capture_message_in_sentry


@aws_lambda_handler
def handler(event, context):
    http_method = event["httpMethod"].lower()  # Convertir en minuscule pour garantir la correspondance
    dic_methods_func = {"post": post_lambda, "get": get_lambda, "put": put_lambda}
    if http_method in dic_methods_func:
        return dic_methods_func[http_method](event)
    else:
        raise BadRequestError("Methode non reconnue", error_code="BAD_REQUEST_ERROR")


def post_lambda(event):
    resource = event["resource"]
    if resource == "/sharepoint/valise/{ValiseUid}":
        # Traiter l'URL /sharepoint/valise/<ValiseUid>
        valise_uid = event["pathParameters"]["ValiseUid"]
        info_xml = SharepointHandlerPremise.create_sharepoint_valise_if_missing(valise_id=valise_uid)
        info_dict = xmltodict.parse(info_xml)
        response = {"statusCode": 200, "body": json.dumps(info_dict)}

        return response
    elif resource == "/sharepoint/valise/copy":
        if not event["body"]:
            raise BadRequestError(
                "Missing body. Expected fields: SourceSiteUrl, DestinationSiteUrl, SourceServerRelativeUrl, DestinationServerRelativeUrl",
                error_code="MISSING_BODY",
            )

        body = json.loads(event["body"])
        source_site_url = body.get("SourceSiteUrl", None)
        destination_site_url = body.get("DestinationSiteUrl", None)
        source_server_relative_url = body.get("SourceServerRelativeUrl", None)
        destination_server_relative_url = body.get("DestinationServerRelativeUrl", None)

        missing_parts = {
            "SourceSiteUrl": source_site_url,
            "DestinationSiteUrl": destination_site_url,
            "SourceServerRelativeUrl": source_server_relative_url,
            "DestinationServerRelativeUrl": destination_server_relative_url,
        }

        missing_keys = [key for key, value in missing_parts.items() if value is None]

        if missing_keys:
            raise BadRequestError(
                f"Missing parts in body: {', '.join(missing_keys)}",
                error_code="MISSING_BODY_PART",
            )
        info_xml = SharepointHandlerPremise.copy_sharepoint_file_to_sharepoint(
            source_site_url,
            destination_site_url,
            source_server_relative_url,
            destination_server_relative_url,
        )
        info_dict = xmltodict.parse(info_xml)
        response = {"statusCode": 200, "body": json.dumps(info_dict)}
        return response

    else:
        raise BadRequestError("GET method has no access to this url", error_code="URL_NOT_ACCESSIBLE")


def put_lambda(event):
    body = json.loads(event["body"])

    metadata = body.get("metadata", None)
    metadata_list = body.get("metadata_list", None)
    emplacement = body.get("emplacement", None)
    mimetype_from_body = body.get("mimetype", None)
    file_data_url = body.get("file_data", None)
    file_data_base64 = body.get("file_data_base64", None)

    if metadata_list and isinstance(metadata_list, list):
        metadata = build_metadata_list(metadata, body["metadata_list"])

    if file_data_url:
        response = requests.get(file_data_url)
        file_data = response.content
    elif file_data_base64:
        file_data = base64.b64decode(file_data_base64)
    else:
        raise BadRequestError("File data can't be empty", error_code="FILE_DATA_EMPTY")
    if not mimetype_from_body:
        raise BadRequestError("mimetype  can't be empty", error_code="MIMETYPE_EMPTY")

    valise_id = event["pathParameters"]["ValiseUid"]
    file_name = event["pathParameters"]["DocumentId"]
    extension = file_name.split(".")[-1].split("?")[0]

    if valise_id == "BP":
        SharepointHandlerOnline.upload_file(file_name, file_data, mimetype_from_body, metadata)
    else:
        SharepointHandlerPremise.send_valise_file_to_sharepoint(
            file_name=file_name,
            extension=extension,
            file_data=file_data,
            valise_id=valise_id,
            metadata=metadata,
            emplacement=emplacement,
        )
    response = {"statusCode": 204}

    return response


def get_lambda(event):
    resource = event["resource"]
    if resource == "/sharepoint/valise/{ValiseUid}":
        valise_uid = event["pathParameters"]["ValiseUid"]
        info_xml = SharepointHandlerPremise.get_sharepoint_valise_info(valise_id=valise_uid)
        info_dict = xmltodict.parse(info_xml)
        response = {"statusCode": 200, "body": json.dumps(info_dict)}

        return response
    elif resource == "/sharepoint/valise/{ValiseUid}/all":
        valise_uid = event["pathParameters"]["ValiseUid"]
        file_info = SharepointHandlerPremise.list_valise_file_from_sharepoint(valise_uid)
        response = {"statusCode": 200, "body": json.dumps(file_info)}

        return response

    elif resource == "/sharepoint/valise/{ValiseUid}/{DocumentId}":
        valise_uid = event["pathParameters"]["ValiseUid"]
        document_id = event["pathParameters"]["DocumentId"]

        document_data = SharepointHandlerPremise.download_valise_file_from_sharepoint(document_id, valise_uid)

        bucket_name = f"{os.environ['BUCKET']}-temp-storage"
        end_uuid = uuid4()
        object_name = f"sharepoint/document/temp_data_{end_uuid}"

        upload_s3_file(bucket_name, object_name, document_data)
        presigned_url = create_s3_presigned_url(bucket_name, object_name)

        response = {
            "statusCode": 303,
            "headers": {"Location": presigned_url, "Cache-Control": "no-cache"},
        }

        return response
    elif resource == "/sharepoint/{valise}/metadata":
        valise = event["pathParameters"]["valise"]

        if not valise or valise == ":valise":
            raise BadRequestError("Missing valise", error_code="MISSING_VALISE")
        elif valise == "BP":
            response_from_listing_mms = SharepointHandlerOnline.get_api_listing_mms()
        else:
            raise NotImplementedError(f"Type : {valise} not supported", error_code="UNSUPPORTED_TYPE")

        if response_from_listing_mms.status_code != 200:
            capture_message_in_sentry(response_from_listing_mms.text, {"event": event})
            raise InternalServerError(
                "An unknown error occurred on with the sharepoint api. Our development team has been notified.",
                error_code="UNKNOWN_ERROR_WITH_SHAREPOINT_API",
            )

        response = {"statusCode": 200, "body": json.dumps(response_from_listing_mms.json()["results"])}

        return response
    else:
        raise BadRequestError("GET method has no access to this url", error_code="URL_NOT_ACCESSIBLE")


def build_metadata_list(metadata: dict | None, metadata_list: list) -> dict:
    """
    Process metadata from the request data and transform it into a specific format.

    This function processes the `metadata_list` element in the request data,
    reformatting it into a key-value structure within the `metadata` dictionary.
    The `metadata_list` can contain entries where values are directly provided
    or in nested structures. These nested values are extracted and converted
    into a flattened dictionary structure for easy access.

    Parameters
    ----------
    metadata : dict
        The original HTTP request object containing the data to be processed.
    metadata_list : list
        Additional parameters passed to the function (currently unused).

    Returns
    -------
    dict
        The updated or created metadata dictionary with the processed values.

    """
    if metadata is None:
        metadata = {}

    for item in metadata_list:
        key = item["key"]
        if "val" in item:
            # directly key: val format
            metadata[key] = item["val"]
        elif "val_list" in item:
            val_list = item["val_list"]
            # val_list could be single dict or list of dicts
            if isinstance(val_list, dict):
                metadata[key] = {val_list["key"]: val_list["val"]}
            elif isinstance(val_list, list):
                nested_dict = {}
                for sub_item in val_list:
                    nested_dict[sub_item["key"]] = sub_item["val"]
                metadata[key] = nested_dict

    return metadata
