import unittest

from sharepointHandler import build_metadata_list
from utils.mocking import mock_api


@mock_api
class TestMetadataList(unittest.TestCase):
    def test_build(self):
        metadata = build_metadata_list(
            None,
            [
                {
                    "key": "String4",
                    "val": "String5",
                },
                {
                    "key": "String6",
                    "val_list": {
                        "key": "String7",
                        "val": "String8",
                    },
                },
                {
                    "key": "String10",
                    "val_list": [
                        {
                            "key": "String11",
                            "val": "String12",
                        },
                        {
                            "key": "String13",
                            "val": "String14",
                        },
                    ],
                },
            ],
        )

        self.assertDictEqual(
            metadata,
            {
                "String4": "String5",
                "String6": {
                    "String7": "String8",
                },
                "String10": {
                    "String11": "String12",
                    "String13": "String14",
                },
            },
        )

    def test_build_with_merge_metadata(self):
        metadata = build_metadata_list(
            {
                "Merge4": "Merge5",
                "Merge6": {
                    "Merge7": "Merge8",
                },
                "Merge10": {
                    "Merge11": "Merge12",
                    "Merge13": "Merge14",
                },
            },
            [
                {
                    "key": "String4",
                    "val": "String5",
                },
                {
                    "key": "String6",
                    "val_list": {
                        "key": "String7",
                        "val": "String8",
                    },
                },
                {
                    "key": "String10",
                    "val_list": [
                        {
                            "key": "String11",
                            "val": "String12",
                        },
                        {
                            "key": "String13",
                            "val": "String14",
                        },
                    ],
                },
            ],
        )

        self.assertDictEqual(
            metadata,
            {
                "Merge4": "Merge5",
                "Merge6": {
                    "Merge7": "Merge8",
                },
                "Merge10": {
                    "Merge11": "Merge12",
                    "Merge13": "Merge14",
                },
                "String4": "String5",
                "String6": {
                    "String7": "String8",
                },
                "String10": {
                    "String11": "String12",
                    "String13": "String14",
                },
            },
        )


if __name__ == "__main__":
    unittest.main()
