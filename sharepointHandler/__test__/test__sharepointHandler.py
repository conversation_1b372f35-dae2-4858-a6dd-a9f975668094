import unittest
from unittest.mock import patch, Mock

from requests import Response

from utils.api import api_caller
from utils.errors import HttpError
from utils.mocking import mock_api


@mock_api
@patch("sharepointOnline._get_oauth_client_credentials_token", new=Mock(return_value="mocked"))
class TestLambdaFunction(unittest.TestCase):
    valise_test = "Dossier Client-2108064-QTA-300"

    @patch("requests.request")
    def test_1_SearchValise_sharepoint(self, mock_request):
        mock_response1 = Mock()
        mock_response1.text = ""
        mock_response2 = Mock()
        mock_response2.text = f"""
        <s:Envelope>
            <s:Body>
                <SearchResponse>
                    <SearchResult>
                        <a:Valises>
                            <a:Datas>
                                <a:MetaDatas></a:MetaDatas>
                                <a:MetaDatas>
                                    <a:TheValue>{self.valise_test}</a:TheValue>
                                </a:MetaDatas>
                            </a:Datas>
                        </a:Valises>
                    </SearchResult>
                </SearchResponse>
            </s:Body>
        </s:Envelope>
        """
        mock_request.side_effect = [mock_response1, mock_response2]

        response_dict = api_caller("GET", f"/sharepoint/valise/{self.valise_test}")
        value = response_dict["s:Envelope"]["s:Body"]["SearchResponse"]["SearchResult"]["a:Valises"]["a:Datas"]["a:MetaDatas"][1]["a:TheValue"]

        self.assertEqual(value, self.valise_test)

    @patch("requests.request")
    def test_2_failed_SearchValise_sharepoint(self, mock_request):
        mock_response = Mock()
        mock_response.text = f"<a:Comment>Error : impossible de trouver la valise : Fake{self.valise_test}</a:Comment>"

        mock_request.return_value = mock_response

        with self.assertRaises(HttpError) as context:
            api_caller("GET", f"/sharepoint/valise/Fake{self.valise_test}")
        self.assertIn("Error 400 : Valise doesn't exist\n  ErrorCode : NO_SUCH_NAME_FOR_VALISEID", str(context.exception))

    @patch("requests.request")
    def test_3_ListDocument_sharepoint(self, mock_request):
        mock_response1 = Mock()
        mock_response1.text = ""
        mock_response2 = Mock()
        mock_response2.text = """
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <GetListDocumentsResponse xmlns="http://tempuri.org/">
            <GetListDocumentsResult>
                <a:ListDocumentsData xmlns:a="http://schemas.datacontract.org/2004/07/ListDocuments">
                    <a:Document>Document1</a:Document>
                </a:ListDocumentsData>
                <a:ListDocumentsData xmlns:a="http://schemas.datacontract.org/2004/07/ListDocuments">
                    <a:Document>Document2</a:Document>
                </a:ListDocumentsData>
            </GetListDocumentsResult>
        </GetListDocumentsResponse>
    </s:Body>
</s:Envelope>
"""
        mock_request.side_effect = [mock_response1, mock_response2]

        response_list_document = api_caller("GET", f"/sharepoint/valise/{self.valise_test}/all")

        self.assertIsInstance(response_list_document, list)

    @patch("requests.request")
    def test_4_failed_ListDocument_sharepoint(self, mock_request):
        mock_response = Mock()
        mock_response.text = f"<a:Comment>Error : impossible de trouver la valise : Fake{self.valise_test}</a:Comment>"

        mock_request.return_value = mock_response

        with self.assertRaises(HttpError) as context:
            api_caller("GET", f"/sharepoint/valise/Fake{self.valise_test}/all")
        self.assertIn("Error 400 : Valise doesn't exist\n  ErrorCode : NO_SUCH_NAME_FOR_VALISEID", str(context.exception))

    @patch("requests.request")
    def test_5_FindDocument_sharepoint(self, mock_request):
        mock_response0 = Mock()
        mock_response0.text = ""

        mock_response1 = Mock()
        mock_response1.text = """
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <GetListDocumentsResponse xmlns="http://tempuri.org/">
            <GetListDocumentsResult>
                <a:ListDocumentsData xmlns:a="http://schemas.datacontract.org/2004/07/ListDocuments">
                    <a:Document>Document1</a:Document>
                </a:ListDocumentsData>
                <a:ListDocumentsData xmlns:a="http://schemas.datacontract.org/2004/07/ListDocuments">
                    <a:Document>Document2</a:Document>
                </a:ListDocumentsData>
            </GetListDocumentsResult>
        </GetListDocumentsResponse>
    </s:Body>
</s:Envelope>
"""

        mock_response2 = Mock()
        mock_response2.text = """
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <SearchResponse xmlns="http://tempuri.org/">
            <SearchResult>
                <a:Valises xmlns:a="http://schemas.datacontract.org/2004/07/SearchResultData">
                    <a:Url>http://example.com/document</a:Url>
                </a:Valises>
            </SearchResult>
        </SearchResponse>
    </s:Body>
</s:Envelope>
        """

        mock_response3 = Mock()
        mock_response3.text = """
<root>
    <level1>
        <level2>
            <level3>
                <element>NOPE</element>
                <element>Desired Text Value</element>
            </level3>
        </level2>
    </level1>
</root>
        """

        mock_response4 = Mock()
        mock_response4.history = [Response()]
        mock_response4.history[0].status_code = 303

        mock_request.side_effect = [mock_response0, mock_response1, mock_response0, mock_response0, mock_response2, mock_response3, mock_response4]

        response_list_document = api_caller("GET", f"/sharepoint/valise/{self.valise_test}/all")

        response_find_document: Response = api_caller(
            "GET",
            f"/sharepoint/valise/{self.valise_test}/{response_list_document[0]}",
            raw=True,
        )
        self.assertEqual(response_find_document.status_code, 303)

    @patch("requests.request")
    def test_6_CreateValise_sharepoint(self, mock_request):
        mock_response1 = Mock()
        mock_response1.text = ""
        mock_response2 = Mock()
        mock_response2.json.return_value = {"response": {"message": "Valise already exist"}}
        mock_request.side_effect = [mock_response1, mock_response2]

        response_create_valise = api_caller("POST", f"/sharepoint/valise/{self.valise_test}")

        self.assertEqual(response_create_valise["response"]["message"], "Valise already exist")

    @patch("requests.request")
    def test_7_ListDocument_sharepoint(self, mock_request):
        mock_response1 = Mock()
        mock_response1.text = ""
        mock_response2 = Mock()
        mock_response2.text = """
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <GetListDocumentsResponse xmlns="http://tempuri.org/">
            <GetListDocumentsResult>
                <a:ListDocumentsData xmlns:a="http://schemas.datacontract.org/2004/07/ListDocuments">
                    <a:Document>Document1</a:Document>
                </a:ListDocumentsData>
                <a:ListDocumentsData xmlns:a="http://schemas.datacontract.org/2004/07/ListDocuments">
                    <a:Document>Document2</a:Document>
                </a:ListDocumentsData>
            </GetListDocumentsResult>
        </GetListDocumentsResponse>
    </s:Body>
</s:Envelope>
"""
        mock_request.side_effect = [mock_response1, mock_response2]

        responses = api_caller("GET", f"/sharepoint/valise/{self.valise_test}/all")

        self.assertIsInstance(responses, list)

    @patch("requests.request")
    def test_get_valise_bp_metadata(self, mock_request):
        mock_response = Mock()
        mock_response.json.return_value = []

        mock_request.return_value = mock_response

        response = api_caller("GET", "/sharepoint/BP/metadata")
        self.assertIsInstance(response, list)

    @patch("requests.request")
    def test_get_valise_no_valise_metadata(self, mock_request):
        mock_response = Mock()
        mock_response.json.side_effect = Exception('{"Error": 400, "Message": "Missing valise", "ErrorCode": "MISSING_VALISE"}')

        mock_request.return_value = mock_response

        with self.assertRaises(HttpError) as context:
            api_caller("GET", "/sharepoint/:valise/metadata")
        self.assertEqual("Error 400 : Missing valise\n  ErrorCode : MISSING_VALISE", str(context.exception))

    @patch("requests.request", new=Mock())
    def test_get_valise_wrong_valise_metadata(self):
        with self.assertRaises(HttpError) as context:
            api_caller("GET", "/sharepoint/client/metadata")
        self.assertEqual("Error 501 : Type : client not supported\n  ErrorCode : UNSUPPORTED_TYPE", str(context.exception))
