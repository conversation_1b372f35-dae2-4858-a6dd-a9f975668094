import json
from datetime import datetime, timezone
from os import environ
from uuid import uuid4

import requests

from utils.aws_utils import get_secret
from utils.log_utils import LogTime, log_info_json


def _get_oauth_client_credentials_token(url, payload):
    response = requests.post(
        url=url,
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        data=payload,
    )
    response_json = response.json()

    token = f"{response_json['token_type']} {response_json['access_token']}"

    return token


def oauth_client_credentials(secret_name):
    with LogTime("get OAuth Client Credentials"):
        credentials = get_secret(secret_name)

        payload = f"grant_type=client_credentials&client_id={credentials['ClientID']}&client_secret={credentials['ClientSecret']}&scope={credentials['Scope']}"

        return _get_oauth_client_credentials_token(credentials["AccessTokenURL"], payload)


class SharepointHandlerOnline(object):
    secret_name = "MyResaAPI/SHP_ONLINE_OAUTH"  # Utilisé pour tous les appels

    @classmethod
    def get_token(cls):
        # Cette méthode simule l'obtention d'un token OAuth
        return oauth_client_credentials(cls.secret_name)

    @classmethod
    def upload_file(cls, file_name, file_data, mime_types_data, meta_data):
        shp_api = environ.get("SHP_ONLINE_API", "shponlinedoc-dev.azurewebsites.net")
        token = cls.get_token()
        headers = {
            "Authorization": token,
        }

        inject_payload = {
            "header": {
                "dataActionDescription": "20200420-create-document",
                "action": "import",
                "userId": "20",
                "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f"),
            },
            "ingestDescription": {
                "actionId": str(uuid4()),
                "callbackAPI": shp_api,
                "documents": [{"filename": file_name, "metadata": meta_data}],
            },
        }

        files = [
            (
                "files",
                ("InjectPayload.json", json.dumps(inject_payload), "application/json"),
            ),
            ("files", (file_name, file_data, mime_types_data)),
        ]

        api_url = f"https://{shp_api}/api/inject"
        response = requests.post(api_url, headers=headers, files=files)

        response_body = response.content
        try:
            response_body = json.loads(response.content)
        except:
            pass

        log_info_json(
            {
                "shp_online_request": {
                    "method": response.request.method,
                    "url": response.request.url,
                    "headers": response.request.headers,
                    "payload_metadata": inject_payload,
                },
                "shp_online_response": {
                    "statuts": response.status_code,
                    "headers": response.headers,
                    "body": response_body,
                },
            }
        )

        response.raise_for_status()
        return response

    @classmethod
    def get_api_listing_mms(cls):
        shp_api = environ.get("SHP_ONLINE_API", "shponlinedoc-dev.azurewebsites.net")
        token = cls.get_token()
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
            "accept": "application/json",
        }
        listing_mms_url = f"https://{shp_api}/api/listingMms"
        all_fields = [
            {"fieldName": "rsaDocumentType", "operator": "All", "value": "*"},
            {"fieldName": "rsaDocumentContext", "operator": "All", "value": "*"},
            {"fieldName": "rsaDocumentSource", "operator": "All", "value": "*"},
            {"fieldName": "rsaDocumentOwner", "operator": "All", "value": "*"},
            {"fieldName": "rsaDocumentFlux", "operator": "All", "value": "*"},
        ]
        data = {
            "header": {
                "dataActionDescription": "get-all-types",
                "action": "search",
                "userId": "1",
                "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f"),
            },
            "searchDescription": {
                "actionId": str(uuid4()),
                "pageSize": 0,
                "criterias": all_fields,
                "orderby": [{"fieldName": "label", "direction": "ascending"}],
            },
        }

        response = requests.post(listing_mms_url, headers=headers, json=data)
        return response
