import base64
import json
import urllib.parse
import xml.etree.ElementTree as ET
from datetime import datetime
from os import environ

import boto3
import requests
import xmltodict

from utils.errors import BadRequestError
from utils.log_utils import log_info_json
from utils.type_utils import to_list

SHAREPOINT_SECRET = environ.get("SHAREPOINT_SECRET")
IS_PROD = environ.get("IS_PROD") == "true"


class SharepointHandlerPremise(object):
    _secret: dict = None

    @classmethod
    def secret(cls) -> dict:
        if not cls._secret:
            cls._secret = cls.get_secret()
        return cls._secret

    @classmethod
    def get_sharepoint_valise_info(cls, valise_id):
        url = cls.get_url_from_secret("SearchValise/SearchValise.SearchValise.svc")
        valise = cls.get_valise(urllib.parse.unquote(valise_id))
        if not cls.check_if_sharepoint_valise_exist(valise_id):
            raise BadRequestError("Valise doesn't exist", error_code="NO_SUCH_NAME_FOR_VALISEID")
        payload = f"""
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:Search>
         <tem:VALISE_UID>{valise}</tem:VALISE_UID>
      </tem:Search>
   </soapenv:Body>
</soapenv:Envelope>
        """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/ISearchValise/Search",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})
        response = requests.request("POST", url, headers=headers, data=payload, timeout=(5, 20))
        response_text = response.text

        return response_text

    @classmethod
    def create_sharepoint_valise_if_missing(cls, valise_id):
        if cls.check_if_sharepoint_valise_exist(valise_id):
            response = '<?xml version="1.0" encoding="UTF-8"?> <response> <message>Valise already exist</message> </response>'
            return response

        valise = cls.get_valise(urllib.parse.unquote(valise_id))
        url = cls.get_url_from_secret("CreateValise/CreateValise.CreateValise.svc")
        valise_type, valise_id, valise_env, valise_env_version = valise.split(" ")[1].split("-")

        payload = f"""
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:cre="http://schemas.datacontract.org/2004/07/CreateValise">
       <soapenv:Body>
          <tem:CreateValises>
             <tem:VALISE_UID>{valise}</tem:VALISE_UID>
             <tem:THESYSTEM>{valise_env}</tem:THESYSTEM>
             <tem:MANDT>{valise_env_version}</tem:MANDT>
             <tem:OBJ_TYPE>Inconnu</tem:OBJ_TYPE>
             <tem:ID>{valise_id}</tem:ID>
             <tem:THEMETADATA>
                <cre:TheMetadataClass>
                   <cre:THEID>VALISE_TYPE</cre:THEID>
                   <cre:THEVALUE>Valise_{valise_type}</cre:THEVALUE>
                </cre:TheMetadataClass>
                <cre:TheMetadataClass>
                   <cre:THEID>Valise_Date</cre:THEID>
                   <cre:THEVALUE>{datetime.now().strftime("%Y%m%d")}</cre:THEVALUE>
                </cre:TheMetadataClass>
             </tem:THEMETADATA>
          </tem:CreateValises>
       </soapenv:Body>
    </soapenv:Envelope>
            """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/ICreateValise/CreateValises",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})
        response = requests.request("POST", url, headers=headers, data=payload, timeout=(5, 20))
        return response.text

    @classmethod
    def download_valise_file_from_sharepoint(cls, document_id, valise_id):
        url = cls.get_url_from_secret("FindDocument/FindDocument.FindDocument.svc")
        if not cls.check_if_sharepoint_valise_exist(valise_id):
            raise BadRequestError("Valise doesn't exist", error_code="NO_SUCH_NAME_FOR_VALISEID")
        valise = cls.get_valise(urllib.parse.unquote(valise_id))

        info_xml = cls.get_sharepoint_valise_info(valise_id=valise_id)
        info_dict = xmltodict.parse(info_xml)
        start_url = info_dict["s:Envelope"]["s:Body"]["SearchResponse"]["SearchResult"]["a:Valises"]["a:Url"]

        document_url = f"{start_url}/{valise}/{urllib.parse.unquote(document_id)}"
        payload = f"""
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
       <soapenv:Header/>
       <soapenv:Body>
          <tem:FindDocuments>
             <tem:URL>{document_url}</tem:URL>
             <tem:FichierInclus>true</tem:FichierInclus>
          </tem:FindDocuments>
       </soapenv:Body>
    </soapenv:Envelope>
            """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/IFindDocument/FindDocuments",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})

        response = requests.request("POST", url, headers=headers, data=payload, timeout=(5, 20))
        xml_root = ET.fromstring(response.text)

        try:
            data = base64.b64decode(xml_root[0][0][0][1].text.encode("utf8"))
        except:
            raise BadRequestError(
                "Unable to get sharepoint file data",
                error_code="DATA_NOT_REACHABLE_IN_SHAREPOINT",
            )

        return data

    @classmethod
    def list_valise_file_from_sharepoint(cls, valise_id, type_filter=None):
        valise = cls.get_valise(urllib.parse.unquote(valise_id))
        url = cls.get_url_from_secret("ListDocuments/ListDocuments.svc")
        if not cls.check_if_sharepoint_valise_exist(valise_id):
            raise BadRequestError("Valise doesn't exist", error_code="NO_SUCH_NAME_FOR_VALISEID")

        payload = f"""
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:lis="http://schemas.datacontract.org/2004/07/ListDocuments">
                <soapenv:Header/>
                <soapenv:Body>
                  <tem:GetListDocuments>
                     <tem:data>
                        <lis:Valise>{valise}</lis:Valise>
                     </tem:data>
                  </tem:GetListDocuments>
                </soapenv:Body>
            </soapenv:Envelope>
            """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/IListDocuments/GetListDocuments",
        }

        log_info_json({"payload": payload, "headers": headers})

        response = requests.request("POST", url, headers=headers, data=payload, timeout=(5, 20))
        try:
            data = xmltodict.parse(response.text)
        except:
            raise BadRequestError(
                "Unable to get sharepoint file data",
                error_code="DATA_NOT_REACHABLE_IN_SHAREPOINT",
            )
        files_data = data.get("s:Envelope", {}).get("s:Body", {}).get("GetListDocumentsResponse", {}).get("GetListDocumentsResult", {}).get("a:ListDocumentsData", [])
        files_data = to_list(files_data)

        files = []
        for file in files_data:
            if type_filter:
                for meta in file.get("a:MetaDataList", {}).get("a:ListDocumentsData.MetaDatas"):
                    if meta.get("a:TheId", "") == "Type_Correspondance_Out" and (meta.get("a:TheValue") or "").upper() == type_filter.upper():
                        files.append(file.get("a:Document", ""))
            else:
                files.append(file.get("a:Document", ""))
        return files

    @classmethod
    def get_value(cls, full_document, file_extension):
        """
        Get the name of the document, without the extension
        """
        if file_extension in full_document:
            return ".".join(full_document.split(".")[:-1])
        else:
            return full_document

    @classmethod
    def get_valise(cls, valise: str) -> str:
        if not valise.endswith(f"-{cls.secret()['env']}"):
            valise = f"{valise}-{cls.secret()['env']}"
        return valise

    # Used in other function, care when modifying
    @classmethod
    def get_url_from_secret(cls, path):
        return f"http://{cls.secret()['host']}:{cls.secret()['port']}/{path}"

    @classmethod
    def get_secret(cls):
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager", region_name="eu-west-1")

        try:
            get_secret_value_response = client.get_secret_value(SecretId=SHAREPOINT_SECRET)
            return json.loads(get_secret_value_response["SecretString"])
        except Exception as e:
            raise e

    @classmethod
    def check_if_sharepoint_valise_exist(cls, valise_id):
        valise = cls.get_valise(valise_id)
        url = cls.get_url_from_secret("SearchValise/SearchValise.SearchValise.svc")

        payload = f"""
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
       <soapenv:Header/>
       <soapenv:Body>
          <tem:Search>
             <tem:VALISE_UID>{valise}</tem:VALISE_UID>
          </tem:Search>
       </soapenv:Body>
    </soapenv:Envelope>
            """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/ISearchValise/Search",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})

        response = requests.request("POST", url, headers=headers, data=payload, timeout=(5, 20))
        response_text = response.text

        return f"<a:Comment>Error : impossible de trouver la valise : {valise}</a:Comment>" not in response_text

    @classmethod
    def copy_sharepoint_file_to_sharepoint(
        cls,
        sources_site_url,
        destination_site_url,
        source_server_relative_url,
        destination_server_relative_url,
    ):
        url = cls.get_url_from_secret("ManageDocument/ManageDocument.CopyDocument.svc")
        payload = f"""
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:man="http://schemas.datacontract.org/2004/07/ManageDocument">
                   <soapenv:Header/>
                   <soapenv:Body>
                      <tem:CopyDocuments>
                         <!--Optional:-->
                         <tem:data>
                              <!--Optional:-->
                            <man:SourceSiteColUrl>{sources_site_url}</man:SourceSiteColUrl>
                            <!--Optional:-->
                            <man:DestinationSiteColUrl>{destination_site_url}</man:DestinationSiteColUrl>
                            <!--Optional:-->
                            <man:E1ServerRelativeUrl>{source_server_relative_url}</man:E1ServerRelativeUrl>
                            <!--Optional:-->
                            <man:E2ServerRelativeUrl>{destination_server_relative_url}</man:E2ServerRelativeUrl>
                         </tem:data>
                      </tem:CopyDocuments>
                   </soapenv:Body>
                </soapenv:Envelope>
                """.strip().encode("utf-8")

        headers = {
            "Content-Type": "*",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/IManageDocument/CopyDocuments",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})

        response = requests.request("POST", url, headers=headers, data=payload, timeout=(5, 20))
        response_text = response.text

        return response_text

    @classmethod
    def send_valise_file_to_sharepoint(
        cls,
        file_name,
        extension,
        file_data,
        valise_id,
        emplacement,
        metadata: dict = None,
    ):
        if metadata is None:
            metadata = {}
        if extension is None:
            extension = file_name.split(".")[-1]
        if file_data is None and emplacement is None:
            raise BadRequestError(
                "file data and emplacement can't be both empty",
                error_code="FILEDATA_AND_EMPLACEMENT_EMPTY",
            )

        value = cls.get_value(file_name, extension)
        valise = cls.get_valise(valise_id)
        url = cls.get_url_from_secret("ManageDocument/ManageDocument.ManageDocument.svc")

        if "Title" not in metadata:
            metadata["Title"] = value

        emplacement_content = f"<a:Emplacement><![CDATA[{emplacement}]]></a:Emplacement>" if emplacement else ""
        file_data_content = f"<a:FileData><![CDATA[{file_data}]]></a:FileData>" if file_data else ""

        payload = f"""
        <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
            <s:Body>
                <PostDocument xmlns="http://tempuri.org/">
                    <data xmlns:a="http://schemas.datacontract.org/2004/07/ManageDocument" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                        <a:Document>{value}</a:Document>
                        {emplacement_content}
                        <a:Extension>{extension}</a:Extension>
                        {file_data_content}
                        <a:MetaDataList>
                            {"".join([f"<a:PostDocumentData.MetaDatas><a:TheId>{id}</a:TheId><a:TheValue>{val}</a:TheValue></a:PostDocumentData.MetaDatas>" for id, val in metadata.items()])}
                        </a:MetaDataList>
                        <a:Valise>{valise}</a:Valise>
                    </data>
                </PostDocument>
            </s:Body>
        </s:Envelope>
        """.strip().encode("utf-8")

        headers = {
            "Content-Type": "*",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/IManageDocument/PostDocument",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})

        response = requests.request("POST", url, headers=headers, data=payload, timeout=(5, 20))
        response_text = response.text

        return response_text
