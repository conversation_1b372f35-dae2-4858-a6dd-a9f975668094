import base64
import json
import os
from datetime import datetime
from decimal import Decimal
from urllib.parse import unquote_plus

import boto3

from payload import SharepointHandler
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_dynamodb_table

##________________________________________________________________________||
from utils.dict_utils import first
from utils.log_utils import log_err

REGION_NAME = os.environ["API_REGION"]
SHAREPOINT_SECRET = os.environ["SHAREPOINT_SECRET"]
IS_PROD = os.environ["IS_PROD"] == "true"

MAX_WRITE_ATTEMPTS = 3
s3 = boto3.client("s3", region_name=REGION_NAME)


##________________________________________________________________________||
@aws_lambda_handler
def handler(event, context):
    for record in event["Records"]:
        try:
            record_body = first(json.loads(record["body"])["Records"])
            bucket = record_body["s3"]["bucket"]["name"]
            key = unquote_plus(record_body["s3"]["object"]["key"])
            file_extension = get_file_extension(key)

            resource = f"s3://{bucket}/{key}"
            table = get_dynamodb_table(os.environ["PowalcoFiles"])
            item = table.get_item(Key={"resource": resource})["Item"]
            reseau_ami_split = item["reseau_ami"].split("-")
            dossierId = reseau_ami_split[0] + reseau_ami_split[1]

            if file_extension != "pdf":
                raise Exception("file is not PDF")
            else:
                print("File already PDF")
                # If already PDF document, send to S3 bucket

            document = key.split("/")[-1]

            # Extract PDFBase64 from S3 bucket
            file_data = get_file_data(bucket, key)

            # File extension is PDF
            sharepoint_response = upload_document(dossierId, document, "pdf", file_data)
            table.update_item(
                Key={"resource": resource},
                UpdateExpression="SET sharepointUpload = :sharepointUpload",
                ExpressionAttributeValues={
                    ":sharepointUpload": {
                        "success": True,
                        "timestamp": Decimal(int(datetime.now().timestamp() * 1000)),
                    }
                },
            )
        except Exception as e:
            log_err(e)
            table.update_item(
                Key={"resource": resource},
                UpdateExpression="SET sharepointUpload = :sharepointUpload",
                ExpressionAttributeValues={
                    ":sharepointUpload": {
                        "success": False,
                        "timestamp": Decimal(int(datetime.now().timestamp() * 1000)),
                        "error": str(e),
                    }
                },
            )
            raise e
        print("sharepoint_response")
        print(sharepoint_response)

        if not sharepoint_response:
            status_code = 500
            body = json.dumps({"file_path": document})
        else:
            status_code = 200
            body = json.dumps(
                {
                    "file_path": document
                    ### TODO return share_point response by parsing xml with https://pypi.org/project/xmltodict/
                    # 'sharepoint_response': sharepoint_response,
                }
            )

        response = {
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": "application/json",
            },
            "isBase64Encoded": False,
            "statusCode": status_code,
            "body": body,
        }

        return response


##________________________________________________________________________||
def get_file_extension(document_name):
    file_extension = document_name.split(".")[-1]
    return file_extension


##________________________________________________________________________||
def get_file_data(bucket, key):
    """
    Get PDFBase64 from S3 bucket
    """

    try:
        obj = s3.get_object(Bucket=bucket, Key=key)
        pdf_base = obj["Body"].read()
        file_data = base64.b64encode(pdf_base).decode("utf-8")

        return file_data
    except Exception as e:
        raise IOError(e)


##________________________________________________________________________||
def upload_document(dossierId, document, file_extension, file_data):
    """
    Upload PDF document to Sharepoint and return response
    """

    dry_run = False  # not IS_PROD

    sharepoint_handler = SharepointHandler(dossierId, document)

    response = sharepoint_handler.send_to_sharepoint(file_extension, file_data, SHAREPOINT_SECRET, dry_run)

    return response


##________________________________________________________________________||
