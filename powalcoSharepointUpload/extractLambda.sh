#!/bin/bash
ENV=$1
LAMBDA_FULL_NAME=$2
docker build -f powalcoSharepointUpload/Dockerfile --tag powalco-sharepoint-upload .
docker run powalco-sharepoint-upload /bin/bash
docker cp `docker ps -a | sed -n 2p | awk '{print $1}'`:/resa/lambda.zip .
mv lambda.zip powalcoSharepointUpload.zip
aws s3 cp powalcoSharepointUpload.zip  s3://my-resa-api-$ENV/lambdas/powalcoSharepointUpload.zip --region eu-west-1  
aws lambda update-function-code --function-name $LAMBDA_FULL_NAME  --s3-bucket my-resa-api-$ENV --s3-key lambdas/powalcoSharepointUpload.zip --region eu-west-1
rm powalcoSharepointUpload.zip