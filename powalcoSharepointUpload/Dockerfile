FROM amazonlinux:latest

RUN yum update -y

# Install apt dependencies
RUN yum install -y gcc gcc-c++ freetype-devel yum-utils findutils openssl-devel

RUN yum -y groupinstall development

# Mock current AWS Lambda docker image
# Find complete list of package https://gist.github.com/vincentsarago/acb33eb9f0502fcd38e0feadfe098eb7
RUN  yum install -y libjpeg-devel libpng-devel libcurl-devel ImageMagick-devel.x86_64

RUN curl https://www.python.org/ftp/python/3.6.1/Python-3.6.1.tar.xz | tar -xJ \
    && cd Python-3.6.1 \
    && ./configure --prefix=/usr/local --enable-shared \
    && make \
    && make install \
    && cd .. \
    && rm -rf Python-3.6.1

ENV LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH

WORKDIR /resa
ENV WORKDIR /resa

COPY powalcoSharepointUpload/requirements.txt /resa/

RUN mkdir -p packages
RUN pip3.6 install -r requirements.txt -t packages --no-cache-dir

WORKDIR /resa

COPY powalcoSharepointUpload/powalcoSharepointUpload.py "$WORKDIR/packages/powalcoSharepointUpload.py"
COPY powalcoSharepointUpload/payload.py "$WORKDIR/packages/payload.py"
COPY utils "$WORKDIR/packages/utils"

RUN cd packages && \
    zip -r $WORKDIR/lambda.zip *

CMD ["/bin/bash"]
