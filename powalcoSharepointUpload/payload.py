import datetime as dt
import json
import os

import boto3
import requests

SHAREPOINT_SYSTEM = os.environ["SHAREPOINT_SYSTEM"]


##________________________________________________________________________||
class SharepointHandler(object):
    def __init__(self, dossierId, document):
        self.dossierId = dossierId
        self.document = document
        self.date = dt.date.today().strftime("%Y-%m-%d")

        # TODO: payload SYSTEM : QTE/PTE
        self.system = SHAREPOINT_SYSTEM

        self.payload = """
        <s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">
            <s:Body>
                <PostDocument xmlns=\"http://tempuri.org/\">
                    <data xmlns:a=\"http://schemas.datacontract.org/2004/07/ManageDocument\" xmlns:i=\"http://www.w3.org/2001/XMLSchema-instance\">
                        <a:Document>{document}</a:Document>
                        <a:Extension>{extension}</a:Extension>
                        <a:FileData>{fileData}</a:FileData>
                        <a:MetaDataList>
                            <a:PostDocumentData.MetaDatas>
                                <a:TheId>ContentType</a:TheId>
                                <a:TheValue>Document</a:TheValue>
                            </a:PostDocumentData.MetaDatas>
                            <a:PostDocumentData.MetaDatas>
                                <a:TheId>Date_Reception</a:TheId>
                                <a:TheValue>{date}</a:TheValue>
                            </a:PostDocumentData.MetaDatas>
                            <a:PostDocumentData.MetaDatas>
                                <a:TheId>SYSTEM</a:TheId>
                                <a:TheValue>{system}</a:TheValue>
                            </a:PostDocumentData.MetaDatas>
                            <a:PostDocumentData.MetaDatas>
                                <a:TheId>MANDT</a:TheId>
                                <a:TheValue>300</a:TheValue>
                            </a:PostDocumentData.MetaDatas>
                            <a:PostDocumentData.MetaDatas>
                                <a:TheId>VALISE_OPERATIONNELLE</a:TheId>
                                <a:TheValue>0</a:TheValue>
                            </a:PostDocumentData.MetaDatas>
                            <a:PostDocumentData.MetaDatas>
                                <a:TheId>Title</a:TheId>
                                <a:TheValue>{file_name}</a:TheValue>
                            </a:PostDocumentData.MetaDatas>
                        </a:MetaDataList>
                        <a:Valise>{valise}</a:Valise>
                    </data>
                </PostDocument>
            </s:Body>
        </s:Envelope>
        """

        self.headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/IManageDocument/PostDocument",
        }

    def send_to_sharepoint(self, extension, fileData, secret_name, dry_run):
        file_name = self.get_file_name(self.document, extension)
        valise = self.get_valise(self.dossierId, self.system)
        document = self.get_document()
        url = self.get_url_from_secret(secret_name)
        date = self.date

        payload = self.payload.format(
            document=document,
            extension=extension,
            fileData=fileData,
            date=date,
            file_name=file_name,
            valise=valise,
            system=self.system,
        )

        print(payload.replace("\n", ""))
        if dry_run:
            print("Not sending to Sharepoint")
            response = None
        else:
            response = requests.request("POST", url, headers=self.headers, data=payload)
            resp = str(response.text.encode("utf8"))
            print(resp)
            if response.status_code != 200 or "error" in resp.lower():
                raise Exception(resp)

        return response

    def get_file_name(self, full_document, file_extension):
        """
        Get the name of the document, without the extension
        """
        value = [e for e in full_document.split("-") if file_extension in e]
        if value:
            return value[0].split(".", 1)[0]
        else:
            return full_document

    def get_document(self):
        return self.document

    def get_valise(self, dossierId, system):
        valise = "Projet d'investissement-{0}-{1}-300".format(dossierId, system)
        return valise

    def get_url_from_secret(self, secret_name):
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager", region_name="eu-west-1")

        try:
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        except Exception as e:
            raise e
        else:
            secret = json.loads(get_secret_value_response["SecretString"])

            url = "http://{0}:{1}/{2}".format(secret["host"], secret["port"], secret["path"])

        return url


##________________________________________________________________________||
