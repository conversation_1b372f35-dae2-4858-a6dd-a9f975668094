FROM public.ecr.aws/lambda/python:3.11

# Install needed tools
RUN yum clean all && \
    yum install -y yum-utils rpmdevtools aws-cli git zip jq
RUN curl https://releases.hashicorp.com/terraform/0.12.31/terraform_0.12.31_linux_amd64.zip > terraform.zip && \
    mkdir .bin && \
    unzip terraform.zip -d /bin && \
    chmod a+x /bin/terraform
RUN pip install boto3 PyYaml requests

COPY --from=public.ecr.aws/docker/library/docker /usr/local/bin/docker /usr/local/bin/
COPY ./ /project 
WORKDIR /project

ENTRYPOINT [ "./bin/completeRefresh.sh" ]