#!/bin/bash
# generate a zip file ready to run in a lambda

set -e  # Stop the script if any command fails

if [ -z "$1" ]; then
    echo "Usage : ./build.sh folder"
    exit 1
fi

mkdir -p tmp
mkdir -p tmp/$1
cd tmp/$1
rm -f bundle.zip || true
rm -rf bundle || true
rm -rf venv || true
python -m venv venv
source venv/bin/activate
PYTHONPATH=../../$1
mkdir bundle
cp -r ../../$1/* bundle 2> /dev/null
cp -r ../../utils bundle
pip install -r ../../$1/requirements.txt -t bundle
if [ -f ../../$1/build_extra.sh ]; then
  chmod +x ../../$1/build_extra.sh
  ../../$1/build_extra.sh
fi
( cd bundle ; zip -r9 ../bundle.zip .)
rm -rf bundle
rm -rf venv
