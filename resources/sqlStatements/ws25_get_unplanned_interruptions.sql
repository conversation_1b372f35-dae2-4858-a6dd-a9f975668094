WITH panne AS (
    SELECT
        CITY1 AS localite,
        POST_CODE1 AS cd_postal,
        STREET AS rue,
        HOUSE_NUM1 AS num_rue,
        QMDAT AS date_panne,
        heure_prise_charge,
        heure_fin,
        date_fin,
        cause_code,
        GPS_LAT AS Lat,
        GPS_LONG AS Long,
        QMEL.QMNUM AS "id",
        CASE WHEN
            (SELECT COUNT(*) FROM {HanaSchema}.AUFK JOIN {HanaSchema}.JEST ON JEST.Objnr = AUFK.Objnr WHERE AUFK.AUFNR = QMEL.AUFNR AND Stat IN ('I0009', 'I0045')) > 0 
            OR (SELECT COUNT(*) FROM {HanaSchema}.JEST WHERE JEST.Objnr = QMEL.Objnr AND Stat = 'I0072') > 0
        THEN TRUE ELSE FALSE END AS closed
    from
        (SELECT
          ADDRNUMBER,
          STREET,
          HOUSE_NUM1,
          CITY1,
          POST_CODE1,
          UPPER(TRIM(STREET)) AS STREET_CLEAN,
          UPPER(TRIM(HOUSE_NUM1)) AS HOUSE_NUM_CLEAN,
          UPPER(TRIM(CITY1)) AS CLEAN_CITY
          FROM {HanaSchema}.ADRC {GeoFilter}) as ADRC
    inner join
        (SELECT
          UPPER(TRIM(RUE_OR)) AS RUE_OR,
          UPPER(TRIM(NUMERO_OR)) AS NUMERO_OR,
          UPPER(TRIM(LOCALITE_OR)) AS LOCALITE_OR,
          MAX(GPS_LAT) AS GPS_LAT,
          MIN(GPS_LONG) AS GPS_LONG
        FROM {GisSchema}.EAN_GPS {GpsFilter}
        GROUP BY RUE_OR, NUMERO_OR, LOCALITE_OR
        ) as EAN_GPS
        on ADRC.STREET_CLEAN = EAN_GPS.RUE_OR and ADRC.HOUSE_NUM_CLEAN = EAN_GPS.NUMERO_OR and CLEAN_CITY = LOCALITE_OR
    inner join
        (SELECT *
        FROM {HanaSchema}.QMEL
        WHERE QMART = 'E1' AND PRIOK = '1' AND ERDAT >= TO_VARCHAR(ADD_DAYS(CURRENT_DATE, -365), 'YYYYMMDD')
        ) as QMEL
        on ADRC.ADDRNUMBER = QMEL.ADRNR
    left join
        (SELECT QMNUM, PSTUR AS heure_prise_charge
        FROM {HanaSchema}.QMMA
        WHERE MNCOD = '02'
        ) as PRISE_CHARGE
        on QMEL.QMNUM = PRISE_CHARGE.QMNUM
    left join
        (SELECT QMNUM, PSTER AS date_fin, PSTUR AS heure_fin
        FROM {HanaSchema}.QMMA
        WHERE MNCOD = '03'
        ) as FIN_TRAITEMENT
        on QMEL.QMNUM = FIN_TRAITEMENT.QMNUM
    left join
        (SELECT QMNUM, OTEIL AS cause_code
        FROM {HanaSchema}.QMFE
        ) as CAUSE
        on QMEL.QMNUM = CAUSE.QMNUM
    ORDER BY localite, cd_postal, rue, num_rue, date_panne, heure_prise_charge, heure_fin, date_fin, cause_code, GPS_LAT, GPS_LONG
)
SELECT COUNT(*) OVER () AS nb_items, *
FROM panne
WHERE (:EnCours IS NULL OR closed != :EnCours)
    AND (CLOSED = TRUE AND DATE_FIN >= ADD_MONTHS(CURRENT_DATE, -1) OR (CLOSED = FALSE AND DATE_PANNE >= ADD_YEARS(CURRENT_DATE, -1)))
LIMIT :PageSize
OFFSET :PageOffset