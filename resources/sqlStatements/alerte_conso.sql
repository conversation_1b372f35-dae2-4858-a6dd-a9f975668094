(SELECT meterid, measuredatetime, measurevalue, readingtypeid, measurestate, 
       ean, readingfrequency, standardizationtimestamp, measureUnit, 'elec' AS type_energie
FROM {red_table_elec}
WHERE ean IN {Ean}
  AND (measureDateTime BETWEEN {StartDate} AND {EndStatement}) 
  AND readingtypeid NOT IN ('2.8.1', '2.8.2')
ORDER BY ean, readingtypeid, measuredatetime)
UNION ALL
(
    SELECT meterid, measuredatetime, measurevalue, readingtypeid, measurestate,
           ean, readingfrequency, standardizationtimestamp, measureUnit, 'gaz' AS type_energie
    FROM {red_table_gaz}
    WHERE ean IN {Ean}
    AND (measureDateTime BETWEEN {StartDate} AND {EndStatement}) 
    AND readingtypeid NOT IN ('2.8.1', '2.8.2')
)
ORDER BY ean, readingtypeid, measuredatetime;
