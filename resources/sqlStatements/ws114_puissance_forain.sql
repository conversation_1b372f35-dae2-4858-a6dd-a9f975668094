SELECT MAKT.MAT<PERSON> AS "Id", MAKT.MAKTX AS "Categorie", PRIX_ARTICLE.KBETR AS "Tarif"
FROM {HanaSchema}.MAKT
JOIN (
	SELECT MATNR, <PERSON><PERSON><PERSON> FROM (
		SELECT * FROM (
			SELECT KNUMH, KBETR, KONWA FROM {HanaSchema}.KONP WHERE KOPOS = '01'
		) AS KONP
		JOIN (
			SELECT * FROM (
				SELECT *
				FROM {HanaSchema}.A004
				WHERE KAPPL = 'V'
					AND KSCHL = 'ZPR1'
					AND VKORG = '1000'
					AND VTWEG = '01'
					AND DATAB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
					AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= DATBI
      		)
  		) AS A004 ON KONP.KNUMH = A004.KNUMH
	)
) AS PRIX_ARTICLE ON PRIX_ARTICLE.MATNR = MAKT.MATNR
WHERE MAKT.MATNR LIKE 'EZ%' AND MAKT.SPRAS = LEFT(:<PERSON><PERSON>, 1)