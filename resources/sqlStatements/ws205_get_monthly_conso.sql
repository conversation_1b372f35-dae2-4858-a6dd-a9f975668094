WITH ean_data AS (
    SELECT meterid, measuredatetime, measurevalue, readingtypeid, measurestate,
           ean, readingfrequency, standardizationtimestamp, measureUnit
    FROM {red_table}
    WHERE meterid = :MeterId AND ean = :Ean
), measure_dates AS (
  {dynamic_clause}
)
SELECT ean_data.* FROM ean_data
JOIN measure_dates ON measure_dates.measure_date = ean_data.measuredatetime
ORDER BY readingtypeid, measuredatetime