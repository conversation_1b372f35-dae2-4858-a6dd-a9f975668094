with get_adress_info as (
  select distinct
    INSTALL_CITY1,
    INSTAL<PERSON>_<PERSON>ITYCODE,
    ADRSTREETT.STRT_CODE as STRT_CODE,
    INSTALL_POSTCODE_1,
    INSTALL_STREET,
    INSTALL_HOUSENUM_1,
    INSTALL_ADDRNUMBER,
    IDRAD_RUE,
    ID<PERSON><PERSON>_LOCALITE,
    HAUS,
    HAUS_NUM2,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    REG<PERSON>GROUP,
    DESCRIPT
  from
  (select
    VSTELLE,
    HAUS,
    HAUS_NUM2,
    FLOOR,
    ROOMNUMBER
    from {HanaSchema}.EVBS
    where VSTELLE = :VSTELLE
    ) as <PERSON><PERSON><PERSON>
  left join
  (select
    <PERSON><PERSON><PERSON>,
    TPLNR
    from {HanaSchema}.ILOA
    ) as ILOA
  on EVBS.HAUS = ILOA.TPL<PERSON>
  left join
  (select
    CITY1 as INSTALL_CITY1,
    R<PERSON><PERSON><PERSON>O<PERSON> as INSTALL_CITYCODE,
    POST_CODE1 as INSTALL_POSTCODE_1,
    STREET as INSTALL_STREET,
    <PERSON>_<PERSON><PERSON><PERSON> as <PERSON>_STREET,
    <PERSON>_<PERSON>ITY<PERSON> as <PERSON>_<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>_NUM1 as INSTALL_HOUSENUM_1,
    <PERSON><PERSON><PERSON><PERSON><PERSON> as INSTALL_ADDRNUMBER
    from {HanaSchema}.ADRC
    ) as ADRC
  on ILOA.ADRNR = ADRC.INSTALL_ADDRNUMBER
  left join
  (select REGIOGROUP, DESCRIPT from {HanaSchema}.ADRREGGRPT) as REGIODESC
    on ADRC.INSTALL_CITYCODE = REGIODESC.REGIOGROUP
  left join
  (select CITY_CODE, MC_CITY from {HanaSchema}.ADRCITYT) as ADRCITYT
  on ADRCITYT.MC_CITY = ADRC.MC_CITY
  left join
  (select STRT_CODE, CITY_CODE ,MC_STREET from {HanaSchema}.ADRSTREETT) as ADRSTREETT
  on ADRSTREETT.MC_STREET = ADRC.MC_STREET and ADRSTREETT.CITY_CODE = ADRCITYT.CITY_CODE
  left join
  (select STRT_CODE, CITY_CODE, POST_CODE, IDRAD_RUE, IDRAD_LOCALITE
  from {HanaSchema}.ZCA_RAD_MAPP
  ) as ZCA_RAD_MAPP
  on
   ADRSTREETT.STRT_CODE = ZCA_RAD_MAPP.STRT_CODE and
   ADRCITYT.CITY_CODE = ZCA_RAD_MAPP.CITY_CODE and
   ADRC.INSTALL_POSTCODE_1 = ZCA_RAD_MAPP.POST_CODE
)
select * from get_adress_info
LIMIT 1