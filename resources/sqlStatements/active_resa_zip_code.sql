with getLocalite as 
( 
    SELECT distinct 
        ADRCITYT.CITY_NAME AS Localite, 
        ADRPCDCITY.POST_CODE AS Code_Postal,
        TO_BOOlEAN(MAX(CASE WHEN ADRCITYCCS.SPARTE = '01' THEN 1 ELSE 0 END)) AS "GRD_Elec",
        TO_BOOlEAN(MAX(CASE WHEN ADRCITYCCS.SPARTE = '02' THEN 1 ELSE 0 END)) AS "GRD_Gaz",
        TO_BOOlEAN(MAX(CASE WHEN ADRCITYCCS.SPARTE = '03' THEN 1 ELSE 0 END)) AS "GRD_Ep" 	
    FROM {HanaSchema}.ADRCITYCCS
    inner JOIN {HanaSchema}.ADRPCDCITY ON ADRPCDCITY.CITY_CODE = ADRCITYCCS.CITY_CODE
    inner JOIN {HanaSchema}.ADRCITYT ON ADRCITYT.CITY_CODE = ADRCITYCCS.CITY_CODE
    GROUP BY CITY_NAME, POST_CODE
    Order by Code_Postal
)
SELECT * 
FROM getLocalite
WHERE (:grdElec = "GRD_Elec" or :grdElec is Null) and (:grdGaz = "GRD_Gaz" or :grdGaz is Null)