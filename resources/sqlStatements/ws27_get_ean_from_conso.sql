WITH
get_ean_from_conso AS (

  SELECT
    EAN,
    V<PERSON><PERSON><PERSON>,
    <PERSON>AR<PERSON>,
    <PERSON>LAGE,
    AKLASSE,
    ABLEINH,
    <PERSON><PERSON><PERSON>,
    TARIFTYP,
    VKONT,
    GPART
    FROM
    (SELECT
      SPARTE,
      ANLART,
      <PERSON><PERSON><PERSON>,
      VSTELLE
      FROM {HanaSchema}.EANL
      WHERE VSTELLE = (:VSTELLE)
    ) AS EANL
    INNER JOIN
    (SELECT
      INT_UI,
      ANLAGE AS EUIINSTLN_ANLAGE
      FROM {HanaSchema}.EUIINSTLN
    ) AS EUIINSTLN
    ON EANL.ANLAGE = EUIINSTLN.EUIINSTLN_ANLAGE
    INNER JOIN
    (SELECT
      EXT_UI AS EAN,
      INT_UI AS EUITRANS_INT_UI
      FROM {HanaSchema}.EUITRANS
      ) AS EUITRANS
    ON EUITRANS.EUITRANS_INT_UI = EUIINSTLN.INT_UI
    INNER JOIN
    (SELECT
        ANLAGE AS EANLH_ANLAGE,
        ABLEINH,
        TARIFTY<PERSON>,
        AKLA<PERSON>E
      FROM {HanaSchema}.EANLH
      WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
    ) AS EANLH
    ON EANLH.EANLH_ANLAGE = EANL.ANLAGE
    LEFT JOIN
    (SELECT VKONT, GPART, VKBEZ FROM {HanaSchema}.FKKVKP WHERE MAHNV IN('G1', 'G2', 'G4', 'Z1', 'Z2', 'Z4', 'Z5')) AS partner
    ON EUITRANS.EAN = PARTNER.VKBEZ
  )
SELECT * FROM get_ean_from_conso