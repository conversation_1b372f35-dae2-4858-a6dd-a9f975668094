with get_tarifs as (
  select
        NB_PHASE,
        EXCL_NUIT,
        AMPERAGE,
        PUISSANCE,
        MAKT.MATNR as "Article",
        QUANTITE as "Quantite",
        KBETR as "PrixHtvaUnit",
        LIBELLE as "LibIdCpt",
        NUMSEQ as "Lang"
    from (
        select * from {HanaSchema}.ZWS63_PRIX
        where NB_CPT = :NbCpt and
        NB_PHASE = :NbPhase and
        EXCL_NUIT= :ExclNuit and
        AMPERAGE = :Amperage and
        PUISSANCE = :Puissance
    ) as ARTICLE_INFO
    left join
        (select MATNR, MAKTX from {HanaSchema}.MAKT where SPRAS = LEFT(:Langue, 1)) as MAKT
        on MAKT.MATNR = ARTICLE_INFO.ARTICLE
    left join
      (select MATNR, KBETR from (
      select * from
      (select KNUMH, KBETR, KONWA from {HanaSchema}.KONP where KOPOS = '01') as KONP
      inner join
      (select * from (
        select *
        from {HanaSchema}.A004
        where KAPPL = 'V'
            and KSCHL = 'ZPR1'
            and VKORG = '1000'
            and VTWEG = '01'
            and DATAB <= TO_VARCHAR(current_date, 'YYYYMMDD')
            and TO_VARCHAR(current_date, 'YYYYMMDD') <= DATBI
      )) as A004
      on KONP.KNUMH = A004.KNUMH)) as PRIX_ARTICLE
    on ARTICLE_INFO.ARTICLE = PRIX_ARTICLE.MATNR
)
select
    round(sum("Quantite"*"PrixHtvaUnit"),2) as "PrixHtva",
    max(case when LEFT("Lang", 1) = LEFT(:Langue, 1) then "LibIdCpt" else null end) as "LibIdCpt"
from get_tarifs
group by NB_PHASE, EXCL_NUIT, AMPERAGE, PUISSANCE