WITH get_postes_techniques_columns AS (
  SELECT DISTINCT TPLNR_6, TPLNR_5, TPLNR_4, TPLNR_3, EQUI_RUE, EQUI_LOCALITE, <PERSON>QUI_CP, GPS_LAT, GPS_LONG FROM
  ( SELECT * FROM
    (SELECT * FROM
      (SELECT *
        FROM {HanaSchema}.ZPM_ELEC_DELES
        WHERE EQUI_RUE IS NOT NULL AND EQUI_RUE != '' {CdPostalFilter}
      ) AS ELEC_DELES
      LEFT JOIN
      (SELECT OBJ_RACC, MAX(GPS_LAT) AS GPS_LAT, MAX(GPS_LONG) AS GPS_LONG
      FROM {GisSchema}.EAN_GPS
      GROUP BY OBJ_RACC
      ) AS EAN_GPS
      ON ELEC_DELES.OBJ_RAC = EAN_GPS.OBJ_RACC
      {GPSFilter}
    ) AS INTERRUPTIONS_GEO
    {StreetFilter}
    {CityFilter}
    {<PERSON>d<PERSON>ilt<PERSON>}
  INNER JOIN
    (SELECT
        TPLNR AS TPLNR_6,
        TPLMA AS TPLMA_6
      FROM {HanaSchema}.IFLOT) AS IFLOT_6
  ON IFLOT_6.TPLNR_6 = INTERRUPTIONS_GEO.TPVON
  LEFT JOIN
    (SELECT
        TPLNR AS TPLNR_5,
        TPLMA AS TPLMA_5
      FROM {HanaSchema}.IFLOT) AS IFLOT_5
  ON IFLOT_5.TPLNR_5 = IFLOT_6.TPLMA_6
  LEFT JOIN
    (SELECT
        TPLNR AS TPLNR_4,
        TPLMA AS TPLMA_4
      FROM {HanaSchema}.IFLOT) AS IFLOT_4
  ON IFLOT_4.TPLNR_4 = IFLOT_5.TPLMA_5
  LEFT JOIN
    (SELECT
        TPLNR AS TPLNR_3
      FROM {HanaSchema}.IFLOT) AS IFLOT_3
  ON IFLOT_3.TPLNR_3 = IFLOT_4.TPLMA_4
  )
),
get_postes_techniques AS (
  SELECT * FROM (
    SELECT TPLNR_6 AS TPLNR, EQUI_RUE, EQUI_LOCALITE, EQUI_CP, GPS_LAT, GPS_LONG FROM get_postes_techniques_columns
    UNION
    SELECT TPLNR_5 AS TPLNR, EQUI_RUE, EQUI_LOCALITE, EQUI_CP, GPS_LAT, GPS_LONG FROM get_postes_techniques_columns
    UNION
    SELECT TPLNR_4 AS TPLNR, EQUI_RUE, EQUI_LOCALITE, EQUI_CP, GPS_LAT, GPS_LONG FROM get_postes_techniques_columns
    UNION
    SELECT TPLNR_3 AS TPLNR, EQUI_RUE, EQUI_LOCALITE, EQUI_CP, GPS_LAT, GPS_LONG FROM get_postes_techniques_columns
    ) AS TPLNR_TABLE
  WHERE TPLNR IS NOT NULL
),
get_interruptions AS (
  SELECT DISTINCT
    QMEL.QMNUM AS panne_id,
    EQUI_RUE AS rue,
    EQUI_LOCALITE as localite,
    EQUI_CP as cd_postal,
    date_debut,
    heure_debut,
    date_fin,
    heure_fin,
    STRING_AGG(CONCAT(GPS_LAT, CONCAT(' ', GPS_LONG)), ';')  AS positions,
    AVG(GPS_LAT)  AS avg_lat,
    AVG(GPS_LONG) AS avg_long,
    MNCOD AS coupure_ou_g_electrogene FROM (
        (SELECT TPLNR AS INPUT_TPLNR, EQUI_RUE, EQUI_LOCALITE, EQUI_CP, GPS_LAT, GPS_LONG FROM get_postes_techniques) AS INPUTS
        INNER JOIN
        (SELECT TPLNR, ILOAN FROM {HanaSchema}.ILOA) AS ILOA
        ON INPUTS.INPUT_TPLNR = ILOA.TPLNR
        INNER JOIN
        (SELECT ILOAN, QMNUM FROM {HanaSchema}.QMIH) AS QMIH
        ON QMIH.ILOAN = ILOA.ILOAN
        INNER JOIN
        (SELECT QMNUM FROM {HanaSchema}.QMEL WHERE QMART = 'AC') AS QMEL
        ON QMIH.QMNUM = QMEL.QMNUM
        INNER JOIN
        (SELECT QMNUM, PSTER AS date_debut, PSTUR AS heure_debut, PETER AS date_fin, PETUR as heure_fin, MNCOD
        FROM {HanaSchema}.QMMA
        WHERE PETER >= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
        ) AS QMMA
        ON QMIH.QMNUM = QMMA.QMNUM
    )
    GROUP BY QMEL.QMNUM, EQUI_RUE, EQUI_LOCALITE, EQUI_CP, date_debut, heure_debut, date_fin, heure_fin, MNCOD
)
SELECT
  count(*) OVER () as nb_items,
  cd_postal,
  date_debut,
  heure_debut,
  date_fin,
  heure_fin,
  localite,
  rue,
  coupure_ou_g_electrogene,
  positions,
  avg_lat,
  avg_long,
  panne_id as "id"
FROM get_interruptions
LIMIT :PageSize
OFFSET :PageOffset;