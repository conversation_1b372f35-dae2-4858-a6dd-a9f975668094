WITH get_num_cpt_from_install AS (
  SELECT EASTL_ANLAGE AS INSTALL, NUM_CPT, EGERH_EQUNR AS NUM_EQUIPEMENT FROM
  (SELECT ANLAGE AS EASTL_ANLAGE, LO<PERSON><PERSON>NR FROM {HanaSchema}.EASTL WHERE ANLAGE = '4100720790') AS EASTL
  LEFT JOIN
  (SELECT * FROM {HanaSchema}.EZUZ) AS EZUZ
  ON EASTL.LOGIKNR = EZUZ.LOGIKNR2
  LEFT JOIN
  (SELECT EQUNR AS EGERH_EQUNR, ZWGRUPPE AS EGERH_ZWGRUPPE, LOGIKNR AS EGERH_LOGIKNR FROM {HanaSchema}.EGERH) AS EGERH
  ON EASTL.LOGIKNR = EGERH.EGERH_LOGIKNR
  LEFT JOIN
  (SELECT *  FROM {HanaSchema}.EASTS WHERE ANLAGE = '4100720790' AND ZWNABR IS NULL OR ZWNABR = '') AS EASTS
  ON EZUZ.LOGIKZW = EASTS.LOGIKZW
  LEFT JOIN
  (SELECT ZWGRUPPE, COUNT(*) AS N_BILLING FROM {HanaSchema}.EZWG WHERE ZWNABR IS NULL OR ZWNABR = '' GROUP BY ZWGRUPPE) AS EZWG
  ON EGERH.EGERH_ZWGRUPPE = EZWG.ZWGRUPPE
  LEFT JOIN
  (SELECT EQUNR, SERNR AS NUM_CPT FROM {HanaSchema}.EQUI WHERE EQART != 'IG_CNV') AS EQUI
  ON EGERH.EGERH_EQUNR = EQUI.EQUNR
)
SELECT * FROM get_num_cpt_from_install