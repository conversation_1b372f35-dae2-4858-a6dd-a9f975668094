WITH tarif AS (
    SELECT 
        A004.MATNR AS "Id",
        CASE WHEN A004.MATNR = 'EB111' THEN KONP.KBETR * -1 ELSE KONP.KBETR END AS "Price"
    FROM {HanaSchema}.A004
    JOIN {HanaSchema}.KONP ON A004.KNUMH = KONP.KNUMH
    WHERE
        A004.VTWEG = '01'
        AND A004.DATAB < NOW()
        AND A004.DATBI > NOW()
)
SELECT
    TRIM(SUBSTRING(MAX(LIBELLE), LOCATE(MAX(LIBELLE), 'Forfait ') + LENGTH('Forfait '))) AS FORFAIT,
    NB_PHASE,
    CAST(AMPERAGE AS DECIMAL) AS AMPERAGE,
    PUISSANCE,
    SUM(tarif."Price") AS PRICE
FROM {HanaSchema}.ZWS63_PRIX
LEFT JOIN tarif ON tarif."Id" = ARTICLE
WHERE NB_CPT = 1 AND NUMSEQ != 'DE'
GROUP BY NB_PHASE, AMPERAG<PERSON>, PUI<PERSON>ANCE
ORDER BY NB_PHASE, <PERSON>PERAG<PERSON>, <PERSON><PERSON><PERSON>ANCE