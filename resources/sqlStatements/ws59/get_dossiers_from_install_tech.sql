WITH get_adresse_poste_technique AS (
  SELECT DISTINCT AUFNR AS "DossierId" FROM (
  SELECT * FROM
  (
    SELECT ADDRNUMBER
    FROM {HanaSchema}.ADRC
    WHERE
      POST_CODE1 = :CdPostal
      AND UPPER(TRIM(STREET))  IN (:Rue, :CleanedRue)
      AND UPPER(TRIM(CITY1)) IN (:Localite, :CleanedLocalite)
      AND UPPER(TRIM(HOUSE_NUM1)) = :NumRue
  ) AS ADRC
  INNER JOIN
  (
    SELECT DISTINCT TPLNR, ADRNR
    FROM {HanaSchema}.ILOA
  ) AS ILOA
  ON ADRC.ADDRNUMBER = ILOA.ADRNR
  INNER JOIN
  (
    SELECT VSTELLE, HAUS
    FROM {HanaSchema}.EVBS
  ) AS EVBS
  ON ILOA.TPLNR = EVBS.HAUS
  INNER JOIN
   (
    SELECT VSTELLE, EQUNR
    FROM {HanaSchema}.ETINS
  ) AS ETINS
  ON EVBS.VSTELLE = ETINS.VSTELLE
  INNER JOIN
  (
    SELECT QMNUM, EQUNR
    FROM {HanaSchema}.QMIH
  ) AS QMIH
  ON ETINS.EQUNR = QMIH.EQUNR
  INNER JOIN
   (
    SELECT QMNUM, AUFNR AS QMEL_AUFNR
    FROM {HanaSchema}.QMEL
  ) AS QMEL
  ON QMEL.QMNUM = QMIH.QMNUM
  INNER JOIN
   (
    SELECT AUFNR
    FROM {HanaSchema}.AUFK
    WHERE AUART IN ('DRE1', 'DRG1')
  ) AS AUFK
  ON QMEL.QMEL_AUFNR = AUFK.AUFNR
))
SELECT * FROM get_adresse_poste_technique