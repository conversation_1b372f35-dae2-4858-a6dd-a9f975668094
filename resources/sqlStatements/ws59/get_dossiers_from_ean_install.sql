WITH get_dossier_from_ean_install AS (
  SELECT DISTINCT
    AUFNR AS "DossierId",
    EAN AS "Ean"
    FROM
    (SELECT
      EXT_UI AS EAN,
      INT_UI
      FROM {HanaSchema}.EUITRANS
      WHERE EXT_UI IN ({ParamsValue})
      )AS EUITRANS
  INNER JOIN
  (SELECT
    INT_UI,
    ANLAGE AS EUIINSTLN_ANLAGE
    FROM {HanaSchema}.EUIINSTLN
  )AS EUIINSTLN
  ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
  INNER JOIN
  (SELECT
    ANLAGE,
    VSTELLE
    FROM {HanaSchema}.EANL
  ) AS EANL
  ON EUIINSTLN.EUIINSTLN_ANLAGE = EANL.ANLAGE
  INNER JOIN
  (SELECT VSTELLE, EQUNR
  FROM {HanaSchema}.ETINS
  ) AS ETINS
  ON EANL.VSTELLE = ETINS.VSTELLE
  INNER JOIN
  (
    SELECT QMNUM, EQUNR
    FROM {HanaSchema}.QMIH
  ) AS QMIH
  ON ETINS.EQUNR = QMIH.EQUNR
  INNER JOIN
   (
    SELECT QMNUM, AUFNR AS QMEL_AUFNR
    FROM {HanaSchema}.QMEL
  ) AS QMEL
  ON QMEL.QMNUM = QMIH.QMNUM
  INNER JOIN
   (
    SELECT AUFNR
    FROM {HanaSchema}.AUFK
    WHERE AUART IN ('DRE1', 'DRG1')
  ) AS AUFK
  ON QMEL.QMEL_AUFNR = AUFK.AUFNR
)
SELECT * FROM get_dossier_from_ean_install