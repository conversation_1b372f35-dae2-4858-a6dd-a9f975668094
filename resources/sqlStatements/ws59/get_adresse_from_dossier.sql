SELECT
  AUFNR AS "NumDoss<PERSON>",
  <PERSON>DR<PERSON>MBER AS "IdAdresse",
  POST_CODE1 AS "CdPostal",
  STREETCODE AS "IdRue",
  IDRAD_RUE AS "IdRadRue",
  TRIM(STREET) AS "Rue",
  <PERSON><PERSON><PERSON>_CODE AS "Id<PERSON>ocalite",
  <PERSON>RAD_LOCALITE AS "IdRadLocalite",
  TRIM(CITY1) AS "Localite",
  TRIM(HOUSE_NUM1) AS "NumRue",
  TRIM(HOUSE_NUM2) AS "NumComp"
FROM (
  (
    SELECT AUFNR AS AUFK_AUFNR
    FROM {HanaSchema}.AUFK
    WHERE AUART IN ('DRE1', 'DRG1') AND AUFNR IN ({ListeDossiers})
  ) AS AUFK
INNER JOIN
  (SELECT QMNUM, AUFNR
  FROM {HanaSchema}.QMEL
  ) AS QMEL
ON AUFK.AUFK_AUFNR = QMEL.AUFNR
INNER JOIN
  (SELECT QMNUM, ILOAN FROM {HanaSchema}.QMIH) AS QMIH
ON QMEL.QMNUM = QMIH.QMNUM
INNER JOIN
  (SELECT ILOAN, ADRNR FROM {HanaSchema}.ILOA) AS ILOA
ON ILOA.ILOAN = QMIH.ILOAN
INNER JOIN
  (SELECT * FROM {HanaSchema}.ADRC) AS ADRC
ON ADRC.ADDRNUMBER = ILOA.ADRNR
LEFT JOIN
  (SELECT STRT_CODE, IDRAD_RUE
  FROM {HanaSchema}.ZCA_RAD_MAPP
  WHERE TYPE = 'RUE'
  ) AS ZCA_RAD_MAPP_STRT
  ON ZCA_RAD_MAPP_STRT.STRT_CODE = ADRC.STREETCODE
LEFT JOIN
  (SELECT CITY_CODE AS ZCA_CITY_CODE, IDRAD_LOCALITE
  FROM {HanaSchema}.ZCA_RAD_MAPP
  WHERE TYPE = 'LOCALITE'
  ) AS ZCA_RAD_MAPP_CITY
  ON ZCA_RAD_MAPP_CITY.ZCA_CITY_CODE = ADRC.CITY_CODE
)