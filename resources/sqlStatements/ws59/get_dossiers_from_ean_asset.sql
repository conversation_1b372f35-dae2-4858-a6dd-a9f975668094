WITH get_dossier_from_ean_asset AS (
  SELECT DISTINCT
    AUFNR AS "DossierId",
    EAN AS "Ean"
    FROM
    (SELECT
      EXT_UI AS EAN,
      INT_UI
      FROM {HanaSchema}.EUITRANS
      WHERE EXT_UI IN ({ParamsValue})
      )AS EUITRANS
  INNER JOIN
  (SELECT
    EQUNR,
    TIDNR
    FROM {HanaSchema}.EQUZ
  )AS EQUZ
  ON EQUZ.TIDNR = EUITRANS.EAN
  INNER JOIN
  (
    SELECT QMNUM, EQUNR
    FROM {HanaSchema}.QMIH
  ) AS QMIH
  ON EQUZ.EQUNR = QMIH.EQUNR
  INNER JOIN
   (
    SELECT QMNUM, AUFNR AS QMEL_AUFNR
    FROM {HanaSchema}.QMEL
  ) AS QMEL
  ON QMEL.QMNUM = QMIH.QMNUM
  INNER JOIN
   (
    SELECT AUFNR
    FROM {HanaSchema}.AUFK
    WHERE AUART IN ('DRE1', 'DRG1')
  ) AS AUFK
  ON QMEL.QMEL_AUFNR = AUFK.AUFNR
)
SELECT * FROM get_dossier_from_ean_asset