SELECT TRIM(ATNAM) AS ATNAM, TRIM(ATWRT) AS ATWRT, ATFLV, (CASE WHEN TRIM(ATWRT) = '' OR ATWRT IS NULL THEN 'ATFLV' ELSE 'ATWRT' END) AS VAL_TO_USE
FROM (
  SELECT OBJNR, <PERSON>FNR
  FROM {HanaSchema}.AUFK
  WHERE AUFNR = LPAD(:DossierId, 12, '0')
) AS AUFK
INNER JOIN (
  SELECT CUOBJ, OBJNR FROM {HanaSchema}.PMSDO
) AS PMSDO
  ON PMSDO.OBJNR = AUFK.OBJNR
INNER JOIN (
  SELECT IBIN.INSTANCE, ATINN, ATWRT, ATFLV
  FROM {HanaSchema}.IBIN
  INNER JOIN (
    SELECT INSTANCE, OBJKEY
    FROM {HanaSchema}.IBINOWN
  ) AS IBINOWN 
    ON IBINOWN.INSTANCE = IBIN.INSTANCE
  INNER JOIN (
    SELECT SYMBOL_ID, IN_RECNO
    FROM {HanaSchema}.IBINVALUES
  ) AS IBINVALUES
    ON IBINVALUES.IN_RECNO = IBIN.IN_RECNO
  INNER JOIN (
    SELECT SYMBOL_ID, ATINN, ATWRT, ATFLV
    FROM {HanaSchema}.IBSYMBOL
  ) AS IBSYMBOL
    ON IBINVALUES.SYMBOL_ID = IBSYMBOL.SYMBOL_ID
) AS IBINVAL 
  ON IBINVAL.INSTANCE = PMSDO.CUOBJ
INNER JOIN (
  SELECT CABN.ATINN, ATNAM, ATBEZ
  FROM {HanaSchema}.CABN
  INNER JOIN {HanaSchema}.CABNT ON CABNT.ATINN = CABN.ATINN
  WHERE SPRAS = 'F'
) AS CABN
  ON CABN.ATINN = IBINVAL.ATINN