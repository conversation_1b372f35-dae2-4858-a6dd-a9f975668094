  SELECT DISTINCT QMEL.AUFNR AS "DossierId", <PERSON>RNR  AS "IdPartenaire" FROM
  (SELECT DISTINCT OBJNR, PARNR FROM {HanaSchema}.IHPA
  WHERE PARNR IN ({ParamsValue})) AS IHPA
  INNER JOIN
  (SELECT AUFNR, OBJNR
  FROM {HanaSchema}.QMEL
  WHERE AUFNR != '' AND AUFNR IS NOT NULL) AS QMEL
  ON IHPA.OBJNR = QMEL.OBJNR
  INNER JOIN
  (SELECT AUART, AUFNR FROM {HanaSchema}.AUFK) AS AUFK
  ON QMEL.AUFNR = AUFK.AUFNR
  WHERE AUART IN ('DRE1', 'DRG1')