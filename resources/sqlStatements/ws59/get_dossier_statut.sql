with ls_header as (
  select * from (
    (select MANDT as AUFK_MANDT, AUF<PERSON> as INPUT_AUFNR, ERDAT, OBJNR as AUFK_OBJNR, IDAT2
    from {HanaSchema}.AUFK
    where AUFNR = lpad(:DossierId, 12, '0')) as AUFK
    inner join
    (select AUFPL, AUFNR, MAUF<PERSON> from {HanaSchema}.AFKO) as AFKO
    on AUFK.INPUT_AUFNR = AFKO.AUFNR
    inner join
    (select AUFNR, QMNUM, ILART, INGPR from {HanaSchema}.AFIH) as AFIH
    on AUFK.INPUT_AUFNR = AFIH.AUFNR
  )
), ls_operation AS (
    (select * from ls_header
    inner join
    (select APLZL, AUFPL, OBJNR as AFVC_OBJNR, VORNR, STEUS from {HanaSchema}.AFVC) as AFVC
    on AFVC.AUFPL = ls_header.AUFPL
    inner join
    (select APLZL, AUFPL, FSAVD from {HanaSchema}.AFVV) as AFVV
    on AFVC.APLZL = AFVV.APLZL and AFVC.AUFPL = AFVV.AUFPL
  )
), get_dossier_statut AS (
    select * from ls_operation
    inner join
      (select MANDT, OBJNR as JEST_OBJNR, STAT, CHGNR
      from {HanaSchema}.JEST
      where INACT = '') as JEST
    on ls_operation.AUFK_OBJNR = JEST.JEST_OBJNR and ls_operation.AUFK_MANDT = JEST.MANDT
    inner join
      (select OBJNR as JSTO_OBJNR, STSMA as JSTO_STSMA
      from {HanaSchema}.JSTO) as JSTO
    on ls_operation.AUFK_OBJNR = JSTO.JSTO_OBJNR and ls_operation.AUFK_MANDT = JEST.MANDT
    inner join
      (select distinct ESTAT, STSMA as E_STSMA, LINEP as E_LINEP, STATP as E_STATP
      from {HanaSchema}.TJ30) as TJ30
    on JEST.STAT =TJ30.ESTAT and JSTO.JSTO_STSMA = TJ30.E_STSMA
    inner join
      (select distinct STSMA as TJ30T_STSMA, ESTAT as TJ30T_ESTAT, TXT04 as ESTAT_CODE
      from {HanaSchema}.TJ30T) as TJ30T
    on TJ30T.TJ30T_STSMA = TJ30.E_STSMA and TJ30.ESTAT = TJ30T.TJ30T_ESTAT
), format_dossier_statut AS (
  select distinct
    MANDT, ILART, INGPR, AUFK_OBJNR, ESTAT_CODE, E_LINEP, E_STATP, ERDAT, INPUT_AUFNR, QMNUM, IDAT2
  from get_dossier_statut
), ordered_statut AS (
  (select *,
    row_number() over (partition by MANDT order by E_LINEP desc, E_STATP desc, ESTAT_CODE) as ROW_NUM
    from format_dossier_statut
    order by E_LINEP desc, E_STATP desc, ESTAT_CODE
  )
), first_statut_selection AS (
  select *
  from (
    (select * from {HanaSchema}.ZWS59_STATUT) as STAT_TABLE
    inner join
    (select ESTAT_CODE as ESTAT_1, ILART as ILART_1, INGPR as INGRP_1, ERDAT, INPUT_AUFNR, QMNUM, IDAT2 from ordered_statut where ROW_NUM = 1) as FIRST_STATUT
    on STAT_TABLE.STATUT1 = FIRST_STATUT.ESTAT_1
  )
  WHERE
    ((ILA = 'R*' AND ILART_1 LIKE 'R%')
      OR (ILA = 'R0' AND ILART_1 LIKE 'R0%')
      OR (ILA = 'RT' AND ILART_1 LIKE 'RT%')
      OR ILA = ILART_1
      OR ILA = '*')
    AND (INGRP = '*' OR INGRP = INGRP_1)
), second_statut_selection AS (
    select
      sum(case when ESTAT_2 is not null then 1 else 0 end) over (partition by DUMMY) as HAS_ESTAT_2,
      sum(case when trim(STATUT2) = '' then 1 else 0 end) over (partition by DUMMY) as HAS_EMPTY_STATUT2,*
    from
      (select *, 1 as DUMMY from first_statut_selection
        left join
      (select ESTAT_CODE as ESTAT_2 from ordered_statut where ROW_NUM != 1) as SECOND_STATUT
      on first_statut_selection.STATUT2 = SECOND_STATUT.ESTAT_2)
), keep_second_status_or_all AS (
  select *
  from second_statut_selection
  where (ESTAT_2 is not null and HAS_ESTAT_2 > 0)
    or (HAS_ESTAT_2 = 0 and HAS_EMPTY_STATUT2 > 0 and STATUT2 = '')
    or (HAS_ESTAT_2 = 0 and HAS_EMPTY_STATUT2 = 0)
), keep_higher_priority AS (
    select * from
      (
        select row_number() over (partition by DUMMY order by PRIORI desc) as PRIO_NUM, *
        from keep_second_status_or_all
      )
    where PRIO_NUM = 1
), add_libelles AS (
    select * from keep_higher_priority
    left join
    (select CODE as CODE_ETAPE, LIB_CODE as LIB_ETAPE, LEFT(CDLANGU, 1) as "Lang"
      from {HanaSchema}.ZWS59_LIB_STATUT
      where TYPE = 'ETAP' ) as LIBELLE_ETAP
    on keep_higher_priority.ETAPE = LIBELLE_ETAP.CODE_ETAPE
    left join
    (select CODE as CODE_STAT, LIB_CODE as LIB_STAT, CDLANGU
      from {HanaSchema}.ZWS59_LIB_STATUT
      where TYPE = 'STATUT' ) as LIBELLE_STAT
    on keep_higher_priority.CODE_STATUT = LIBELLE_STAT.CODE_STAT AND LEFT(LIBELLE_STAT.CDLANGU, 1) = "Lang"
), get_date_dossier AS (
	select PSTER from {HanaSchema}.QMSM
	JOIN add_libelles ON QMSM.QMNUM = add_libelles.QMNUM
    where MNGRP = 'RAC' and MNCOD = 'INIT'
	order by PSTER asc
	LIMIT 1
), get_all_statut_info AS (
  select
    INPUT_AUFNR as "NumDossier",
    TO_VARCHAR(to_date(IFNULL((select * from get_date_dossier), ERDAT), 'YYYYMMDD'), 'DD/MM/YYYY') as "DateDossier",
    ILART_1 as "TypeDossier",
    INGRP_1 as "GroupeGestionnaire",
    LIB_STAT as "LibStatut",
    STATUT1,
    STATUT2,
    LIB_ETAPE as "LibEtape",
    ETAPE as "NumEtape",
    CODE_STATUT as "CodeStatut",
    NB_ACTION,
    ACTION,
    "Lang"
    --(CASE WHEN IDAT2 is null or IDAT2 = '' or IDAT2 = '00000000' THEN null ELSE TO_DATE(IDAT2, 'YYYYMMDD') END) as "DateCloture"
  from add_libelles
)

select * from get_all_statut_info