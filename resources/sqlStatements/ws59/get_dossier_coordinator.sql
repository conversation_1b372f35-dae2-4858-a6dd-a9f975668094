SELECT DISTINCT
    PA0002.NCHMC AS "Nom",
    PA0002.VNAMC AS "Prenom",
    PA9004.ZTEL AS "Tel"
FROM {HanaSchema}.IHPA
JOIN {HanaSchema}.PA0002 ON PA0002.PERNR = IHPA.PARNR AND PA0002.ENDDA = '99991231'
JOIN {HanaSchema}.PA9004 ON PA9004.PERNR = IHPA.PARNR AND PA9004.ENDDA = '99991231'
WHERE IHPA.OBJNR = CONCAT('OR', LPAD(:DossierId, 12, '0')) AND IHPA.PARVW = 'ZA' AND PA9004.ZTEL != ''  AND IHPA.KZLOESCH  != 'X'