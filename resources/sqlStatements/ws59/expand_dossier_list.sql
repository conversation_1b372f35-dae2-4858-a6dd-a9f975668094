WITH filter_dossier AS (
  SELECT AUFNR as "NumDossier" 
  FROM {HanaSchema}.AUFK
  WHERE AUFNR IN ({ListeDossiers}) AND
        AUART IN ('DRE1', 'DRG1')
), get_dossier_sup AS (
  SELECT MAUFNR AS "NumDossierSup", "NumDossier"
  FROM filter_dossier
  INNER JOIN {HanaSchema}.AFKO
  ON AFKO.AUFNR = filter_dossier."NumDossier"
  WHERE MAUFNR IS NOT NULL AND MAUFNR != ''
), get_dossier_inf AS (
    SELECT "NumDossier", AUFNR AS "NumDossierInf"
    FROM filter_dossier
    INNER JOIN {HanaSchema}.AFKO
    ON AFKO.MAUFNR = filter_dossier."NumDossier"
    WHERE AUFNR IS NOT NULL AND AUFNR != ''
), all_dossiers AS (
 
 SELECT DISTINCT "NumDossier", "NumDossierSup", NULL AS "NumDossierInf"
  FROM get_dossier_sup
    UNION
 SELECT DISTINCT "NumDossier", NULL AS "NumDossierSup", "NumDossierInf"
  FROM get_dossier_inf
 UNION
 SELECT DISTINCT "NumDossier", NULL AS "NumDossierSup", NULL AS "NumDossierInf"
  FROM filter_dossier
  WHERE "NumDossier" not in (SELECT "NumDossier" from get_dossier_sup)
    AND "NumDossier" not in (SELECT "NumDossier" from get_dossier_inf)
 
), add_date AS (
    SELECT * FROM all_dossiers
    INNER JOIN
    (SELECT AUFNR AS AUFNR_DAT, IDAT2 FROM {HanaSchema}.AUFK) AS AUFK_DAT
    ON all_dossiers."NumDossier" = AUFK_DAT.AUFNR_DAT
)
SELECT "NumDossier", "NumDossierSup", "NumDossierInf" FROM add_date
{DateFilter}
ORDER BY "NumDossier", "NumDossierSup", "NumDossierInf"