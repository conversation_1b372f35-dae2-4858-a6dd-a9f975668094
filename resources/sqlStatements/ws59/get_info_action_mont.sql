with vbak_info as (
    select *
    from {HanaSchema}.VBAK
    where VBTYP = :TypeMontant and AUFNR = lpad(:DossierId, 12, '0')
    and ((VBTYP='C' and AUART in ('DRE2', 'DRG2')) or VBTYP='B')
), action_montant as (
    select VBAK.NETWR + sum(KONV.KWERT) as val
    from {HanaSchema}.KONV
    join vbak_info AS VBAK on KONV.KNUMV = VBAK.KNUMV
    where KONV.KSCHL = 'MWST'
    group by VBAK.NETWR
), action_restant_du_devis as (
    select abs(sum(betrh)) as val
    from {HanaSchema}.dfkkop
where blart in ('RC','MP','GL','MO','TN','SC')
    and bukrs = '101' 
    and xblnr = (select lpad(XBLNR, 16, '0') from {HanaSchema}.VBAK where TRVOG = 2 and AUFNR = lpad(:DossierId, 12, '0') LIMIT 1)
    and augrd not in ('02','03','04','05','14')
group by xblnr
), action_restant_du_etude as (
    select abs(sum(betrh)) as val
    from {HanaSchema}.dfkkop
	where 
		blart = 'RC'
	    and xblnr = (select lpad(VBELN, 16, '0') from vbak_info limit 1)
	group by xblnr
)
select CODE as "CodeInfo", LIB_CODE as "LibInfo", TYPE_INFO as "TypeInfo", CDLANGU as "Lang", (
    case when CODE = '44_2' then (select val from action_montant) - ifnull((select val from action_restant_du_devis),0) 
    when CODE = '32_2' then (select val from action_montant) - ifnull((select val from action_restant_du_etude),0) 
    else (select val from action_montant) end
) as "ValeurInfo"
from {HanaSchema}.ZWS59_LIB_STATUT
where TYPE = '{Action}'
