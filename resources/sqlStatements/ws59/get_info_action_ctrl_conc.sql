WITH get_possible_matches AS  (
    SELECT *  FROM (
      SELECT * FROM (
        SELECT ROW_NUMBER() OVER (PARTITION BY DUMMY ORDER BY MANUM DESC) AS ROW_NUMBER, * FROM (
          (SELECT QMNUM AS QMEL_QMNUM, AUFNR AS "NumDossier", ERDAT
            FROM {HanaSchema}.QMEL
            WHERE AUFNR = LPAD(:DossierId, 12, '0') AND MAKNZ = 'X'
          ) AS QMEL
          INNER JOIN
          (SELECT QMNUM, MNGRP, MNCOD, MANUM, 1 AS DUMMY
            FROM {HanaSchema}.QMSM
            WHERE MNCOD IN ('0256', '0257') AND (FENUM = 0 OR FENUM IS NULL)
          ) AS QMSM
          ON QMEL.QMEL_QMNUM = QMSM.QMNUM
        )
      )
      WHERE ROW_NUMBER = 1
   ) AS T
   RIGHT JOIN
  (SELECT TYPE AS ACTION, CODE AS "CodeInfo", LIB_CODE AS "LibInfo", TYPE_INFO AS "TypeInfo", <PERSON><PERSON>NGU AS "Lang", 1 AS DUMMY2
    FROM {HanaSchema}.ZWS59_LIB_STATUT
    WHERE TYPE LIKE 'CTRL_CONC%'
  ) AS INFO_ACTION
  ON INFO_ACTION.DUMMY2 = T.DUMMY
), keep_one_action AS (
   SELECT "CodeInfo", "LibInfo", "TypeInfo", NULL AS "ValeurInfo", "Lang"
   FROM get_possible_matches
   WHERE
      (ACTION = 'CTRL_CONC1' AND MNCOD IS NULL)
      OR (ACTION = 'CTRL_CONC2' AND MNCOD = '0256')
      OR (ACTION = 'CTRL_CONC3' AND MNCOD = '0257')
)

SELECT * FROM keep_one_action