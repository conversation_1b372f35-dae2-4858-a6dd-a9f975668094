WITH get_adresse_objet_racc AS (
  SELECT DISTINCT AUFNR AS "DossierId"
  FROM
  (
    SELECT ADDRNUMBER
    FROM {HanaSchema}.ADRC
    WHERE
      POST_CODE1 = :CdPostal
      AND UPPER(TRIM(STREET)) IN (:Rue, :CleanedRue)
      AND UPPER(TRIM(CITY1)) IN (:Localite, :CleanedLocalite)
      AND UPPER(TRIM(HOUSE_NUM1)) = :NumRue
  ) AS ADRC
  INNER JOIN
  (
    SELECT DISTINCT ILOAN, ADRNR
    FROM {HanaSchema}.ILOA
  ) AS ILOA
  ON ADRC.ADDRNUMBER = ILOA.ADRNR
  INNER JOIN
  (
    SELECT QMNUM, ILOAN
    FROM {HanaSchema}.QMIH
  ) AS QMIH
  ON ILOA.ILOAN = QMIH.ILOAN
  INNER JOIN
   (
    SELECT AUFNR AS QMEM_AUFNR, QMNUM
    FROM {HanaSchema}.QMEL
  ) AS QMEL
  ON QMEL.QMNUM = QMIH.QMNUM
  INNER JOIN
   (
    SELECT AUFNR
    FROM {HanaSchema}.AUFK
    WHERE AUART IN ('DRE1', 'DRG1')
  ) AS AUFK
  ON QMEL.QMEM_AUFNR = AUFK.AUFNR
)
SELECT * FROM get_adresse_objet_racc