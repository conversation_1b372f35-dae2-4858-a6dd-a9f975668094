WITH ls_header AS (
  SELECT * FROM (
    (SELECT MANDT AS AUFK_MANDT, AUFNR AS INPUT_AUFNR, ERDAT, OBJNR AS AUFK_OBJNR
    FROM {HanaSchema}.AUFK
    WHERE AUFNR = LPAD(:DossierId, 12, '0')) AS AUFK
    INNER JOIN
    (SELECT AUFPL, AUFNR, MAUFNR FROM {HanaSchema}.AFKO) AS AFKO
    ON AUFK.INPUT_AUFNR = AFKO.AUFNR
    INNER JOIN
    (SELECT AUFNR, QMNUM, ILART, INGPR FROM {HanaSchema}.AFIH) AS AFIH
    ON AUFK.INPUT_AUFNR = AFIH.AUFNR
  )
), ls_operation AS (
    (SELECT * FROM ls_header
    INNER JOIN
    (SELECT APLZL, AUFPL, OBJNR AS AFVC_OBJNR, VORNR, STEUS FROM {HanaSchema}.AFVC) AS AFVC
    ON AFVC.AUFPL = ls_header.AUFPL
    INNER JOIN
    (SELECT APLZL, AUFPL, FSAVD FROM {HanaSchema}.AFVV) AS AFVV
    ON AFVC.APLZL = AFVV.APLZL AND AFVC.AUFPL = AFVV.AUFPL
  )
), action_date_planif AS (
    SELECT  "CodeInfo", "LibInfo", "TypeInfo", "ValeurInfo", "Lang"
    FROM
    (
      SELECT ROW_NUMBER() OVER (PARTITION BY "Lang" ORDER BY VORNR DESC) AS ROW_NUMBER, * FROM (
        SELECT DISTINCT INPUT_AUFNR AS "NumDossier", VORNR, AFVC_OBJNR, STEUS, FSAVD AS "ValeurInfo"
        FROM ls_operation
        WHERE
          (STEUS IN ('RACC', 'RAMS') AND ('RACP' = :STATUT2 OR 'RACE' = :STATUT2))
          OR (STEUS = 'VISI' AND ('VPLA' = :STATUT2 OR 'VREA' = :STATUT2))
          OR (STEUS = 'MISE' AND 'MESP' = :STATUT2)
          OR :STATUT1 = 'MESE'
      ) AS DP_CANDIDATES
      INNER JOIN
        (SELECT MANDT, OBJNR AS JEST_OBJNR, STAT, CHGNR
        FROM {HanaSchema}.JEST
        WHERE INACT = '') AS JEST
      ON DP_CANDIDATES.AFVC_OBJNR = JEST.JEST_OBJNR
      INNER JOIN
      (SELECT *, LEFT(SPRAS, 1) AS "Lang"
        FROM {HanaSchema}.TJ02T
      ) AS STATUS_DESC
      ON JEST.STAT = STATUS_DESC.ISTAT
      INNER JOIN
      (SELECT CODE AS "CodeInfo", LIB_CODE AS "LibInfo", TYPE_INFO AS "TypeInfo", CDLANGU
        FROM {HanaSchema}.ZWS59_LIB_STATUT
        WHERE TYPE = 'DATE_PLANIF'
      ) AS INFO_ACTION
      ON LEFT(INFO_ACTION.CDLANGU, 1) = "Lang"
    )
   WHERE ROW_NUMBER = 1
)

SELECT * FROM action_date_planif