WITH "search1" AS (
	SELECT AUFNR AS "DossierId"
	FROM {HanaSchema}.AUFK
	JOIN {HanaSchema}.BUT000
	    ON (
	    	(
	    		CONTAINS(BUT000.NAME_LAST, trim(:Lastname), FUZZY(0.85)) 
	    		AND CONTAINS(BUT000.NAME_FIRST, trim(:Firstname), FUZZY(0.85))
    		) OR (
	    		CONTAINS(BUT000.NAME_LAST, trim(:Firstname), FUZZY(0.85))
    			AND CONTAINS(BUT000.NAME_FIRST, trim(:Lastname), FUZZY(0.85))
			) OR (
	    		(
	    			CONTAINS(BUT000.NAME_LAST, trim(concat(concat(trim(:Lastname), ' '),trim(:Firstname))), FUZZY(0.85))
	    			OR CONTAINS(BUT000.NAME_LAST, trim(concat(concat(trim(:Firstname), ' '),trim(:Lastname))), FUZZY(0.85))
	    		) AND (
	    			BUT000.NAME_FIRST IS NULL OR trim(BUT000.NAME_FIRST) = ''
	    		)
    		) OR (
	    		(
	    			CONTAINS(BUT000.NAME_FIRST, trim(concat(concat(trim(:Lastname), ' '),trim(:Firstname))), FUZZY(0.85))
	    			OR CONTAINS(BUT000.NAME_FIRST, trim(concat(concat(trim(:Firstname), ' '),trim(:Lastname))), FUZZY(0.85))
	    		) AND (
	    			BUT000.NAME_LAST IS NULL OR trim(BUT000.NAME_LAST) = ''
	    		)
    		)
			OR lower(trim(concat(concat(trim(BUT000.NAME_LAST), ' '),trim(BUT000.NAME_FIRST)))) = lower(trim(concat(concat(trim(:Lastname), ' '),trim(:Firstname))))
			OR lower(trim(concat(concat(trim(BUT000.NAME_LAST), ' '),trim(BUT000.NAME_FIRST)))) = lower(trim(concat(concat(trim(:Firstname), ' '),trim(:Lastname))))
	    )
	    AND BUT000.BPKIND = 'ZMET' AND NOT (BUT000.PERSNUMBER IS NULL OR BUT000.PERSNUMBER = '')
	LEFT JOIN {HanaSchema}.ADR2
		ON ADR2.PERSNUMBER = BUT000.PERSNUMBER AND ADR2.R3_USER IN ('1','3')
	LEFT JOIN {HanaSchema}.ADR6
		ON ADR6.PERSNUMBER = BUT000.PERSNUMBER
	JOIN {HanaSchema}.IHPA
		ON IHPA.OBJNR = AUFK.OBJNR
		AND IHPA.PARNR = BUT000.PARTNER
		AND IHPA.OBTYP = 'ORI'
		AND IHPA.PARVW = 'Z2'
		AND (IHPA.KZLOESCH IS NULL OR IHPA.KZLOESCH = '')
	WHERE AUFK.AUART IN ('DRE1', 'DRG1')
		AND ((
            NOT (:Phone IS NULL OR :Phone IN ('', '+32'))
            AND substring(:Phone, length(:Phone)-8) IN (
                substring(ADR2.TELNR_LONG, length(ADR2.TELNR_LONG)-8),
                substring(ADR2.TELNR_CALL, length(ADR2.TELNR_CALL)-8)
            )
        ) OR (
            NOT (:PhoneFixe IS NULL OR :PhoneFixe IN ('', '+32'))
            AND substring(:PhoneFixe, length(:PhoneFixe)-7) IN (
                substring(ADR2.TELNR_LONG, length(ADR2.TELNR_LONG)-7),
                substring(ADR2.TELNR_CALL, length(ADR2.TELNR_CALL)-7)
            )
        ))
		OR UPPER(ADR6.SMTP_ADDR) IN (UPPER(:Email), UPPER(:ContactEmail))
), "search2" AS (
	SELECT AUFNR AS "DossierId"
	FROM {HanaSchema}.AUFK
	JOIN {HanaSchema}.ADRC
		ON CONTAINS(ADRC.NAME1, CONCAT(CONCAT(TRIM(:Firstname),' '),TRIM(:Lastname)), FUZZY(0.85))
		OR CONTAINS(ADRC.NAME1, CONCAT(CONCAT(TRIM(:Lastname),' '),TRIM(:Firstname)), FUZZY(0.85))
	LEFT JOIN {HanaSchema}.ADR2
		ON ADR2.ADDRNUMBER = ADRC.ADDRNUMBER AND ADR2.R3_USER IN ('1','3')
	LEFT JOIN {HanaSchema}.ADR6
		ON ADR6.ADDRNUMBER = ADRC.ADDRNUMBER
	JOIN {HanaSchema}.IHPA
		ON IHPA.OBJNR = AUFK.OBJNR
		AND IHPA.ADRNR = ADRC.ADDRNUMBER
		AND IHPA.OBTYP = 'ORI'
		AND IHPA.PARVW = 'Z2'
		AND (IHPA.KZLOESCH IS NULL OR IHPA.KZLOESCH = '')
	WHERE AUFK.AUART IN ('DRE1', 'DRG1')
		AND ((
            NOT (:Phone IS NULL OR :Phone IN ('', '+32'))
            AND substring(:Phone, length(:Phone)-8) IN (
                substring(ADR2.TELNR_LONG, length(ADR2.TELNR_LONG)-8),
                substring(ADR2.TELNR_CALL, length(ADR2.TELNR_CALL)-8)
            )
        ) OR (
            NOT (:PhoneFixe IS NULL OR :PhoneFixe IN ('', '+32'))
            AND substring(:PhoneFixe, length(:PhoneFixe)-7) IN (
                substring(ADR2.TELNR_LONG, length(ADR2.TELNR_LONG)-7),
                substring(ADR2.TELNR_CALL, length(ADR2.TELNR_CALL)-7)
            )
        ))
		OR UPPER(ADR6.SMTP_ADDR) IN (UPPER(:Email), UPPER(:ContactEmail))
), "search1_only_mail" AS (
	SELECT AUFNR AS "DossierId"
	FROM {HanaSchema}.AUFK
	JOIN {HanaSchema}.ADR6
	    ON UPPER(ADR6.SMTP_ADDR) = UPPER(:Email)
	JOIN {HanaSchema}.BUT000
		ON BUT000.PERSNUMBER = ADR6.PERSNUMBER
	JOIN {HanaSchema}.IHPA
		ON IHPA.OBJNR = AUFK.OBJNR
		AND IHPA.PARNR = BUT000.PARTNER
		AND IHPA.OBTYP = 'ORI'
		AND IHPA.PARVW = 'Z2'
		AND (IHPA.KZLOESCH IS NULL OR IHPA.KZLOESCH = '')
	WHERE AUFK.AUART IN ('DRE1', 'DRG1') AND ADR6.PERSNUMBER != '' AND ADR6.PERSNUMBER IS NOT NULL
), "search2_only_mail" AS (
	SELECT AUFNR AS "DossierId"
	FROM {HanaSchema}.AUFK
	JOIN {HanaSchema}.ADR6
		ON UPPER(ADR6.SMTP_ADDR) = UPPER(:Email)
	JOIN {HanaSchema}.ADRC
		ON ADRC.ADDRNUMBER = ADR6.ADDRNUMBER
	JOIN {HanaSchema}.IHPA
		ON IHPA.OBJNR = AUFK.OBJNR
		AND IHPA.ADRNR = ADRC.ADDRNUMBER
		AND IHPA.OBTYP = 'ORI'
		AND IHPA.PARVW = 'Z2'
		AND (IHPA.KZLOESCH IS NULL OR IHPA.KZLOESCH = '')
	WHERE AUFK.AUART IN ('DRE1', 'DRG1')
), "search3_only_mail" AS (
	SELECT AUFNR AS "DossierId"
	FROM {HanaSchema}.AUFK
	JOIN {HanaSchema}.ADR6
	    ON UPPER(ADR6.SMTP_ADDR) = UPPER(:Email)
	JOIN {HanaSchema}.BUT021_FS
		ON BUT021_FS.ADDRNUMBER = ADR6.ADDRNUMBER 
	JOIN {HanaSchema}.IHPA
		ON IHPA.OBJNR = AUFK.OBJNR
		AND IHPA.PARNR = BUT021_FS.PARTNER
		AND IHPA.OBTYP = 'ORI'
		AND IHPA.PARVW = 'Z2'
		AND (IHPA.KZLOESCH IS NULL OR IHPA.KZLOESCH = '')
	WHERE AUFK.AUART IN ('DRE1', 'DRG1') AND ADR6.ADDRNUMBER != '' AND ADR6.ADDRNUMBER IS NOT NULL
)
SELECT * FROM "search1"
UNION
SELECT * FROM "search2"
UNION
SELECT * FROM "search1_only_mail"
UNION
SELECT * FROM "search2_only_mail"
UNION
SELECT * FROM "search3_only_mail"