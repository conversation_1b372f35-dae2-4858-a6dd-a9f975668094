SELECT AUSP.ATWRT AS PIN
FROM {HanaSchema}.AUSP
WHERE
    AUSP.OBJEK IN (
    SELECT INOB.CUOBJ
    FROM {HanaSchema}.INOB
    INNER JOIN {HanaSchema}.EQUI ON EQUI.EQUNR = INOB.OBJEK
    WHERE INOB.KLART = '002'
  AND INOB.OBTAB = 'EQUI'
  AND EQUI.SERNR = (:sn)
  AND EQUI.EQTYP = 'I'
  AND EQUI.EQART = 'IG_CPT'
    )
  AND AUSP.ATINN IN (
    SELECT CABN.ATINN
    FROM {HanaSchema}.CABN
    WHERE CABN.ATNAM = 'IC_CPT_SHIPMENT_PIN'
    );