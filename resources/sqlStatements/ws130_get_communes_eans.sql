WITH ean_commune AS (
	SELECT DISTINCT
		EXT_UI AS EAN,
		EANL.SPARTE,
	    EANL.ANLAGE,
	    EANL.VSTELLE
	FROM {HanaSchema}.ETTIFN
	JOIN {HanaSchema}.EUIINSTLN ON EUIINSTLN.ANLAGE = ETTIFN.ANLAGE
    JOIN {HanaSchema}.EUITRANS ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
    JOIN {HanaSchema}.EANL ON EANL.ANLAGE = ETTIFN.ANLAGE AND EANL.SPARTE = (CASE WHEN ETTIFN.OPERAND = 'E_COMMUNE' THEN '01' WHEN ETTIFN.OPERAND = 'G_COMMUNE' THEN '02' END)
	WHERE ETTIFN.OPERAND IN ('G_COMMUNE', 'E_COMMUNE') AND ETTIFN.STRING1 = :IdCommune AND ETTIFN.AB <= CURRENT_DATE AND CURRENT_DATE <= ETTIFN.BIS
), get_adress_info AS (
  SELECT DISTINCT
    CITY1,
    POST_CODE1,
    STREET,
    HOUSE_NUM1,
    HAUS_NUM2,
    V<PERSON><PERSON>LE
  FROM {HanaSchema}.EVBS
  LEFT JOIN {HanaSchema}.ILOA ON EVBS.HAUS = ILOA.TPLNR
  LEFT JOIN {HanaSchema}.ADRC ON ILOA.ADRNR = ADRC.ADDRNUMBER
), get_counter_sernr AS (
    SELECT DISTINCT ANLAGE, SERNR,
    	CASE WHEN CAP_ACT_GRP IN('9000','9001', '9002') THEN TRUE ELSE FALSE END AS IS_SMART,
    	(CASE WHEN EASTI.ZWZUART = 04 AND EASTI.OPCODE = 6 THEN true ELSE false END) AS CPT_CONTROL
     FROM (
     	SELECT ANLAGE, LOGIKNR
        FROM {HanaSchema}.EASTL
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
          AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
    ) AS EASTL
    JOIN (
        SELECT
            EQUNR AS EGERH_EQUNR,
            ZWGRUPPE AS EGERH_ZWGRUPPE,
            LOGIKNR AS EGERH_LOGIKNR,
            DEVLOC AS DEVLOC,
            CAP_ACT_GRP
        FROM {HanaSchema}.EGERH
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
    ) AS EGERH
    ON EASTL.LOGIKNR = EGERH.EGERH_LOGIKNR
    JOIN (SELECT * FROM {HanaSchema}.EQUI WHERE EQART != 'IG_CNV') AS EQUI ON EGERH.EGERH_EQUNR = EQUI.EQUNR
    JOIN {HanaSchema}.ETDZ ON ETDZ.EQUNR = EGERH_EQUNR /*AND ETDZ.nablesen != 'X'*/ AND ETDZ.MASSREAD in ('KWH','M3') AND ETDZ.AB <= TO_VARCHAR(current_date, 'YYYYMMDD') AND TO_VARCHAR(current_date, 'YYYYMMDD') <= ETDZ.BIS
	LEFT JOIN {HanaSchema}.EASTI on EASTI.LOGIKZW = ETDZ.LOGIKZW and EASTI.AB <= TO_VARCHAR(current_date, 'YYYYMMDD') and TO_VARCHAR(current_date, 'YYYYMMDD') <= EASTI.BIS
), latest_index AS (
	WITH WS34_GET_INDEX AS (
	SELECT *,
	 MIN(ORDERED_ABLESGR) OVER (PARTITION BY EAN, ETDZ_LOGIKZW, ADAT, ANLAGE_INFO) AS BEST_ABLESGR
	 FROM (
	    ( SELECT
	        EUITRANS.EXT_UI AS EAN,
	        EUITRANS.INT_UI
	      FROM {HanaSchema}.EUITRANS
	      JOIN ean_commune ON ean_commune.EAN = EUITRANS.EXT_UI
	    ) AS EUITRANS
	  INNER JOIN
	    ( SELECT
	      EUIINSTLN.INT_UI,
	      ANLAGE
	      FROM {HanaSchema}.EUIINSTLN
	    ) AS EUIINSTLN
	  ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
	  INNER JOIN
	  (
	    SELECT
	      ANLAGE,
	      SPARTE,
	      VERTRAG,
	      VKONTO,
	      EINZDAT,
	      AUSZDAT,
	      INVOICING_PARTY
	    FROM {HanaSchema}.EVER
	  ) AS EVER
	  ON EUIINSTLN.ANLAGE = EVER.ANLAGE
	  INNER JOIN
	  (
	    SELECT
	      VKONT,
	      GPART
	    FROM {HanaSchema}.FKKVKP
	  ) AS FKKVKP
	  ON FKKVKP.VKONT = EVER.VKONTO
	  INNER JOIN
	  (
	    SELECT
	      PARTNER,
	      UPPER(TRIM(NAME_FIRST)) AS NAME_FIRST,
	      UPPER(TRIM(NAME_LAST)) AS NAME_LAST
	    FROM {HanaSchema}.BUT000
	  ) AS BUT000
	  ON BUT000.PARTNER = FKKVKP.GPART
	  INNER JOIN
	  (
	    SELECT
	      ANLAGE,
	      LOGIKZW,
	      TARIFART,
	      AB AS EASTS_AB,
	      BIS AS EASTS_BIS
	    FROM {HanaSchema}.EASTS
	  ) AS EASTS
	  ON EVER.ANLAGE = EASTS.ANLAGE
	  INNER JOIN
	  (
	    SELECT DISTINCT
	      AKLASSE,
	      ANLAGE AS ANLAGE_INFO,
	      ABLEINH AS EANLH_ABLEINH
	    FROM {HanaSchema}.EANLH
	  ) AS EANLH
	  ON EVER.ANLAGE = EANLH.ANLAGE_INFO
	  INNER JOIN
	  (
	    SELECT
	      LOGIKZW AS ETDZ_LOGIKZW,
	      EQUNR,
	      SPARTYP,
	      ZWNUMMER AS ETDZ_ZWNUMMER,
	      ZWKENN,
	      ZWART,
	      AB AS ETDZ_AB,
	      BIS AS ETDZ_BIS,
	      KENNZIFF
	    FROM {HanaSchema}.ETDZ
	  ) AS ETDZ
	  ON EASTS.LOGIKZW = ETDZ.ETDZ_LOGIKZW
	  INNER JOIN
	  (
	    SELECT
	      EQUNR AS EQUI_EQUNR,
	      SERNR,
	      MATNR
	    FROM {HanaSchema}.EQUI
	  ) AS EQUI
	  ON EQUI.EQUI_EQUNR = ETDZ.EQUNR
	  INNER JOIN
	  (
	    SELECT
	      ABLBELNR,
	      ANLAGE,
	      ABLEINH,
	      ADATSOLL AS DatePlanned,
	      ABLESGR,
	      /* Select in priority index 1 (communique), then 3 (indexier) then 2 (estimé) */
	      (CASE WHEN ABLESGR = '02' THEN '03' ELSE CASE WHEN ABLESGR = '03' THEN '02' ELSE ABLESGR END END) AS ORDERED_ABLESGR
	    FROM {HanaSchema}.EABLG
	  ) AS EABLG
	  ON EABLG.ANLAGE = EVER.ANLAGE AND EABLG.ABLEINH = EANLH.EANLH_ABLEINH
	  INNER JOIN
	  (
	    SELECT
	      GERNR,
	      EQUNR AS EABL_EQUNR,
	      ABLBELNR AS EABL_ABLBELNR,
	      ZWNUMMER,
	      ADATSOLL,
	      SUBSTRING(ADATSOLL, 0, 4) AS ADATSOLL_YEAR,
	      ADAT,
	      V_ZWSTAND,
	      N_ZWSTAND,
	      MASSREAD,
	      MASSBILL,
	      ABLESTYP
	    FROM {HanaSchema}.EABL
	  ) AS EABL
	  ON
	  /* Same transaction */
	  EABLG.ABLBELNR = EABL.EABL_ABLBELNR
	  /* Same counter and cadran: avoids duplicates but then we have "missing data" or incomplete history */
	  AND ETDZ.EQUNR = EABL.EABL_EQUNR
	  AND EQUI.SERNR = EABL.GERNR
	  AND ETDZ.ETDZ_ZWNUMMER = EABL.ZWNUMMER
	  LEFT JOIN(
	    SELECT
	      TERMSCHL,
	      SUBSTRING(TERMTDAT, 0, 4) AS UR_YEAR,
	      SUBSTRING(ADATSOLL, 0, 4) AS UR_ADATSOLL_YEAR,
	      ADATSOLL AS UR_ADATSOLL
	    FROM {HanaSchema}.TE418
	  ) AS TE418
	  ON EABLG.ABLEINH = TE418.TERMSCHL AND ADATSOLL = UR_ADATSOLL
	)
	  WHERE
	    /* Valid equipement */
	    ETDZ_AB <= ADAT
	    AND ADAT <= ETDZ_BIS
	    /* Valid cadran */
	    AND EASTS_AB <= ADAT
	    AND ADAT <= EASTS_BIS
	    /* Cadran in validity of equipement */
	    AND ETDZ_AB <= EASTS_AB
	    AND ETDZ_BIS >= EASTS_BIS
	    /* DateReleve in date contrat */
	    AND EINZDAT <= EABL.ADAT
	    AND EABL.ADAT <= AUSZDAT
	), WS34_SELECT_INDEX AS (
	  SELECT
	    EAN,
	    SPARTE,
	    GPART,
	    NAME_FIRST,
	    NAME_LAST,
	    INVOICING_PARTY,
	    WS34_GET_INDEX.ANLAGE_INFO AS ANLAGE,
	    VERTRAG,
	    EANLH_ABLEINH AS ABLEINH,
	    EASTS_AB,
	    EASTS_BIS,
	    EINZDAT,
	    AUSZDAT,
	    ETDZ_AB,
	    ETDZ_BIS,
	    ETDZ_LOGIKZW AS LOGIKZW,
	    EABL_ABLBELNR AS ABLBELNR,
	    SERNR AS EQUNR_SERNR,
	    GERNR AS EABL_GERNR,
	    EQUNR AS ETDZ_EQUNR,
	    EABL_EQUNR,
	    ETDZ_ZWNUMMER,
	    ZWNUMMER AS EABL_ZWNUMMER,
	    UR_YEAR,
	    ADATSOLL,
	    UR_ADATSOLL,
	    ABS(DAYS_BETWEEN(ADAT, UR_ADATSOLL)) AS DAYS_TO_PLAN,
	    ADAT,
	    ABLESGR,
	   (CASE WHEN ABLESGR = '02' THEN '03' ELSE CASE WHEN ABLESGR = '03' THEN '02' ELSE ABLESGR END END) AS ORDERED_ABLESGR,
	    ABLESTYP,
	    CAST(V_ZWSTAND AS int) AS INDEX,
	    SPARTYP,
	    TARIFART,
	    ZWART,
	    ZWKENN,
	    AKLASSE,
	    MASSBILL,
	    MASSREAD,
	    MATNR,
	    EQUI_EQUNR,
	    KENNZIFF
	    FROM WS34_GET_INDEX
	    WHERE ORDERED_ABLESGR = BEST_ABLESGR AND MASSBILL = 'KWH'
	), WS34_ADD_CONSO AS (
	  SELECT * ,
	     MIN(ORDERED_ABLESGR) OVER (PARTITION BY EAN, UR_YEAR, ETDZ_EQUNR, LOGIKZW) AS BEST_ABLESGR,
	     MIN(DAYS_TO_PLAN) OVER (PARTITION BY EAN, UR_YEAR, ETDZ_EQUNR, LOGIKZW) AS BEST_DAYS_TO_PLAN
	   FROM WS34_SELECT_INDEX
	  INNER JOIN
	    (
	        SELECT SPARTE AS ZISU_SPARTE,
				ZWART AS ZISU_ZWART,
				ZWKENN AS ZISU_ZWKENN,
				AKLASSE AS ZISU_AKLASSE,
				MASSREAD_BIL AS ZISU_MASSREAD,
				TARIFART AS ZISU_TARIFART,
				MAP(element_number,1,'CONS_BILL', 2,'CONS_BILL_COMP', 3,'INJE_BILL', 4,'INJE_BILL_COMP') AS BILL_TYPE,
				MAP(element_number,1,CONS_BILL, 2,CONS_BILL_COMP, 3,INJE_BILL, 4,INJE_BILL_COMP) AS BILL
			FROM {HanaSchema}.ZISU_REG_TYPE, SERIES_GENERATE_INTEGER(1, 1, 5)
			ORDER BY element_number
	    ) AS ZISU_REG_TYPE
	    ON WS34_SELECT_INDEX.SPARTYP = ZISU_REG_TYPE.ZISU_SPARTE
	      AND WS34_SELECT_INDEX.ZWKENN = ZISU_REG_TYPE.ZISU_ZWKENN
	      AND WS34_SELECT_INDEX.AKLASSE = ZISU_REG_TYPE.ZISU_AKLASSE
	      AND WS34_SELECT_INDEX.MASSBILL = ZISU_REG_TYPE.ZISU_MASSREAD
	      AND WS34_SELECT_INDEX.TARIFART = ZISU_REG_TYPE.ZISU_TARIFART
	      AND (KENNZIFF = '' OR (ZISU_REG_TYPE.BILL_TYPE LIKE 'CONS_%' AND KENNZIFF LIKE '1.%') OR (ZISU_REG_TYPE.BILL_TYPE LIKE 'INJE_%' AND KENNZIFF LIKE '2.%'))
	), WS34_CREATE_INTERVALS AS (
	SELECT
	    EAN AS "Ean",
	    NAME_FIRST AS "FirstName",
	    NAME_LAST AS "LastName",
	    MAX(CASE WHEN
	      EINZDAT <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
	      AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= AUSZDAT
	    THEN NAME_FIRST
	    ELSE NULL
	    END) OVER (PARTITION BY EAN)  AS "CurrentFirstName",
	    MAX(CASE WHEN
	      EINZDAT <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
	      AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= AUSZDAT
	    THEN NAME_LAST
	    ELSE NULL
	    END) OVER (PARTITION BY EAN) AS "CurrentLastName",
	    VERTRAG AS "Contrat",
	    EINZDAT AS "DebutContrat",
	    AUSZDAT AS "FinContrat",
	    EQUNR_SERNR AS "NumCompteur",
	    LOGIKZW AS "NumCadran",
	    ZWART AS "CatCadran",
	    ADAT AS "DateRel",
	    INDEX AS "Index",
	    MASSREAD AS "IndexUnit",
	    ABLESTYP AS "IndexQual",
	    TARIFART AS "CatTarif",
	    CASE WHEN
	      (ORDERED_ABLESGR = BEST_ABLESGR AND DAYS_TO_PLAN = BEST_DAYS_TO_PLAN)
	      OR (ABLESGR = '01' AND ADATSOLL = UR_ADATSOLL)
	    THEN ADAT
	    ELSE NULL
	    END
	    AS "intervalEnd",
	    MASSBILL AS "QttUnit",
	    ORDERED_ABLESGR,
	    BEST_ABLESGR,
	    DAYS_TO_PLAN,
	    ETDZ_ZWNUMMER,
	    MATNR,
	    EQUI_EQUNR,
	    CASE WHEN
	      (ORDERED_ABLESGR = BEST_ABLESGR AND DAYS_TO_PLAN = BEST_DAYS_TO_PLAN)
	      OR (ABLESGR = '01' AND ADATSOLL = UR_ADATSOLL)
	    THEN TRUE
	    ELSE FALSE
	    END AS CHOOSE_INDEX,
	    ANLAGE,
	    BILL,
	    ADAT,
	    KENNZIFF
	FROM WS34_ADD_CONSO
	), WS34_POPULATE_INTERVALS AS (
	    SELECT
	      *,
	      MAX("intervalEnd") OVER (PARTITION BY "Ean", "nextInterval") AS "DateQtt",
	      MAX("DateRel") OVER (PARTITION BY "Ean") AS LAST_DATE
	    FROM (
	      SELECT
	        *,
	        MIN(CASE WHEN "intervalEnd" IS NOT NULL THEN "DateRel" END) OVER (PARTITION BY "Ean" ORDER BY "DateRel" DESC) AS "nextInterval"
	      FROM WS34_CREATE_INTERVALS
	      JOIN
	        (
	          SELECT ANLAGE AS QTT_ANLAGE, BIS AS QTT_DATE, OPERAND, WERT1 AS "Qtt"
	          FROM {HanaSchema}.ETTIFN
	          WHERE INAKTIV != 'X'
	        ) AS ETTIFN
	        ON ANLAGE = ETTIFN.QTT_ANLAGE
	        AND BILL = ETTIFN.OPERAND
	        AND ADAT = ETTIFN.QTT_DATE
	      WHERE ORDERED_ABLESGR IN ('01', '02', '03')
	     ) t
	), COUNT_ENTRY AS (
      SELECT a.equnr, COUNT(*) AS "lv_nb_lines"
      FROM {HanaSchema}.EQUI AS a
        INNER JOIN {HanaSchema}.ETDZ AS b ON a.equnr = b.equnr AND b.nablesen != 'X' AND b.ab  <= CURRENT_DATE AND b.bis >= CURRENT_DATE
        INNER JOIN {HanaSchema}.ETYP AS i ON  i.matnr = a.matnr
        INNER JOIN {HanaSchema}.EZWG AS h ON h.zwgruppe = i.zwgruppe AND h.zwnummer = b.zwnummer
      GROUP BY a.equnr
    ), ANZART AS (
      SELECT h.anzart, b.zwnummer, a.equnr, 
    	CASE WHEN CAP_ACT_GRP IN('9000','9001', '9002') THEN TRUE ELSE FALSE END AS "Smart"
      FROM {HanaSchema}.EQUI AS a
      INNER JOIN {HanaSchema}.ETDZ AS b ON a.equnr = b.equnr
      INNER JOIN {HanaSchema}.EASTS AS c ON b.logikzw = c.logikzw AND c.ab <= CURRENT_DATE AND c.bis >= CURRENT_DATE
      INNER JOIN {HanaSchema}.EGERH AS i ON i.equnr = a.equnr AND i.ab <= CURRENT_DATE AND i.bis >= CURRENT_DATE
      INNER JOIN {HanaSchema}.EZWG AS h ON h.zwgruppe = i.zwgruppe AND h.zwnummer = b.zwnummer
    ), ATWRT AS (
        SELECT AUSP.atwrt, AUSP.objek
        FROM {HanaSchema}.AUSP
        INNER JOIN {HanaSchema}.KSSK ON KSSK.objek = AUSP.objek AND KSSK.klart = '001'
        INNER JOIN {HanaSchema}.CABN ON CABN.atinn = AUSP.atinn AND CABN.atnam = 'IE_TA_TYPE_CPT'
        INNER JOIN {HanaSchema}.KLAH ON KLAH.clint = KSSK.clint AND KLAH.class = 'IE_TA_GEN'
        WHERE AUSP.klart = '001'
    )
	SELECT DISTINCT
	  "Ean",
	  "FirstName",
	  "LastName",
	  "CurrentFirstName",
	  "CurrentLastName",
	  LTRIM("NumCompteur", '0') AS "NumCompteur",
	  "CatCadran",
	  "DateRel",
	  "Index",
	  "IndexUnit",
      "CatTarif",
	  ETDZ_ZWNUMMER,
	  MATNR,
	  EQUI_EQUNR,
	  "lv_nb_lines",
	  ATWRT,
	  KENNZIFF AS "CodeCadran",
	  "Smart"
	FROM WS34_POPULATE_INTERVALS
    LEFT OUTER JOIN COUNT_ENTRY ON COUNT_ENTRY.equnr = WS34_POPULATE_INTERVALS.EQUI_EQUNR
    LEFT OUTER JOIN ANZART ON ANZART.equnr = WS34_POPULATE_INTERVALS.EQUI_EQUNR AND ANZART.zwnummer = WS34_POPULATE_INTERVALS.ETDZ_ZWNUMMER
    LEFT OUTER JOIN ATWRT ON ATWRT.objek = WS34_POPULATE_INTERVALS.matnr
	WHERE "CurrentFirstName" = "FirstName" AND "CurrentLastName" = "LastName" AND "DateRel" = LAST_DATE
	ORDER BY "Ean","DateRel" DESC, "NumCompteur", "CodeCadran"
), ean_info AS (
	SELECT DISTINCT
		ec.EAN AS "Ean",
		CASE WHEN SPARTE = '01' THEN 'Elec' WHEN SPARTE = '02' THEN 'Gaz' END AS "Type",
		LTRIM(SERNR, '0') AS "NumCpt",
	    IS_SMART AS "Smart",
	    STREET AS "Adresse_Rue",
	    HOUSE_NUM1 AS "Adresse_NumRue",
	    HAUS_NUM2 AS "Adresse_NumComp",
	    POST_CODE1 AS "Adresse_CodePostal",
	    CITY1 AS "Adresse_Localite"
	FROM ean_commune ec
	JOIN get_counter_sernr gcs ON gcs.ANLAGE = ec.ANLAGE
	JOIN get_adress_info gai ON gai.VSTELLE = ec.VSTELLE
	WHERE (:Smart IS NULL OR IS_SMART = :Smart) AND CPT_CONTROL = FALSE
)
SELECT *, (
    	SELECT *
    	FROM latest_index
    	WHERE latest_index."Ean" = ean_info."Ean" AND latest_index."NumCompteur" = ean_info."NumCpt"
    	FOR JSON
    ) AS "Index"
FROM ean_info