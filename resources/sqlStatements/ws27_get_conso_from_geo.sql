with get_point_conso_from_geo as (
  (select VSTELLE, <PERSON><PERSON><PERSON> from
    (select * from
    (select
      ADDRNUMBER,
        replace(
          replace(
          REPLACE_REGEXPR('[`,|!+-.<=>^_`|~()\\$;:/''"@&%#]+' in
            replace(
                replace(
                  replace(
                    replace(
                      replace(
                        replace(
                          replace(
                            replace(
                              replace(
                                replace(
                                  replace(
                                    replace(
                                      replace(
                                        replace(
                                          replace(upper(trim(STREET)),
                                            'É', 'E') ,
                                            'Á', 'A'),
                                            'Í', 'I'),
                                            'Ó', 'O'),
                                            'Ú', 'U'),
                                            'È', 'E') ,
                                            'À', 'A'),
                                            'Ì', 'I'),
                                            'Ò', 'O'),
                                            'Ù', 'U'),
                                            'Ê', 'E'),
                                            'Â', 'A'),
                                            'Î', 'I'),
                                            'Ô', 'O'),
                                            'Û', 'U')
          with ' ' OCCURRENCE ALL),
        '  ', ' '),
      '  ', ' ') AS STREET_CLEAN,
        replace(
          replace(
          REPLACE_REGEXPR('[`,|!+-.<=>^_`|~()\\$;:/''"@&%#]+' in
            replace(
                replace(
                  replace(
                    replace(
                      replace(
                        replace(
                          replace(
                            replace(
                              replace(
                                replace(
                                  replace(
                                    replace(
                                      replace(
                                        replace(
                                          replace(upper(trim(:Rue)),
                                            'É', 'E') ,
                                            'Á', 'A'),
                                            'Í', 'I'),
                                            'Ó', 'O'),
                                            'Ú', 'U'),
                                            'È', 'E') ,
                                            'À', 'A'),
                                            'Ì', 'I'),
                                            'Ò', 'O'),
                                            'Ù', 'U'),
                                            'Ê', 'E'),
                                            'Â', 'A'),
                                            'Î', 'I'),
                                            'Ô', 'O'),
                                            'Û', 'U')
          with ' ' OCCURRENCE ALL),
        '  ', ' '),
      '  ', ' ') AS RUE_CLEAN,
      UPPER(TRIM(REPLACE(HOUSE_NUM1, ' ', ''))) AS NUM_CLEAN
    FROM {HanaSchema}.ADRC
    WHERE POST_CODE1 = (:Cdpostal) AND (CITY1 = (:Localite) OR (:Localite) = '')) AS T
    WHERE STREET_CLEAN = (:RueNum) OR (STREET_CLEAN = RUE_CLEAN AND NUM_CLEAN = UPPER(TRIM(REPLACE(:NumRue, ' ', ''))))) AS ADRC
  INNER JOIN
    (select TPLNR, ADRNR
    from {HanaSchema}.ILOA) AS ILOA
  ON ILOA.ADRNR = ADRC.ADDRNUMBER
  INNER JOIN
    (select HAUS, VSTELLE as EVER_VSTELLE
    from {HanaSchema}.EVBS
    ) AS EVBS
  ON EVBS.HAUS = ILOA.TPLNR
  INNER JOIN
    (select ANLAGE, VSTELLE
    from {HanaSchema}.EANL
    ) AS EANL
  ON EANL.VSTELLE = EVBS.EVER_VSTELLE
    INNER JOIN
    (select ANLAGE as EVER_ANLAGE
    from {HanaSchema}.EVER
    where EINZDAT <= TO_VARCHAR(current_date, 'YYYYMMDD') and TO_VARCHAR(current_date, 'YYYYMMDD') <= AUSZDAT
    ) AS EVER
  ON EVER.EVER_ANLAGE = EANL.ANLAGE
  )
)
select * from get_point_conso_from_geo