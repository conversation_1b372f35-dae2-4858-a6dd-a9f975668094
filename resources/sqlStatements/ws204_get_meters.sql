WITH get_ean_myre AS (
  SELECT
    EVER.ANLAGE,
    BUT000.PARTNER AS MYRE
  FROM {HanaSchema}.EVER
  JOIN {HanaSchema}.FKKVKP ON FKKVKP.VKONT = EVER.VKONTO
  JOIN {HanaSchema}.BUT050 ON BUT050.PARTNER2 = FKKVKP.GPART AND BUT050.RELTYP = 'BUR998'
  JOIN {HanaSchema}.BUT000 ON BUT000.PARTNER = BUT050.PARTNER1 AND BUT000.BPKIND = 'MYRE' AND XDELE = '' AND XBLCK = ''
  WHERE AUSZDAT >= NOW()
)
   , get_ean_info AS (
  SELECT DISTINCT
    ADRC.CITY1,
    ADRC.POST_CODE1,
    ADRC.STREET,
    ADRC.HOUSE_NUM1,
    get_ean_myre.MYRE 
  FROM {HanaSchema}.EUITRANS
  JOIN {HanaSchema}.EUIINSTLN ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
  JOIN {HanaSche<PERSON>}.EANL ON EANL.ANLAGE = EUIINSTLN.ANLAGE
  JOIN {HanaSchema}.EVBS ON EVBS.VSTELLE = EANL.VSTELLE
  JOIN {HanaSchema}.ILOA ON EVBS.HAUS = ILOA.TPLNR
  JOIN {HanaSchema}.ADRC ON ILOA.ADRNR = ADRC.ADDRNUMBER
  JOIN {HanaSchema}.EASTL ON EASTL.ANLAGE = EUIINSTLN.ANLAGE
  JOIN {HanaSchema}.EGERH ON EASTL.LOGIKNR = EGERH.LOGIKNR 
  JOIN {HanaSchema}.EQUI ON EGERH.EQUNR = EQUI.EQUNR
  JOIN get_ean_myre ON get_ean_myre.ANLAGE = EUIINSTLN.ANLAGE
  WHERE EXT_UI = :Ean
    AND LTRIM(EQUI.SERNR, '0') = LTRIM(:MeterId, '0')
)
   , get_ean_from_address AS (
  SELECT DISTINCT
    EUITRANS.EXT_UI AS EAN,
    EUITRANS.INT_UI,
    EUIINSTLN.ANLAGE,
    EANL.SPARTE,
    ETINS.EQUNR,
    EQUI.EQART,
    EANL.VSTELLE,
    get_ean_myre.MYRE
  FROM get_ean_info
  JOIN {HanaSchema}.ADRC ON
    ADRC.CITY1 = get_ean_info.CITY1
      AND ADRC.POST_CODE1 = get_ean_info.POST_CODE1
      AND ADRC.STREET = get_ean_info.STREET
      AND ADRC.HOUSE_NUM1 = get_ean_info.HOUSE_NUM1
  JOIN {HanaSchema}.ILOA ON ILOA.ADRNR = ADRC.ADDRNUMBER
  JOIN {HanaSchema}.EVBS ON EVBS.HAUS = ILOA.TPLNR
  JOIN {HanaSchema}.EANL ON EANL.VSTELLE = EVBS.VSTELLE
  JOIN {HanaSchema}.EUIINSTLN ON EANL.ANLAGE = EUIINSTLN.ANLAGE
  JOIN {HanaSchema}.EUITRANS ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
  JOIN {HanaSchema}.ETINS ON EANL.VSTELLE = ETINS.VSTELLE
  JOIN get_ean_myre ON get_ean_myre.ANLAGE = EUIINSTLN.ANLAGE AND get_ean_myre.MYRE = get_ean_info.MYRE
  JOIN {HanaSchema}.EQUI ON ETINS.EQUNR = EQUI.EQUNR
  WHERE ((EANL.SPARTE = '01' AND EQUI.EQART LIKE '%IE_%') OR (EANL.SPARTE = '02' AND EQUI.EQART LIKE '%IG_%'))
    AND EUITRANS.EXT_UI NOT LIKE '%_F'
)
SELECT
  SUM(WERT1) AS "TotalPowerElec"
FROM get_ean_from_address
JOIN {HanaSchema}.ETTIFN ON ETTIFN.ANLAGE = get_ean_from_address.ANLAGE
WHERE AB <= CURRENT_DATE
  AND CURRENT_DATE <= BIS
  AND OPERAND = 'E_PCP'