with adresseNumbers as (
select <PERSON><PERSON><PERSON><PERSON>, AD<PERSON><PERSON> from {HanaSchema}.IHPA
WHERE PARVW = 'ZP'
),
adresses as (
 select ADDRNUMBER, STREET as "Street",HOUSE_NUM1 as "Numero", MC_CITY1 as "Localite", POST_CODE1 as "PostCode"
 from {HanaSchema}.ADRC
 where DATE_TO >= CURRENT_DATE
),
powalcoNumbers as (
 select ATWRT, OBJEK from {HanaSchema}.AUSP
 where ATINN = (select ATINN FROM {HanaSchema}.CABN where ATNAM = 'I_NUMERO_POWALCO')
),
chantiers as (
    SELECT adresses."PostCode" as "PostCode", adresses."Street" as "Street",adresses."Numero" as "Numero", adresses."Localite" as "Localite", LFA1.name1 as "NomFournisseur", LFA1.LIFNR as "IdFournisseur", SMTP_ADDR as "EmailFournisseur", NPLNR as "<PERSON>um<PERSON><PERSON><PERSON>", AUFNT as "<PERSON>seauAM<PERSON>", ATWRT as "Id<PERSON>owal<PERSON>"  FROM {HanaSchema}.LFA1
    LEFT OUTER JOIN  {HanaSchema}.ADR6 ON LFA1.ADRNR=ADR6.ADDRNUMBER
    LEFT OUTER JOIN  {HanaSchema}.EKKO ON LFA1.LIFNR=EKKO.LIFNR
    LEFT OUTER JOIN  {HanaSchema}.EKPO ON EKKO.EBELN=EKPO.EBELN
    LEFT OUTER JOIN  {HanaSchema}.EKKN ON EKPO.EBELN=EKKN.EBELN AND EKPO.EBELP=EKKN.EBELP
    LEFT OUTER JOIN  {HanaSchema}.AFKO ON AFKO.AUFNR=EKKN.NPLNR
    LEFT OUTER JOIN  {HanaSchema}.AUFK ON AUFK.AUFNR=EKKN.NPLNR
    LEFT OUTER JOIN  powalcoNumbers ON powalcoNumbers.OBJEK=AFKO.AUFNT
    LEFT OUTER JOIN  {HanaSchema}.PROJ ON PROJ.OBJNR= CONCAT('PD', AFKO.PRONR)
    LEFT OUTER JOIN  adresseNumbers ON PROJ.OBJNR=adresseNumbers.OBJNR
    LEFT OUTER JOIN adresses ON adresses.ADDRNUMBER = adresseNumbers.ADRNR
    WHERE LFA1.LIFNR in (SELECT LIFNR FROM {HanaSchema}.ZPSFOURPW)
),
entrepreneurs as (
    SELECT name1 as "Name", SMTP_ADDR as "Email", ADRNR as "Id", LIFNR as "IdFournisseur"
    FROM {HanaSchema}.LFA1 LEFT OUTER JOIN  {HanaSchema}.ADR6 ON LFA1.ADRNR=ADR6.ADDRNUMBER
    WHERE LIFNR in (SELECT LIFNR FROM {HanaSchema}.ZPSFOURPW)
)
SELECT *, count(*) OVER () as "NbItems" FROM (
    SELECT DISTINCT c."IdPowalco" as "PowalcoId", c."NumReseau" as "Id", c."ReseauAMI", c."Street", c."Numero", c."Localite", c."PostCode"
    from chantiers c
    LEFT JOIN entrepreneurs e ON e."IdFournisseur" = c."IdFournisseur"
    WHERE c."ReseauAMI" is not null and e."Id" = (:EntrepreneurId) and c."IdPowalco" is not null
) ORDER BY "Id" LIMIT (:PageItems) OFFSET (:PageOffset) ;