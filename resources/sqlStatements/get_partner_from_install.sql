WITH get_partner_from_install AS (
    SELECT ANLAGE, PARTNER
    FROM
    (SELECT
      ANLAGE,
      VKONTO
      FROM {HanaSchema}.EVER
      WHERE ANLAGE  = '4100592399' AND AUSZDAT IN
        (SELECT
          MAX(AUSZDAT) FROM
          (SELECT
            AUSZDAT
            FROM {HanaSchema}.EVER WHERE ANLAGE = '4100592399')
        )
    ) AS EVER_RELEVANT
    LEFT JOIN
    (SELECT GPART AS PARTNER, VKONT FROM {HanaSchema}.FKKVKP) AS FKKVKP
    ON EVER_RELEVANT.VKONTO = FKKVKP.VKONT
)
SELECT * FROM get_partner_from_install