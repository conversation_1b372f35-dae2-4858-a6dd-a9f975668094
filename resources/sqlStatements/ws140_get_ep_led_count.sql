WITH select_commune AS (
	SELECT DISTINCT CONCAT('L-',ADRCITY.REGIO<PERSON>O<PERSON>) AS TPLNR
	FROM {HanaSchema}.ADRCITY
	WHERE ADRCITY.COMMU_CODE = :IdCommune
), select_all_post_code AS (
	SELECT DISTINCT POST_CODE1
	FROM select_commune
	JOIN {HanaSchema}.IFLOT ON IFLOT.TPLMA = select_commune.TPLNR
	JOIN {HanaSchema}.ILOA on ILOA.ILOAN = IFLOT.ILOAN
	JOIN {HanaSchema}.ADRC on ADRC.ADDRNUMBER = ILOA.ADRNR
	WHERE POST_CODE1 != ''
), get_equipment AS (
	SELECT DISTINCT ADRC.POST_CODE1, EQUI.EQUNR
	FROM {HanaSchema}.EQUI
	JOIN {HanaSchema}.EQUZ on EQUZ.EQUNR = EQUI.EQUNR
	JOIN {HanaSchema}.ILOA on ILOA.ILOAN = EQUZ.ILOAN
	JOIN {HanaSchema}.ADRC on ADRC.ADDRNUMBER = ILOA.ADRNR
	JOIN {HanaSchema}.JEST on JEST.OBJNR = EQUI.OBJNR
	JOIN select_all_post_code on select_all_post_code.POST_CODE1 = ADRC.POST_CODE1
	WHERE EQUI.EQART = 'EP_LUMIN' AND EQUZ.DATBI = '99991231' AND JEST.STAT = 'E0004' AND JEST.INACT = ''
	/*
INPUT: dans la table EQUI, EQUI-EQART = EP_LUMIN
OUTPUT: EQUI-EQUNR

INPUT: EQUI-EQUNR dans EQUZ-EQUNR avec EQUZ-DATBI = 31.12.9999
OUTPUT:EQUZ-ILOAN

INPUT: EQUZ-ILOAN dans ILOA-ILOAN
OUTPUT: ILOA-ADRNR

INPUT: ILOA-ADRNR dans ADRC-ADRNUMBER
OUTPUT: ADRC-POST_CODE1

INPUT: dans la table EQUI, EQUI-EQART = EP_LUMIN
OUTPUT: EQUI-EQUNR

JEST-OBJNR et on regarde si le statut E004 (JEST-STAT) est actif (JEST-INACT = « ») 
*/
)
SELECT
    SUM(CASE WHEN AUSP.ATWRT = '000' THEN 1 ELSE 0 END) AS "NbrEpLed",
    SUM(CASE WHEN AUSP.ATWRT != '000' THEN 1 ELSE 0 END) AS "NbrEpNonLed"
FROM get_equipment
JOIN {HanaSchema}.INOB ON INOB.OBJEK = get_equipment.EQUNR
JOIN {HanaSchema}.KSSK on KSSK.OBJEK = INOB.CUOBJ
JOIN {HanaSchema}.AUSP on AUSP.OBJEK = KSSK.OBJEK
WHERE AUSP.ATINN= '0000000639'
/*
INPUT: EQUI-EQUNR dans KSSK_INOB-INOB_OBJEK
OUPUT: KSSK_INOB_OBJEK

INPUT: KSSK_INOB_OBJEK dans AUSP OBJEK avec  AUSP-ATINN= '0000000639'
OUTPUT: AUSP-ATWRT

Si AUSP-ATWRT= 000 alors LED sinon non LED
*/