SELECT DISTINCT EUITRANS.EXT_UI AS "Ean"
FROM (
    SELECT PARTNER, PERSNUMBER
    FROM {HanaSchema}.BUT000
    WHERE (
	    	(
	    		CONTAINS(NAME_LAST, TRIM(:Lastname), FUZZY(0.85)) 
	    		AND CONTAINS(NAME_FIRST, TRIM(:Firstname), FUZZY(0.85))
    		) OR (
	    		CONTAINS(NAME_LAST, TRIM(:Firstname), FUZZY(0.85))
    			AND CONTAINS(NAME_FIRST, TRIM(:Lastname), FUZZY(0.85))
			) OR (
	    		(
	    			CONTAINS(NAME_LAST, TRIM(CONCAT(CONCAT(TRIM(:Lastname), ' '),TRIM(:Firstname))), FUZZY(0.85))
	    			OR CONTAINS(NAME_LAST, TRIM(CONCAT(CONCAT(TRIM(:Firstname), ' '),TRIM(:Lastname))), FUZZY(0.85))
	    		) AND (
	    			NAME_FIRST IS NULL OR TRIM(NAME_FIRST) = ''
	    		)
    		) OR (
	    		(
	    			CONTAINS(NAME_FIRST, TRIM(CONCAT(CONCAT(TRIM(:Lastname), ' '),TRIM(:Firstname))), FUZZY(0.85))
	    			OR CONTAINS(NAME_FIRST, TRIM(CONCAT(CONCAT(TRIM(:Firstname), ' '),TRIM(:Lastname))), FUZZY(0.85))
	    		) AND (
	    			NAME_LAST IS NULL OR TRIM(NAME_LAST) = ''
	    		)
    		)
			OR LOWER(TRIM(CONCAT(CONCAT(TRIM(NAME_LAST), ' '),TRIM(NAME_FIRST)))) = LOWER(TRIM(CONCAT(CONCAT(TRIM(:Lastname), ' '),TRIM(:Firstname))))
			OR LOWER(TRIM(CONCAT(CONCAT(TRIM(NAME_LAST), ' '),TRIM(NAME_FIRST)))) = LOWER(TRIM(CONCAT(CONCAT(TRIM(:Firstname), ' '),TRIM(:Lastname))))
	    )
        AND BPKIND = 'ZMET' AND NOT (PERSNUMBER IS NULL OR PERSNUMBER = '')) AS BUT000
JOIN {HanaSchema}.FKKVKP
	ON FKKVKP.GPART = BUT000.PARTNER
JOIN {HanaSchema}.EVER
	ON EVER.VKONTO = FKKVKP.VKONT
JOIN {HanaSchema}.EUIINSTLN
	ON EUIINSTLN.ANLAGE = EVER.ANLAGE
JOIN {HanaSchema}.EUITRANS
	ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
LEFT JOIN {HanaSchema}.ADR2
	ON ADR2.PERSNUMBER = BUT000.PERSNUMBER AND ADR2.R3_USER IN ('1','3')
LEFT JOIN {HanaSchema}.ADR6
	ON ADR6.PERSNUMBER = BUT000.PERSNUMBER
WHERE
	EVER.AUSZDAT = '99991231'
	AND (((
	    NOT (:Phone IS NULL OR :Phone IN ('', '+32'))
		AND substring(:Phone, LENGTH(:Phone)-8) IN (
		    substring(ADR2.TELNR_LONG, LENGTH(ADR2.TELNR_LONG)-8),
		    substring(ADR2.TELNR_CALL, LENGTH(ADR2.TELNR_CALL)-8)
        )
	) OR (
	    NOT (:PhoneFixe IS NULL OR :PhoneFixe IN ('', '+32'))
		AND substring(:PhoneFixe, LENGTH(:PhoneFixe)-7) IN (
		    substring(ADR2.TELNR_LONG, LENGTH(ADR2.TELNR_LONG)-7),
		    substring(ADR2.TELNR_CALL, LENGTH(ADR2.TELNR_CALL)-7)
        )
	))
	OR UPPER(ADR6.SMTP_ADDR) IN (UPPER(:Email), UPPER(:ContactEmail)))