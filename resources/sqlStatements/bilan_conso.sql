(
    SELECT meterid, measuredatetime, measurevalue, readingtypeid, measurestate, 
           ean, readingfrequency, standardizationtimestamp, measureUnit, 'elec' AS type_energie
    FROM {red_table_elec}
    WHERE ean IN {Ean}
    AND measureDateTime IN ({start_date}, {mid_date}, {end_date})
)
UNION ALL
(
    SELECT meterid, measuredatetime, measurevalue, readingtypeid, measurestate,
           ean, readingfrequency, standardizationtimestamp, measureUnit, 'gaz' AS type_energie
    FROM {red_table_gaz}
    WHERE ean IN {Ean}
    AND measureDateTime IN ({start_date_6}, {mid_date_6}, {end_date_6})
 )
ORDER BY ean, readingtypeid, measuredatetime;
