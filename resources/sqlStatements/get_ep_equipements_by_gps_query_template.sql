with
preselect_avis_ep as (
  select Qm<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON>tStr ,to_date(<PERSON><PERSON><PERSON>, 'yyyyMMdd') as <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as AvisAdrnr, Aufnr
    from {HanaSchema}.Qmel
   where (Qmart = 'E1' or Qmart = 'E2') and <PERSON><PERSON><PERSON> in ('8', '9')
),
keep_avis_ep_only as (
  select <PERSON><PERSON>, <PERSON><PERSON><PERSON> as EquipementAdrnr
    from {HanaSchema}.Iloa
    where upper(LEFT(Tplnr, 1)) = 'L'
),
get_avis as (
    select *
    from preselect_avis_ep
    inner join
      (select Qmnum as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> from {HanaSchema}.Qmih) as avis_header
    on preselect_avis_ep.Qmnum = avis_header.QmnnumHeader
    inner join
      (select <PERSON>oan as PanneIloan, Tplnr, <PERSON>rn<PERSON> as EquipementAdrnr from {HanaSchema}.Iloa) as Iloa
    on avis_header.Iloan = Iloa.PanneIloan
),
get_avis_adresse as (
  select *,
    case when AvidAdrc.Street is not null and AvidAdrc.Street != '' then AvidAdrc.Street when PanneAdrc.Street is not null and PanneAdrc.Street != '' then PanneAdrc.Street else null end as avis_rue,
    case when AvidAdrc.Post_Code1 is not null and AvidAdrc.Post_Code1 != '' then AvidAdrc.Post_Code1 when PanneAdrc.Post_Code1 is not null and PanneAdrc.Post_Code1 != '' then PanneAdrc.Post_Code1 else null end as avis_zipcode,
    case when AvidAdrc.City1 is not null and AvidAdrc.City1 != '' then AvidAdrc.City1 when PanneAdrc.City1 is not null and PanneAdrc.City1 != '' then PanneAdrc.City1 else null end as avis_ville,
    case when AvidAdrc.HOUSE_NUM1 is not null and AvidAdrc.HOUSE_NUM1 != '' then AvidAdrc.HOUSE_NUM1 when PanneAdrc.HOUSE_NUM1 is not null and PanneAdrc.HOUSE_NUM1 != '' then PanneAdrc.HOUSE_NUM1 else null end as avis_num
  from get_avis
  left join {HanaSchema}.ADRC AS AvidAdrc on get_avis.EquipementAdrnr = AvidAdrc.Addrnumber
  left join {HanaSchema}.ADRC AS PanneAdrc on get_avis.AvisAdrnr = PanneAdrc.Addrnumber
),
equipment_coordinates as (
  select CoordinatesEqunr, equipment_attributes.Cuobj, filtered_locations.ColambX, filtered_locations.ColambY
   from
     (select Equnr as CoordinatesEqunr, Eqart
        from {HanaSchema}.Equi
       where Eqart = 'EP_LUMIN'
      ) as  ep_lumin
      inner join
      (select Objek, Cuobj
           from {HanaSchema}.Inob
      ) as equipment_attributes
      on ep_lumin.CoordinatesEqunr = equipment_attributes.Objek
      inner join
      (select * from
        (select Objek,
                sum(case when Atinn = 0000001210 then atflv else null end) as ColambX,
                sum(case when Atinn = 0000001113 then atflv else null end) as ColambY
          from {HanaSchema}.Ausp
         group by Objek
      ) as attributes
        where ColambX != 0 or ColambY != 0
      ) as filtered_locations
      on equipment_attributes.Cuobj = filtered_locations.Objek
),
deduplicated_equipments as (
  select CoordinatesEqunr, ColambX, ColambY, Iloan, type, sous_type, illumination_type
    from
    (
      select CoordinatesEqunr,
             ColambX,
             ColambY,
             Iloan,
             Zhe_ep_a_type_a as sous_type,
             Zhe_ep_a_nature as type,
             Zhe_ep_l_famill as illumination_type,
             to_date(Datbi, 'yyyyMMdd') as DatbiDat,
             Eqlfn,
             max(to_date(Datbi, 'yyyyMMdd')) over (partition by Equnr) as MaxDatbi,
             max(Eqlfn) over (partition by Equnr, Datbi) as MaxEqlfn
      from
      (
        select *
          from equipment_coordinates
          left join
          (
            select Equnr, Iloan, Datbi, Eqlfn, Zhe_ep_a_type_a, Zhe_ep_a_nature, Zhe_ep_l_famill
              from {HanaSchema}.EQUZ
          ) as Assets
          on equipment_coordinates.CoordinatesEqunr = Assets.Equnr
        ) as EquipmentsWithDuplicates
    )
  where DatbiDat = MaxDatbi and Eqlfn = MaxEqlfn
),
equipments_pannes as (
  select CoordinatesEqunr as ep_id,
    colambx as gps_lat,
    colamby as gps_long,
    EquipementRue as rue,
    EquipementVille as ville,
    EquipementZipcode as zipcode,
    EquipementNumber as numero,
          type, sous_type, illumination_type,
          (case when HeaderAvisId is not null then HeaderAvisId else get_avis_adresse.Qmnum end) as HeaderAvisId, Erdat as DateAvis, ErdatStr, Objnr as AvisObjnr, Aufnr,
          deduplicated_equipments.Iloan
    from deduplicated_equipments
  left join
      keep_avis_ep_only
  on deduplicated_equipments.Iloan = keep_avis_ep_only.Iloan
  left join
    (select adrc.Addrnumber as EquipementAdrnr, Street as EquipementRue, City1 as EquipementVille, Post_Code1 as EquipementZipcode, remark as EquipementNumber
        from {HanaSchema}.Adrc
        left join {HanaSchema}.adrct on adrct.ADDRNUMBER = adrc.ADDRNUMBER
    ) AS equipment_adresses
  ON equipment_adresses.EquipementAdrnr = keep_avis_ep_only.EquipementAdrnr
  LEFT JOIN
    (select Qmnum as HeaderAvisId, Equnr as AvisEqunr from {HanaSchema}.Qmih) AS avis_header
  ON deduplicated_equipments.CoordinatesEqunr = avis_header.AvisEqunr
  LEFT JOIN
    get_avis_adresse
  ON avis_header.HeaderAvisId = get_avis_adresse.Qmnum OR (
  	get_avis_adresse.avis_rue = EquipementRue
	AND get_avis_adresse.avis_zipcode = EquipementZipcode
	AND get_avis_adresse.avis_ville = EquipementVille
	AND (
		get_avis_adresse.avis_num = EquipementNumber
		OR (EquipementNumber LIKE '%-%' AND get_avis_adresse.avis_num >= SUBSTR_BEFORE(EquipementNumber,'-') AND get_avis_adresse.avis_num <= SUBSTR_AFTER(EquipementNumber,'-'))
		OR EquipementNumber LIKE '%' || get_avis_adresse.avis_num || '%'
		)
	)
),
equipement_last_statut AS (
  select ep_id, gps_lat, gps_long, rue, ville, zipcode, numero,
      type, sous_type, illumination_type,
       HeaderAvisId, DateAvis,  ErdatStr, Aufnr,
       Stat as AvisStat, Udate as StatutDate, Utime as StatutTime, StatutTimestamp
  from
  (
    select *,
            max(Chgnr) over (partition by Objnr, ep_id) MaxChgnr,
            max(StatutTimestamp) over (partition by ep_id) MaxStatutTimestamp
      from
      (
        select *,
              to_timestamp(concat(concat(Udate, ' '), Utime), 'yyyyMMdd HH24MISS') StatutTimestamp
        from
        (
          select *
            from equipments_pannes
            left join
              (select Objnr, Chgnr, Stat, Inact, Udate, Utime from {HanaSchema}.JCDS) as StatusTable
            on equipments_pannes.AvisObjnr = StatusTable.Objnr
        ) as EquipmentsFullStatusHistory
        where Objnr is null or (LEFT(Objnr, 2) = 'QM' and Stat != 'E0006' and trim(Inact) = '')
      ) as EquipmentStatusHistory
  )
  where Objnr is null or (Chgnr = MaxChgnr and StatutTimestamp = MaxStatutTimestamp)
),
equipment_related_order AS (
  select *
    from equipement_last_statut
  left join
  (
    select Aufnr, Objnr as OrderObjnr
      from {HanaSchema}.Aufk
  ) as Orders
    on equipement_last_statut.Aufnr = Orders.Aufnr
),
equipement_last_statut_order AS (
  select ep_id, gps_lat, gps_long, rue, ville, zipcode, numero,
          type, sous_type, illumination_type,
         HeaderAvisId, DateAvis,  ErdatStr,
         AvisStat, StatutDate, StatutTime, StatutTimestamp,
         OrderStat, OrderDate, OrderTime, OrderTimestamp
    from
    (
      select *,
              max(Chgnr) over (partition by Objnr, ep_id) MaxChgnr,
              max(OrderTimestamp) over (partition by ep_id) MaxOrderTimestamp
        from
        (
          select *,
                to_timestamp(concat(concat(OrderDate, ' '), OrderTime), 'yyyyMMdd HH24MISS') OrderTimestamp
          from
          (
            select *
              from equipment_related_order
              left join
                (select Objnr, Chgnr, Stat as OrderStat, Inact, Udate as OrderDate, Utime as OrderTime from {HanaSchema}.JCDS) as OrderTable
              on equipment_related_order.OrderObjnr = OrderTable.Objnr
              where Objnr is null or (LEFT(Objnr, 2) = 'OR' and OrderStat = 'I0009' and trim(Inact) = '')
          ) as EquipmentsFullStatusHistory
        ) as EquipmentStatusHistory
    )
    where Objnr is null or (Chgnr = MaxChgnr and OrderTimestamp = MaxOrderTimestamp)
)

select count(*) over () as nb_items, *
  from
  (
    select  EQKTU as id,
            type,
            sous_type,
            illumination_type,
            rue,
            zipcode,
            ville,
            IFLOTX.TPLNR as city_code,
            numero,
            gps_long as long,
            gps_lat as lat,
            ErdatStr as date_creation,
            DateAvis as Erdat,
            AvisStat as id_statut,
            StatutDate as date_statut,
            StatutTime as heure_statut,
            HeaderAvisId as avis_id,
            OrderDate as order_date,
            OrderTime as order_time,
            StatutTimestamp as StatutTimestamp,
            count(AvisStat) over (partition by ep_id) as NbAvis,
            count(OrderStat) over (partition by ep_id) as NbOrder,
            max(DateAvis) over (partition by ep_id) as MaxDateAvis,
            max(StatutTimestamp) over (partition by DateAvis, ep_id) as MaxStatutTimestamp,
            max(HeaderAvisId) over (partition by DateAvis, StatutTimestamp, ep_id) as MaxAvisId,
            TJ02T.TXT04 as descr,
			TJ02T.TXT30  as short_descr
      from equipement_last_statut_order
      join {HanaSchema}.EQKT ON ep_id = EQUNR
      left join {HanaSchema}.TJ02T on TJ02T.ISTAT = AvisStat and TJ02T.SPRAS = 'F'
      left join (select * from {HanaSchema}.IFLOTX where TPLNR like 'L-%-%') as IFLOTX on IFLOTX.PLTXT = ville
      where EQKTU != '00000000' and EQKTU != '' and EQKTU is not null
    ) as T
    where ((:FiltreEnPanneClause))
    and (:DateDebut is null or MaxStatutTimestamp >= to_date(:DateDebut, 'DD/MM/YYYY'))
    and (:DateFin is null or MaxStatutTimestamp <= to_date(:DateFin, 'DD/MM/YYYY'))
    and (:Lat0 is null or lat >= :Lat0)
    and (:Lat1 is null or lat <= :Lat1)
    and (:Long0 is null or long >= :Long0)
    and (:Long1 is null or long <= :Long1)
    order by id
    LIMIT :PageSize
    OFFSET :Offset