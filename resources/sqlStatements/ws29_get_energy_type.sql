WITH
get_energy_type AS (
  SELECT
    EAN,
    SPARTE,
    EANL.ANLAGE,
    EANL.VSTELLE,
    ANLART,
    ETINS_EQUNR,
    ETINS_EQART,
    MANDT
    FROM
    (SELECT
      EXT_UI AS EAN,
      INT_UI
      FROM {HanaSchema}.EUITRANS
      WHERE EXT_UI = (:Ean)
      )AS EUITRANS
  INNER JOIN
  (SELECT
    INT_UI,
    ANLAGE AS EUIINSTLN_ANLAGE
    FROM {HanaSchema}.EUIINSTLN
  )AS EUIINSTLN
  ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
  LEFT JOIN
  (SELECT
    SPARTE,
    ANLART,
    <PERSON>LAGE,
    VSTELLE FROM {HanaSchema}.EANL
  ) AS EANL
  ON EUIINSTLN.EUIINSTLN_ANLAGE = EANL.ANLAGE
  INNER JOIN
  (SELECT MANDT, VSTELLE, EQUNR AS ETINS_EQUNR FROM {HanaSchema}.ETINS) AS ETINS
  ON EANL.VSTELLE = ETINS.VSTELLE
  LEFT JOIN
  (SELECT EQUNR AS EQNUM, EQART AS ETINS_EQART FROM {HanaSchema}.EQ<PERSON>) AS EQUNR_ETINS
  ON ETINS.ETINS_EQUNR = EQUNR_ETINS.EQNUM
)
SELECT * FROM get_energy_type WHERE (SPARTE = '01' AND ETINS_EQART LIKE '%IE_%') OR (SPARTE = '02' AND ETINS_EQART LIKE '%IG_%')