SELECT 
    A004.<PERSON><PERSON><PERSON> AS "Id", 
    MAKT.MAKTX AS "Label", 
    KONP.KBETR AS "Price"
FROM {HanaSchema}.A004
LEFT JOIN {HanaSchema}.MAKT ON A004.MATNR = MAKT.MATNR AND MAKT.SPRAS = LEFT(:Langue, 1)
JOIN 
    {HanaSchema}.KONP ON A004.KNUMH = KONP.KNUMH
WHERE 
    ((A004.MATNR >= 'EB100' AND A004.MATNR <=  'EB121') OR A004.MATNR IN ('EK001','ER015'))
    AND A004.VTWEG = '01'
    AND A004.DATAB < NOW()
    AND A004.DATBI > NOW()