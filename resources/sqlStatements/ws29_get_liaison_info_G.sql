WITH get_liaison_info AS (
  SELECT COUNT(*) AS HAS_VANNE_EXT, VINET_EQUNR_EQUI FROM
  ((SELECT TPNACH, TPKANT, KANTE FROM {HanaSchema}.INET WHERE NETYP = (:SPARTE_LIT) AND TPNACH = (:TPLNR)) AS INET
  LEFT JOIN
  (SELECT TPLNR AS VINET_TPLNR, ILOAN AS VINET_ILOAN FROM {HanaSchema}.ILOA) AS VINET_ILOA
  ON VINET_ILOA.VINET_TPLNR = INET.TPKANT
  LEFT JOIN
  (SELECT EQUNR AS VINET_EQUNR, ILOAN AS VINET_EQUZ_ILOAN
  FROM {HanaSchema}.EQUZ
  WHERE DATAB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= DATBI
  ) AS VINET_EQUZ
  ON VINET_ILOA.VINET_ILOAN = VINET_EQUZ.VINET_EQUZ_ILOAN
  LEFT JOIN
  (SELECT EQUNR AS VINET_EQUNR_EQUI, EQART AS VINET_EQART FROM {HanaSchema}.EQUI) AS VINET_EQUI
  ON VINET_EQUZ.VINET_EQUNR = VINET_EQUI.VINET_EQUNR_EQUI)
  WHERE VINET_EQUNR_EQUI IS NOT NULL
  GROUP BY VINET_EQUNR_EQUI
)
SELECT * FROM get_liaison_info