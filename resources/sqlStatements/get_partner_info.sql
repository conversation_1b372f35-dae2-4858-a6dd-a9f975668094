WITH get_partner_info AS (
    SELECT TOP 1 *
    FROM
    (SELECT
      PARTNER,
      NAME_FIRST AS "Prenom",
      NAME_LAST AS "Nom"
      FROM {HanaSchema}.BUT000
      WHERE PARTNER = :PartenaireId
      ) AS BUT000
    LEFT JOIN
    (SELECT
      PARTNER,
      ADDR<PERSON>MBER
    FROM {HanaSchema}.BUT020) AS BUT020
    ON BUT000.PARTNER = BUT020.PARTNER
    LEFT JOIN
    (SELECT
    ADDRNUMBER AS PARTNER_ADDRNUMBER
    FROM {HanaSchema}.ADRC
    ) AS PARTNER_ADRC
    ON BUT020.ADDRNUMBER = PARTNER_ADRC.PARTNER_ADDRNUMBER
    LEFT JOIN
    (SELECT ADDRNUMBER, TELNR_CALL AS "Tel" FROM {HanaSchema}.ADR2) AS ADR2
    ON BUT020.ADDRNUMBER = ADR2.ADDRNUMBER
    LEFT JOIN
    (SELECT ADDRNUMBER, SMTP_ADDR AS "Email" FROM {HanaSchema}.ADR6) AS ADR6
    ON BUT020.ADDRNUMBER = ADR6.<PERSON><PERSON><PERSON>MBER

)
SELECT "Nom", "Prenom", "Tel", "Email" FROM get_partner_info