WITH
  filter_avis_by_id AS (
    SELECT Qmnum, Objnr, Qmart, Erdat, Qmdab, Aufnr, Adrnr AS PanneAdrnr, Priok
    FROM {HanaSchema}.Qmel
    WHERE Qmnum = LPAD(:id, 12, '0')
  ),
  get_avis AS (
    SELECT *
    FROM filter_avis_by_id
    INNER JOIN
      (SELECT Qmnum AS QmnnumHeader, Equnr, Iloan FROM {HanaSchema}.Qmih) AS avis_header
    ON filter_avis_by_id.Qmnum = avis_header.QmnnumHeader
    INNER JOIN
      (SELECT Iloan AS PanneIloan, Tplnr, Adrnr AS AvisAdrnr FROM {HanaSchema}.Iloa) AS Iloa
    ON avis_header.Iloan = Iloa.PanneIloan
  ),
  get_avis_status AS (
    SELECT Qmnum, Erdat, Equnr, Iloan, Tplnr, Aufnr, Priok,
       Stat AS AvisStat, Udate AS StatutDate, Utime AS StatutTime, StatutTimestamp,
    CASE WHEN AvidAdrc.Street IS NOT NULL AND AvidAdrc.Street != '' THEN AvidAdrc.Street WHEN PanneAdrc.Street IS NOT NULL AND PanneAdrc.Street != '' THEN PanneAdrc.Street ELSE NULL END AS avis_rue,
    CASE WHEN AvidAdrc.Post_Code1 IS NOT NULL AND AvidAdrc.Post_Code1 != '' THEN AvidAdrc.Post_Code1 WHEN PanneAdrc.Post_Code1 IS NOT NULL AND PanneAdrc.Post_Code1 != '' THEN PanneAdrc.Post_Code1 ELSE NULL END AS avis_zipcode,
    CASE WHEN AvidAdrc.City1 IS NOT NULL AND AvidAdrc.City1 != '' THEN AvidAdrc.City1 WHEN PanneAdrc.City1 IS NOT NULL AND PanneAdrc.City1 != '' THEN PanneAdrc.City1 ELSE NULL END AS avis_ville,
    CASE WHEN AvidAdrc.HOUSE_NUM1 IS NOT NULL AND AvidAdrc.HOUSE_NUM1 != '' THEN AvidAdrc.HOUSE_NUM1 WHEN PanneAdrc.HOUSE_NUM1 IS NOT NULL AND PanneAdrc.HOUSE_NUM1 != '' THEN PanneAdrc.HOUSE_NUM1 ELSE NULL END AS avis_num
    FROM
        (
          SELECT *,
                TO_TIMESTAMP(CONCAT(CONCAT(Udate, ' '), Utime), 'yyyyMMdd HH24MISS') StatutTimestamp
          FROM
          (
            SELECT *
              FROM get_avis
              LEFT JOIN
                (SELECT Objnr AS StatusObjnr, Chgnr, Stat, Inact, Udate, Utime FROM {HanaSchema}.JCDS) AS StatusTable
              ON get_avis.Objnr = StatusTable.StatusObjnr
          ) AS EquipmentsFullStatusHistory
          WHERE Stat != 'E0006' AND TRIM(Inact) = ''
        )
	LEFT JOIN {HanaSchema}.ADRC AS AvidAdrc ON AvisAdrnr = AvidAdrc.Addrnumber
	LEFT JOIN {HanaSchema}.ADRC AS PanneAdrc ON PanneAdrnr = PanneAdrc.Addrnumber
  ),
  get_ep_info AS (
  SELECT Equnr AS AssetsEqunr,
                     Datbi,
                     Eqlfn,
                    Zhe_ep_a_type_a AS sous_type,
                    Zhe_ep_a_nature AS type,
                    Zhe_ep_l_famill AS illumination_type,
                    EquipementRue, EquipementVille, EquipementZipcode, EquipementNumber
    FROM {HanaSchema}.EQUZ
    LEFT JOIN (
	    SELECT Iloan AS Iloa_Iloan, Adrnr AS EquipementAdrnr
	    FROM {HanaSchema}.Iloa
	    WHERE UPPER(LEFT(Tplnr, 1)) = 'L'
	) AS Ep ON Iloan = Iloa_Iloan
    LEFT JOIN
    (SELECT adrc.Addrnumber AS EquipementAdrnr, Street AS EquipementRue, City1 AS EquipementVille, Post_Code1 AS EquipementZipcode, remark AS EquipementNumber
        FROM {HanaSchema}.Adrc
        LEFT join {HanaSchema}.adrct ON adrct.ADDRNUMBER = adrc.ADDRNUMBER
    ) AS equipment_adresses
  ON equipment_adresses.EquipementAdrnr = Ep.EquipementAdrnr
  ),
  get_last_equipment_id as (
      SELECT Qmnum, Erdat, Equnr, Iloan, Tplnr, Aufnr, Priok,
            AvisStat, StatutDate, StatutTime, StatutTimestamp,
            type, sous_type, illumination_type, avis_rue, avis_zipcode, avis_ville, avis_num
      FROM
      (
        SELECT *,
               TO_DATE(Datbi, 'yyyyMMdd') AS DatbiDat,
               MAX(TO_DATE(Datbi, 'yyyyMMdd')) OVER (PARTITION BY Equnr) AS MaxDatbi,
               MAX(Eqlfn) OVER (PARTITION BY Equnr, Datbi) AS MaxEqlfn
        FROM
        (
          SELECT *
            FROM get_avis_status
            LEFT JOIN get_ep_info
            ON get_avis_status.Equnr = get_ep_info.AssetsEqunr OR (
			  	get_avis_status.avis_rue = get_ep_info.EquipementRue
				AND get_avis_status.avis_zipcode = get_ep_info.EquipementZipcode
				AND get_avis_status.avis_ville = get_ep_info.EquipementVille
				AND (
					get_avis_status.avis_num = get_ep_info.EquipementNumber
					OR (get_ep_info.EquipementNumber LIKE '%-%' AND get_avis_status.avis_num >= SUBSTR_BEFORE(get_ep_info.EquipementNumber,'-') AND get_avis_status.avis_num <= SUBSTR_AFTER(get_ep_info.EquipementNumber,'-'))
					OR get_ep_info.EquipementNumber LIKE '%' || get_avis_status.avis_num || '%'
				)
			)
          ) AS EquipmentsWithDuplicates
      )
      WHERE (DatbiDat = MaxDatbi OR (DatbiDat IS NULL AND MaxDatbi IS NULL)) AND (Eqlfn = MaxEqlfn OR (Eqlfn IS NULL AND MaxEqlfn IS NULL))
  ),
  get_equipment_coordinates as (
    SELECT * FROM get_last_equipment_id
    LEFT JOIN
      (SELECT Objek, Cuobj
         FROM {HanaSchema}.Inob
      ) AS equipment_attributes
    ON get_last_equipment_id.Equnr = equipment_attributes.Objek
    LEFT JOIN
      (SELECT Objek,
            SUM(CASE WHEN Atinn = 0000001210 THEN atflv ELSE NULL END) AS ColambX,
            SUM(CASE WHEN Atinn = 0000001113 THEN atflv ELSE NULL END) AS ColambY
        FROM {HanaSchema}.Ausp
       GROUP BY Objek
      ) AS attributes
    ON Cuobj = attributes.Objek
  ),
  get_related_orders as (
    SELECT * FROM get_equipment_coordinates
    LEFT JOIN
      (SELECT Aufnr AS OrderAufnr, Objnr AS OrderObjnr FROM {HanaSchema}.Aufk) AS Orders
    ON get_equipment_coordinates.Aufnr = Orders.OrderAufnr
  ),
  last_order_status as (
     SELECT Qmnum, Erdat, Equnr, Iloan, Tplnr, Priok,
            AvisStat, StatutDate, StatutTime, StatutTimestamp,
            OrderStat, OrderDate, OrderTime, OrderTimestamp,
            type, sous_type, illumination_type, ColambX, ColambY, avis_rue, avis_zipcode, avis_ville, avis_num
    FROM
    (
      SELECT *,
              MAX(Chgnr) OVER (PARTITION BY Objnr, Qmnum) MaxChgnr,
              MAX(OrderTimestamp) OVER (PARTITION BY Qmnum) MaxOrderTimestamp
        FROM
        (
          SELECT *,
                TO_TIMESTAMP(CONCAT(CONCAT(OrderDate, ' '), OrderTime), 'yyyyMMdd HH24MISS') OrderTimestamp
          FROM
          (
            SELECT *
              FROM get_related_orders
              LEFT JOIN
                (SELECT Objnr, Chgnr, Stat AS OrderStat, Inact, Udate AS OrderDate, Utime AS OrderTime
                   FROM {HanaSchema}.JCDS
                  WHERE (LEFT(Objnr, 2) = 'OR' AND Stat IN('I0009') AND TRIM(Inact) = '')
                ) AS OrderTable
              ON get_related_orders.OrderObjnr = OrderTable.Objnr
          ) AS PanneOrdersFull
        ) AS PanneOrdersFullConv
    )
    WHERE Objnr IS NULL OR (Chgnr = MaxChgnr AND OrderTimestamp = MaxOrderTimestamp)
  )
SELECT
    qmnum AS id,
    (CASE WHEN Priok IN ('8', '9') THEN true ELSE false END) AS ep,
    avis_rue, avis_zipcode, avis_ville, avis_num,
    Equnr AS equipement_id,
    sous_type,
    type,
    illumination_type,
    ColambX AS long,
    ColambY AS lat,
    Erdat AS date_creation,
    AvisStat AS stat,
    StatutDate AS date_statut,
    StatutTime AS heure_statut,
    OrderDate AS date_order,
    OrderTime AS heure_order,
    (CASE WHEN StatutTimestamp = MAX(StatutTimestamp) OVER (PARTITION BY qmnum) THEN TRUE ELSE FALSE END) AS is_last_statut,
    StatutTimestamp AS statut_timestamp,
    TJ02T.TXT04 AS descr,
    TJ02T.TXT30  AS short_descr
FROM last_order_status
LEFT JOIN {HanaSchema}.TJ02T ON TJ02T.ISTAT = AvisStat AND TJ02T.SPRAS = 'F'
ORDER BY id, StatutTimestamp DESC