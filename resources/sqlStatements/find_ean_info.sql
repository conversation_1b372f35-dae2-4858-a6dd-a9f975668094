WITH
get_install_from_ean AS (
  SELECT ANLAGE AS INSTALL
    FROM
    (SELECT
      EXT_UI AS EAN,
      INT_UI
      FROM {HanaSchema}.EUITRANS
      WHERE EXT_UI = (:Ean)
      )AS EUITRANS
  INNER JOIN
  (SELECT
    INT_UI,
    ANLAGE
    FROM {HanaSchema}.EUIINSTLN
  )AS EUIINSTLN
  ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
),
get_is_compteur_ouvert AS (
  SELECT OBJEK
  FROM
  (SELECT OBJEK AS INPUT_OBJEK, CUOBJ FROM {HanaSchema}.INOB) AS INOB
  INNER JOIN
  (SELECT OBJEK, ATINN, ATFLV, ATWRT FROM {HanaSchema}.AUSP WHERE ATWRT = 'O') AS AUSP
  ON INOB.CUOBJ = AUSP.OBJEK
  INNER JOIN
  (SELECT ATINN AS CABN_ATTIN, ATNAM FROM {HanaSchema}.CABN WHERE ATNAM = 'IC_CPT_STATUT_GRD') AS CABN
  ON AUSP.ATINN = CABN.CABN_ATTIN
),
get_num_cpt_from_install AS (
  SELECT DISTINCT
    NUM_CPT
    FROM
  (SELECT ANLAGE AS EASTL_ANLAGE, LOGIKNR FROM {HanaSchema}.EASTL WHERE ANLAGE = (SELECT * FROM get_install_from_ean)) AS EASTL
  LEFT JOIN
  (SELECT * FROM {HanaSchema}.EZUZ) AS EZUZ
  ON EASTL.LOGIKNR = EZUZ.LOGIKNR2
  LEFT JOIN
  (SELECT EQUNR AS EGERH_EQUNR, ZWGRUPPE AS EGERH_ZWGRUPPE, LOGIKNR AS EGERH_LOGIKNR FROM {HanaSchema}.EGERH) AS EGERH
  ON EASTL.LOGIKNR = EGERH.EGERH_LOGIKNR
  LEFT JOIN
  (SELECT *  FROM {HanaSchema}.EASTS WHERE ANLAGE = (SELECT * FROM get_install_from_ean) AND ZWNABR IS NULL OR ZWNABR = '') AS EASTS
  ON EZUZ.LOGIKZW = EASTS.LOGIKZW
  LEFT JOIN
  (SELECT ZWGRUPPE, COUNT(*) AS N_BILLING FROM {HanaSchema}.EZWG WHERE ZWNABR IS NULL OR ZWNABR = '' GROUP BY ZWGRUPPE) AS EZWG
  ON EGERH.EGERH_ZWGRUPPE = EZWG.ZWGRUPPE
  LEFT JOIN
  (SELECT EQUNR, SERNR AS NUM_CPT FROM {HanaSchema}.EQUI WHERE EQART != 'IG_CNV') AS EQUI
  ON EGERH.EGERH_EQUNR = EQUI.EQUNR
  LEFT JOIN
  (SELECT OBJEK AS INPUT_OBJEK, CUOBJ FROM {HanaSchema}.INOB ) AS INOB
  ON INPUT_OBJEK  = EGERH_EQUNR
  INNER JOIN
  (SELECT OBJEK, ATINN, ATFLV, ATWRT FROM {HanaSchema}.AUSP) AS AUSP
  ON INOB.CUOBJ = AUSP.OBJEK
  INNER JOIN
  (SELECT ATINN AS CABN_ATTIN, ATNAM FROM {HanaSchema}.CABN WHERE ATNAM = 'IC_CPT_STATUT_GRD') AS CABN
  ON AUSP.ATINN = CABN.CABN_ATTIN
  WHERE ATWRT = 'O'
),
get_partner_from_install AS (
    SELECT ANLAGE AS INSTALL, PARTNER
    FROM
    (SELECT
      ANLAGE,
      VKONTO
      FROM {HanaSchema}.EVER
      WHERE ANLAGE  = (SELECT * FROM get_install_from_ean) AND AUSZDAT IN
        (SELECT
          MAX(AUSZDAT) FROM
          (SELECT
            AUSZDAT
            FROM {HanaSchema}.EVER WHERE ANLAGE = (SELECT * FROM get_install_from_ean))
        )
    ) AS EVER_RELEVANT
    LEFT JOIN
    (SELECT GPART AS PARTNER, VKONT FROM {HanaSchema}.FKKVKP) AS FKKVKP
    ON EVER_RELEVANT.VKONTO = FKKVKP.VKONT
)
SELECT
    CAST(PARTNER AS INTEGER) AS "PartenaireId",
    c.NUM_CPT as "Cpt"
FROM get_install_from_ean, get_partner_from_install p, get_num_cpt_from_install c
