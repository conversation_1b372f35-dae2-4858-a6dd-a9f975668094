WITH bp_hierarchy AS (
    SELECT * FROM HIERARCHY(
        SOURCE(
            SELECT PARTNER1 AS parent_id, PARTNER2 AS node_id FROM {HanaSchema}.BUT050 WHERE PARTNER1 = :bp AND RELTYP = 'BUR998'
        )
        START WHERE PARTNER1 in (SELECT PARTNER FROM {HanaSchema}.BUT000 WHERE BPKIND = 'MYRE' AND XDELE = '' AND XBLCK = '')
    )
), find_root as (
    SELECT item.node_id AS bp, root.parent_id AS root FROM bp_hierarchy item JOIN bp_hierarchy root ON root.HIERARCHY_RANK = item.HIERARCHY_ROOT_RANK
    UNION ALL
    SELECT DISTINCT parent_id AS bp, parent_id AS root FROM bp_hierarchy WHERE HIERARCHY_PARENT_RANK = 0
), user_mail AS (
    SELECT DISTINCT ADR6_ConnexionMail.SMTP_ADDR AS "Email"
    FROM (
        SELECT TOP 1 *
        FROM {HanaSchema}.BUT000
        WHERE PARTNER = :bp AND XDELE = '' AND XBLCK = ''
    ) AS BUT000
        LEFT JOIN (
        SELECT *
        FROM {HanaSchema}.BUT021_FS
        WHERE VALID_FROM < CAST(TO_VARCHAR(NOW(), 'YYYYMMDDHHMISS') AS decimal(15)) AND VALID_TO > CAST(TO_VARCHAR(NOW(), 'YYYYMMDDHHMISS') AS decimal(15))
    ) AS BUT021_FS ON BUT021_FS.PARTNER = BUT000.PARTNER
    JOIN (SELECT ADDRNUMBER, SMTP_ADDR FROM {HanaSchema}.ADR6 WHERE VALID_TO = '') AS ADR6_ConnexionMail ON BUT021_FS.ADDRNUMBER = ADR6_ConnexionMail.ADDRNUMBER
), find_user_dossier AS (
    SELECT
        find_root.root AS MYRE,
        find_root.bp AS PARTNER,
        QMEL.AUFNR AS DOSSIER
    FROM find_root
    INNER JOIN {HanaSchema}.IHPA ON find_root.bp = IHPA.PARNR
    INNER JOIN (SELECT * FROM {HanaSchema}.QMEL WHERE AUFNR != '' AND AUFNR IS NOT NULL) AS QMEL ON IHPA.OBJNR = QMEL.OBJNR
), find_dossier_by_mail AS (
	SELECT DISTINCT '' AS MYRE, BUT000.PARTNER, AUFNR AS DOSSIER
	FROM user_mail
	JOIN {HanaSchema}.ADR6
	    ON UPPER(ADR6.SMTP_ADDR) = UPPER("Email")
	LEFT JOIN {HanaSchema}.ADRC
		ON ADRC.ADDRNUMBER = ADR6.ADDRNUMBER AND ADR6.ADDRNUMBER != '' AND ADR6.ADDRNUMBER IS NOT NULL
	LEFT JOIN {HanaSchema}.BUT000
		ON BUT000.PERSNUMBER = ADR6.PERSNUMBER AND ADR6.PERSNUMBER != '' AND ADR6.PERSNUMBER IS NOT NULL
	LEFT JOIN {HanaSchema}.BUT021_FS
		ON BUT021_FS.ADDRNUMBER = ADR6.ADDRNUMBER AND ADR6.ADDRNUMBER != '' AND ADR6.ADDRNUMBER IS NOT NULL
	JOIN {HanaSchema}.IHPA
		ON (IHPA.ADRNR = ADRC.ADDRNUMBER OR IHPA.PARNR = BUT000.PARTNER OR IHPA.PARNR = BUT021_FS.PARTNER)
		AND IHPA.OBTYP = 'ORI'
		AND IHPA.PARVW = 'Z2'
		AND (IHPA.KZLOESCH IS NULL OR IHPA.KZLOESCH = '')
	JOIN {HanaSchema}.AUFK ON AUFK.OBJNR = IHPA.OBJNR
	WHERE AUFK.AUART IN ('DRE1', 'DRG1')
), filter_dossiers as (
    SELECT
        MYRE,
        PARTNER,
        LTRIM(DOSSIER, '0') AS "Dossier"
    FROM (
    	SELECT * FROM find_user_dossier
		UNION 
		SELECT * FROM find_dossier_by_mail
    )
    LEFT JOIN {HanaSchema}.AUFK ON AUFK.AUFNR = DOSSIER
    LEFT JOIN {HanaSchema}.AFKO ON AFKO.AUFNR = DOSSIER
    LEFT JOIN {HanaSchema}.AFIH ON AFIH.AUFNR = DOSSIER
    WHERE AUFK.AUART IN ('DRE1', 'DRG1')
        AND (AUFK.IDAT2 = '00000000' OR ADD_YEARS(CURRENT_DATE, -1) <= AUFK.IDAT2)
        AND AFIH.ILART IN ('RT1','RT2','R01','R02','R03','R04','R09')
        AND AFKO.MAUFNR  = ''
)
SELECT * FROM filter_dossiers