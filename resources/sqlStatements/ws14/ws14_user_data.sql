WITH partner_info AS (
    SELECT TOP 1
		BUT000.PARTNER,
		BUT000.<PERSON><PERSON><PERSON><PERSON>,
		BUT000.TYPE,
		BUT000.NAME_FIRST,
		BUT000.NAME_ORG1,
		BUT000.NAME_LAST,
		BUT000.NAME_ORG2,
		BUT021_FS.ADDR<PERSON>MBER
    FROM {HanaSchema}.BUT000
    LEFT JOIN (
	    SELECT PARTNER, ADDR<PERSON>MBER
	    FROM {HanaSchema}.BUT021_FS
	    WHERE VALID_FROM < CAST(TO_VARCHAR(NOW(), 'YYYYMMDDHHMISS') AS decimal(15)) AND VALID_TO > CAST(TO_VARCHAR(NOW(), 'YYYYMMDDHHMISS') AS decimal(15))
	) AS BUT021_FS ON BUT021_FS.PARTNER = BUT000.PARTNER
    WHERE BUT000.PARTNER = :bp AND XDELE = '' AND XBLCK = ''
), tel_fixe AS (
	SELECT TOP 1 TELNR_LONG
	FROM {HanaSchema}.ADR2
	JOIN partner_info ON partner_info.ADDRNUMBER = ADR2.AD<PERSON>NUMBER
	WHERE VALID_TO = '' AND R3_USER = '1'
	ORDER BY CONSNUMBER DESC
), tel_portable AS (
	SELECT TOP 1 TELNR_LONG
	FROM {HanaSchema}.ADR2
	JOIN partner_info ON partner_info.ADDRNUMBER = ADR2.ADDRNUMBER
	WHERE VALID_TO = '' AND R3_USER = '3'
	ORDER BY CONSNUMBER DESC
), mail_connexion AS (
	SELECT TOP 1 SMTP_ADDR
	FROM {HanaSchema}.ADR6
	JOIN partner_info ON partner_info.ADDRNUMBER = ADR6.ADDRNUMBER
	WHERE VALID_TO = ''
	ORDER BY CONSNUMBER DESC
), mail_contact AS (
	SELECT TOP 1 SMTP_ADDR
	FROM {HanaSchema}.ADR6
	JOIN partner_info ON partner_info.ADDRCOMM = ADR6.ADDRNUMBER
	WHERE VALID_TO = '' 
	ORDER BY CONSNUMBER DESC
)
SELECT DISTINCT
    CAST(partner_info.PARTNER AS number) AS "Bp",
    (CASE WHEN partner_info.TYPE = 1 THEN partner_info.NAME_FIRST ELSE partner_info.NAME_ORG1 END) AS "Firstname",
    (CASE WHEN partner_info.TYPE = 1 THEN partner_info.NAME_LAST ELSE partner_info.NAME_ORG2 END) AS "Lastname",
    (SELECT * FROM tel_fixe) AS "PhoneFixe",
    (SELECT * FROM tel_portable) AS "Phone",
    (SELECT * FROM mail_connexion) AS "Email",
    (SELECT * FROM mail_contact) AS "ContactEmail",
    ADRC.HOUSE_NUM1 AS "NumRue",
    ADRC.STREET  AS "Rue",
    ADRC.POST_CODE1 AS "Cdpostal",
    ADRC.CITY1 AS "Localite",
    ADRC.COUNTRY AS "CodePays" 
FROM partner_info
LEFT JOIN {HanaSchema}.ADRC ON partner_info.ADDRNUMBER = ADRC.ADDRNUMBER