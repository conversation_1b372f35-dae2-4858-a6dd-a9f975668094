WITH
bp_hierarchy AS (
    SELECT * FROM HIERARCHY(
        SOURCE(
            SELECT PARTNER1 AS parent_id, PARTNER2 AS node_id FROM {HanaSchema}.BUT050 WHERE PARTNER1 = :bp AND RELTYP = 'BUR998'
        )
        START WHERE PARTNER1 in (SELECT PARTNER FROM {HanaSchema}.BUT000 WHERE BPKIND = 'MYRE' AND XDELE = '' AND XBLCK = '')
    )
),
find_root as (
    SELECT item.node_id AS bp, root.parent_id AS root FROM bp_hierarchy item JOIN bp_hierarchy root ON root.HIERARCHY_RANK = item.HIERARCHY_ROOT_RANK
    UNION ALL
    SELECT DISTINCT parent_id AS bp, parent_id AS root FROM bp_hierarchy WHERE HIERARCHY_PARENT_RANK = 0
),
find_eans as (
    SELECT DISTINCT find_root.root AS MYRE, find_root.bp AS PARTNER, TABLE_EAN.EXT_UI AS EAN, TABLE_CONTRAT.ANLAGE, AUSZDAT, EINZDAT, VKONTO, GPART
    FROM {HanaSchema}.FKKVKP AS REL_TABLE_CONTRAT
    INNER JOIN ( SELECT * FROM {HanaSchema}.EVER WHERE AUSZDAT >= ADD_YEARS(NOW(),-3) ) AS TABLE_CONTRAT ON TABLE_CONTRAT.VKONTO = REL_TABLE_CONTRAT.VKONT
    INNER JOIN {HanaSchema}.EUIINSTLN AS TABLE_EAN_INSTALLATION ON TABLE_CONTRAT.ANLAGE = TABLE_EAN_INSTALLATION.ANLAGE
    INNER JOIN {HanaSchema}.EUITRANS AS TABLE_EAN ON TABLE_EAN.INT_UI = TABLE_EAN_INSTALLATION.INT_UI
    INNER JOIN find_root ON bp = REL_TABLE_CONTRAT.GPART
    WHERE TABLE_EAN.EXT_UI NOT LIKE '%_F'
),
ean_compteurs as (
    SELECT DISTINCT EAN, SERNR AS NUM_CPT, CASE WHEN CAP_ACT_GRP IN('9000','9001', '9002') THEN TRUE ELSE FALSE END AS IS_SMART, EGERH.AB, EGERH.BIS,
        (
            SELECT DISTINCT STRING_AGG(EASTS.TARIFART, ',')
            FROM {HanaSchema}.EASTS
            JOIN {HanaSchema}.ETDZ ON EASTS.LOGIKZW = ETDZ.LOGIKZW
            WHERE ETDZ.EQUNR = EQUI.EQUNR
                AND EASTS.TARIFART != ''
                AND ETDZ.NABLESEN != 'X'
                AND EASTS.AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')  <= EASTS.BIS
                AND ETDZ.AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')  <= ETDZ.BIS
        ) AS "Ean_Rates"
    FROM find_eans
    INNER JOIN {HanaSchema}.EASTL ON find_eans.ANLAGE = EASTL.ANLAGE
    INNER JOIN (SELECT * FROM {HanaSchema}.EGERH WHERE ADD_YEARS(NOW(),-3) <= BIS) as EGERH ON EGERH.LOGIKNR = EASTL.LOGIKNR
    INNER JOIN (SELECT * FROM {HanaSchema}.EQUI WHERE EQART != 'IG_CNV') AS EQUI ON EGERH.EQUNR = EQUI.EQUNR
),
ean_details as (
    SELECT DISTINCT
        EAN,
        EANL.SPARTE AS "Ean_SectActivite",
        --(CASE WHEN TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= AUSZDAT THEN 'ACTIF' ELSE 'INACTIF' END) AS "Ean_Status",
        HOUSE_NUM1 AS "Ean_NumRue",
        STREET AS "Ean_Rue",
        POST_CODE1 AS "Ean_Cdpostal",
        CITY1 AS "Ean_Localite",
        EINZDAT,
        AUSZDAT,
        ZUORDDAT,
        CAST(VKONTO AS number) AS "Ean_NumeroContrat",
        CAST(GPART AS number) AS "Ean_HaugazelId", -- TODO : check if its correct (seems = to PartenaireId)
        PARTNER,
        MYRE,
        find_eans.ANLAGE
    FROM find_eans
    LEFT JOIN {HanaSchema}.EANL ON find_eans.ANLAGE = EANL.ANLAGE
    LEFT JOIN {HanaSchema}.EVBS ON EANL.VSTELLE = EVBS.VSTELLE
    LEFT JOIN {HanaSchema}.ILOA ON EVBS.HAUS = ILOA.TPLNR
    LEFT JOIN {HanaSchema}.ADRC ON ADRC.ADDRNUMBER = ILOA.ADRNR
    LEFT JOIN (SELECT * FROM {HanaSchema}.EANLH WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')  <= BIS) AS EANLH ON find_eans.ANLAGE = EANLH.ANLAGE
    LEFT JOIN {HanaSchema}.TE418 ON EANLH.ABLEINH = TE418.TERMSCHL
),
user_eans as (
    SELECT DISTINCT
        MYRE,
        ean_details.EAN AS "Ean",
        ean_details.PARTNER AS "Ean_PartenaireId",
        "Ean_SectActivite",
        --"Ean_Status",
        "Ean_NumRue",
        "Ean_Rue",
        "Ean_Cdpostal",
        "Ean_Localite",
        (CASE WHEN ean_compteurs.AB < EINZDAT THEN EINZDAT ELSE ean_compteurs.AB END) AS "Ean_DateFrom",
        (CASE WHEN ean_compteurs.BIS < AUSZDAT THEN ean_compteurs.BIS ELSE AUSZDAT END) AS "Ean_DateTo",
        "Ean_NumeroContrat",
        "Ean_HaugazelId",
        ean_compteurs.NUM_CPT AS "Ean_Cpt",
        ean_compteurs.IS_SMART AS "Ean_Smart",
        "Ean_Rates"
    FROM ean_details
    LEFT JOIN ean_compteurs ON ean_compteurs.EAN = ean_details.EAN
)
SELECT 
    *,
    (CASE WHEN NOW() <= "Ean_DateTo" THEN 'ACTIF' ELSE 'INACTIF' END) AS "Ean_Status"
FROM user_eans
ORDER BY "Ean_DateTo" DESC