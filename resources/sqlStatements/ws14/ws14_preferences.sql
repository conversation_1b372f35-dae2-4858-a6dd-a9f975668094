with user_prefs as (
    select <PERSON>RTNE<PERSON>, <PERSON><PERSON><PERSON><PERSON> as "Preference_Key", INSTITUT<PERSON> as "Preference_Value"
    from {HanaSchema}.BUT0ID
    where TYPE='MRPREF'
    and cast(VALID_DATE_FROM as number) < cast(TO_VARCHAR(NOW(), 'YYYYMMDDHH') as number)
    and cast(VALID_DATE_TO as number) > cast(TO_VARCHAR(NOW(), 'YYYYMMDD') as number)
    and PARTNER = :bp
)
select *
from user_prefs 
where "Preference_Value" is not null and "Preference_Value" != ''