SELECT IDRAD_RUE AS id_rad_rue, IDRAD_LOCALITE AS id_rad_localite
FROM (
  (SELECT STRT_CODE, CITY_CODE
  FROM {HanaSchema}.ADRSTREETT
  WHERE UPPER(TRIM(STREET)) = UPPER(TRIM(:InputStreet))
  GROUP BY STRT_CODE, CITY_CODE
  ) AS STREET_FILTER
INNER JOIN
  (SELECT CITY_CODE
  FROM {HanaSchema}.ADRCITYT
  WHERE UPPER(TRIM(CITY_NAME)) = UPPER(TRIM(:InputCity))
  GROUP BY CITY_CODE
  ) AS CITY_FILTER
ON STREET_FILTER.CITY_CODE = CITY_FILTER.CITY_CODE
INNER JOIN
  (SELECT IDRAD_RUE, IDRAD_LOCALITE, STRT_CODE, CITY_CODE
  FROM {HanaSchema}.ZCA_RAD_MAPP
  WHERE TRIM(POST_CODE) = TRIM(:InputCdpostal)
  ) AS RAD_IDS
ON STREET_FILTER.STRT_CODE = RAD_IDS.STRT_CODE AND CITY_FILTER.CITY_CODE = RAD_IDS.CITY_CODE)