with get_counter_info as (
    select *,
       case when CAP_ACT_GRP in('9000', '9001', '9002') then TRUE else FALSE end as IS_SMART,
       case when CAP_ACT_GRP in('9001', '9002') then TRUE else FALSE end as IS_SMART_COMM
     from (
     	select ANLAGE as EASTL_ANLAGE, <PERSON><PERSON><PERSON><PERSON><PERSON>
        from {HanaSchema}.EASTL
        where ANLAGE = :ANLAGE
          and AB <= TO_VARCHAR(current_date, 'YYYYMMDD')
          and TO_VARCHAR(current_date, 'YYYYMMDD') <= BIS
    ) as EASTL
    inner join (
        select
            EQUNR as EGERH_EQUNR,
            ZWGRUPPE as EGERH_ZWGRUPPE,
            LOGIKNR as EGERH_LOGIKNR,
            DEVLOC as DEVLOC,
            CAP_ACT_GRP
        from {HanaSchema}.EGERH
        where AB <= TO_VARCHAR(current_date, 'YYYYMMDD') and TO_VARCHAR(current_date, 'YYYYMMDD') <= BIS
    ) as EGERH
    on EASTL.LOGIKNR = EGERH.EGERH_LOGIKNR
    inner join
    (
      select distinct EQUNR as N_EQUNR, count(*) over (partition by EQUNR) as N_BILLING, CPT_CONTROL
      from  (
        select *, (case when EASTI.ZWZUART = 04 and EASTI.OPCODE = 6 then true else false end) as CPT_CONTROL
        from
        (
          (
              select
                LOGIKZW,
                ZWNABR
              from {HanaSchema}.EASTS
              where
              AB <= TO_VARCHAR(current_date, 'YYYYMMDD')
              and TO_VARCHAR(current_date, 'YYYYMMDD') <= BIS
              and ANLAGE = :ANLAGE
              and (ZWNABR = '' or :IncludeControlCpt = 1)
          ) as EASTS
          inner join
          (
            select
              EQUNR,
              LOGIKZW
            from {HanaSchema}.ETDZ
            where
              AB <= TO_VARCHAR(current_date, 'YYYYMMDD')
              and TO_VARCHAR(current_date, 'YYYYMMDD') <= BIS
          ) as ETDZ
          on EASTS.LOGIKZW = ETDZ.LOGIKZW
          left join {HanaSchema}.EASTI on EASTI.LOGIKZW = ETDZ.LOGIKZW and EASTI.BIS = '99991231'
        )
      )
    ) AS N_COUNTER
    ON EGERH.EGERH_EQUNR = N_COUNTER.N_EQUNR
    INNER JOIN
    (select * from {HanaSchema}.EQUI where EQART != 'IG_CNV') AS EQUI
    ON EGERH.EGERH_EQUNR = EQUI.EQUNR
    LEFT JOIN
    (select distinct LOGIKNR2, MESSDRCK from {HanaSchema}.EZUZ) AS EZUZ
    ON EASTL.LOGIKNR = EZUZ.LOGIKNR2
), config_info AS (
  select
  	EQUNR,
    max(case when ATNAM = 'IE_PRT_REGL_INJE' then ATFLV else null end) as IE_PRT_REGL_INJE,
    max(case when ATNAM = 'IE_PRT_REGL_PREL' then ATFLV else null end) as IE_PRT_REGL_PREL
  from {HanaSchema}.EGERH
      INNER JOIN
      (
        select LOGIKNR as EZUG_LOGIKNR, LOGIKNR2 as PRT_LOGIKNR
        from {HanaSchema}.EZUG
      ) AS EZUG
      ON EZUG.EZUG_LOGIKNR = EGERH.LOGIKNR
      INNER JOIN
      (
        select EQUNR as PRT_EQUNR, LOGIKNR as PRT_LOGIKNR_EGERH
        from {HanaSchema}.EGERH
        where AB <= TO_VARCHAR(current_date, 'YYYYMMDD')
        and TO_VARCHAR(current_date, 'YYYYMMDD') <= BIS
      ) AS PRT_EGERH
      ON PRT_EGERH.PRT_LOGIKNR_EGERH = EZUG.PRT_LOGIKNR
      INNER JOIN
      (
        select EQUNR as EQUI_EQUNR, SERNR
        from {HanaSchema}.EQUI
      ) AS EQUI
      ON EQUI.EQUI_EQUNR = PRT_EGERH.PRT_EQUNR
      INNER JOIN
      (
        select OBJEK as INPUT_OBJEK,
        CUOBJ
        from {HanaSchema}.INOB
      ) AS INOB
      ON INOB.INPUT_OBJEK = EQUI_EQUNR
      INNER JOIN
      (
        select
          OBJEK, ATINN, ATFLV, ATWRT
        from {HanaSchema}.AUSP
      ) AS AUSP
      ON INOB.CUOBJ = AUSP.OBJEK
      INNER JOIN
      (
        select ATINN as CABN_ATTIN, ATNAM
        from {HanaSchema}.CABN
        where ATNAM in ('IE_PRT_REGL_PREL', 'IE_PRT_REGL_INJE')
      ) AS CABN
      ON AUSP.ATINN = CABN.CABN_ATTIN
      GROUP BY EQUNR
)
select * from get_counter_info
left join config_info on config_info.EQUNR = get_counter_info.EGERH_EQUNR