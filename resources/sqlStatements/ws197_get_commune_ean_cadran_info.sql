WITH ean_commune AS (
	SELECT DISTINCT
		EXT_UI AS EAN,
		EANL.SPARTE,
	    EANL.ANLAGE,
	    EANL.VSTELLE
	FROM {HanaSchema}.ETTIFN
	JOIN {HanaSchema}.EUIINSTLN ON EUIINSTLN.ANLAGE = ETTIFN.ANLAGE
    JOIN {HanaSchema}.EUITRANS ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
    JOIN {HanaSchema}.EANL ON EANL.ANLAGE = ETTIFN.ANLAGE AND EANL.SPARTE = (CASE WHEN ETTIFN.OPERAND = 'E_COMMUNE' THEN '01' WHEN ETTIFN.OPERAND = 'G_COMMUNE' THEN '02' END)
	WHERE ETTIFN.OPERAND IN ('G_COMMUNE', 'E_COMMUNE') AND ETTIFN.STRING1 = :IdCommune AND ETTIFN.AB <= CURRENT_DATE AND CURRENT_DATE <= ETTIFN.BIS
)
SELECT DISTINCT
    e.ext_ui AS "Ean",
    a.sernr AS "NumCpt",
    a.eq<PERSON>,
    b.log<PERSON><PERSON>,
    b.<PERSON><PERSON> "CatCadran",
    b.stanzvor,
    b.stanznac,
    f.zwzuart,
    b.zwnummer AS "ETDZ_ZWNUMMER",
    a.matnr,
    h.zwgruppe,
    h.kennziff AS "CodeCadran",
    h.anzart AS "ANZART",
    c.anlage,
    c.tarifart AS "CatTarif",
    (
        SELECT COUNT(*)
        FROM {HanaSchema}.equi AS a2
        INNER JOIN {HanaSchema}.etdz AS b2 ON a2.equnr = b2.equnr
        INNER JOIN {HanaSchema}.etyp AS i2 ON i2.matnr = a2.matnr
        INNER JOIN {HanaSchema}.ezwg AS h2 ON h2.zwgruppe = i2.zwgruppe AND h2.zwnummer = b2.zwnummer
        WHERE
            a2.equnr = a.equnr
            AND h2.zwgruppe = h.zwgruppe
            AND b2.nablesen != 'X'
            AND b2.ab <= CURRENT_DATE 
            AND b2.bis >= CURRENT_DATE 
    ) AS "lv_nb_lines",
    (
        SELECT DISTINCT AUSP.atwrt
        FROM {HanaSchema}.AUSP
        INNER JOIN {HanaSchema}.KSSK ON KSSK.objek = AUSP.objek AND KSSK.klart = '001'
        INNER JOIN {HanaSchema}.CABN ON CABN.atinn = AUSP.atinn AND CABN.atnam = 'IE_TA_TYPE_CPT'
        INNER JOIN {HanaSchema}.KLAH ON KLAH.clint = KSSK.clint AND KLAH.class = 'IE_TA_GEN'
        WHERE AUSP.klart = '001' AND AUSP.objek = a.matnr
    ) AS "ATWRT"
FROM {HanaSchema}.equi AS a 
    INNER JOIN {HanaSchema}.etdz AS b ON a.equnr = b.equnr
    INNER JOIN {HanaSchema}.easts AS c ON b.logikzw = c.logikzw
    INNER JOIN {HanaSchema}.etyp AS i ON  i.matnr = a.matnr
    INNER JOIN {HanaSchema}.ezwg AS h ON h.zwgruppe = i.zwgruppe
        AND h.zwnummer = b.zwnummer
    LEFT OUTER JOIN {HanaSchema}.easti AS f ON b.logikzw = f.logikzw
    INNER JOIN {HanaSchema}.euiinstln AS d ON c.anlage = d.anlage
    INNER JOIN {HanaSchema}.euitrans AS e ON d.int_ui = e.int_ui
        AND d.dateto = e.dateto
        AND d.timeto = e.timeto
    JOIN ean_commune on ean_commune.EAN = e.ext_ui
WHERE b.nablesen  != 'X'
    AND b.ab <= CURRENT_DATE 
    AND b.bis > CURRENT_DATE 
    AND b.intsizeid = '' 
    AND c.bis > CURRENT_DATE 
    AND c.ab <= CURRENT_DATE