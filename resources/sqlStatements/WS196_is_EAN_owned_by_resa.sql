SELECT 
    TO_BOOLEAN(COUNT(EUITRANS.EXT_UI)) AS "ResaIsGRD",
    CASE 
        WHEN EANL.SPARTE = '01' THEN 'ELEC'
        WHEN EANL.SPARTE = '02' THEN 'GAZ'
        ELSE 'UNKNOWN'
    END AS "EnergyType"
FROM 
    {HanaSchema}.EUITRANS
LEFT JOIN 
    {HanaSchema}.EUIINSTLN ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
LEFT JOIN 
    {HanaSchema}.EANL ON EUIINSTLN.ANLAGE = EANL.ANLAGE
WHERE 
    EUITRANS.EXT_UI = :<PERSON><PERSON> BY
    EANL.SPARTE
LIMIT 1