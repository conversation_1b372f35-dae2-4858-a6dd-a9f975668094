WITH WS28_GET_EAN_WITH_SRNR AS (SELECT DISTINCT
        EAN,
        ANLAGE,
        EQUNR,
        TARIFTYP,
        AKLASSE,
        ABLEINH,
        CASE WHEN CAP_ACT_GRP IN('9000','9001', '9002') THEN TRUE ELSE FALSE END AS IS_SMART,
        CASE WHEN CAP_ACT_GRP IN('9001', '9002') THEN TRUE ELSE FALSE END AS IS_SMART_COMM
    FROM
        (((SELECT
            EQUNR
        FROM {HanaSchema}.EQUI
        WHERE SERNR = (:NumCompteur)) AS EQUI
    INNER JOIN
        (SELECT
            LOGIKZW AS ETDZ_LOGIKZW,
            EQUNR AS ETDZ_EQUNR,
            AB AS ETDZ_AB,
            BIS AS ETDZ_BIS
        FROM {HanaSchema}.ETDZ
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS) AS ETDZ
        ON EQUI.EQUNR = ETDZ.ETDZ_EQUNR)
    INNER JOIN
        (SELECT
            ANLAGE AS EASTS_ANLAGE,
            LOGIKZW AS EASTS_LOGIKZW,
            AB AS EASTS_AB,
            BIS AS EASTS_BIS
        FROM {HanaSchema}.EASTS
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS) AS EASTS
        ON EASTS.EASTS_LOGIKZW = ETDZ.ETDZ_LOGIKZW
    INNER JOIN
        (SELECT
            EUIINSTLN.INT_UI,
            EUIINSTLN.ANLAGE AS EUINSTLN_ANLAGE
        FROM {HanaSchema}.EUIINSTLN) AS EUIINSTLN
        ON EUIINSTLN.EUINSTLN_ANLAGE = EASTS.EASTS_ANLAGE
    INNER JOIN
        (SELECT
            EUITRANS.EXT_UI AS EAN,
            EUITRANS.INT_UI
        FROM {HanaSchema}.EUITRANS) AS EUITRANS
        ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
    INNER JOIN
        (SELECT
            TARIFTYP,
            EANLH.AKLASSE,
            EANLH.ABLEINH,
            ANLAGE
        FROM {HanaSchema}.EANLH
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS) AS EANLH
        ON EANLH.ANLAGE = EASTS.EASTS_ANLAGE)
    INNER JOIN
        (SELECT
            EQUNR AS EGERH_EQUNR,
            CAP_ACT_GRP
        FROM {HanaSchema}.EGERH
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
        ) AS EGERH
    ON EQUI.EQUNR = EGERH.EGERH_EQUNR
    WHERE ETDZ_AB <= EASTS_AB AND ETDZ_BIS <= EASTS_BIS)

SELECT
    *
FROM
    WS28_GET_EAN_WITH_SRNR