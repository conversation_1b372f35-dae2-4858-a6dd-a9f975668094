with
WS43 as (
select
    cast(DELESTAGE.TRANCHE as integer) as "Tranche",
    MSG.COMMENTAIRE as "Message"
from (
   (select TRANCHE from {HanaSchema}.ZPS_DELESTAGE
        where CDPOSTAL = :Cdpostal
        and upper(RUE) = upper(:Rue)
        and (upper(MC_LOCALITE) = upper(:Localite) or upper(LOCALITE) = upper(:Localite) or upper(COMMUNE) = upper(:Localite))
    ) as DELESTAGE
    inner join
        (select COMMENTAIRE, TRANCHE from {HanaSchema}.ZPS_DELEST_MSG
            where upper(LEFT(LANGUE, 1)) = upper(LEFT(:Langue, 1))) as MSG
    on DELESTAGE.TRANCHE = MSG.TRANCHE
  )
)
select * from WS43