INNER JOIN
  (
    SELECT STREET_REFERENCE.STREET, LOCALITE_REFERENCE.CITY_NAME
    FROM (
      SELECT CITY_CODE, STRT_CODE
      FROM {HanaSchema}.ZCA_RAD_MAPP
      WHERE IDRAD_RUE = :IdRadRue AND IDRAD_LOCALITE = :Id<PERSON><PERSON><PERSON><PERSON>alite
    ) AS RAD
    INNER JOIN {HanaSchema}.ADRCITYT AS LOCALITE_REFERENCE
    ON LOCALITE_REFERENCE.CITY_CODE = RAD.CITY_CODE
    INNER JOIN {HanaSchema}.ADRSTREETT AS STREET_REFERENCE
    ON STREET_REFERENCE.STRT_CODE = RAD.STRT_CODE AND STREET_REFERENCE.CITY_CODE = RAD.CITY_CODE
  ) AS RAD_REFERENCE
ON RAD_REFERENCE.STREET = INTERRUPTIONS_GEO.EQUI_RUE AND RAD_REFERENCE.CITY_NAME = INTERRUPTIONS_GEO.EQUI_LOCALITE