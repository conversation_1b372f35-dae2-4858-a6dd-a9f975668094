with town as (
        select distinct SPARTE
        from {HanaSchema}.ADRCITYCCS
        JOIN {HanaSchema}.ADRPCDCITY ON ADRPCDCITY.CITY_CODE = ADRCITYCCS.CITY_CODE
        JOIN {HanaSchema}.ADRCITYT ON ADRCITYT.CITY_CODE = ADRCITYCCS.CITY_CODE
        JOIN {HanaSchema}.ADRCITY ON ADRCITY.CITY_CODE = ADRCITYCCS.CITY_CODE
        where trim(ADRPCDCITY.POST_CODE) = trim(:Cdpostal) or trim(upper(ADRCITYT.CITY_EXT)) = trim(upper(:Localite)) or ADRCITY.COMMU_CODE = :IdCommune
), road as (
        select distinct SPARTYP, CITY_NAME, POST_CODE, STREET, COMMU_CODE
        from {HanaSchema}.ADRSTRTMRU
        JOIN {HanaSchema}.ADRSTREETT ON TRIM(UPPER(ADRSTREETT.STREET)) = TRIM(UPPER(:Rue))
        JOIN {HanaSchema}.ADRPCDCITY ON ADRPCDCITY.CITY_CODE = ADRSTREETT.CITY_CODE
        JOIN {HanaSchema}.ADRCITYT ON ADRCITYT.CITY_CODE = ADRSTREETT.CITY_CODE
        JOIN {HanaSchema}.ADRCITY ON ADRCITY.CITY_CODE = ADRSTREETT.CITY_CODE
        WHERE ADRSTRTMRU.STRT_CODE = ADRSTREETT.STRT_CODE AND (TRIM(ADRPCDCITY.POST_CODE) = TRIM(:Cdpostal) OR TRIM(UPPER(ADRCITYT.CITY_EXT)) = TRIM(UPPER(:Localite)) OR ADRCITY.COMMU_CODE = :IdCommune)
), dist_gaz AS (
        select DISTBUFF
        from SAPDIADM_READ.GIS_ADRESSES
        join road on commu_ins = road.COMMU_CODE
            and trim(upper(CODEPOSTAL)) = trim(upper(road.POST_CODE)) 
            and trim(upper(RUENOMLANGUEPRIMAIRE)) = trim(upper(road.STREET))
        where RACCGAZ = 'Yes'
            and (trim(upper(NUMERO)) = trim(upper(:Numero)) or :Numero is null)
        order by lpad(NUMERO, 5, '0')
        LIMIT 1
)
select
        case when :Rue is not null then IFNULL((select 'X' from road where SPARTYP = '01' LIMIT 1), '') else IFNULL((select 'X' from town where SPARTE = '01' LIMIT 1), '') end as "Elec",
        case when :Rue is not null then IFNULL((select 'X' from road where SPARTYP = '02' LIMIT 1), '') else IFNULL((select 'X' from town where SPARTE = '02' LIMIT 1), '') end as "Gaz",
        IFNULL((select 'X' from town where SPARTE = '01' LIMIT 1), '') as "IsGRDElec",
        IFNULL((select 'X' from town where SPARTE = '02' LIMIT 1), '') as "IsGRDGaz",
        IFNULL((select 'X' from town where SPARTE = '03' LIMIT 1), '') as "Ep",
        (select DISTBUFF from dist_gaz) as "DistGaz",
        IFNULL((case when (select DISTBUFF from dist_gaz) <= 8 then 'YES' when (select DISTBUFF from dist_gaz) <= 25 then 'MAYBE' else 'NO' end), 'NO') as "DispoGaz"
from DUMMY