WITH bp_hierarchy AS (
    SELECT * FROM HIERARCHY(
        SOURCE(
            SELECT PARTNER1 AS parent_id, PARTNER2 AS node_id FROM {HanaSchema}.BUT050 WHERE PARTNER1 = :bp AND RELTYP = 'BUR998'
        )
        START WHERE PARTNER1 in (SELECT PARTNER FROM {HanaSchema}.BUT000 WHERE BPKIND = 'MYRE' AND XDELE = '' AND XBLCK = '')
    )
),
find_root as (
    SELECT item.node_id AS bp, root.parent_id AS root FROM bp_hierarchy item JOIN bp_hierarchy root ON root.HIERARCHY_RANK = item.HIERARCHY_ROOT_RANK
    UNION ALL
    SELECT DISTINCT parent_id AS bp, parent_id AS root FROM bp_hierarchy WHERE HIERARCHY_PARENT_RANK = 0
),
get_ean_contract as (
    SELECT DISTINCT EUITRANS.EXT_UI AS EAN, EVER.*
    FROM find_root
    INNER JOIN {HanaSchema}.FKKVKP ON FKKVKP.GPART = bp
    INNER JOIN {HanaSchema}.EVER ON EVER.VKONTO = FKKVKP.VKONT
    INNER JOIN {HanaSchema}.EUIINSTLN ON EUIINSTLN.ANLAGE = EVER.ANLAGE
    INNER JOIN {HanaSchema}.EUITRANS ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
    WHERE EUITRANS.EXT_UI = :ean
),
WS34_GET_INDEX AS (
SELECT *,
 MIN(ORDERED_ABLESGR) OVER (PARTITION BY EAN, ETDZ_LOGIKZW, ADAT, ANLAGE_INFO) AS BEST_ABLESGR
 FROM get_ean_contract
  INNER JOIN
  (
    SELECT
      VKONT,
      GPART
    FROM {HanaSchema}.FKKVKP
  ) AS FKKVKP
  ON FKKVKP.VKONT = get_ean_contract.VKONTO
  INNER JOIN
  (
    SELECT
      ANLAGE,
      LOGIKZW,
      TARIFART,
      AB AS EASTS_AB,
      BIS AS EASTS_BIS
    FROM {HanaSchema}.EASTS
  ) AS EASTS
  ON EASTS.ANLAGE = get_ean_contract.ANLAGE
  INNER JOIN
  (
    SELECT DISTINCT
      AKLASSE,
      ANLAGE AS ANLAGE_INFO,
      ABLEINH AS EANLH_ABLEINH
    FROM {HanaSchema}.EANLH
  ) AS EANLH
  ON EANLH.ANLAGE_INFO = get_ean_contract.ANLAGE
  INNER JOIN
  (
    SELECT
      LOGIKZW AS ETDZ_LOGIKZW,
      EQUNR,
      SPARTYP,
      ZWNUMMER AS ETDZ_ZWNUMMER,
      ZWKENN,
      ZWART,
      AB AS ETDZ_AB,
      BIS AS ETDZ_BIS,
      KENNZIFF
    FROM {HanaSchema}.ETDZ
  ) AS ETDZ
  ON EASTS.LOGIKZW = ETDZ.ETDZ_LOGIKZW
  INNER JOIN
  (
    SELECT
      EQUNR AS EQUI_EQUNR,
      SERNR,
      MATNR
    FROM {HanaSchema}.EQUI
  ) AS EQUI
  ON EQUI.EQUI_EQUNR = ETDZ.EQUNR
  INNER JOIN
  (
    SELECT
      ABLBELNR,
      ANLAGE,
      ABLEINH,
      ADATSOLL AS DatePlanned,
      ABLESGR,
      /* Select in priority index 1 (communique), then 3 (indexier) then 2 (estimé) */
      (CASE WHEN ABLESGR = '02' THEN '03' ELSE CASE WHEN ABLESGR = '03' THEN '02' ELSE ABLESGR END END) AS ORDERED_ABLESGR
    FROM {HanaSchema}.EABLG
  ) AS EABLG
  ON EABLG.ANLAGE = get_ean_contract.ANLAGE AND EABLG.ABLEINH = EANLH.EANLH_ABLEINH
  INNER JOIN
  (
    SELECT
      GERNR,
      EQUNR AS EABL_EQUNR,
      ABLBELNR AS EABL_ABLBELNR,
      ZWNUMMER,
      ADATSOLL,
      SUBSTRING(ADATSOLL, 0, 4) AS ADATSOLL_YEAR,
      ADAT,
      V_ZWSTAND,
      N_ZWSTAND,
      MASSREAD,
      MASSBILL,
      ABLESTYP
    FROM {HanaSchema}.EABL
  ) AS EABL
  ON
  /* Same transaction */
  EABLG.ABLBELNR = EABL.EABL_ABLBELNR
  /* Same counter and cadran: avoids duplicates but then we have "missing data" or incomplete history */
  AND ETDZ.EQUNR = EABL.EABL_EQUNR
  AND EQUI.SERNR = EABL.GERNR
  AND ETDZ.ETDZ_ZWNUMMER = EABL.ZWNUMMER
  LEFT JOIN(
    SELECT
      TERMSCHL,
      SUBSTRING(TERMTDAT, 0, 4) AS UR_YEAR,
      SUBSTRING(ADATSOLL, 0, 4) AS UR_ADATSOLL_YEAR,
      ADATSOLL AS UR_ADATSOLL
    FROM {HanaSchema}.TE418
  ) AS TE418
  ON EABLG.ABLEINH = TE418.TERMSCHL AND ADATSOLL = UR_ADATSOLL
  WHERE
    /* Valid equipement */
    ETDZ_AB <= ADAT
    AND ADAT <= ETDZ_BIS
    /* Valid cadran */
    AND EASTS_AB <= ADAT
    AND ADAT <= EASTS_BIS
    /* Cadran in validity of equipement */
    AND ETDZ_AB <= EASTS_AB
    AND ETDZ_BIS >= EASTS_BIS
    /* DateReleve in date contrat */
    AND EINZDAT <= EABL.ADAT
    AND EABL.ADAT <= AUSZDAT
), WS34_SELECT_INDEX AS (
  SELECT
    EAN,
    SPARTE,
    GPART,
    INVOICING_PARTY,
    WS34_GET_INDEX.ANLAGE_INFO AS ANLAGE,
    VERTRAG,
    EANLH_ABLEINH AS ABLEINH,
    EASTS_AB,
    EASTS_BIS,
    EINZDAT,
    AUSZDAT,
    ETDZ_AB,
    ETDZ_BIS,
    ETDZ_LOGIKZW AS LOGIKZW,
    EABL_ABLBELNR AS ABLBELNR,
    SERNR AS EQUNR_SERNR,
    GERNR AS EABL_GERNR,
    EQUNR AS ETDZ_EQUNR,
    EABL_EQUNR,
    ETDZ_ZWNUMMER,
    ZWNUMMER AS EABL_ZWNUMMER,
    UR_YEAR,
    ADATSOLL,
    UR_ADATSOLL,
    ABS(DAYS_BETWEEN(ADAT, UR_ADATSOLL)) AS DAYS_TO_PLAN,
    ADAT,
    ABLESGR,
   (CASE WHEN ABLESGR = '02' THEN '03' ELSE CASE WHEN ABLESGR = '03' THEN '02' ELSE ABLESGR END END) AS ORDERED_ABLESGR,
    ABLESTYP,
    CAST(V_ZWSTAND AS int) AS INDEX,
    SPARTYP,
    TARIFART,
    ZWART,
    ZWKENN,
    AKLASSE,
    MASSBILL,
    MASSREAD,
    MATNR,
    EQUI_EQUNR,
    KENNZIFF
    FROM WS34_GET_INDEX
    WHERE ORDERED_ABLESGR = BEST_ABLESGR AND MASSBILL = 'KWH'
), WS34_ADD_CONSO AS (
  SELECT * ,
     MIN(ORDERED_ABLESGR) OVER (PARTITION BY EAN, UR_YEAR, ETDZ_EQUNR, LOGIKZW) AS BEST_ABLESGR,
     MIN(DAYS_TO_PLAN) OVER (PARTITION BY EAN, UR_YEAR, ETDZ_EQUNR, LOGIKZW) AS BEST_DAYS_TO_PLAN
   FROM WS34_SELECT_INDEX
  INNER JOIN
    (
        SELECT SPARTE AS ZISU_SPARTE,
			ZWART AS ZISU_ZWART,
			ZWKENN AS ZISU_ZWKENN,
			AKLASSE AS ZISU_AKLASSE,
			MASSREAD_BIL AS ZISU_MASSREAD,
			TARIFART AS ZISU_TARIFART,
			MAP(element_number,1,'CONS_BILL', 2,'CONS_BILL_COMP', 3,'INJE_BILL', 4,'INJE_BILL_COMP') AS BILL_TYPE,
			MAP(element_number,1,CONS_BILL, 2,CONS_BILL_COMP, 3,INJE_BILL, 4,INJE_BILL_COMP) AS BILL
		FROM {HanaSchema}.ZISU_REG_TYPE, SERIES_GENERATE_INTEGER(1, 1, 5)
		ORDER BY element_number
    ) AS ZISU_REG_TYPE
    ON WS34_SELECT_INDEX.SPARTYP = ZISU_REG_TYPE.ZISU_SPARTE
      AND WS34_SELECT_INDEX.ZWKENN = ZISU_REG_TYPE.ZISU_ZWKENN
      AND WS34_SELECT_INDEX.AKLASSE = ZISU_REG_TYPE.ZISU_AKLASSE
      AND WS34_SELECT_INDEX.MASSBILL = ZISU_REG_TYPE.ZISU_MASSREAD
      AND WS34_SELECT_INDEX.TARIFART = ZISU_REG_TYPE.ZISU_TARIFART
      AND ((ZISU_REG_TYPE.BILL_TYPE LIKE 'CONS_%' AND KENNZIFF LIKE '1.%') OR (ZISU_REG_TYPE.BILL_TYPE LIKE 'INJE_%' AND KENNZIFF LIKE '2.%') OR (KENNZIFF NOT LIKE '1.%' AND KENNZIFF NOT LIKE '2.%'))
), WS34_CREATE_INTERVALS AS (
SELECT
    EAN AS "Ean",
    VERTRAG AS "Contrat",
    EINZDAT AS "DebutContrat",
    AUSZDAT AS "FinContrat",
    EQUNR_SERNR AS "NumCompteur",
    LOGIKZW AS "NumCadran",
    ZWART AS "CatCadran",
    ADAT AS "DateRel",
    INDEX AS "Index",
    MASSREAD AS "IndexUnit",
    ABLESTYP AS "IndexQual",
    TARIFART AS "CatTarif",
    CASE WHEN
      (ORDERED_ABLESGR = BEST_ABLESGR AND DAYS_TO_PLAN = BEST_DAYS_TO_PLAN)
      OR (ABLESGR = '01' AND ADATSOLL = UR_ADATSOLL)
    THEN ADAT
    ELSE NULL
    END
    AS "intervalEnd",
    MASSBILL AS "QttUnit",
    ORDERED_ABLESGR,
    BEST_ABLESGR,
    DAYS_TO_PLAN,
    ETDZ_ZWNUMMER,
    MATNR,
    EQUI_EQUNR,
    CASE WHEN
      (ORDERED_ABLESGR = BEST_ABLESGR AND DAYS_TO_PLAN = BEST_DAYS_TO_PLAN)
      OR (ABLESGR = '01' AND ADATSOLL = UR_ADATSOLL)
    THEN TRUE
    ELSE FALSE
    END AS CHOOSE_INDEX,
    ANLAGE,
    BILL,
    ADAT,
    KENNZIFF
FROM WS34_ADD_CONSO
), WS34_POPULATE_INTERVALS AS (
    SELECT
      *,
      MAX("intervalEnd") OVER (PARTITION BY "Ean", "nextInterval") AS "DateQtt"
    FROM (
      SELECT
        *,
        MIN(CASE WHEN "intervalEnd" IS NOT NULL THEN "DateRel" END) OVER (PARTITION BY "Ean" ORDER BY "DateRel" DESC) AS "nextInterval"
      FROM WS34_CREATE_INTERVALS
      JOIN
        (
          SELECT ANLAGE AS QTT_ANLAGE, BIS AS QTT_DATE, OPERAND, WERT1 AS "Qtt"
          FROM {HanaSchema}.ETTIFN
          WHERE INAKTIV != 'X'
        ) AS ETTIFN
        ON ANLAGE = ETTIFN.QTT_ANLAGE
        AND BILL = ETTIFN.OPERAND
        AND ADAT = ETTIFN.QTT_DATE
      WHERE ORDERED_ABLESGR IN ('01', '02', '03')
     ) t
), WS34_AGG_CONSUMPTION AS (
  SELECT
    *,
    (SUM("Qtt") OVER (PARTITION BY "Ean","DateQtt", "NumCompteur", "NumCadran")) AS "QttAnn"
  FROM WS34_POPULATE_INTERVALS
), WS34_SELECT_FIELDS AS (
  SELECT
    "Ean",
    CAST("Contrat" AS int) AS "Contrat",
    "NumCompteur",
    "NumCadran",
    "CatCadran",
    "intervalEnd" AS "DateQttAnn",
    CAST("DateRel" AS int) AS "DateRel",
    "Index",
    "IndexUnit",
    "IndexQual",
    "CatTarif",
    "QttAnn",
    TO_VARCHAR("Qtt") AS "Qtt",
    "QttUnit",
    ETDZ_ZWNUMMER,
    MATNR,
    EQUI_EQUNR,
    KENNZIFF AS "CodeCadran"
  FROM WS34_AGG_CONSUMPTION
), COUNT_ENTRY AS (
  SELECT a.equnr, COUNT(*) AS "lv_nb_lines"
  FROM {HanaSchema}.EQUI AS a
    INNER JOIN {HanaSchema}.ETDZ AS b ON a.equnr = b.equnr AND b.nablesen != 'X' AND b.ab  <= CURRENT_DATE AND b.bis >= CURRENT_DATE
    INNER JOIN {HanaSchema}.ETYP AS i ON  i.matnr = a.matnr
    INNER JOIN {HanaSchema}.EZWG AS h ON h.zwgruppe = i.zwgruppe AND h.zwnummer = b.zwnummer
  GROUP BY a.equnr
), ANZART AS (
  SELECT h.anzart, b.zwnummer, a.equnr, 
    	CASE WHEN CAP_ACT_GRP IN('9000','9001', '9002') THEN TRUE ELSE FALSE END AS "Smart"
  FROM {HanaSchema}.EQUI AS a
  INNER JOIN {HanaSchema}.ETDZ AS b ON a.equnr = b.equnr
  INNER JOIN {HanaSchema}.EASTS AS c ON b.logikzw = c.logikzw AND c.ab <= CURRENT_DATE AND c.bis >= CURRENT_DATE
  INNER JOIN {HanaSchema}.EGERH AS i ON i.equnr = a.equnr AND i.ab <= CURRENT_DATE AND i.bis >= CURRENT_DATE
  INNER JOIN {HanaSchema}.EZWG AS h ON h.zwgruppe = i.zwgruppe AND h.zwnummer = b.zwnummer
), ATWRT AS (
    SELECT AUSP.atwrt, AUSP.objek
    FROM {HanaSchema}.AUSP
    INNER JOIN {HanaSchema}.KSSK ON KSSK.objek = AUSP.objek AND KSSK.klart = '001'
    INNER JOIN {HanaSchema}.CABN ON CABN.atinn = AUSP.atinn AND CABN.atnam = 'IE_TA_TYPE_CPT'
    INNER JOIN {HanaSchema}.KLAH ON KLAH.clint = KSSK.clint AND KLAH.class = 'IE_TA_GEN'
    WHERE AUSP.klart = '001'
)
SELECT DISTINCT * FROM WS34_SELECT_FIELDS
LEFT OUTER JOIN COUNT_ENTRY ON COUNT_ENTRY.equnr = WS34_SELECT_FIELDS.EQUI_EQUNR
LEFT OUTER JOIN ANZART ON ANZART.equnr = WS34_SELECT_FIELDS.EQUI_EQUNR AND ANZART.zwnummer = WS34_SELECT_FIELDS.ETDZ_ZWNUMMER
LEFT OUTER JOIN ATWRT ON ATWRT.objek = WS34_SELECT_FIELDS.matnr
WHERE ("Smart" = TRUE OR "CodeCadran" LIKE '1.%' OR ("CodeCadran" NOT LIKE '1.%' AND "CodeCadran" NOT LIKE '2.%'))
ORDER BY "Ean","DateRel", "NumCompteur", "NumCadran"