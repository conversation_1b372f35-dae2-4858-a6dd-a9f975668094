SELECT
   MAX(IE_PRT_TENSION) AS IE_PRT_TENSION,
   MAX(IE_PRT_NB_PHASES) AS IE_PRT_NB_PHASES,
   MAX(IE_PRT_NB_PHASES_CODE) AS IE_PRT_NB_PHASES_CODE,
   SUM(IE_PRT_REGL_INJE) AS IE_PRT_REGL_INJE,
   SUM(IE_PRT_REGL_PREL) AS IE_PRT_REGL_PREL
FROM (
  SELECT
    SERNR AS SERNR_PRT,
    MAX(CASE WHEN ATNAM = 'IE_PRT_REGL_INJE' THEN ATFLV ELSE NULL END) AS IE_PRT_REGL_INJE,
    MAX(CASE WHEN ATNAM = 'IE_PRT_REGL_PREL' THEN ATFLV ELSE NULL END) AS IE_PRT_REGL_PREL,
    MAX(CASE WHEN ATNAM = 'IE_PRT_TENSION' THEN ATFLV ELSE NULL END) AS IE_PRT_TENSION,
    MAX(CASE WHEN ATNAM = 'IE_PRT_NB_PHASES' THEN ATWRT ELSE NULL END) AS IE_PRT_NB_PHASES,
    MAX(CASE WHEN ATNAM = 'IE_PRT_NB_PHASES_CODE' THEN ATWTB ELSE NULL END) AS IE_PRT_NB_PHASES_CODE
    FROM
      (
        SELECT LOGIKNR AS CPT_LOGIKNR, EQUNR AS EQUNR_CPT
        FROM {HanaSchema}.EGERH
        WHERE
         EQUNR IN (
         {ListeEquipements}
        )
        AND AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
        AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
      ) AS EGERH
      INNER JOIN
      (
        SELECT EQUNR AS EQUNR_CPT2, EQART AS EQART_CPT, SERNR AS SERNR_CPT
        FROM {HanaSchema}.EQUI
      ) AS EQUI_CPT
      ON EQUI_CPT.EQUNR_CPT2 = EGERH.EQUNR_CPT
      INNER JOIN
      (
        SELECT LOGIKNR AS EZUG_LOGIKNR, LOGIKNR2 AS PRT_LOGIKNR
        FROM {HanaSchema}.EZUG
      ) AS EZUG
      ON EZUG.EZUG_LOGIKNR = EGERH.CPT_LOGIKNR
      INNER JOIN
      (
        SELECT EQUNR AS PRT_EQUNR, LOGIKNR AS PRT_LOGIKNR_EGERH
        FROM {HanaSchema}.EGERH
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
        AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
      ) AS PRT_EGERH
      ON PRT_EGERH.PRT_LOGIKNR_EGERH = EZUG.PRT_LOGIKNR
      INNER JOIN
      (
        SELECT EQUNR, SERNR
        FROM {HanaSchema}.EQUI
      ) AS EQUI
      ON EQUI.EQUNR = PRT_EGERH.PRT_EQUNR
      INNER JOIN
      (
        SELECT OBJEK AS INPUT_OBJEK,
        CUOBJ
        FROM {HanaSchema}.INOB
      ) AS INOB
      ON INOB.INPUT_OBJEK = EQUNR
      INNER JOIN
      (
        SELECT
          OBJEK, ATINN, ATFLV, ATWRT
        FROM {HanaSchema}.AUSP
      ) AS AUSP
      ON INOB.CUOBJ = AUSP.OBJEK
      INNER JOIN
      (
        SELECT ATINN AS CABN_ATTIN, ATNAM
        FROM {HanaSchema}.CABN
        WHERE ATNAM IN ('IE_PRT_TENSION', 'IE_PRT_NB_PHASES', 'IE_PRT_NB_PHASES_CODE', 'IE_PRT_REGL_PREL', 'IE_PRT_REGL_INJE')
      ) AS CABN
      ON AUSP.ATINN = CABN.CABN_ATTIN
      LEFT JOIN
      (
        SELECT
          ATINN AS CAWNT_ATINN,
          ATZHL,
          ATWTB
        FROM {HanaSchema}.CAWNT
        WHERE SPRAS = 'F'
      ) AS CAWNT
      ON AUSP.ATINN = CAWNT.CAWNT_ATINN AND (LPAD(AUSP.ATWRT, 4, '0') = LPAD(CAWNT.ATZHL, 4, '0') OR LEFT(AUSP.ATWRT,1) = RIGHT(CAWNT.ATZHL, 1))
      GROUP BY
      SERNR
    ) AS T