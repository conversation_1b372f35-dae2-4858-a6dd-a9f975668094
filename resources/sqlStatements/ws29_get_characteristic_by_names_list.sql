WITH get_characteristic AS (
  SELECT INPUT_OBJEK, ATINN, ATNAM, ATFLV, ATWRT, ATWTB, ATWTB2 FROM
  (SELECT OBJEK AS INPUT_OBJEK, CUOBJ FROM {HanaSchema}.INOB WHERE OBJEK  = (:OBJEK)) AS INOB
  INNER JOIN
  (SELECT OBJEK, ATINN, ATFLV, ATWRT FROM {HanaSchema}.AUSP) AS AUSP
  ON INOB.CUOBJ = AUSP.OBJEK
  INNER JOIN
  (SELECT ATINN AS CABN_ATTIN, ATNAM FROM {HanaSchema}.CABN) AS CABN
  ON AUSP.ATINN = CABN.CABN_ATTIN
  LEFT JOIN
  (SELECT ATINN AS CAWNT_ATINN, ATZHL, ATWTB FROM {HanaSchema}.CAWNT WHERE SPRAS = 'F') AS CAWNT
  ON AUSP.ATINN = CAWNT.CAWNT_ATINN AND (LPAD(AUSP.ATWRT, 4, '0') = LPAD(CAWNT.ATZHL, 4, '0') OR LEFT(AUSP.ATWRT,1) = RIGHT(CAWNT.ATZHL, 1))
  LEFT JOIN
  (SELECT CAWN.ATINN AS CAWNT_ATINN, ATWTB AS ATWTB2, ATWRT AS ATWRT2 FROM {HanaSchema}.CAWN JOIN {HanaSchema}.CAWNT ON CAWNT.ATINN = CAWN.ATINN AND CAWNT.ATZHL = CAWN.ATZHL WHERE CAWNT.SPRAS = 'F') AS CAWNT2
  ON CAWNT2.CAWNT_ATINN = AUSP.ATINN AND CAWNT2.ATWRT2 = ATWRT
)
SELECT DISTINCT * FROM get_characteristic