WITH get_ean AS (
  SELECT
    EUITRANS.EXT_UI AS EAN,
    EUITRANS.INT_UI,
    EUIINSTLN.ANLAGE,
    EANL.SPARTE,
    EANL.VSTELLE,
    ETINS.EQUNR
  FROM {HanaSchema}.EUITRANS
  JOIN {Hana<PERSON>che<PERSON>}.EUIINSTLN ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
  JOIN {Hana<PERSON>che<PERSON>}.EANL ON EANL.ANLAGE = EUIINSTLN.ANLAGE
  JOIN {HanaSchema}.ETINS ON EANL.VSTELLE = ETINS.VSTELLE
  WHERE EUITRANS.EXT_UI = (:Ean)
), get_adress_info AS (
  SELECT DISTINCT
    ADRC.CITY1 AS CITY,
    ADRC.POST_CODE1 AS POSTCODE,
    ADRC.STREET AS STREET,
    ADRC.HOUSE_NUM1 AS HOUSE_NUM,
    EVBS.VSTELLE
  FROM {HanaSchema}.EVBS
  LEFT JOIN {HanaSchema}.ILOA ON EVBS.HAUS = ILOA.TPLNR
  LEFT JOIN {<PERSON><PERSON>che<PERSON>}.ADRC ON ILOA.ADRNR = ADRC.ADDRNUMBER
), get_counter_info AS (
  SELECT 
    EASTL.ANLAGE,
    EGERH.EQUNR,
    EQUI.SERNR AS METER_NUMBER
  FROM {HanaSchema}.EASTL
  JOIN {HanaSchema}.EGERH ON EASTL.LOGIKNR = EGERH.LOGIKNR
  JOIN {HanaSchema}.EQUI ON EGERH.EQUNR = EQUI.EQUNR
WHERE EASTL.AB <= now()
  AND EASTL.BIS >= now()
)
SELECT DISTINCT
  get_ean.EAN as "Ean",
  get_counter_info.METER_NUMBER as "MeterNumber"
FROM get_ean
JOIN get_adress_info ON get_adress_info.VSTELLE = get_ean.VSTELLE
JOIN get_counter_info ON get_counter_info.ANLAGE = get_ean.ANLAGE
WHERE CONTAINS(get_adress_info.STREET, trim(:Street), FUZZY(0.90))
  AND get_adress_info.POSTCODE = trim(:PostCode)
ORDER BY get_counter_info.METER_NUMBER
