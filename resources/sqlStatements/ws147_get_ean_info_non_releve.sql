WITH ean_commune AS (
    SELECT DISTINCT
       EXT_UI AS EAN,
       EANL.SPARTE,
        EANL.ANLAGE,
        EANL.VSTELLE
    FROM {HanaSchema}.ETTIFN
    JOIN {HanaSchema}.EUIINSTLN ON EUIINSTLN.ANLAGE = ETTIFN.ANLAGE
    JOIN {HanaSchema}.EUITRANS ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
    JOIN {HanaSchema}.EANL ON EANL.ANLAGE = ETTIFN.ANLAGE AND EANL.SPARTE = (CASE WHEN ETTIFN.OPERAND = 'E_COMMUNE' THEN '01' WHEN ETTIFN.OPERAND = 'G_COMMUNE' THEN '02' END)
    WHERE ETTIFN.OPERAND IN ('G_COMMUNE', 'E_COMMUNE') AND ETTIFN.STRING1 = :IdCommune AND ETTIFN.AB <= CURRENT_DATE AND CURRENT_DATE <= ETTIFN.BIS
), get_adress_info AS (
  SELECT DISTINCT
    CITY1,
    POST_CODE1,
    STREET,
    HOUSE_NUM1,
    HAUS_NUM2,
    V<PERSON><PERSON>LE
  FROM {HanaSchema}.EVBS
  LEFT JOIN {HanaSchema}.ILOA ON EVBS.HAUS = ILOA.TPLNR
  LEFT JOIN {HanaSchema}.ADRC ON ILOA.ADRNR = ADRC.ADDRNUMBER
), get_counter_sernr AS (
    SELECT DISTINCT ANLAGE, SERNR, ETDZ.EQUNR,
        CASE WHEN CAP_ACT_GRP IN('9000','9001', '9002') THEN TRUE ELSE FALSE END AS IS_SMART,
        (CASE WHEN EASTI.ZWZUART = 04 AND EASTI.OPCODE = 6 THEN true ELSE false END) AS CPT_CONTROL
     FROM (
        SELECT ANLAGE, LOGIKNR
        FROM {HanaSchema}.EASTL
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
          AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
    ) AS EASTL
    JOIN (
        SELECT
            EQUNR AS EGERH_EQUNR,
            ZWGRUPPE AS EGERH_ZWGRUPPE,
            LOGIKNR AS EGERH_LOGIKNR,
            DEVLOC AS DEVLOC,
            CAP_ACT_GRP
        FROM {HanaSchema}.EGERH
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
    ) AS EGERH
    ON EASTL.LOGIKNR = EGERH.EGERH_LOGIKNR
    JOIN (SELECT * FROM {HanaSchema}.EQUI WHERE EQART != 'IG_CNV') AS EQUI ON EGERH.EGERH_EQUNR = EQUI.EQUNR
    JOIN {HanaSchema}.ETDZ ON ETDZ.EQUNR = EGERH_EQUNR /*AND ETDZ.nablesen != 'X'*/ AND ETDZ.MASSREAD IN ('KWH','M3') AND ETDZ.AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= ETDZ.BIS
    LEFT JOIN {HanaSchema}.EASTI ON EASTI.LOGIKZW = ETDZ.LOGIKZW AND EASTI.AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= EASTI.BIS
), latest_index AS (
    WITH WS34_GET_INDEX AS (
    SELECT *,
     MIN(ORDERED_ABLESGR) OVER (PARTITION BY EAN, ETDZ_LOGIKZW, ADAT, ANLAGE_INFO) AS BEST_ABLESGR
     FROM (
        ( SELECT
            EUITRANS.EXT_UI AS EAN,
            EUITRANS.INT_UI
          FROM {HanaSchema}.EUITRANS
          JOIN ean_commune ON ean_commune.EAN = EUITRANS.EXT_UI
        ) AS EUITRANS
      INNER JOIN
        ( SELECT
          EUIINSTLN.INT_UI,
          ANLAGE
          FROM {HanaSchema}.EUIINSTLN
        ) AS EUIINSTLN
      ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
      INNER JOIN
      (
        SELECT
          ANLAGE,
          SPARTE,
          VERTRAG,
          VKONTO,
          EINZDAT,
          AUSZDAT,
          INVOICING_PARTY
        FROM {HanaSchema}.EVER
      ) AS EVER
      ON EUIINSTLN.ANLAGE = EVER.ANLAGE
      INNER JOIN
      (
        SELECT
          VKONT,
          GPART
        FROM {HanaSchema}.FKKVKP
      ) AS FKKVKP
      ON FKKVKP.VKONT = EVER.VKONTO
      INNER JOIN
      (
        SELECT
          PARTNER,
          UPPER(TRIM(NAME_FIRST)) AS NAME_FIRST,
          UPPER(TRIM(NAME_LAST)) AS NAME_LAST
        FROM {HanaSchema}.BUT000
      ) AS BUT000
      ON BUT000.PARTNER = FKKVKP.GPART
      INNER JOIN
      (
        SELECT
          ANLAGE,
          LOGIKZW,
          TARIFART,
          AB AS EASTS_AB,
          BIS AS EASTS_BIS
        FROM {HanaSchema}.EASTS
      ) AS EASTS
      ON EVER.ANLAGE = EASTS.ANLAGE
      INNER JOIN
      (
        SELECT DISTINCT
          AKLASSE,
          ANLAGE AS ANLAGE_INFO,
          ABLEINH AS EANLH_ABLEINH
        FROM {HanaSchema}.EANLH
      ) AS EANLH
      ON EVER.ANLAGE = EANLH.ANLAGE_INFO
      INNER JOIN
      (
        SELECT
          LOGIKZW AS ETDZ_LOGIKZW,
          EQUNR,
          SPARTYP,
          ZWNUMMER AS ETDZ_ZWNUMMER,
          ZWKENN,
          ZWART,
          AB AS ETDZ_AB,
          BIS AS ETDZ_BIS,
          KENNZIFF
        FROM {HanaSchema}.ETDZ
      ) AS ETDZ
      ON EASTS.LOGIKZW = ETDZ.ETDZ_LOGIKZW
      LEFT JOIN {HanaSchema}.EASTI ON EASTI.LOGIKZW = ETDZ.ETDZ_LOGIKZW AND EASTI.AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= EASTI.BIS
      INNER JOIN
      (
        SELECT
          EQUNR AS EQUI_EQUNR,
          SERNR,
          MATNR
        FROM {HanaSchema}.EQUI
      ) AS EQUI
      ON EQUI.EQUI_EQUNR = ETDZ.EQUNR
      INNER JOIN
      (
        SELECT
          ABLBELNR,
          ANLAGE,
          ABLEINH,
          ADATSOLL AS DatePlanned,
          ABLESGR,
          /* Select in priority index 1 (communique), then 3 (indexier) then 2 (estimé) */
          (CASE WHEN ABLESGR = '02' THEN '03' ELSE CASE WHEN ABLESGR = '03' THEN '02' ELSE ABLESGR END END) AS ORDERED_ABLESGR
        FROM {HanaSchema}.EABLG
      ) AS EABLG
      ON EABLG.ANLAGE = EVER.ANLAGE AND EABLG.ABLEINH = EANLH.EANLH_ABLEINH
      INNER JOIN
      (
        SELECT
          GERNR,
          EQUNR AS EABL_EQUNR,
          ABLBELNR AS EABL_ABLBELNR,
          ZWNUMMER,
          ADATSOLL,
          SUBSTRING(ADATSOLL, 0, 4) AS ADATSOLL_YEAR,
          ADAT,
          V_ZWSTAND,
          N_ZWSTAND,
          MASSREAD,
          MASSBILL,
          ABLESTYP,
          ABLSTAT
        FROM {HanaSchema}.EABL
      ) AS EABL
      ON
      /* Same transaction */
      EABLG.ABLBELNR = EABL.EABL_ABLBELNR
      /* Same counter and cadran: avoids duplicates but then we have "missing data" or incomplete history */
      AND ETDZ.EQUNR = EABL.EABL_EQUNR
      AND EQUI.SERNR = EABL.GERNR
      AND ETDZ.ETDZ_ZWNUMMER = EABL.ZWNUMMER
      LEFT JOIN(
        SELECT
          TERMSCHL,
          SUBSTRING(TERMTDAT, 0, 4) AS UR_YEAR,
          SUBSTRING(ADATSOLL, 0, 4) AS UR_ADATSOLL_YEAR,
          ADATSOLL AS UR_ADATSOLL
        FROM {HanaSchema}.TE418
      ) AS TE418
      ON EABLG.ABLEINH = TE418.TERMSCHL AND ADATSOLL = UR_ADATSOLL
    )
      WHERE
        /* Valid equipement */
        ETDZ_AB <= ADAT
        AND ADAT <= ETDZ_BIS
        /* Valid cadran */
        AND EASTS_AB <= ADAT
        AND ADAT <= EASTS_BIS
        /* Cadran in validity of equipement */
        AND ETDZ_AB <= EASTS_AB
        AND ETDZ_BIS >= EASTS_BIS
        /* DateReleve in date contrat */
        AND EINZDAT <= EABL.ADAT
        AND EABL.ADAT <= AUSZDAT
        /* Remove CPT_CONTROL */
        AND (CASE WHEN EASTI.ZWZUART = 04 AND EASTI.OPCODE = 6 THEN true ELSE false END) = FALSE
        /* Remove index to validate (that show 0 as index value) */
        AND ABLSTAT != 0
    ), WS34_SELECT_INDEX AS (
      SELECT
        EAN,
        SPARTE,
        GPART,
        NAME_FIRST,
        NAME_LAST,
        INVOICING_PARTY,
        WS34_GET_INDEX.ANLAGE_INFO AS ANLAGE,
        VERTRAG,
        EANLH_ABLEINH AS ABLEINH,
        EASTS_AB,
        EASTS_BIS,
        EINZDAT,
        AUSZDAT,
        ETDZ_AB,
        ETDZ_BIS,
        ETDZ_LOGIKZW AS LOGIKZW,
        EABL_ABLBELNR AS ABLBELNR,
        SERNR AS EQUNR_SERNR,
        GERNR AS EABL_GERNR,
        EQUNR AS ETDZ_EQUNR,
        EABL_EQUNR,
        ETDZ_ZWNUMMER,
        ZWNUMMER AS EABL_ZWNUMMER,
        UR_YEAR,
        ADATSOLL,
        UR_ADATSOLL,
        ABS(DAYS_BETWEEN(ADAT, UR_ADATSOLL)) AS DAYS_TO_PLAN,
        ADAT,
        ABLESGR,
       (CASE WHEN ABLESGR = '02' THEN '03' ELSE CASE WHEN ABLESGR = '03' THEN '02' ELSE ABLESGR END END) AS ORDERED_ABLESGR,
        ABLESTYP,
        CAST(V_ZWSTAND AS VARCHAR) AS "Index_Integer",
        CAST(N_ZWSTAND AS VARCHAR) AS "Index_Decimal",
        SPARTYP,
        TARIFART,
        ZWART,
        ZWKENN,
        AKLASSE,
        MASSBILL,
        MASSREAD,
        MATNR,
        EQUI_EQUNR,
        KENNZIFF,
        MAX(ADAT) OVER (PARTITION BY EAN) AS LAST_DATE
        FROM WS34_GET_INDEX
        WHERE ORDERED_ABLESGR = BEST_ABLESGR AND MASSREAD in ('KWH','M3')  AND MASSBILL in ('KWH','M3')
    ), COUNT_ENTRY AS (
      SELECT a.equnr, COUNT(*) AS "lv_nb_lines"
      FROM {HanaSchema}.EQUI AS a
        INNER JOIN {HanaSchema}.ETDZ AS b ON a.equnr = b.equnr AND b.nablesen != 'X' AND b.ab  <= CURRENT_DATE AND b.bis >= CURRENT_DATE
        INNER JOIN {HanaSchema}.ETYP AS i ON  i.matnr = a.matnr
        INNER JOIN {HanaSchema}.EZWG AS h ON h.zwgruppe = i.zwgruppe AND h.zwnummer = b.zwnummer
      GROUP BY a.equnr
    ), ANZART AS (
      SELECT h.anzart, b.zwnummer, a.equnr, 
        CASE WHEN CAP_ACT_GRP IN('9000','9001', '9002') THEN TRUE ELSE FALSE END AS "Smart"
      FROM {HanaSchema}.EQUI AS a
      INNER JOIN {HanaSchema}.ETDZ AS b ON a.equnr = b.equnr
      INNER JOIN {HanaSchema}.EASTS AS c ON b.logikzw = c.logikzw AND c.ab <= CURRENT_DATE AND c.bis >= CURRENT_DATE
      INNER JOIN {HanaSchema}.EGERH AS i ON i.equnr = a.equnr AND i.ab <= CURRENT_DATE AND i.bis >= CURRENT_DATE
      INNER JOIN {HanaSchema}.EZWG AS h ON h.zwgruppe = i.zwgruppe AND h.zwnummer = b.zwnummer
    ), ATWRT AS (
        SELECT AUSP.atwrt, AUSP.objek
        FROM {HanaSchema}.AUSP
        INNER JOIN {HanaSchema}.KSSK ON KSSK.objek = AUSP.objek AND KSSK.klart = '001'
        INNER JOIN {HanaSchema}.CABN ON CABN.atinn = AUSP.atinn AND CABN.atnam = 'IE_TA_TYPE_CPT'
        INNER JOIN {HanaSchema}.KLAH ON KLAH.clint = KSSK.clint AND KLAH.class = 'IE_TA_GEN'
        WHERE AUSP.klart = '001'
    )
    SELECT DISTINCT
      EAN AS "Ean",
      LTRIM(EQUNR_SERNR, '0') AS "NumCompteur",
      ZWART AS "CatCadran",
      ADAT AS "DateRel",
      "Index_Integer",
      "Index_Decimal",
      MASSREAD AS "IndexUnit",
      ABLESTYP AS "IndexQual",
      TARIFART AS "CatTarif",
      ETDZ_ZWNUMMER,
      MATNR,
      EQUI_EQUNR,
      "lv_nb_lines",
      ATWRT,
      KENNZIFF AS "CodeCadran",
      "Smart",
      LOGIKZW
    FROM WS34_SELECT_INDEX
    LEFT OUTER JOIN COUNT_ENTRY ON COUNT_ENTRY.equnr = WS34_SELECT_INDEX.EQUI_EQUNR
    LEFT OUTER JOIN ANZART ON ANZART.equnr = WS34_SELECT_INDEX.EQUI_EQUNR AND ANZART.zwnummer = WS34_SELECT_INDEX.ETDZ_ZWNUMMER
    LEFT OUTER JOIN ATWRT ON ATWRT.objek = WS34_SELECT_INDEX.matnr
    WHERE ADAT = LAST_DATE
    ORDER BY "Ean","DateRel" DESC, "NumCompteur", "CodeCadran"
), ean_info AS (
    SELECT DISTINCT
       ec.EAN AS "Ean",
       CASE WHEN SPARTE = '01' THEN 'Elec' WHEN SPARTE = '02' THEN 'Gaz' END AS "Type",
       LTRIM(SERNR, '0') AS "NumCpt",
        IS_SMART AS "Smart",
        STREET AS "Adresse_Rue",
        HOUSE_NUM1 AS "Adresse_NumRue",
        HAUS_NUM2 AS "Adresse_NumComp",
        POST_CODE1 AS "Adresse_CodePostal",
        CITY1 AS "Adresse_Localite",
        gcs.EQUNR
    FROM ean_commune ec
    JOIN get_counter_sernr gcs ON gcs.ANLAGE = ec.ANLAGE
    JOIN get_adress_info gai ON gai.VSTELLE = ec.VSTELLE
    WHERE (:Smart IS NULL OR IS_SMART = :Smart) AND CPT_CONTROL = FALSE
), EanWaitingInput AS (
    SELECT DISTINCT ETRG.TRIGSTAT, EUITRANS.EXT_UI AS EAN
    FROM {HanaSchema}.EUITRANS
    JOIN {HanaSchema}.EUIINSTLN ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
    JOIN {HanaSchema}.ETRG ON EUIINSTLN.ANLAGE = ETRG.ANLAGE
), CadranInfo AS (
    SELECT DISTINCT
        e.ext_ui AS "Ean",
        LTRIM(a.sernr, '0') AS "NumCpt",
        a.equnr,
        b.logikzw,
        b.zwart AS "CatCadran",
        b.stanzvor,
        b.stanznac,
        f.zwzuart,
        b.zwnummer AS "ETDZ_ZWNUMMER",
        a.matnr,
        h.zwgruppe,
        h.kennziff AS "CodeCadran",
        h.anzart AS "ANZART",
        c.anlage,
        c.tarifart AS "CatTarif",
        (
            SELECT COUNT(*)
            FROM {HanaSchema}.equi AS a2
            INNER JOIN {HanaSchema}.etdz AS b2 ON a2.equnr = b2.equnr
            INNER JOIN {HanaSchema}.etyp AS i2 ON i2.matnr = a2.matnr
            INNER JOIN {HanaSchema}.ezwg AS h2 ON h2.zwgruppe = i2.zwgruppe AND h2.zwnummer = b2.zwnummer
            WHERE
                a2.equnr = a.equnr
                AND h2.zwgruppe = h.zwgruppe
                AND b2.nablesen != 'X'
                AND b2.ab <= CURRENT_DATE 
                AND b2.bis >= CURRENT_DATE 
        ) AS lv_nb_lines,
        (
            SELECT DISTINCT AUSP.atwrt
            FROM {HanaSchema}.AUSP
            INNER JOIN {HanaSchema}.KSSK ON KSSK.objek = AUSP.objek AND KSSK.klart = '001'
            INNER JOIN {HanaSchema}.CABN ON CABN.atinn = AUSP.atinn AND CABN.atnam = 'IE_TA_TYPE_CPT'
            INNER JOIN {HanaSchema}.KLAH ON KLAH.clint = KSSK.clint AND KLAH.class = 'IE_TA_GEN'
            WHERE AUSP.klart = '001' AND AUSP.objek = a.matnr
        ) AS "ATWRT"
    FROM {HanaSchema}.equi AS a 
        INNER JOIN {HanaSchema}.etdz AS b ON a.equnr = b.equnr
        INNER JOIN {HanaSchema}.easts AS c ON b.logikzw = c.logikzw
        INNER JOIN {HanaSchema}.etyp AS i ON  i.matnr = a.matnr
        INNER JOIN {HanaSchema}.ezwg AS h ON h.zwgruppe = i.zwgruppe
            AND h.zwnummer = b.zwnummer
        LEFT OUTER JOIN {HanaSchema}.easti AS f ON b.logikzw = f.logikzw
        INNER JOIN {HanaSchema}.euiinstln AS d ON c.anlage = d.anlage
        INNER JOIN {HanaSchema}.euitrans AS e ON d.int_ui = e.int_ui
            AND d.dateto = e.dateto
            AND d.timeto = e.timeto
    WHERE b.nablesen  != 'X'
        AND b.ab <= CURRENT_DATE 
        AND b.bis > CURRENT_DATE 
        AND b.intsizeid = '' 
        AND c.bis > CURRENT_DATE 
        AND c.ab <= CURRENT_DATE 
), FondEchelle AS(
    SELECT DISTINCT ETDZ.STANZVOR AS NombreGauche, ETDZ.STANZNAC AS NombreDroite,  EUITRANS.EXT_UI AS EAN, EQUNR
    FROM {HanaSchema}.ETDZ
    JOIN {HanaSchema}.EASTS ON  ETDZ.LOGIKZW = EASTS.LOGIKZW
    JOIN {HanaSchema}.EUIINSTLN ON EASTS.ANLAGE = EUIINSTLN.ANLAGE
    JOIN {HanaSchema}.EUITRANS ON EUIINSTLN.INT_UI = EUITRANS.INT_UI
    WHERE EASTS.bis = '99991231' AND EASTS.ZWNABR = '' AND MASSREAD IN ('KWH','M3')
)
SELECT  *, (
        SELECT *
        FROM latest_index
        WHERE latest_index."Ean" = ean_info."Ean" AND latest_index."NumCompteur" = ean_info."NumCpt"
        FOR JSON
    ) AS "Index", (
        SELECT *
        FROM CadranInfo
        WHERE CadranInfo."Ean" = ean_info."Ean" AND CadranInfo."NumCpt" = ean_info."NumCpt"
        FOR JSON
    ) AS "CadranInfo", (
        SELECT 
          ZUORDDAT,
          ADATSOLL,
          TARIFTYP
        FROM (
          SELECT 
            MIN(BEGABL) OVER (PARTITION BY TERMSCHL) AS MIN_BEGABL,  
            BEGABL,
            ZUORDDAT,
            ADATSOLL,
            TARIFTYP
          FROM {HanaSchema}.TE418
          JOIN (
              SELECT
               ANLAGE, 
               ABLEINH,
               TARIFTYP
              FROM (
                SELECT
                  EXT_UI AS EAN,
                  INT_UI
                FROM {HanaSchema}.EUITRANS
                WHERE EXT_UI = ean_info."Ean"
              ) AS EUITRANS
              INNER JOIN (
                SELECT 
                  INT_UI,
                  ANLAGE AS EUIINSTLN_ANLAGE
                FROM {HanaSchema}.EUIINSTLN
              ) AS EUIINSTLN
              ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
              LEFT JOIN (
                SELECT * 
                FROM {HanaSchema}.EANLH
                WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') 
                  AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
              ) AS EANLH
              ON EUIINSTLN.EUIINSTLN_ANLAGE = EANLH.ANLAGE
          ) ON TERMSCHL = ABLEINH
          WHERE BEGABL >= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
        )
        WHERE MIN_BEGABL = BEGABL
        FOR JSON
    ) AS "Periode"
FROM ean_info
LEFT JOIN EanWaitingInput ON ean_info."Ean" = EanWaitingInput.EAN
LEFT JOIN FondEchelle ON ean_info."Ean" = FondEchelle.EAN AND ean_info.EQUNR = FondEchelle.EQUNR
WHERE (TRIGSTAT = 1 OR :FullEan = TRUE)