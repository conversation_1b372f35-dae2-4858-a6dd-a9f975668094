WITH
get_installation_from_ean AS (
  SELECT
   ANLAGE,
   AKLASSE,
   BIS,
   AB,
   ABLEINH,
   TARIFTYP,
   PORTION
    FROM
    (SELECT
      EXT_UI AS EAN,
      INT_UI
      FROM {HanaSchema}.EUITRANS
      WHERE EXT_UI = (:Ean)
      )AS EUITRANS
  INNER JOIN
  (SELECT
    INT_UI,
    ANLAGE AS EUIINSTLN_ANLAGE
    FROM {HanaSchema}.EUIINSTLN
  )AS EUIINSTLN
  ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
  LEFT JOIN
  (SELECT *
    FROM {HanaSchema}.EANLH
    WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')  <= BIS
  ) AS EANLH
  ON EUIINSTLN.EUIINSTLN_ANLAGE = EANLH.ANLAGE
  LEFT JOIN
  (SELECT TERMSCHL, PORTION FROM {HanaSchema}.TE422) AS PERIODE_ENCODAGE
  ON EANLH.ABLEINH = PERIODE_ENCODAGE.TERMSCHL
)
SELECT * FROM get_installation_from_ean