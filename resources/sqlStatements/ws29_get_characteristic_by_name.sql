WITH get_characteristic AS (
  SELECT INPUT_OBJEK, ATINN, ATNAM, ATFLV, ATWRT, ATWTB FROM
  (SELECT OBJEK AS INPUT_OBJEK, CUOBJ FROM {HanaSchema}.INOB WHERE OBJEK  = (:OBJEK)) AS INOB
  INNER JOIN
  (SELECT OBJEK, ATINN, ATFLV, ATWRT FROM {HanaSchema}.AUSP) AS AUSP
  ON INOB.CUOBJ = AUSP.OBJEK
  INNER JOIN
  (SELECT ATINN AS CABN_ATTIN, ATNAM FROM {HanaSchema}.CABN WHERE ATNAM = (:ATNAM)) AS CABN
  ON AUSP.ATINN = CABN.CABN_ATTIN
  LEFT JOIN
  (SELECT ATINN AS CAWNT_ATINN, ATZHL, ATWTB FROM {HanaSchema}.CAWNT WHERE SPRAS = 'F') AS CAWNT
  ON AUSP.ATINN = CAWNT.CAWNT_ATINN AND LPAD(AUSP.ATWRT, 4, '0') = CAWNT.ATZHL
)
SELECT * FROM get_characteristic