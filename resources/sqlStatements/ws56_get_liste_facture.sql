
WITH
IHPA_AUFK AS (
    SELECT DISTINCT * FROM (
        (SELECT IHPA.OBJNR AS IHPA_OBJNR, IHPA.PARNR AS PARNR FROM {HanaSchema}.IHPA where (:PartenaireId) is null or LPAD (PARNR, 12, '0') = LPAD ((:PartenaireId), 12, '0') ) AS IHPA
        INNER JOIN
        (SELECT OBJNR AS AUFK_OBJNR, AUFNR AS AUFK_AUFNR, KTEXT AS "KTEXT" FROM {HanaSchema}.AUFK) AS AUFK
        ON AUFK_OBJNR = IHPA.IHPA_OBJNR
    )
),
WS39 AS (
SELECT DISTINCT
        (CASE WHEN DATS_IS_VALID(ANGDT) = 1 THEN TO_VARCHAR(TO_DATE(ANGDT, 'YYYYMMDD'), 'DD/MM/YYYY') ELSE '00000000' END) AS "Date",
        Devis as "Devi<PERSON>",
        <PERSON>TE<PERSON> as "Description",
        <PERSON><PERSON><PERSON><PERSON> as "Montant<PERSON>inal",
        '' AS "AmountOpen",
        (CASE WHEN DATS_IS_VALID(DtFin) = 1 THEN TO_VARCHAR(TO_DATE(DtFin, 'YYYYMMDD'), 'DD/MM/YYYY') ELSE '00000000' END) AS "DtFin",
        '' AS "Statut"  FROM(
            IHPA_AUFK
            LEFT JOIN
                (SELECT QMEL.QMNUM AS QMEL_QMNUM, QMEL.OBJNR AS QMEL_OBJNR, QMEL.AUFNR AS QMEL_AUFNR FROM {HanaSchema}.QMEL WHERE QMNUM is not null and QMNUM = AUFNR) AS QMEL
            ON QMEL.QMEL_OBJNR = IHPA_AUFK.IHPA_OBJNR
            INNER JOIN
                (SELECT VBAK.AUFNR AS VBAK_AUFNR, LTRIM(VBAK.VBELN,'0') AS Devis, VBAK.BNDDT AS DtFin, VBAK.NETWR AS VBAK_NETWR, VBAK.KNUMV AS VBAK_KNUMV, VBAK.ANGDT AS "ANGDT",
                    KONV.KNUMV AS KONV_KNUMV,
                    KONV.KSCHL,
                    KONV.KWERT AS KONV_KWERT,
                    TO_VARCHAR(VBAK.NETWR + KONV.KWERT, '9999999.99') AS MontantFinal
                FROM {HanaSchema}.VBAK AS VBAK, {HanaSchema}.KONV AS KONV
                WHERE VBAK.KNUMV = KONV.KNUMV AND KONV.KSCHL = 'MWST' ) AS VBAKKONF
            ON IHPA_AUFK.AUFK_AUFNR = VBAK_AUFNR
            INNER JOIN
                (SELECT JCDS.OBJNR AS JCDS_OBJNR
                FROM {HanaSchema}.JCDS
                WHERE STAT = 'E0068' AND INACT = '') AS JCDS
            ON JCDS.JCDS_OBJNR = IHPA_AUFK.AUFK_OBJNR
)
WHERE DATS_IS_VALID(DtFin) = 1
)
SELECT * FROM WS39