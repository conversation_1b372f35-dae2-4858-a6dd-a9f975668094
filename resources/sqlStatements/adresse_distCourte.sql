SELECT CASE
        WHEN q."Adresse" ->> 'Rue' is null THEN raise_data_error(concat('Invalid destination adress : ', q."input"->>'NumRue',' ', q."input"->>'Rue', ', ', q."input"->>'Cdpostal',' ', q."input"->>'Localite' )) -- note : this function is created on the db
        WHEN q."Distance" is null THEN raise_data_error(concat('Invalid source adress : ', q."source"->>'NumRue',' ', q."source"->>'Rue', ', ', q."source"->>'Cdpostal',' ', q."source"->>'Localite' )) -- note : this function is created on the db
    END as "Distance",
    q."Adresse",
    q."Distance",
    q."GPS"
FROM (
    SELECT
        %(Source)s::json as "source",
        json_build_object(
            'NumRue',input_dest."NumRue",
            'Rue',input_dest."Rue",
            'Cdpostal',input_dest."Cdpostal",
            'Localite',input_dest."Localite"
        ) as "input",
        json_build_object(
            'NumRue',numero,
            'Rue',ruenomlangueprimaire,
            'Cdpostal',codepostal,
            'Localite',communenomlangueprimaire
        ) as "Adresse",
        (SELECT (point(db.longprojfacadewgs84,db.latprojfacadewgs84) <@> point(db_.longprojfacadewgs84, db_.latprojfacadewgs84))*1.609344  from resa_geo.addresses db_
            where(
                db_.ruenomlangueprimaire = %(Source)s::json ->> 'Rue' AND
                db_.numero = %(Source)s::json ->> 'NumRue' AND
                db_.codepostal = %(Source)s::json ->> 'Cdpostal'
                -- AND db_.communenomlangueprimaire = %(Source)s::json ->>'Localite'
            )
        ) as "Distance",
        json_build_object(
            'ProjectionFacade', json_build_object(
                'Long', longprojfacadewgs84,
                'Lat', latprojfacadewgs84
            ),
            'MilieuBatiment', json_build_object(
                'Long', longmilieubatiwgs84,
                'Lat', latmilieubatiwgs84
            )
        ) as "GPS"
    from resa_geo.addresses db
    RIGHT JOIN (
        SELECT value ->> 'NumRue' as "NumRue",
            value ->> 'Rue' as "Rue",
            value ->> 'Localite' as "Localite",
            value ->> 'Cdpostal' as "Cdpostal"
            FROM json_array_elements(%(Destinations)s::json)
        ) input_dest
        ON (
            input_dest."NumRue" = db.numero AND
            input_dest."Rue" = db.ruenomlangueprimaire AND
            input_dest."Cdpostal" = db.codepostal
            -- AND input_dest."Localite" = db.communenomlangueprimaire
        )
) q
ORDER BY q."Distance"
;
