SELECT
    CITY1 AS localite,
    POST_CODE1 as cd_postal,
    STREET AS rue,
    HOUSE_NUM1 as num_rue,
    QMDAT AS date_panne,
    heure_prise_charge,
    heure_fin,
    date_fin,
    cause_code,
    GPS_LAT AS Lat,
    GPS_LONG AS Long,
    QMEL.QMNUM AS "id"
    FROM
        (SELECT
          ADDRNUMBER,
          STREET,
          HOUSE_NUM1,
          CITY1,
          POST_CODE1,
          UPPER(TRIM(STREET)) AS STREET_CLEAN,
          UPPER(TRIM(HOUSE_NUM1)) AS HOUSE_NUM_CLEAN,
          UPPER(TRIM(CITY1)) AS CLEAN_CITY
          FROM {HanaSchema}.ADRC) AS ADRC
        INNER JOIN
        (SELECT
          UPPER(TRIM(RUE_OR)) AS RUE_OR,
          UPPER(TRIM(NUMERO_OR)) AS NUMERO_OR,
          UPPER(TRIM(LOCALITE_OR)) AS LOCALITE_OR,
          MAX(GPS_LAT) AS GPS_LAT,
          MIN(GPS_LONG) AS GPS_LONG
        FROM {GisSchema}.EAN_GPS
        GROUP BY RUE_OR, NUMERO_OR, LOCALITE_OR
        ) AS EAN_GPS
        ON ADRC.STREET_CLEAN = EAN_GPS.RUE_OR AND ADRC.HOUSE_NUM_CLEAN = EAN_GPS.NUMERO_OR AND CLEAN_CITY = LOCALITE_OR
        INNER JOIN
        (SELECT *
        FROM {HanaSchema}.QMEL
        WHERE QMART = 'E1' AND PRIOK = '1' AND ERDAT >= TO_VARCHAR(ADD_DAYS(CURRENT_DATE, -365), 'YYYYMMDD')
        AND QMEL.QMNUM = :Id
        ) AS QMEL
        ON ADRC.ADDRNUMBER = QMEL.ADRNR
        LEFT JOIN
        (SELECT QMNUM, PSTUR AS heure_prise_charge
        FROM {HanaSchema}.QMMA
        WHERE MNCOD = '01'
        ) AS PRISE_CHARGE
        ON QMEL.QMNUM = PRISE_CHARGE.QMNUM
        LEFT JOIN
        (SELECT QMNUM, PSTER AS date_fin, PSTUR AS heure_fin
        FROM {HanaSchema}.QMMA
        WHERE MNCOD = '03'
        ) AS FIN_TRAITEMENT
        ON QMEL.QMNUM = FIN_TRAITEMENT.QMNUM
        LEFT JOIN
        (SELECT QMNUM, OTEIL AS cause_code
        FROM {HanaSchema}.QMFE
        ) AS CAUSE
        ON QMEL.QMNUM = CAUSE.QMNUM