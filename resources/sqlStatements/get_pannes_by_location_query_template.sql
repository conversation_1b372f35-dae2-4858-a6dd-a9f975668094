WITH
  filter_avis_by_id AS (
    SELECT Qmnum, Objnr, Qmart, <PERSON><PERSON><PERSON>, Qmdab, Aufnr, Adrnr AS PanneAdrnr, 
		(CASE WHEN Priok IN ('8', '9') THEN true ELSE false END) AS ep
    FROM {HanaSchema}.Qmel
    WHERE Qmart = 'E1'
  ),
  get_avis AS (
    SELECT *
    FROM filter_avis_by_id
    INNER JOIN
      (SELECT Qmnum AS QmnnumHeader, Equnr, Iloan FROM {HanaSchema}.Qmih) AS avis_header
    ON filter_avis_by_id.Qmnum = avis_header.QmnnumHeader
    INNER JOIN
      (SELECT Iloan AS PanneIloan, Tplnr, Adrnr AS AvisAdrnr FROM {HanaSchema}.Iloa) AS Iloa
    ON avis_header.Iloan = Iloa.PanneIloan
  	WHERE :OnlyEp = false OR ep = true
  ), get_avis_adresse AS (
  SELECT *,
    CASE WHEN AvidAdrc.Street IS NOT NULL AND AvidAdrc.Street != '' THEN AvidAdrc.Street WHEN PanneAdrc.Street IS NOT NULL AND PanneAdrc.Street != '' THEN PanneAdrc.Street ELSE NULL END AS avis_rue,
    CASE WHEN AvidAdrc.Post_Code1 IS NOT NULL AND AvidAdrc.Post_Code1 != '' THEN AvidAdrc.Post_Code1 WHEN PanneAdrc.Post_Code1 IS NOT NULL AND PanneAdrc.Post_Code1 != '' THEN PanneAdrc.Post_Code1 ELSE NULL END AS avis_zipcode,
    CASE WHEN AvidAdrc.City1 IS NOT NULL AND AvidAdrc.City1 != '' THEN AvidAdrc.City1 WHEN PanneAdrc.City1 IS NOT NULL AND PanneAdrc.City1 != '' THEN PanneAdrc.City1 ELSE NULL END AS avis_ville,
    CASE WHEN AvidAdrc.HOUSE_NUM1 IS NOT NULL AND AvidAdrc.HOUSE_NUM1 != '' THEN AvidAdrc.HOUSE_NUM1 WHEN PanneAdrc.HOUSE_NUM1 IS NOT NULL AND PanneAdrc.HOUSE_NUM1 != '' THEN PanneAdrc.HOUSE_NUM1 ELSE NULL END AS avis_num
  FROM get_avis
  LEFT JOIN {HanaSchema}.ADRC AS AvidAdrc ON get_avis.AvisAdrnr = AvidAdrc.Addrnumber
  LEFT JOIN {HanaSchema}.ADRC AS PanneAdrc ON get_avis.PanneAdrnr = PanneAdrc.Addrnumber
), filter_avis_adresse AS (
  SELECT *
  FROM get_avis_adresse
  WHERE (:Rue IS NULL OR avis_rue = :Rue)
    and (({{ZipcodeQuery}}) and (:Zipcode IS NOT null OR :Ville is null or avis_ville = :Ville))
    and (:Numero is null or avis_num = :Numero)
),
  get_equipment_coordinates as (
    SELECT *, COUNT(*) OVER() AS total_elems FROM filter_avis_adresse
    LEFT JOIN
      (SELECT Objek, Cuobj
         FROM {HanaSchema}.Inob
      ) AS equipment_attributes
    ON filter_avis_adresse.Equnr = equipment_attributes.Objek
    LEFT JOIN
      (SELECT Objek,
            SUM(CASE WHEN Atinn = 0000001210 THEN atflv ELSE NULL END) AS ColambX,
            SUM(CASE WHEN Atinn = 0000001113 THEN atflv ELSE NULL END) AS ColambY
        FROM {HanaSchema}.Ausp
       GROUP BY Objek
      ) AS attributes
    ON Cuobj = attributes.Objek
    WHERE (:Lat0 IS NULL OR ColambY >= :Lat0)
    AND (:Lat1 IS NULL OR ColambY <= :Lat1)
    AND (:Long0 IS NULL OR ColambX >= :Long0)
    AND (:Long1 IS NULL OR ColambX <= :Long1)
    ORDER BY qmnum DESC
    LIMIT :PageSize
    offset :Offset
  ), 
  get_last_equipment_id as (
      SELECT Qmnum, Erdat, Equnr, Iloan, Tplnr, AvisAdrnr, PanneAdrnr, Aufnr, ep,
            Objnr,
            type, sous_type, illumination_type, ColambX, ColambY,
    avis_rue,
	avis_zipcode,
	avis_ville,
	avis_num,
	total_elems
      FROM
      (
        SELECT *,
               TO_DATE(Datbi, 'yyyyMMdd') AS DatbiDat,
               MAX(TO_DATE(Datbi, 'yyyyMMdd')) OVER (PARTITION BY Equnr) AS MaxDatbi,
               MAX(Eqlfn) OVER (PARTITION BY Equnr, Datbi) AS MaxEqlfn
        FROM
        (
          SELECT *
            FROM get_equipment_coordinates
            LEFT JOIN
            (
              SELECT Equnr AS AssetsEqunr,
                     Datbi,
                     Eqlfn,
                    Zhe_ep_a_type_a AS sous_type,
                    Zhe_ep_a_nature AS type,
                    Zhe_ep_l_famill AS illumination_type
                FROM {HanaSchema}.EQUZ
            ) AS Assets
            ON get_equipment_coordinates.Equnr = Assets.AssetsEqunr
          ) AS EquipmentsWithDuplicates
      )
      WHERE (DatbiDat = MaxDatbi OR (DatbiDat IS NULL AND MaxDatbi IS NULL)) AND (Eqlfn = MaxEqlfn OR (Eqlfn IS NULL AND MaxEqlfn IS NULL))
  ),
  get_related_orders as (
    SELECT * FROM get_last_equipment_id
    LEFT JOIN
      (SELECT Aufnr AS OrderAufnr, Objnr AS OrderObjnr FROM {HanaSchema}.Aufk) AS Orders
    ON get_last_equipment_id.Aufnr = Orders.OrderAufnr
  ), get_avis_status as (
    SELECT Qmnum, Erdat, Equnr, Iloan, Tplnr, AvisAdrnr, PanneAdrnr, Aufnr, ep,
       Stat AS AvisStat, Udate AS StatutDate, Utime AS StatutTime, StatutTimestamp, OrderObjnr, type, sous_type, illumination_type, ColambX, ColambY,
    avis_rue,
	avis_zipcode,
	avis_ville,
	avis_num,
	total_elems
    FROM
    (
      SELECT *
        FROM
        (
          SELECT *,
                TO_TIMESTAMP(CONCAT(CONCAT(Udate, ' '), Utime), 'yyyyMMdd HH24MISS') StatutTimestamp
          FROM
          (
            SELECT *
              FROM get_related_orders
              LEFT JOIN
                (SELECT Objnr AS StatusObjnr, Chgnr, Stat, Inact, Udate, Utime FROM {HanaSchema}.JCDS) AS StatusTable
              ON get_related_orders.Objnr = StatusTable.StatusObjnr
          ) AS EquipmentsFullStatusHistory
          WHERE Stat != 'E0006' AND TRIM(Inact) = ''
        ) AS EquipmentStatusHistory
    )
  ),
  last_order_status as (
     SELECT Qmnum, Erdat, Equnr, Iloan, Tplnr, AvisAdrnr, PanneAdrnr, ep,
            AvisStat, StatutDate, StatutTime, StatutTimestamp,
            OrderStat, OrderDate, OrderTime, OrderTimestamp,
            type, sous_type, illumination_type, ColambX, ColambY, MAX(StatutTimestamp) OVER (PARTITION BY qmnum) AS MaxStatutTimestamp, 
    avis_rue,
	avis_zipcode,
	avis_ville,
	avis_num,
	total_elems
    FROM
    (
      SELECT *,
              MAX(Chgnr) OVER (PARTITION BY Objnr, Qmnum) MaxChgnr,
              MAX(OrderTimestamp) OVER (PARTITION BY Qmnum) MaxOrderTimestamp
        FROM
        (
          SELECT *,
                TO_TIMESTAMP(CONCAT(CONCAT(OrderDate, ' '), OrderTime), 'yyyyMMdd HH24MISS') OrderTimestamp
          FROM
          (
            SELECT *
              FROM get_avis_status
              LEFT JOIN
                (SELECT Objnr, Chgnr, Stat AS OrderStat, Inact, Udate AS OrderDate, Utime AS OrderTime
                   FROM {HanaSchema}.JCDS
                  WHERE (LEFT(Objnr, 2) = 'OR' AND Stat IN('I0009') AND TRIM(Inact) = '')
                ) AS OrderTable
              ON get_avis_status.OrderObjnr = OrderTable.Objnr
          ) AS PanneOrdersFull
        ) AS PanneOrdersFullConv
    )
    WHERE Objnr IS NULL OR (Chgnr = MaxChgnr AND OrderTimestamp = MaxOrderTimestamp)
  ),
  select_data AS (
	SELECT
	    qmnum AS id,
	    ep,
	    Equnr AS equipement_id,
	    avis_rue,
		avis_zipcode,
		avis_ville,
		avis_num,
	    sous_type,
	    type,
	    illumination_type,
	    ColambX AS long,
	    ColambY AS lat,
	    Erdat AS date_creation,
	    AvisStat AS stat,
	    StatutDate AS date_statut,
	    StatutTime AS heure_statut,
	    OrderDate AS date_order,
	    OrderTime AS heure_order,
	    (CASE WHEN StatutTimestamp = MaxStatutTimestamp THEN TRUE ELSE FALSE END) AS is_last_statut,
	    StatutTimestamp AS statut_timestamp,
	    MaxStatutTimestamp AS max_statut_timestamp,
	    TJ02T.TXT04 AS descr,
	    TJ02T.TXT30 AS short_descr,
	    MAX(CASE WHEN (AvisStat = 'I0072' OR (OrderDate IS NOT NULL AND StatutTimestamp = MaxStatutTimestamp)) THEN 1 ELSE 0 END) OVER(PARTITION BY qmnum) AS closed,
		total_elems
	FROM last_order_status
	LEFT JOIN {HanaSchema}.TJ02T ON TJ02T.ISTAT = AvisStat AND TJ02T.SPRAS = 'F'
)
SELECT * FROM select_data
WHERE (:DateDebut IS NOT NULL OR :DateFin IS NOT NULL OR max_statut_timestamp >= ADD_MONTHS(CURRENT_DATE, -(CASE WHEN closed = 1 THEN 1 ELSE 12 END)))
    AND (:DateDebut IS NULL OR max_statut_timestamp >= TO_DATE(:DateDebut, 'DD/MM/YYYY'))
    AND (:DateFin IS NULL OR max_statut_timestamp <= TO_DATE(:DateFin, 'DD/MM/YYYY'))
    AND (:EnCours IS NULL OR closed = (CASE WHEN :EnCours IS NULL OR :EnCours = FALSE THEN 1 ELSE 0 END))
ORDER BY id, statut_timestamp DESC