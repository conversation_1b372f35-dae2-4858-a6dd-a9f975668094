WITH select_commune AS (
	SELECT DISTINCT CONCAT('L-',ADRCITY.REG<PERSON><PERSON><PERSON><PERSON>) AS TPLNR
	FROM {HanaSchema}.ADRCITY
	WHERE ADRCITY.COMMU_CODE = :IdCommune
), select_all_post_code AS (
	SELECT DISTINCT POST_CODE1
	FROM select_commune
	JOIN {HanaSchema}.IFLOT ON IFLOT.TPLMA = select_commune.TPLNR
	JOIN {HanaSchema}.ILOA on ILOA.ILOAN = IFLOT.ILOAN
	JOIN {HanaSchema}.ADRC on ADRC.ADDRNUMBER = ILOA.ADRNR
	WHERE POST_CODE1 != ''
), get_equipment AS (
	SELECT DISTINCT ADRC.POST_CODE1, EQUI.EQUNR
	FROM {HanaSchema}.EQUI
	JOIN {HanaSchema}.EQUZ on EQUZ.EQUNR = EQUI.EQUNR
	JOIN {HanaSchema}.ILOA on ILOA.ILOAN = EQUZ.ILOAN
	JOIN {HanaSchema}.ADRC on ADRC.ADDRNUMBER = ILOA.ADRNR
	JOIN select_all_post_code on select_all_post_code.POST_CODE1 = ADRC.POST_CODE1
	WHERE EQUI.EQART = 'EP_LUMIN' AND EQUZ.DATBI = '99991231'
	/*
INPUT: dans la table EQUI, EQUI-EQART = EP_LUMIN
OUTPUT: EQUI-EQUNR

INPUT: EQUI-EQUNR dans EQUZ-EQUNR avec EQUZ-DATBI = 31.12.9999
OUTPUT:EQUZ-ILOAN

INPUT: EQUZ-ILOAN dans ILOA-ILOAN
OUTPUT: ILOA-ADRNR

INPUT: ILOA-ADRNR dans ADRC-ADRNUMBER
OUTPUT: ADRC-POST_CODE1
*/
), get_ean AS (
	SELECT DISTINCT AUSP.ATWRT AS EAN
	FROM select_commune
	JOIN {HanaSchema}.INOB ON INOB.OBJEK = select_commune.TPLNR
	JOIN {HanaSchema}.KSSK on KSSK.OBJEK = INOB.CUOBJ
	JOIN {HanaSchema}.AUSP on AUSP.OBJEK = KSSK.OBJEK
	WHERE AUSP.ATINN= '0000002273'
/*
Retrouver l'EAN:
Input:IFLOT-TPLNR dans KSSK_INOB-INOB_OBJEK
output:KSSK_INOB-OBJEK
Input:KSSK_INOB-OBJEK dans AUSP-OBJEK avec ATINN = 0000002273
Output: AUSP-ATWRT (l'EAN)
 */
), get_conso AS (
	SELECT DISTINCT EAN, EPROFVAL15.VALUEDAY AS FULL_DATE, MONTH(EPROFVAL15.VALUEDAY) AS DATE_MONTH, YEAR(EPROFVAL15.VALUEDAY) AS DATE_YEAR, (VAL0000+VAL0015+VAL0030+VAL0045+VAL0100+VAL0115+VAL0130+VAL0145+VAL0200+VAL0215+VAL0230+VAL0245+VAL0300+VAL0315+VAL0330+VAL0345+VAL0400+VAL0415+VAL0430+VAL0445+VAL0500+VAL0515+VAL0530+VAL0545+VAL0600+VAL0615+VAL0630+VAL0645+VAL0700+VAL0715+VAL0730+VAL0745+VAL0800+VAL0815+VAL0830+VAL0845+VAL0900+VAL0915+VAL0930+VAL0945+VAL1000+VAL1015+VAL1030+VAL1045+VAL1100+VAL1115+VAL1130+VAL1145+VAL1200+VAL1215+VAL1230+VAL1245+VAL1300+VAL1315+VAL1330+VAL1345+VAL1400+VAL1415+VAL1430+VAL1445+VAL1500+VAL1515+VAL1530+VAL1545+VAL1600+VAL1615+VAL1630+VAL1645+VAL1700+VAL1715+VAL1730+VAL1745+VAL1800+VAL1815+VAL1830+VAL1845+VAL1900+VAL1915+VAL1930+VAL1945+VAL2000+VAL2015+VAL2030+VAL2045+VAL2100+VAL2115+VAL2130+VAL2145+VAL2200+VAL2215+VAL2230+VAL2245+VAL2300+VAL2315+VAL2330+VAL2345)/40000 AS TOTAL
	FROM get_ean
	JOIN {HanaSchema}.EUITRANS ON EUITRANS.EXT_UI = get_ean.EAN
	JOIN {HanaSchema}.EUIINSTLN ON EUIINSTLN.INT_UI = EUITRANS.INT_UI
	JOIN {HanaSchema}.EASTS ON EASTS.ANLAGE = EUIINSTLN.ANLAGE AND EASTS.BIS = '99991231' AND EASTS.ZWNABR = ''
	JOIN {HanaSchema}.EPROFASS ON EPROFASS.LOGIKZW = EASTS.LOGIKZW AND EPROFASS.DATETO = '99991231'
	JOIN {HanaSchema}.EPROFVAL15 ON EPROFVAL15.PROFILE = EPROFASS.PROFILE
/*
-	Lire la table ZISU_EUITRANS avec l’EAN pour récupérer l’installation de distribution (anlage) <== ZISU_EUITRANS = EUITRANS - EUIINSTLN
-	Lire la table EASTS : 
o	Anlage = zisu_euitrans-anlage
o	Bis = 31.12.9999
o	ZWNABR = vide
-	Lire la table EPROFASS avec les données de EASTS : 
o	DATETO = 31.12.9999
o	Logikzw = easts-logikzw
-	Lire la table EPROFVAL15 :
o	PROFILE = eprofass-profile
o	Valueday = date à laquelle on veut récupérer les valeurs
	Il faut ensuite cumuler toutes les valeurs quart horaires pour avoir une consommation totale  consommation totale à diviser par 4 pour avoir la consommation puis par 10000 car les 4 derniers chiffres sont des décimales
*/
)
SELECT EAN, DATE_YEAR AS "Year", DATE_MONTH AS "Month", ROUND(SUM(TOTAL)/1000, 2) AS CONSO  -- division par 1000 pour avoir des MWh
FROM get_conso
WHERE FULL_DATE > SERIES_ROUND(ADD_YEARS(CURRENT_DATE, -2), 'INTERVAL 1 MONTH', ROUND_DOWN) AND FULL_DATE < CURRENT_DATE AND NOT (MONTH(CURRENT_DATE) = DATE_MONTH AND YEAR(CURRENT_DATE) = DATE_YEAR)
GROUP BY EAN, DATE_YEAR, DATE_MONTH
ORDER BY DATE_YEAR, DATE_MONTH
