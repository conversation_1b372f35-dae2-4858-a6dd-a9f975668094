SELECT
    id as "Id",
    modele as "<PERSON><PERSON>",
    nbr as "Nbr",
    marque as "Mar<PERSON>",
    nom as "Nom",
    rue as "Rue",
    codepostal as "CodePostal",
    ville as "<PERSON>",
    contactname as "ContactName",
    contact as "<PERSON>",
    horaire as "<PERSON><PERSON><PERSON>",
    latitude as "Latitude",
    longitude as "Longitude",
    active as "Active"
FROM storelocation.cust_storeloc
WHERE active AND latitude >= %(Lat0)s AND latitude <= %(Lat1)s AND longitude >= %(Long0)s AND longitude <= %(Long1)s