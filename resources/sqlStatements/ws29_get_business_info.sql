WITH get_business_info AS (
    SELECT TOP 1 *
    FROM
    (SELECT
      EINZDAT,
      AUSZDAT,
      LOEVM,
      INVOICING_PARTY,
      VKONTO,
      VERTRAG,
      ANLAGE AS EVER_ANLAGE,
      (CASE WHEN EINZDAT <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= AUSZDAT THEN 'ACTIF' ELSE 'INACTIF' END) AS STATUT
      FROM {HanaSchema}.EVER
      WHERE ANLAGE  = :ANLAGE
      ORDER BY AUSZDAT DESC
      LIMIT 1
    ) AS EVER_RELEVANT
    LEFT JOIN
    (
      SELECT SERVICEID, SP_NAME FROM {HanaSchema}.ESERVPROVT
    ) AS ESERVPROVT
    ON EVER_RELEVANT.INVOICING_PARTY = ESERVPROVT.SERVICEID
    LEFT JOIN
    (SELECT GPART, VKONT FROM {HanaSchema}.FKKVKP) AS FKKVKP
    ON EVER_RELEVANT.VKONTO = FKKVKP.VKONT
    LEFT JOIN
    (SELECT
      PARTNER,
      TYPE AS PARTNER_TYPE,
      NAME_FIRST,
      NAME_LAST,
      NAME_ORG1,
      NAME_ORG2,
      NAME_ORG3,
      NAME_ORG4,
      NAME_GRP1,
      NAME_GRP2,
      LANGU_CORR
      FROM {HanaSchema}.BUT000
      ) AS BUT000
    ON GPART = BUT000.PARTNER
    LEFT JOIN
    (SELECT
      PARTNER,
      ADDRNUMBER
    FROM {HanaSchema}.BUT020) AS BUT020
    ON BUT000.PARTNER = BUT020.PARTNER
    LEFT JOIN
    (SELECT
    CITY1 AS PARTNER_CITY1,
    MC_CITY1 AS PARTNER_CITYCODE,
    POST_CODE1 AS PARTNER_POSTCODE_1,
    STREET AS PARTNER_STREET,
    HOUSE_NUM1 AS PARTNER_HOUSE_NUM1,
    ADDRNUMBER AS PARTNER_ADDRNUMBER
    FROM {HanaSchema}.ADRC
    ) AS PARTNER_ADRC
    ON BUT020.ADDRNUMBER = PARTNER_ADRC.PARTNER_ADDRNUMBER
    LEFT JOIN
    (SELECT ADDRNUMBER, TELNR_CALL FROM {HanaSchema}.ADR2) AS ADR2
    ON BUT020.ADDRNUMBER = ADR2.ADDRNUMBER
    LEFT JOIN
    (SELECT ADDRNUMBER, SMTP_ADDR FROM {HanaSchema}.ADR6) AS ADR6
    ON BUT020.ADDRNUMBER = ADR6.ADDRNUMBER
    LEFT JOIN
    (SELECT
      PARTNER,
      MAX(CASE WHEN TYPE = 'NACE' THEN IDNUMBER ELSE NULL END) AS NACE,
      MAX(CASE WHEN TYPE = 'CATEG' THEN IDNUMBER ELSE NULL END) AS CATEG
    FROM {HanaSchema}.BUT0ID GROUP BY PARTNER) AS BUT0ID
    ON BUT000.PARTNER = BUT0ID.PARTNER
)
SELECT * FROM get_business_info