WITH consumption_data AS (
    SELECT 
        meterid, 
        ean, 
        measuredatetime, 
        SUM(CASE WHEN readingtypeid LIKE '1.%' THEN measurevalue ELSE 0 END) AS sum_1x_readings,
        SUM(CASE WHEN readingtypeid LIKE '2.%' THEN measurevalue ELSE 0 END) AS sum_2x_readings,
        SUM(CASE WHEN readingtypeid LIKE '1.%' THEN measurevalue ELSE 0 END) 
        - 
        SUM(CASE WHEN readingtypeid LIKE '2.%' THEN measurevalue ELSE 0 END) AS consumption
    FROM {red_table}
    WHERE ean IN {ean_tuple}
      AND (
           measuredatetime = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month') AT TIME ZONE 'Europe/Brussels'
           OR measuredatetime = (DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 day') AT TIME ZONE 'Europe/Brussels'
      )
    GROUP BY meterid, ean, measuredatetime
)
SELECT 
    c1.meterid, 
    c1.ean, 
    c1.consumption AS start_month_consumption,
    c2.consumption AS end_month_consumption,
    (c2.consumption - c1.consumption) AS consumption_difference
FROM consumption_data c1
JOIN consumption_data c2
ON c1.meterid = c2.meterid
AND c1.ean = c2.ean
AND c1.measuredatetime = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month') AT TIME ZONE 'Europe/Brussels'
AND c2.measuredatetime = (DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 day') AT TIME ZONE 'Europe/Brussels'
ORDER BY c1.ean, c1.meterid;