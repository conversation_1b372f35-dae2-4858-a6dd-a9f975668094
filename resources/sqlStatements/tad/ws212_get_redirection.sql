WITH auto_disabled AS ( -- get the auto disable configuration
    SELECT
        CASE
            WHEN 
                MAX(CASE WHEN c.name = 'AutoDisableRouting' AND c.value = 'True' THEN 1 ELSE 0 END) = 1
                AND
                MAX(CASE WHEN c.name = 'AutoDisableRoutingHour' AND CAST(c.value AS TIME) < CAST(SYSDATETIMEOFFSET() AT TIME ZONE 'Romance Standard Time' AS TIME) THEN 1 ELSE 0 END) = 1
            THEN 1
            ELSE 0
        END AS "Enabled"
    FROM NethysGroup.Config c
), routing AS ( -- get the existing routing events
    SELECT DISTINCT
        1 AS "Enabled",
        CASE WHEN LEFT(rr.Destination, 2) = '00' THEN '+32' + SUBSTRING(rr.Destination, 3, LEN(rr.Destination)) ELSE rr.Destination END AS "Destination"
    FROM NethysGroup.RoutingRule rr
    JOIN NethysGroup.RoutingEvent re ON re.RoutingRuleId = rr.RoutingRuleId
    WHERE re.isActive = 'true'
        AND re.EntityId = 1 -- EntityId : 1 = Resa
        AND re.CallReasonId = 1 -- CallReasonId : 1 = Electricite
        AND re.ApplicationId = 1 -- ApplicationId : 1 = TAD
)
SELECT
    CASE
        WHEN 
            MAX(routing."Enabled") = 1
            AND
            MAX(auto_disabled."Enabled") = 0
        THEN CAST(1 AS BIT)
        ELSE CAST(0 AS BIT)
    END AS "Enabled",
    ISNULL(MAX(routing."Destination"), (
        SELECT TOP 1
        CASE WHEN LEFT(rr.Destination, 2) = '00' THEN '+32' + SUBSTRING(rr.Destination, 3, LEN(rr.Destination)) ELSE rr.Destination END AS "Destination"
        FROM NethysGroup.RoutingRule rr
        WHERE rr.RoutingRuleId = 1
    )) as "Destination"
FROM auto_disabled, routing