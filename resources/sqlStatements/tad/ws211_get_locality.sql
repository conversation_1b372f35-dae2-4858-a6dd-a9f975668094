WITH tmp_result AS (
SELECT
    CASE 
        WHEN l.Services LIKE '%Elec%' THEN 1
        ELSE 0
    END AS "ResaElec",
    CASE 
        WHEN l.Services LIKE '%Gaz%' THEN 1
        ELSE 0
    END AS "ResaGas",
    CASE
        WHEN EXISTS (
            SELECT 1
            FROM NethysGroup.IncidentEvent ie
            WHERE ie.LocalityId = l.LocalityId AND ie.IsActive = 1
        ) THEN 1
        ELSE 0
    END AS "Incident"
FROM NethysGroup.Locality l
WHERE l.Zipcode = %(PostalCode)s
)
SELECT
    CAST(ISNULL(MAX("ResaElec"), 0) AS BIT) AS "ResaElec",
    CAST(ISNULL(MAX("ResaGas"), 0) AS BIT) AS "ResaGas",
    CAST(ISNULL(MAX("Incident"), 0) AS BIT) AS "Incident"
FROM tmp_result