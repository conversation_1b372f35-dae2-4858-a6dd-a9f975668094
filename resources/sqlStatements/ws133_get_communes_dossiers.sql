with ean_commune as (
	select distinct
		EXT_UI as EAN
	from {HanaSchema}.ETTIFN
	JOIN {HanaSchema}.EUIINSTLN ON EUIINSTLN.ANLAGE = ETTIFN.ANLAGE
    JOIN {HanaSchema}.EUITRANS ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
    JOIN {HanaSchema}.EANL ON EANL.ANLAGE = ETTIFN.ANLAGE AND EANL.SPARTE = (CASE WHEN ETTIFN.OPERAND = 'E_COMMUNE' THEN '01' WHEN ETTIFN.OPERAND = 'G_COMMUNE' THEN '02' END)
	WHERE ETTIFN.OPERAND IN ('G_COMMUNE', 'E_COMMUNE') AND ETTIFN.STRING1 = :IdCommune AND ETTIFN.AB <= CURRENT_DATE AND CURRENT_DATE <= ETTIFN.BIS
), dossier_from_ean_1 AS (
	select EAN, AUFNR
	from ean_commune
	join {HanaSchema}.EQUZ ON EQUZ.TIDNR = ean_commune.EAN
	JOIN {HanaSchema}.AFIH ON AFIH.EQUNR = EQUZ.EQUNR
	where AFIH.ILART in ('RT1','RT2','R01','R02','R03','R04','R09')
/*
EQUZ- TIDNR = EAN
AFIH-EQUNR = EQUZ-EQUNR
AFIH- AUFNR = numéro de dossier
*/
), dossier_from_ean_2 AS (
	select EAN, AFIH.AUFNR
	from ean_commune
	join {HanaSchema}.EUITRANS ON EUITRANS.EXT_UI = ean_commune.EAN
	JOIN {HanaSchema}.EUIINSTLN ON EUIINSTLN.INT_UI = EUITRANS.INT_UI
	JOIN {HanaSchema}.EANL ON EANL.ANLAGE = EUIINSTLN.ANLAGE
	JOIN {HanaSchema}.EVBS ON EVBS.VSTELLE = EANL.VSTELLE
	JOIN {HanaSchema}.ILOA ON ILOA.TPLNR = EVBS.HAUS
	JOIN {HanaSchema}.AFIH ON AFIH.ILOAN = ILOA.ILOAN
	where AFIH.ILART in ('RT1','RT2','R01','R02','R03','R04','R09')
/*
EUITRANS- EXT_UI = EAN 
EUIINSTLN-INT_UI = EUITRANS-INT_UI
EANL- ANLAGE = EUIINSTLN – ANLAGE
EVBS- VSTELLE = EANL-VSTELLE
ILOA- TPLNR = EVBS- HAUS
AFIH-ILOAN = ILOA- ILOAN
AFIH- AUFNR = numéro de dossier
*/
), dossier_from_commune AS (
	with IBINVAL_SEL_F as (
		select *
		from {HanaSchema}.IBINVALUES iv
		JOIN {HanaSchema}.IBSYMBOL ibs ON ibs.MANDT = iv.MANDT AND ibs.SYMBOL_ID = iv.SYMBOL_ID
		JOIN {HanaSchema}.IBIN ibin ON ibin.MANDT = iv.MANDT AND ibin.IN_RECNO = iv.IN_RECNO
		JOIN {HanaSchema}.IBST ibst ON ibst.MANDT = iv.MANDT AND ibst."INSTANCE" = ibin."INSTANCE"
		JOIN {HanaSchema}.IBINOWN io ON io.MANDT = iv.MANDT AND io."INSTANCE" = ibst.ROOT 
		JOIN {HanaSchema}.T371F tf ON tf.INTTYP = io.INTTYP
		where ATINN = '0000002674' and ATWRT = :IdCommune
	)
	select null as EAN, AUFK.AUFNR
	from IBINVAL_SEL_F
	join {HanaSchema}.AUFK ON AUFK.OBJNR = IBINVAL_SEL_F.OBJKEY
	JOIN {HanaSchema}.AFIH ON AFIH.AUFNR = AUFK.AUFNR
	JOIN {HanaSchema}.ILOA ON ILOA.ILOAN = AFIH.ILOAN 
	where AFIH.EQUNR = '' and AFIH.ILART in ('RT1','RT2','R01','R02','R03','R04','R09')
/* 
IBINVAL_SEL_F- ATINN = RAC_COMMUNE (caractéristique à créer)
IBINVAL_SEL_F- ATWRT = code commune en input

AUFKS- OBJNR = IBINVAL_SEL_F- OBJKEY
AUFKS- AUFNR = numéro de dossier
AFIH- EQUNR = vide
ILOA- ILOAN = AFIH-ILOAN 
	-- WHERE AFIH.ILART IN ('RT1','RT2','R01','R02','R03','R04','R09')


ADRC- ADDRNUMBER = ILOA-ADRNR
ADRC- POST_CODE1 = code postal en input
*/
), all_dossiers AS (
	select * from dossier_from_ean_1
	union
	select * from dossier_from_ean_2
	union
	select * from dossier_from_commune
), ls_header AS (
  select * from (
    (
	    select MANDT as AUFK_MANDT, AUFK.AUFNR as INPUT_AUFNR, ERDAT, OBJNR as AUFK_OBJNR, EAN, AUFK.AUART
	    from {HanaSchema}.AUFK
	    join all_dossiers on all_dossiers.AUFNR = AUFK.AUFNR
    	where AUFK.IDAT2 = '00000000' or ADD_YEARS(current_date, -1) <= AUFK.IDAT2
    ) AS AUFK
    INNER JOIN
    (select AUFPL, AUFNR, MAUFNR from {HanaSchema}.AFKO where MAUFNR='') AS AFKO
    ON AUFK.INPUT_AUFNR = AFKO.AUFNR
    INNER JOIN
    (select AUFNR, QMNUM, ILART, INGPR from {HanaSchema}.AFIH) AS AFIH
    ON AUFK.INPUT_AUFNR = AFIH.AUFNR
  )
), ls_operation AS (
    (select * from ls_header
    inner join
    (select APLZL, AUFPL, OBJNR as AFVC_OBJNR, VORNR, STEUS from {HanaSchema}.AFVC) as AFVC
    on AFVC.AUFPL = ls_header.AUFPL
    inner join
    (select APLZL, AUFPL, FSAVD from {HanaSchema}.AFVV) as AFVV
    on AFVC.APLZL = AFVV.APLZL and AFVC.AUFPL = AFVV.AUFPL
  )
), get_dossier_statut AS (
    select * from ls_operation
    inner join
      (select MANDT, OBJNR as JEST_OBJNR, STAT, CHGNR
      from {HanaSchema}.JEST
      where INACT = '') as JEST
    on ls_operation.AUFK_OBJNR = JEST.JEST_OBJNR and ls_operation.AUFK_MANDT = JEST.MANDT
    inner join
      (select OBJNR as JSTO_OBJNR, STSMA as JSTO_STSMA
      from {HanaSchema}.JSTO) as JSTO
    on ls_operation.AUFK_OBJNR = JSTO.JSTO_OBJNR and ls_operation.AUFK_MANDT = JEST.MANDT
    inner join
      (select distinct ESTAT, STSMA as E_STSMA, LINEP as E_LINEP, STATP as E_STATP
      from {HanaSchema}.TJ30) as TJ30
    on JEST.STAT =TJ30.ESTAT and JSTO.JSTO_STSMA = TJ30.E_STSMA
    inner join
      (select distinct STSMA as TJ30T_STSMA, ESTAT as TJ30T_ESTAT, TXT04 as ESTAT_CODE
      from {HanaSchema}.TJ30T) as TJ30T
    on TJ30T.TJ30T_STSMA = TJ30.E_STSMA and TJ30.ESTAT = TJ30T.TJ30T_ESTAT
), format_dossier_statut AS (
  select distinct
    MANDT, ILART, INGPR, AUFK_OBJNR, ESTAT_CODE, E_LINEP, E_STATP, ERDAT, INPUT_AUFNR, QMNUM, EAN, AUART
  from get_dossier_statut
), ordered_statut AS (
  (select *,
    row_number() over (partition by INPUT_AUFNR order by E_LINEP desc, E_STATP desc, ESTAT_CODE) as ROW_NUM
    from format_dossier_statut
    order by E_LINEP desc, E_STATP desc, ESTAT_CODE
  )
), first_statut_selection AS (
  select *
  from (
    (select * from {HanaSchema}.ZWS59_STATUT) as STAT_TABLE
    inner join
    (select ESTAT_CODE as ESTAT_1, ILART as ILART_1, INGPR as INGRP_1, ERDAT, INPUT_AUFNR, QMNUM, EAN, ROW_NUM, AUART from ordered_statut where ROW_NUM = 1) as FIRST_STATUT
    on STAT_TABLE.STATUT1 = FIRST_STATUT.ESTAT_1
  )
  WHERE
    ((ILA = 'R*' AND ILART_1 LIKE 'R%')
      OR (ILA = 'R0' AND ILART_1 LIKE 'R0%')
      OR (ILA = 'RT' AND ILART_1 LIKE 'RT%')
      OR ILA = ILART_1
      OR ILA = '*')
    AND (INGRP = '*' OR INGRP = INGRP_1)
), second_statut_selection AS (
    select
      sum(case when ESTAT_2 is not null then 1 else 0 end) over (partition by INPUT_AUFNR) as HAS_ESTAT_2,
      sum(case when trim(STATUT2) = '' then 1 else 0 end) over (partition by INPUT_AUFNR) as HAS_EMPTY_STATUT2,*
    from (
      select * from first_statut_selection
      left join(
      	select INPUT_AUFNR as INPUT_AUFNR_2, ESTAT_CODE as ESTAT_2 from ordered_statut where ROW_NUM != 1
	  ) as SECOND_STATUT on first_statut_selection.STATUT2 = SECOND_STATUT.ESTAT_2 and INPUT_AUFNR = INPUT_AUFNR_2
    )
), keep_second_status_or_all AS (
  select *
  from second_statut_selection
  where (ESTAT_2 is not null and HAS_ESTAT_2 > 0)
    or (HAS_ESTAT_2 = 0 and HAS_EMPTY_STATUT2 > 0 and STATUT2 = '')
    or (HAS_ESTAT_2 = 0 and HAS_EMPTY_STATUT2 = 0)
), keep_higher_priority AS (
    select * from
      (
        select row_number() over (partition by INPUT_AUFNR order by PRIORI desc) as PRIO_NUM, *
        from keep_second_status_or_all
      )
    where PRIO_NUM = 1
), add_libelles AS (
    select * from keep_higher_priority
    left join
    (select CODE as CODE_ETAPE, LIB_CODE as LIB_ETAPE, LEFT(CDLANGU, 1) as "Lang"
      from {HanaSchema}.ZWS59_LIB_STATUT
      where TYPE = 'ETAP' ) as LIBELLE_ETAP
    on keep_higher_priority.ETAPE = LIBELLE_ETAP.CODE_ETAPE
    left join
    (select CODE as CODE_STAT, LIB_CODE as LIB_STAT, CDLANGU
      from {HanaSchema}.ZWS59_LIB_STATUT
      where TYPE = 'STATUT' ) as LIBELLE_STAT
    on keep_higher_priority.CODE_STATUT = LIBELLE_STAT.CODE_STAT AND LEFT(LIBELLE_STAT.CDLANGU, 1) = "Lang"
    WHERE "Lang" = LEFT(:Langue, 1)
), dossier_adresse AS (
	select
	  AUFNR,
	  trim(STREET) as "Adresse_Rue",
	  trim(HOUSE_NUM1) as "Adresse_NumRue",
	  trim(HOUSE_NUM2) as "Adresse_NumComp",
	  POST_CODE1 as "Adresse_CodePostal",
	  trim(CITY1) as "Adresse_Localite"
	from (
	  (
	    select QMNUM, AUFNR
	  	from {HanaSchema}.QMEL
	  ) as QMEL
	inner join
	  (select QMNUM, ILOAN from {HanaSchema}.QMIH) as QMIH
	on QMEL.QMNUM = QMIH.QMNUM
	inner join
	  (select ILOAN, ADRNR from {HanaSchema}.ILOA) as ILOA
	on ILOA.ILOAN = QMIH.ILOAN
	inner join
	  (select * from {HanaSchema}.ADRC) as ADRC
	on ADRC.ADDRNUMBER = ILOA.ADRNR
	left join
	  (select STRT_CODE, IDRAD_RUE
	  from {HanaSchema}.ZCA_RAD_MAPP
	  where TYPE = 'RUE'
	  ) as ZCA_RAD_MAPP_STRT
	  on ZCA_RAD_MAPP_STRT.STRT_CODE = ADRC.STREETCODE
	left join
	  (select CITY_CODE as ZCA_CITY_CODE, IDRAD_LOCALITE
	  from {HanaSchema}.ZCA_RAD_MAPP
	  where TYPE = 'LOCALITE'
	  ) as ZCA_RAD_MAPP_CITY
	  on ZCA_RAD_MAPP_CITY.ZCA_CITY_CODE = ADRC.CITY_CODE
	)
), config_dosrac AS (
    select AUFNR, trim(ATNAM) as "Name", (case when trim(ATWRT) = '' or ATWRT is null then TO_VARCHAR(ATFLV) else trim(ATWRT) end) as "Value"
    from (
      select OBJNR, AUFNR
      from {HanaSchema}.AUFK
      join add_libelles on AUFNR = INPUT_AUFNR
    ) AS AUFK
    INNER JOIN (
      select CUOBJ, OBJNR from {HanaSchema}.PMSDO
    ) AS PMSDO
      ON PMSDO.OBJNR = AUFK.OBJNR
    INNER JOIN (
      select IBIN.INSTANCE, ATINN, ATWRT, ATFLV
      from {HanaSchema}.IBIN
      INNER JOIN (
        select INSTANCE, OBJKEY
        from {HanaSchema}.IBINOWN
      ) AS IBINOWN 
        ON IBINOWN.INSTANCE = IBIN.INSTANCE
      INNER JOIN (
        select SYMBOL_ID, IN_RECNO
        from {HanaSchema}.IBINVALUES
      ) AS IBINVALUES
        ON IBINVALUES.IN_RECNO = IBIN.IN_RECNO
      INNER JOIN (
        select SYMBOL_ID, ATINN, ATWRT, ATFLV
        from {HanaSchema}.IBSYMBOL
      ) AS IBSYMBOL
        ON IBINVALUES.SYMBOL_ID = IBSYMBOL.SYMBOL_ID
    ) AS IBINVAL 
      ON IBINVAL.INSTANCE = PMSDO.CUOBJ
    INNER JOIN (
      select CABN.ATINN, ATNAM, ATBEZ
      from {HanaSchema}.CABN
      INNER JOIN {HanaSchema}.CABNT ON CABNT.ATINN = CABN.ATINN
      where SPRAS = 'F'
    ) AS CABN
      ON CABN.ATINN = IBINVAL.ATINN
), montant as (
    with vbak_info as (
        select *
        from {HanaSchema}.VBAK
        JOIN add_libelles ON AUFNR = INPUT_AUFNR
        where ((VBTYP='C' and VBAK.AUART in ('DRE2', 'DRG2')) or VBTYP='B')
    ), action_montant as (
        select AUFNR, VBTYP, VBAK.NETWR + (sum(KONV.KWERT) over (partition by VBAK.NETWR, AUFNR)) as montant
        from {HanaSchema}.KONV
        join vbak_info AS VBAK on KONV.KNUMV = VBAK.KNUMV
        where KONV.KSCHL = 'MWST'
    ), action_restant_du_devis as (
        select AUFNR, abs(sum(betrh) over (partition by DFKKOP.xblnr, AUFNR)) as restant_du_devis
        from {HanaSchema}.DFKKOP
        join {HanaSchema}.VBAK on lpad(VBAK.XBLNR, 16, '0') = DFKKOP.xblnr
        JOIN add_libelles ON AUFNR = INPUT_AUFNR
        where blart in ('RC','MP','GL','MO','TN','SC')
        and bukrs = '101' 
        and augrd not in ('02','03','04','05','14')
        and TRVOG = 2
    ), action_restant_du_etude as (
        select AUFNR, abs(sum(betrh) over (partition by DFKKOP.xblnr, AUFNR)) as restant_du_etude
        from {HanaSchema}.DFKKOP
        join vbak_info on lpad(VBELN, 16, '0') = DFKKOP.xblnr
        where blart = 'RC'
    )
    select VBTYP, TYPE, action_montant.AUFNR as AUFNR, CODE as "CodeInfo", LIB_CODE as "LibInfo", TYPE_INFO as "TypeInfo", (
        montant - ifnull(case when CODE = '44_2' then restant_du_devis when CODE = '32_2' then restant_du_etude else 0 end,0)
    ) as "ValeurInfo"
    from action_montant
    left join action_restant_du_devis on action_restant_du_devis.AUFNR = action_montant.AUFNR
    left join action_restant_du_etude on action_restant_du_etude.AUFNR = action_montant.AUFNR
    join {HanaSchema}.ZWS59_LIB_STATUT ON 1=1
    where LEFT(CDLANGU, 1) = LEFT(:Langue, 1)
)
select
	ltrim(INPUT_AUFNR, '0') as "Reference",
    case when ILART_1 in ('R01', 'R06') then 'NOUV' else 'MODIF' end as "Type",
	EAN as "Ean",
    LIB_STAT as "Statut",
    case when CODE_STATUT in ('0', '21', '32', '42', '44', '61', '72', '81', '84') then TRUE else FALSE end as "Action",
    case when ETAPE = '9' then TRUE else FALSE end as "Cloture",
    "Adresse_Rue",
    "Adresse_NumRue",
    "Adresse_NumComp",
    "Adresse_CodePostal",
    "Adresse_Localite",
    case when AUART = 'DRG1' then 'Gaz' when AUART = 'DRE1' then 'Elec' else AUART end as "TypeFluide",
    CODE_STATUT as "CodeStatut",
    AUART,
    ACTION, (
        select "Name", "Value"
        from config_dosrac
        where AUFNR = INPUT_AUFNR
        for JSON
    ) AS "Config", (
        select "CodeInfo", "LibInfo", "TypeInfo", "ValeurInfo"
        from montant
        where AUFNR = INPUT_AUFNR
        and TYPE = 'MONT_ETUD'
        and VBTYP = 'C'
        for JSON
    ) AS "MontEtud", (
        select "CodeInfo", "LibInfo", "TypeInfo", "ValeurInfo"
        from montant
        where AUFNR = INPUT_AUFNR
        and TYPE = 'MONT_DEVIS'
        and VBTYP = 'B'
        for JSON
    ) AS "MontDevis"
FROM add_libelles
JOIN dossier_adresse ON dossier_adresse.AUFNR = INPUT_AUFNR
WHERE ETAPE != '9' OR ERDAT > ADD_YEARS(CURRENT_DATE, -1)
ORDER BY ERDAT DESC 