with
get_devis_from_dossier as (
   {DynamicQuery}
    INNER JOIN
    (select AUFNR as AUFK_AUFNR, OBJNR as AUFK_OBJNR, MANDT
      from {HanaSchema}.AUFK
    ) AS AUFK
    ON AUFK.AUFK_AUFNR = QMEL.AUFNR
    INNER JOIN
    (select
            VBAK.AUFNR as VBAK_AUFNR,
            ltrim(VBAK.VBELN,'0') as Devi<PERSON>,
            (case
                when VBTYP='C' then
                    BSTDK
                when VBTYP='B' then
                    BNDDT
                else null
            end) as DtFin,
            VBAK.NETWR,
            VBAK.KNUMV,
            (case
                when VBTYP='C' then
                    'ETUDE'
                when VBTYP='B' then
                    'DEVIS'
                else null
            end) as Type,
            (case
                when VBAK.WAERK='USD' then
                    '$'
                when VBAK.WAERK='EUR' then
                    '€'
                else VBAK.WAERK
            end) as Devise,
             concat(concat(concat(concat(concat(
                '+++893/',
                SUBSTRING(ltrim(VBAK.VBELN,'0'), 0, 4)), '/'),
                SUBSTRING(ltrim(VBAK.VBELN,'0'), 5, 3)),
                mod((8930000000 + cast (ltrim(VBAK.VBELN,'0') as BIGINT)), 97)),
                '+++'
            ) as CommStruc
            from {HanaSchema}.VBAK
            where (VBTYP='C' and AUART in ('DRE2', 'DRG2')) or VBTYP='B'
    ) AS VBAK
    ON VBAK.VBAK_AUFNR = QMEL.AUFNR
    where exists (
        select OBJNR as JCDS_OBJNR
        from {HanaSchema}.JCDS
        where ((STAT = 'E0068' and INACT = '') or VBAK.Type='ETUDE') and OBJNR = AUFK.AUFK_OBJNR
        group by OBJNR
    )
),
get_montant_final AS (
  select KONV.KNUMV, (NETWR + SUM_KWERT) as "MontantFinal", * from (
  (select * from  get_devis_from_dossier) as DOSSIER
  inner join
  (select
        KONV.KNUMV,
        sum(KONV.KWERT) as SUM_KWERT
        from {HanaSchema}.KONV
        where KONV.KSCHL = 'MWST'
        group by KONV.KNUMV
  ) as KONV
  on DOSSIER.KNUMV = KONV.KNUMV
)),
get_nom_fichier AS (
    select * from get_montant_final
    inner join
      (select ZVALISE, TYPDOC from {HanaSchema}.ZGED_BUS_OBJ where  TYPID = 'BUS2080') as ZGED_BUS
    on ZGED_BUS.TYPDOC = get_montant_final.QMART
    inner join
      (select URL, VALISE_UID from {HanaSchema}.ZGED_URL ) as URL
    on URL.VALISE_UID = concat(concat(concat(concat(ZGED_BUS.ZVALISE, '-'), ltrim(get_montant_final.AUFNR,'0')), :EnvName),MANDT)
    inner join
      (select CUOBJ, OBJNR from {HanaSchema}.PMSDO) as PMSDO
    on PMSDO.OBJNR = get_montant_final.AUFK_OBJNR
    inner join
        (select INSTANCE, OBJKEY from {HanaSchema}.IBINOWN) as INSTANCE
    on INSTANCE.OBJKEY = get_montant_final.AUFK_OBJNR
    inner join
        (select IN_RECNO, INSTANCE from {HanaSchema}.IBIN) as IN_RECNO
    on IN_RECNO.INSTANCE = INSTANCE.INSTANCE
    inner join
        (select SYMBOL_ID, IN_RECNO from {HanaSchema}.IBINVALUES ) as IBINVAL
    on IN_RECNO.IN_RECNO = IBINVAL.IN_RECNO
    inner join
        (select SYMBOL_ID, ATINN, ATWRT from {HanaSchema}.IBSYMBOL) as IBSYMBOL
    on IBINVAL.SYMBOL_ID = IBSYMBOL.SYMBOL_ID
    inner join
        (select ATINN, ATNAM from {HanaSchema}.CABN where ATNAM = 'RAC_PRINT_MODE_ENVOI_DEV') as CABN
    on CABN.ATINN = IBSYMBOL.ATINN
), action_restant_du_devis as (
    select abs(sum(betrh)) as val
    from {HanaSchema}.dfkkop
where blart in ('RC','MP','GL','MO','TN','SC')
    and bukrs = '101' 
    and xblnr = (select lpad(XBLNR, 16, '0') from {HanaSchema}.VBAK JOIN get_devis_from_dossier on VBAK.AUFNR = get_devis_from_dossier.AUFNR where TRVOG = 2 LIMIT 1)
    and augrd not in ('02','03','04','05','14')
group by xblnr
), action_restant_du_etude as (
    select abs(sum(betrh)) as val
    from {HanaSchema}.dfkkop
	where 
		blart = 'RC'
	    and xblnr = (select lpad(VBELN, 16, '0') from {HanaSchema}.VBAK JOIN get_devis_from_dossier on VBAK.AUFNR = get_devis_from_dossier.AUFNR limit 1)
	group by xblnr
), get_output AS (
select
    *,
    "MontantFinal" - ifnull(
        case when get_montant_final.Type='ETUDE' then (select val from action_restant_du_etude)
        when get_montant_final.Type='DEVIS' then (select val from action_restant_du_devis)
        else "MontantFinal"
        end
        ,0) as "MontantRestant"
from get_montant_final
left join
(select AUFK_OBJNR as OBJN_RFICHIER, ATWRT, URL from  get_nom_fichier) as NOM_FICHIER
on NOM_FICHIER.OBJN_RFICHIER = get_montant_final.AUFK_OBJNR
)
select
  AUFNR as "Dossier",
  Devis  as "Devis",
  TYPE as "Type", DEVISE as "Devise",
  TO_VARCHAR (to_date(DTFIN), 'DD/MM/YYYY') as "DtFin",
  TO_VARCHAR(cast("MontantFinal" as float)) as "MontantFinal",
  TO_VARCHAR(cast("MontantRestant" as float)) as "MontantRestant",
  COMMSTRUC as "CommStruc",
  URL as "Url"
from get_output