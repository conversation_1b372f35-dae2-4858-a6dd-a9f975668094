SELECT
    q."MatchingScore",
    json_build_object(
        'NumRue', q."NumRue",
        'Rue', q."Rue",
        'Cdpostal', q."Cdpostal",
        'Localite', q."Localite"
    ) as "Adresse",
    json_build_object(
        'ProjectionFacade', json_build_object(
            'Long', q."LongProjectionFacade",
            'Lat', q."LatProjectionFacade"
        ),
        'MilieuBatiment', json_build_object(
            'Long', q."LongMilieuBatiment",
            'Lat', q."LatMilieuBatiment"
        )
    ) as "GPS"
FROM (
    SELECT DISTINCT
        (
            COALESCE(SIMILARITY( %(Localite)s, communenomlangueprimaire),0) +
            COALESCE(SIMILARITY( %(Rue)s, CASE WHEN %(Rue)s is not null or %(NumRue)s is not null THEN ruenomlangueprimaire ELSE null END), 0) +
            COALESCE(SIMILARITY( %(NumRue)s, CASE WHEN %(NumRue)s is not null THEN numero ELSE null END),0) +
            COALESCE(SIMILARITY( %(Cdpostal)s, codepostal),0)
        )/4 as "MatchingScore",
        CASE WHEN %(NumRue)s is not null THEN numero ELSE null END as "NumRue",
        CASE WHEN %(Rue)s is not null or %(NumRue)s is not null THEN ruenomlangueprimaire ELSE null END as "Rue",
        codepostal as "Cdpostal",
        communenomlangueprimaire as "Localite",
        AVG(latprojfacadewgs84) as "LatProjectionFacade",
        AVG(longprojfacadewgs84) as "LongProjectionFacade",
        AVG(latmilieubatiwgs84) as "LatMilieuBatiment",
        AVG(longmilieubatiwgs84) as "LongMilieuBatiment"
    FROM resa_geo.addresses
    WHERE (
        -- empty response if empty request
        (%(Localite)s is not null) OR
        (%(NumRue)s is not null) OR
        (%(Rue)s is not null) OR
        (%(Cdpostal)s is not null)
    ) AND (
        -- if rue is set then check for an exact match otherwise take all the similar rue
        %(Rue)s is null OR
        (
            lower(unaccent(ruenomlangueprimaire)) = (SELECT DISTINCT lower(unaccent(ruenomlangueprimaire)) FROM resa_geo.addresses WHERE lower(unaccent(ruenomlangueprimaire)) = lower(unaccent(COALESCE(%(Rue)s, ''))) )
            OR
            (
                    SIMILARITY(%(Rue)s, ruenomlangueprimaire) > 0.1 AND
                    (SELECT DISTINCT lower(unaccent(ruenomlangueprimaire)) FROM resa_geo.addresses WHERE lower(unaccent(ruenomlangueprimaire)) = lower(unaccent(COALESCE(%(Rue)s, ''))) ) is null
            )
        )
    ) AND (
        -- if localite is set then check for an exact match otherwise take all the similar localite
        %(Localite)s is null OR
        (
            lower(unaccent(communenomlangueprimaire)) = (SELECT DISTINCT lower(unaccent(communenomlangueprimaire)) FROM resa_geo.addresses WHERE lower(unaccent(communenomlangueprimaire)) = lower(unaccent(COALESCE(%(Localite)s, ''))) )
            OR
            (
                    SIMILARITY(%(Localite)s, communenomlangueprimaire) > 0.1 AND
                    (SELECT DISTINCT lower(unaccent(communenomlangueprimaire)) FROM resa_geo.addresses WHERE lower(unaccent(communenomlangueprimaire)) = lower(unaccent(COALESCE(%(Localite)s, ''))) ) is null
            )
        )
    ) AND (
        -- drop all the row without codepostal
        codepostal is not null
    ) AND (
        -- if cdpostal is set then check for an exact match
        %(Cdpostal)s is null OR
        codepostal = COALESCE(%(Cdpostal)s, '')
    ) AND (
        -- if numrue is set then take all the numrue with the highest similarity score
        -- meaning it will return the possibilities if we precise a number without letter
        %(NumRue)s is null OR
        SIMILARITY(%(NumRue)s, numero) = (SELECT max(SIMILARITY(%(NumRue)s, numero)) from resa_geo.addresses )
    )
    GROUP BY
        CASE WHEN %(NumRue)s is not null THEN numero ELSE null END,
        CASE WHEN %(Rue)s is not null or %(NumRue)s is not null THEN ruenomlangueprimaire ELSE null END,
        codepostal,
        communenomlangueprimaire
    ORDER BY
        -- sort by similatity score
        (
            COALESCE(SIMILARITY( %(Localite)s, communenomlangueprimaire),0) +
            COALESCE(SIMILARITY( %(Rue)s, CASE WHEN %(Rue)s is not null or %(NumRue)s is not null THEN ruenomlangueprimaire ELSE null END), 0) +
            COALESCE(SIMILARITY( %(NumRue)s, CASE WHEN %(NumRue)s is not null THEN numero ELSE null END),0) +
            COALESCE(SIMILARITY( %(Cdpostal)s, codepostal),0)
        )/4 DESC
) q;