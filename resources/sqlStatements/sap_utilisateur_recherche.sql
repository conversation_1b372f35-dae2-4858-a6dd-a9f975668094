WITH
info_partner AS (
    SELECT DISTINCT "Bp", "BpN<PERSON>", "Bp<PERSON>ren<PERSON>", "Bp<PERSON><PERSON><PERSON>ixe", "BpTelPortable", "BpEmail", "BpRue", "Bp<PERSON><PERSON><PERSON>", "Bp<PERSON><PERSON>postal", "BpLocalite"
    FROM
    (SELECT
      BUT000.PARTNER,
      CAST(BUT000.PARTNER AS BIGINT) AS "Bp",
      CASE WHEN BUT000.TYPE=1 THEN NAME_FIRST ELSE NAME_ORG1 END AS "BpPrenom",
      CASE WHEN BUT000.TYPE=1 THEN NAME_LAST ELSE NAME_ORG2 END AS "BpNom"
      FROM {HanaSchema}.BUT000
      LEFT JOIN {HanaSchema}.BUT0ID ON BUT0ID.PARTNER = BUT000.PARTNER AND IDNUMBER = 'BP_CONNECTE' -- prevent linking two account to same BP
      WHERE BPKIND='MYRE' AND VALID_FROM < CAST(TO_VARCHAR(NOW(), 'YYYYMMDDHHMISS') AS decimal(15)) AND VALID_TO > CAST(TO_VARCHAR(NOW(), 'YYYYMMDDHHMISS') AS decimal(15))
        AND XDELE = '' AND XBLCK = '' 
        AND (INSTITUTE IS NULL OR INSTITUTE != TRUE) -- prevent linking two account to same BP
      ) AS BUT000
    LEFT JOIN
    (SELECT
      PARTNER,
      ADDRNUMBER
    FROM {HanaSchema}.BUT021_FS
    WHERE VALID_FROM < CAST(TO_VARCHAR(NOW(), 'YYYYMMDDHHMISS') AS decimal(15)) AND VALID_TO > CAST(TO_VARCHAR(NOW(), 'YYYYMMDDHHMISS') AS decimal(15))
    ) AS BUT021_FS
    ON BUT000.PARTNER = BUT021_FS.PARTNER
    LEFT JOIN
    (SELECT
      ADDRNUMBER,
      TELNR_LONG AS "BpTelFixe"
    FROM {HanaSchema}.ADR2
    WHERE R3_USER = '1') AS ADR2_BpTelFixe
    ON BUT021_FS.ADDRNUMBER = ADR2_BpTelFixe.ADDRNUMBER
    LEFT JOIN
    (SELECT
      ADDRNUMBER,
      TELNR_LONG AS "BpTelPortable"
    FROM {HanaSchema}.ADR2
    WHERE R3_USER = '2' OR R3_USER = '3') AS ADR2_BpTelPortable
    ON BUT021_FS.ADDRNUMBER = ADR2_BpTelPortable.ADDRNUMBER
    LEFT JOIN
    (SELECT
      ADDRNUMBER,
      SMTP_ADDR AS "BpEmail"
    FROM {HanaSchema}.ADR6) AS ADR6
    ON BUT021_FS.ADDRNUMBER = ADR6.ADDRNUMBER
    LEFT JOIN
    (SELECT
      ADDRNUMBER,
      STREET  AS "BpRue",
      HOUSE_NUM1 AS "BpNumrue",
      POST_CODE1 AS "BpCdpostal",
      CITY1 AS "BpLocalite"
    FROM {HanaSchema}.ADRC) AS ADRC
    ON BUT021_FS.ADDRNUMBER = ADRC.ADDRNUMBER
),
bp_matching_search as (
    SELECT "Bp", "BpNom", "BpPrenom", "BpTelFixe", "BpTelPortable", "BpEmail", "BpRue", "BpNumrue", "BpCdpostal", "BpLocalite" FROM info_partner
    WHERE (
      REPLACE_REGEXPR('[''\-"._,&]' IN LOWER(REPLACE(CONCAT(IFNULL("BpPrenom", ''), IFNULL("BpNom", '')), ' ', '')) WITH '')
      =
      REPLACE_REGEXPR('[''\-"._,&]' IN LOWER(REPLACE(CONCAT(IFNULL(:BpPrenom, ''), IFNULL(:BpNom, '')), ' ', '')) WITH '')
    )
    AND (
        ( "BpTelFixe" IS NOT NULL AND SUBSTRING("BpTelFixe", LENGTH("BpTelFixe") - 7) = SUBSTRING(:BpTelFixe, LENGTH(:BpTelFixe) - 7) ) -- compare the last 8 digits of the fix phone number
        OR
        ( "BpTelPortable" IS NOT NULL AND SUBSTRING("BpTelPortable", LENGTH("BpTelPortable") - 8) = SUBSTRING(:BpTelPortable, LENGTH(:BpTelPortable) - 8) ) -- compare the last 9 digits of the mobile phone number
        OR
        ( ("BpEmail" IS NOT NULL AND LOWER("BpEmail") = LOWER(:BpEmail)) )
        OR
        (
            "BpRue" is not null and ("BpCdpostal" is not null or "BpLocalite" is not null)
            AND
            REPLACE_REGEXPR('[''\-"._,&]' IN LOWER(REPLACE(CONCAT("BpRue",IFNULL("BpNumrue", '')), ' ', '')) WITH '')
            =
            REPLACE_REGEXPR('[''\-"._,&]' IN LOWER(REPLACE(CONCAT(:BpRue,IFNULL(:BpNumrue, '')),   ' ', '')) WITH '')
            AND
            (
                LOWER(TRIM("BpCdpostal")) = Lower(TRIM(:BpCdpostal))
                OR
                LOWER(REPLACE("BpLocalite", ' ', '')) = Lower(REPLACE(:BpLocalite, ' ', ''))
            )
        )
    )
)
SELECT DISTINCT "Bp", "BpNom", "BpPrenom", "BpTelFixe", "BpTelPortable", "BpEmail", "BpRue", "BpNumrue", "BpCdpostal", "BpLocalite"
FROM bp_matching_search