WITH
get_ean AS (
  SELECT
    EUITRANS.EXT_UI AS EAN,
    EUITRANS.INT_UI,
    EUIINSTLN.ANLAGE,
    EANL.SPARTE,
    EANL.VSTELLE,
    ETINS.EQUNR,
    EQUI.EQART
  FROM {HanaSchema}.EUIT<PERSON><PERSON>
  JOIN {HanaSchema}.EUIINSTLN ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
  JOIN {HanaSchema}.EANL ON EANL.ANLAGE = EUIINSTLN.ANLAGE
  JOIN {HanaSchema}.ETINS ON EANL.VSTELLE = ETINS.VSTELLE
  LEFT JOIN {HanaSchema}.EQUI ON ETINS.EQUNR = EQUI.EQUNR
  WHERE EXT_UI IN ({Ean}) AND ((EANL.SPARTE = '01' AND EQUI.EQART LIKE '%IE_%') OR (EANL.SPARTE = '02' AND EQUI.EQART LIKE '%IG_%'))
), get_installation_properties AS (
  SELECT ANLAGE, OPERAND, AB, BIS, BELNR, WERT1, STRING1
  FROM {HanaSchema}.ETTIFN
  WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
), get_adress_info AS (
  SELECT DISTINCT
    ADRC.CITY1,
    ADRC.POST_CODE1,
    ADRC.STREET,
    ADRC.HOUSE_NUM1,
    EVBS.HAUS_NUM2,
    EVBS.FLOOR,
    EVBS.ROOMNUMBER,
    EVBS.VSTELLE
  FROM {HanaSchema}.EVBS
  LEFT JOIN {HanaSchema}.ILOA ON EVBS.HAUS = ILOA.TPLNR
  LEFT JOIN {HanaSchema}.ADRC ON ILOA.ADRNR = ADRC.ADDRNUMBER
), get_counter_info AS (
    SELECT 
        EASTL.ANLAGE,
        EGERH.EQUNR,
        EQUI.SERNR,
        CASE WHEN CAP_ACT_GRP IN('9000', '9001', '9002') THEN TRUE ELSE FALSE END AS IS_SMART
    FROM {HanaSchema}.EASTL
    JOIN {HanaSchema}.EGERH ON EASTL.LOGIKNR = EGERH.LOGIKNR
    JOIN {HanaSchema}.EQUI ON EGERH.EQUNR = EQUI.EQUNR
    WHERE EASTL.AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= EASTL.BIS
        AND EGERH.AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= EGERH.BIS
        AND EQUI.EQART != 'IG_CNV'
), config_info AS (
  SELECT
    EGERH.EQUNR,
    MAX(CASE WHEN ATNAM = 'IE_PRT_REGL_PREL' THEN ATFLV ELSE NULL END) AS IE_PRT_REGL_PREL,
    MAX(CASE WHEN ATNAM = 'IE_PRT_NB_PHASES' THEN ATWRT ELSE NULL END) AS IE_PRT_NB_PHASES
  FROM {HanaSchema}.EGERH
      INNER JOIN
      (
        SELECT LOGIKNR AS EZUG_LOGIKNR, LOGIKNR2 AS PRT_LOGIKNR
        FROM {HanaSchema}.EZUG
      ) AS EZUG
      ON EZUG.EZUG_LOGIKNR = EGERH.LOGIKNR
      INNER JOIN
      (
        SELECT EQUNR AS PRT_EQUNR, LOGIKNR AS PRT_LOGIKNR_EGERH
        FROM {HanaSchema}.EGERH
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
        AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
      ) AS PRT_EGERH
      ON PRT_EGERH.PRT_LOGIKNR_EGERH = EZUG.PRT_LOGIKNR
      INNER JOIN
      (
        SELECT EQUNR, SERNR
        FROM {HanaSchema}.EQUI
      ) AS EQUI
      ON EQUI.EQUNR = PRT_EGERH.PRT_EQUNR
      INNER JOIN
      (
        SELECT OBJEK AS INPUT_OBJEK,
        CUOBJ
        FROM {HanaSchema}.INOB
      ) AS INOB
      ON INOB.INPUT_OBJEK = EQUI.EQUNR
      INNER JOIN
      (
        SELECT
          OBJEK, ATINN, ATFLV, ATWRT
        FROM {HanaSchema}.AUSP
      ) AS AUSP
      ON INOB.CUOBJ = AUSP.OBJEK
      INNER JOIN
      (
        SELECT ATINN AS CABN_ATTIN, ATNAM
        FROM {HanaSchema}.CABN
      ) AS CABN
      ON AUSP.ATINN = CABN.CABN_ATTIN
      GROUP BY EGERH.EQUNR
), rate_info AS (
    WITH tarif AS (
        SELECT 
            A004.MATNR AS "Id",
            KONP.KBETR AS "Price"
        FROM {HanaSchema}.A004
        JOIN {HanaSchema}.KONP ON A004.KNUMH = KONP.KNUMH
        WHERE 
            A004.MATNR != 'EB111'
            AND A004.VTWEG = '01'
            AND A004.DATAB < NOW()
            AND A004.DATBI > NOW()
    )
    SELECT
        TRIM(SUBSTRING(MAX(LIBELLE), LOCATE(MAX(LIBELLE), 'Forfait ') + LENGTH('Forfait '))) AS FORFAIT,
        NB_PHASE,
        AMPERAGE,
        PUISSANCE,
        SUM(tarif."Price") AS PRICE
    FROM {HanaSchema}.ZWS63_PRIX
    LEFT JOIN tarif ON tarif."Id" = ARTICLE
    WHERE NB_CPT = 1 AND NUMSEQ != 'DE'
    GROUP BY NB_PHASE, AMPERAGE, PUISSANCE
    ORDER BY NB_PHASE, AMPERAGE, PUISSANCE
), tmp_res AS (
    SELECT
        get_ean.EAN AS "Ean",
        get_adress_info.STREET AS "Address_Street",
        get_adress_info.HOUSE_NUM1 AS "Address_Number",
        get_adress_info.POST_CODE1 AS "Address_Poscode",
        get_adress_info.CITY1 AS "Address_City",
        CONCAT(get_adress_info.HAUS_NUM2, CONCAT(get_adress_info.FLOOR, get_adress_info.ROOMNUMBER)) AS "Address_Box",
        get_ean.SPARTE AS "Meter_Type",
        get_counter_info.SERNR AS "Meter_Number",
        (SELECT MAX(CASE WHEN OPERAND LIKE '__PCP' THEN WERT1 END) FROM get_installation_properties WHERE get_installation_properties.ANLAGE = get_ean.ANLAGE) AS "Meter_Power", -- E_PCP WERT1
        config_info.IE_PRT_REGL_PREL AS "Meter_Amper",
        (SELECT MAX(CASE WHEN OPERAND LIKE '__UCODE' THEN STRING1 END) FROM get_installation_properties WHERE get_installation_properties.ANLAGE = get_ean.ANLAGE) AS "Meter_Tarif", -- E_UCODE STRING1
        (SELECT MAX(CASE WHEN OPERAND LIKE '__PPR' THEN WERT1 END) FROM get_installation_properties WHERE get_installation_properties.ANLAGE = get_ean.ANLAGE) AS "Meter_Production",
        get_counter_info.IS_SMART AS "Meter_Smart",
        config_info.IE_PRT_NB_PHASES AS "Meter_Phase"
    FROM get_ean
    JOIN get_adress_info ON get_adress_info.VSTELLE = get_ean.VSTELLE
    JOIN get_counter_info ON get_counter_info.ANLAGE = get_ean.ANLAGE
    LEFT JOIN config_info ON config_info.EQUNR = get_counter_info.EQUNR
    ORDER BY "Address_Poscode", "Address_City", "Address_Street", "Address_Number", "Ean"
)
SELECT
    *,
    (
        SELECT FIRST_VALUE(FORFAIT ORDER BY NB_PHASE, AMPERAGE, PUISSANCE)
        FROM rate_info
        WHERE
            NB_PHASE >= 1
            AND AMPERAGE >= "Meter_Amper"
            AND PUISSANCE >= "Meter_Power"
    ) AS "Meter_Rate",
    CASE
        WHEN "Meter_Phase" = '3' THEN 'TRI'
        WHEN "Meter_Phase" = '3N' THEN 'TRI_N'
        WHEN "Meter_Phase" = '1N' THEN 'MONO_N'
        WHEN "Meter_Phase" = '2' THEN 'MONO'
    ELSE NULL
    END AS "Meter_PhaseType"
FROM tmp_res