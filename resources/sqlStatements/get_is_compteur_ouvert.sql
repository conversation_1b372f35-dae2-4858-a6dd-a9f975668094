WITH get_is_compteur_ouvert AS (
  SELECT INPUT_OBJEK, ATINN, ATNAM, ATFLV, ATWRT FROM
  (SELECT OBJEK AS INPUT_OBJEK, CUOBJ FROM {HanaSchema}.INOB WHERE OBJEK  = '000000000501095738') AS INOB
  INNER JOIN
  (SELECT OBJEK, ATINN, ATFLV, ATWRT FROM {HanaSchema}.AUSP) AS AUSP
  ON INOB.CUOBJ = AUSP.OBJEK
  INNER JOIN
  (SELECT ATINN AS CABN_ATTIN, ATNAM FROM {HanaSchema}.CABN WHERE ATNAM = 'IC_CPT_STATUT_GRD') AS CABN
  ON AUSP.ATINN = CABN.CABN_ATTIN
)
SELECT * FROM get_is_compteur_ouvert