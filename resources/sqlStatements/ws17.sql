with WS17_core as (
  select  *
    from (
    select
      ADRSTREETT.STRT_CODE as id_rue,
      ADRSTREETT.STREET as rue,
      ADRPCDCITY.CITY_CODE as id_localite,
      *
    from (
      (select
      CITY_CODE,
      POST_CODE
      from {HanaSchema}.ADRPCDCITY
      where POST_CODE = :Cdpostal
      ) as ADRPCDCITY
      inner join
        (select
        CITY_CODE,
        MC_CITY,
        CITY_EXT
        from {HanaSchema}.ADRCITYT)
         as ADRCITYT
      on ADRPCDCITY.CITY_CODE = ADRCITYT.CITY_CODE
    inner join
    (select *
    from (
      select
        CITY_CODE,
        STRT_CODE,
        MC_STREET,
        STREET,
        LANGU
      from {HanaSchema}.ADRSTREETT)
      ) as ADRSTREETT
    on ADRPCDCITY.CITY_CODE = ADRSTREETT.CITY_CODE
    inner join
    (select
      CITY_CODE,
      STRT_CODE as RUE_CODE
      from {HanaSchema}.ADRSTREET
      where STRTYPEAB != '02'
    ) as ADRSTREET
    on ADRSTREETT.STRT_CODE = ADRSTREET.RUE_CODE and
    <PERSON>RPCDCI<PERSON>.CITY_CODE = ADRSTREET.CITY_CODE
    inner join
    (select distinct
      POST_CODE, STRT_CODE as STREET_CODE
      from {HanaSchema}.ADRSTRPCD
    ) as ADRSTRPCD
    on ADRPCDCITY.POST_CODE = ADRSTRPCD.POST_CODE and
    ADRSTREETT.STRT_CODE = ADRSTRPCD.STREET_CODE
    left join
    (select
      STRT_CODE, POST_CODE, CITY_CODE as VILLE_RAD, IDRAD_LOCALITE, IDRAD_RUE
      from {HanaSchema}.ZCA_RAD_MAPP
      where POST_CODE = :Cdpostal
    ) as ZCA_RAD_MAPP
    on ADRSTREETT.STRT_CODE = ZCA_RAD_MAPP.STRT_CODE and
    ADRPCDCITY.CITY_CODE = ZCA_RAD_MAPP.VILLE_RAD
  ))
  WHERE TRIM(Rue) != 'Abribus (EP)'
  ORDER BY id_rue
), get_city_code AS (
    select distinct MC_CITY as mc_localite, TPLNR, PLTXT
     from WS17_core
    inner join
    (
      select REGIOGROUP, CITY_CODE
      from {HanaSchema}.ADRCITY
    ) as ADRCITY
    on
    WS17_core.id_localite = ADRCITY.CITY_CODE
   inner join
    (
      select TPLNR, PLTXU, PLTXT
      from {HanaSchema}.IFLOTX
      where TPLNR like 'L-%' and not TPLNR like 'L-999'
    ) as IFLOTX
    on trim(concat('L-', concat(REGIOGROUP, '-'))) = SUBSTRING(trim(TPLNR), 0, 6)
    where upper(trim(PLTXU))  like concat(upper(trim(MC_CITY)), '%')
), add_city_code AS (
  select
    ID_RUE as "IdRue",
    RUE as "Rue",
    id_localite as "IdLocalite",
    CITY_EXT as "Localite",
    cast(IDRAD_RUE as int) as "IdRadRue",
    cast(IDRAD_LOCALITE as int) as "IdRadLocalite",
    case when PLTXT is not null then PLTXT else INITCAP(CITY_EXT) end as "Ville",
    case when TPLNR is not null then TPLNR else "MappedCity" end as "City"
  from WS17_core
  left join
  (select * from get_city_code) as get_city
  on WS17_core.MC_CITY = get_city.mc_localite
  left join
  (
     select FROM_VALUE3, TO_VALUE1 as "MappedCity"
     from {HanaSchema}.ZCA_MAPPING
     where IDMAPPING = 'LOCALITE_TO_POSTE_TECHNIQUE'
  ) as ZCA_MAPPING
  on WS17_core.MC_CITY = ZCA_MAPPING.FROM_VALUE3
)
select distinct * from add_city_code
order by "IdLocalite", "IdRue"