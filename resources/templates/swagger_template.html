<html>

<head>
  <title>MyResaAPI documentation for developers</title>
  <meta charset="utf-8" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bignumber.js/8.0.2/bignumber.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/json-bigint-parser-browser@1.0.4/json-bigint-browser.min.js"></script>
  <script>
    JSON.parse = (...arguments) => {
      const ret = JSONbig.parse(arguments[0], (key, value, x) => {
        console.log(key, value)
        try {
          if (typeof value !== "string")
            return BigNumber(BigInt(value))
        } finally { return value; }
      });
      return ret
    }
    JSON.stringify = (...arguments) => {
      const ret = JSONbig.stringify(...arguments);
      return ret
    }
  </script>
  <link href="https://unpkg.com/swagger-ui-dist@5/swagger-ui.css" rel="stylesheet" type="text/css">
  <script src="https://unpkg.com/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
</head>

<body>
  <div id="swagger-ui"></div>
</body>

<script>
  function sortOnKeys(dict) {

    var sorted = [];
    for (var key in dict) {
      sorted[sorted.length] = key;
    }
    sorted.sort();

    var tempDict = {};
    for (var i = 0; i < sorted.length; i++) {
      tempDict[sorted[i]] = dict[sorted[i]];
    }

    return tempDict;
  }
  function sort_path(doc) {
    doc.paths = sortOnKeys(doc.paths)
    return doc
  }

  window.onload = function () {
    const ui = SwaggerUIBundle(
      {
        spec: sort_path("#openapi#"),
        dom_id: "#swagger-ui",
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIBundle.SwaggerUIStandalonePreset
        ]
      })
    window.ui = ui
  }
</script>

</html>