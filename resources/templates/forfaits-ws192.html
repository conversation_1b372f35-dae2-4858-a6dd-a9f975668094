<html dir="ltr" lang="en-US">

<head>
    <title>Template Resa | Tarif de raccordement élec</title>
    <meta charset="utf-8">
    <meta content="width=device-width,initial-scale=1" name="viewport">
    <meta content="The interactive documentation for our Design System" name="description">

    <style>
        @page {
        	size:landscape;
	    }

        html {
            --color-primary: #D3450D;
            --color-text: #333333;
            --space-table: 0.4rem;
            color: var(--color-text);
            font-size: 10px;
            font-family: proxima-nova, sans-serif;
        }

        body {
            box-sizing: border-box;
            line-height: 1.3;
            padding: 0.8rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--color-primary);
            margin: 1.6rem 0;
        }

        .header__group {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .header__brand {
            flex: 0 0 auto;
            width: auto;
            height: 2.4rem;
            margin-right: 1.6rem;
        }

        .header__title {
            position: relative;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin: 0 0.8rem 0 1.6rem;
            font-size: 1.8rem;
        }

        .header__title::before {
            content: "";
            display: block;
            flex: 0 0 auto;
            position: absolute;
            top: -0.4rem;
            left: -1.6rem;
            background-color: var(--color-primary);
            width: 0.1rem;
            height: 3.2rem;
        }

        .header__energyIcon {
            flex: 0 0 auto;

        }

        .header__decoration {
            flex: 0 0 auto;
            width: auto;
            height: 4rem;
            margin-left: 3.2rem;
        }

        .table {
            width: 100%;
            border: 1px solid var(--color-primary);
            border-collapse: collapse;
        }

        caption {
            padding: calc(var(--space-table) * 2) calc(var(--space-table) * 2) var(--space-table) calc(var(--space-table) * 2);
            color: white;
            font-weight: bold;
            background-color: var(--color-primary);
            text-transform: uppercase;
        }

        th {
            text-align: left;
            color: var(--color-primary);
            font-weight: bold;
        }

        th,
        td {
            border: 1px solid var(--color-primary);
            padding: var(--space-table) calc(var(--space-table)*4);
        }

        .table__legend {
            font-size: 1.4rem;
            margin: var(--space-table) calc(var(--space-table)*4);
        }
    </style>
</head>

<body>
<div class="header">
    <div class="header__group">
        <h1 class="header__title">Tarif&nbsp;de&nbsp;raccordement&nbsp;-&nbsp;Électricité</h1>
        <svg class="header__energyIcon" fill="none" height="24" viewBox="0 0 16 24" width="16"
             xmlns="http://www.w3.org/2000/svg">
            <path
                    d="M5.39695 23.9793L5.44863 23.969C5.98398 23.9008 6.44906 23.6383 6.79218 23.2063L14.1962 12.8774C14.4752 12.4806 14.6365 12.0052 14.6365 11.5794C14.6365 10.4342 13.7208 9.50202 12.5943 9.50202C12.4392 9.50202 12.2677 9.44828 12.0837 9.34286C11.8853 9.21884 11.7427 9.01628 11.6662 8.7517L11.6579 8.70829C11.6186 8.43958 11.6641 8.19981 11.7943 7.97864L14.7212 3.18112C14.9341 2.87727 15.0519 2.48454 15.0519 2.07734C15.0519 0.93222 14.1362 0 13.0097 0H5.0683C4.19189 0 3.41469 0.57256 3.13565 1.42417L0.113685 10.7608C0.0330721 10.9799 0 11.1804 0 11.4223C0 12.5674 0.915683 13.4996 2.0422 13.4996H5.0683C5.1117 13.4996 5.14064 13.4996 5.17165 13.5037L5.26053 13.5265C5.76281 13.6319 6.08733 14.0722 6.03566 14.5931L6.03152 14.6385L3.17079 21.441L3.16459 21.4638C3.16045 21.4741 3.15838 21.4886 3.15632 21.5031C3.02196 22.272 3.29481 23.0285 3.87977 23.5411L3.89217 23.5515C4.2663 23.8429 4.71071 24 5.17165 24C5.24606 24 5.32461 24 5.39695 23.9773V23.9793ZM4.20636 21.8131V21.7594L7.10016 14.9093C7.1043 14.899 7.10636 14.8845 7.10843 14.87C7.31927 13.7725 6.60408 12.6831 5.51684 12.4289L5.49617 12.4248C5.33907 12.3979 5.20265 12.3979 5.0931 12.3979H2.0422C1.50478 12.3979 1.08518 11.968 1.08518 11.4202C1.08518 11.3706 1.09345 11.2817 1.12858 11.1329L1.13479 11.1102L4.15468 1.78383C4.28697 1.38076 4.66316 1.09965 5.0683 1.09965H13.0345C13.572 1.09965 13.9916 1.52958 13.9916 2.07734C13.9916 2.23443 13.9399 2.41013 13.8365 2.59823L10.8869 7.39368C10.6141 7.83808 10.5211 8.40858 10.6347 8.95427C10.7422 9.47102 11.0647 9.9361 11.5194 10.2317C11.8336 10.459 12.2201 10.5789 12.6232 10.5789C13.1606 10.5789 13.5802 11.0089 13.5802 11.5566C13.5802 11.7799 13.4996 12.0072 13.3529 12.1995L5.95918 22.5138C5.78968 22.7288 5.56231 22.8569 5.31427 22.8859C5.27913 22.89 5.24193 22.8921 5.20679 22.8921C4.98768 22.8921 4.77478 22.8156 4.59495 22.673C4.32831 22.4642 4.19189 22.1604 4.21049 21.8172L4.20636 21.8131Z"
                    fill="#F7A600"/>
        </svg>
    </div>
    <svg class="header__decoration" fill="none" height="39" viewBox="0 0 120 39" width="120"
         xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_871_1812)">
            <path
                    d="M63.3604 29.3081C63.1421 29.3081 62.9238 29.2255 62.7586 29.0603C62.4281 28.7299 62.4281 28.187 62.7586 27.8566L68.2225 22.3927C68.5529 22.0622 69.0958 22.0622 69.4262 22.3927C69.7567 22.7231 69.7567 23.2659 69.4262 23.5964L63.9623 29.0603C63.7971 29.2255 63.5787 29.3081 63.3604 29.3081Z"
                    fill="#F7A600"/>
            <path
                    d="M74.6895 29.3081C74.4712 29.3081 74.2529 29.2255 74.0877 29.0603C73.7572 28.7299 73.7572 28.187 74.0877 27.8566L79.5516 22.3927C79.882 22.0622 80.4249 22.0622 80.7553 22.3927C81.0858 22.7231 81.0858 23.2659 80.7553 23.5964L75.2914 29.0603C75.1262 29.2255 74.9079 29.3081 74.6895 29.3081Z"
                    fill="#F7A600"/>
            <path
                    d="M57.6959 18.2387C57.4776 18.2387 57.2592 18.1561 57.094 17.9909C56.7636 17.6604 56.7636 17.1176 57.094 16.7871L62.5579 11.3232C62.8884 10.9928 63.4312 10.9928 63.7617 11.3232C64.0921 11.6536 64.0921 12.1965 63.7617 12.5269L58.2977 17.9909C58.1325 18.1561 57.9142 18.2387 57.6959 18.2387Z"
                    fill="#F7A600"/>
            <path
                    d="M69.025 18.2387C68.8067 18.2387 68.5883 18.1561 68.4231 17.9909C68.0927 17.6604 68.0927 17.1176 68.4231 16.7871L73.8871 11.3232C74.2175 10.9928 74.7603 10.9928 75.0908 11.3232C75.4212 11.6536 75.4212 12.1965 75.0908 12.5269L69.6268 17.9909C69.4616 18.1561 69.2433 18.2387 69.025 18.2387Z"
                    fill="#F7A600"/>
            <path
                    d="M80.3541 18.2387C80.1357 18.2387 79.9174 18.1561 79.7522 17.9909C79.4218 17.6604 79.4218 17.1176 79.7522 16.7871L85.2161 11.3232C85.5466 10.9928 86.0894 10.9928 86.4199 11.3232C86.7503 11.6536 86.7503 12.1965 86.4199 12.5269L80.9559 17.9909C80.7907 18.1561 80.5724 18.2387 80.3541 18.2387Z"
                    fill="#F7A600"/>
            <path
                    d="M74.6895 7.1692C74.4712 7.1692 74.2529 7.08659 74.0877 6.92137C73.7572 6.59094 73.7572 6.04809 74.0877 5.71766L79.5516 0.247824C79.882 -0.0826081 80.4249 -0.0826081 80.7553 0.247824C81.0858 0.578256 81.0858 1.12111 80.7553 1.45154L75.2914 6.91547C75.1262 7.08069 74.9079 7.1633 74.6895 7.1633V7.1692Z"
                    fill="#F7A600"/>
            <path
                    d="M86.0186 7.1692C85.8003 7.1692 85.582 7.08659 85.4168 6.92137C85.0863 6.59094 85.0863 6.04809 85.4168 5.71766L90.8807 0.247824C91.2111 -0.0826081 91.754 -0.0826081 92.0844 0.247824C92.4149 0.578256 92.4149 1.12111 92.0844 1.45154L86.6205 6.91547C86.4553 7.08069 86.237 7.1633 86.0186 7.1633V7.1692Z"
                    fill="#F7A600"/>
            <path
                    d="M97.3477 7.1692C97.1294 7.1692 96.9111 7.08659 96.7459 6.92137C96.4154 6.59094 96.4154 6.04809 96.7459 5.71766L102.21 0.247824C102.54 -0.0826081 103.083 -0.0826081 103.414 0.247824C103.744 0.578256 103.744 1.12111 103.414 1.45154L97.9496 6.91547C97.7844 7.08069 97.566 7.1633 97.3477 7.1633V7.1692Z"
                    fill="#F7A600"/>
            <path
                    d="M44.8148 13.5949C45.8415 13.5949 46.6676 14.4269 46.6676 15.4477C46.6676 16.4685 45.8356 17.3005 44.8148 17.3005C43.794 17.3005 42.962 16.4685 42.962 15.4477C42.962 14.4269 43.794 13.5949 44.8148 13.5949ZM44.8148 12.2496C43.0505 12.2496 41.6167 13.6834 41.6167 15.4477C41.6167 17.212 43.0505 18.6458 44.8148 18.6458C46.5791 18.6458 48.0129 17.212 48.0129 15.4477C48.0129 13.6834 46.5791 12.2496 44.8148 12.2496Z"
                    fill="#3E7A6B"/>
            <path
                    d="M34.5538 13.5949C35.5805 13.5949 36.4066 14.4269 36.4066 15.4477C36.4066 16.4685 35.5746 17.3005 34.5538 17.3005C33.533 17.3005 32.701 16.4685 32.701 15.4477C32.701 14.4269 33.533 13.5949 34.5538 13.5949ZM34.5538 12.2496C32.7896 12.2496 31.3557 13.6834 31.3557 15.4477C31.3557 17.212 32.7896 18.6458 34.5538 18.6458C36.3181 18.6458 37.7519 17.212 37.7519 15.4477C37.7519 13.6834 36.3181 12.2496 34.5538 12.2496Z"
                    fill="#3E7A6B"/>
            <path
                    d="M49.5294 2.82635C50.5561 2.82635 51.3822 3.65833 51.3822 4.67913C51.3822 5.69993 50.5502 6.53191 49.5294 6.53191C48.5086 6.53191 47.6766 5.69993 47.6766 4.67913C47.6766 3.65243 48.5086 2.82635 49.5294 2.82635ZM49.5294 1.48102C47.7651 1.48102 46.3313 2.91486 46.3313 4.67913C46.3313 6.4434 47.7651 7.87724 49.5294 7.87724C51.2937 7.87724 52.7275 6.4434 52.7275 4.67913C52.7275 2.91486 51.2937 1.48102 49.5294 1.48102Z"
                    fill="#3E7A6B"/>
            <path
                    d="M24.2926 13.5949C25.3193 13.5949 26.1454 14.4269 26.1454 15.4477C26.1454 16.4685 25.3134 17.3005 24.2926 17.3005C23.2718 17.3005 22.4398 16.4685 22.4398 15.4477C22.4398 14.4269 23.2718 13.5949 24.2926 13.5949ZM24.2926 12.2496C22.5283 12.2496 21.0945 13.6834 21.0945 15.4477C21.0945 17.212 22.5283 18.6458 24.2926 18.6458C26.0569 18.6458 27.4907 17.212 27.4907 15.4477C27.4907 13.6834 26.0569 12.2496 24.2926 12.2496Z"
                    fill="#3E7A6B"/>
            <path
                    d="M49.9484 23.6377C50.9751 23.6377 51.8011 24.4697 51.8011 25.4905C51.8011 26.5113 50.9692 27.3433 49.9484 27.3433C48.9276 27.3433 48.0956 26.5113 48.0956 25.4905C48.0956 24.4697 48.9276 23.6377 49.9484 23.6377ZM49.9484 22.2924C48.1841 22.2924 46.7502 23.7262 46.7502 25.4905C46.7502 27.2547 48.1841 28.6886 49.9484 28.6886C51.7126 28.6886 53.1465 27.2547 53.1465 25.4905C53.1465 23.7262 51.7126 22.2924 49.9484 22.2924Z"
                    fill="#3E7A6B"/>
            <path
                    d="M39.6874 23.6377C40.7141 23.6377 41.5402 24.4697 41.5402 25.4905C41.5402 26.5113 40.7082 27.3433 39.6874 27.3433C38.6666 27.3433 37.8346 26.5113 37.8346 25.4905C37.8346 24.4697 38.6666 23.6377 39.6874 23.6377ZM39.6874 22.2924C37.9231 22.2924 36.4893 23.7262 36.4893 25.4905C36.4893 27.2547 37.9231 28.6886 39.6874 28.6886C41.4516 28.6886 42.8855 27.2547 42.8855 25.4905C42.8855 23.7262 41.4516 22.2924 39.6874 22.2924Z"
                    fill="#3E7A6B"/>
            <path
                    d="M29.4261 23.6377C30.4528 23.6377 31.2789 24.4697 31.2789 25.4905C31.2789 26.5113 30.4469 27.3433 29.4261 27.3433C28.4053 27.3433 27.5734 26.5113 27.5734 25.4905C27.5734 24.4697 28.4053 23.6377 29.4261 23.6377ZM29.4261 22.2924C27.6619 22.2924 26.228 23.7262 26.228 25.4905C26.228 27.2547 27.6619 28.6886 29.4261 28.6886C31.1904 28.6886 32.6243 27.2547 32.6243 25.4905C32.6243 23.7262 31.1904 22.2924 29.4261 22.2924Z"
                    fill="#3E7A6B"/>
            <path
                    d="M34.5538 33.6746C35.5805 33.6746 36.4066 34.5065 36.4066 35.5273C36.4066 36.5481 35.5746 37.3801 34.5538 37.3801C33.533 37.3801 32.701 36.5481 32.701 35.5273C32.701 34.5065 33.533 33.6746 34.5538 33.6746ZM34.5538 32.3292C32.7896 32.3292 31.3557 33.7631 31.3557 35.5273C31.3557 37.2916 32.7896 38.7254 34.5538 38.7254C36.3181 38.7254 37.7519 37.2916 37.7519 35.5273C37.7519 33.7631 36.3181 32.3292 34.5538 32.3292Z"
                    fill="#3E7A6B"/>
            <path
                    d="M24.2926 33.6746C25.3193 33.6746 26.1454 34.5065 26.1454 35.5273C26.1454 36.5481 25.3134 37.3801 24.2926 37.3801C23.2718 37.3801 22.4398 36.5481 22.4398 35.5273C22.4398 34.5065 23.2718 33.6746 24.2926 33.6746ZM24.2926 32.3292C22.5283 32.3292 21.0945 33.7631 21.0945 35.5273C21.0945 37.2916 22.5283 38.7254 24.2926 38.7254C26.0569 38.7254 27.4907 37.2916 27.4907 35.5273C27.4907 33.7631 26.0569 32.3292 24.2926 32.3292Z"
                    fill="#3E7A6B"/>
            <path
                    d="M28.9718 5.33407C29.5617 5.33407 30.0398 4.85591 30.0398 4.26606C30.0398 3.67622 29.5617 3.19806 28.9718 3.19806C28.382 3.19806 27.9038 3.67622 27.9038 4.26606C27.9038 4.85591 28.382 5.33407 28.9718 5.33407Z"
                    fill="#D3450D"/>
            <path
                    d="M28.9718 6.10707C27.9569 6.10707 27.1309 5.28099 27.1309 4.26609C27.1309 3.25119 27.9569 2.42511 28.9718 2.42511C29.9867 2.42511 30.8128 3.25119 30.8128 4.26609C30.8128 5.28099 29.9867 6.10707 28.9718 6.10707ZM28.9718 3.97106C28.8066 3.97106 28.6768 4.10677 28.6768 4.26609C28.6768 4.4254 28.8125 4.56112 28.9718 4.56112C29.1312 4.56112 29.2669 4.4254 29.2669 4.26609C29.2669 4.10677 29.1312 3.97106 28.9718 3.97106Z"
                    fill="#D3450D"/>
            <path
                    d="M39.2506 5.33407C39.8405 5.33407 40.3186 4.85591 40.3186 4.26606C40.3186 3.67622 39.8405 3.19806 39.2506 3.19806C38.6608 3.19806 38.1826 3.67622 38.1826 4.26606C38.1826 4.85591 38.6608 5.33407 39.2506 5.33407Z"
                    fill="#D3450D"/>
            <path
                    d="M39.2506 6.10707C38.2357 6.10707 37.4097 5.28099 37.4097 4.26609C37.4097 3.25119 38.2357 2.42511 39.2506 2.42511C40.2655 2.42511 41.0916 3.25119 41.0916 4.26609C41.0916 5.28099 40.2655 6.10707 39.2506 6.10707ZM39.2506 3.97106C39.0854 3.97106 38.9556 4.10677 38.9556 4.26609C38.9556 4.4254 39.0913 4.56112 39.2506 4.56112C39.41 4.56112 39.5457 4.4254 39.5457 4.26609C39.5457 4.10677 39.41 3.97106 39.2506 3.97106Z"
                    fill="#D3450D"/>
            <path
                    d="M1.84095 16.2974C2.4308 16.2974 2.90896 15.8192 2.90896 15.2294C2.90896 14.6395 2.4308 14.1614 1.84095 14.1614C1.25111 14.1614 0.772949 14.6395 0.772949 15.2294C0.772949 15.8192 1.25111 16.2974 1.84095 16.2974Z"
                    fill="#D3450D"/>
            <path
                    d="M1.84098 17.0704C0.826081 17.0704 0 16.2443 0 15.2294C0 14.2145 0.826081 13.3884 1.84098 13.3884C2.85588 13.3884 3.68196 14.2145 3.68196 15.2294C3.68196 16.2443 2.85588 17.0704 1.84098 17.0704ZM1.84098 14.9285C1.67576 14.9285 1.54595 15.0642 1.54595 15.2235C1.54595 15.3828 1.68166 15.5185 1.84098 15.5185C2.00029 15.5185 2.13601 15.3828 2.13601 15.2235C2.13601 15.0642 2.00029 14.9285 1.84098 14.9285Z"
                    fill="#D3450D"/>
            <path
                    d="M12.1198 16.2974C12.7096 16.2974 13.1878 15.8192 13.1878 15.2294C13.1878 14.6395 12.7096 14.1614 12.1198 14.1614C11.5299 14.1614 11.0518 14.6395 11.0518 15.2294C11.0518 15.8192 11.5299 16.2974 12.1198 16.2974Z"
                    fill="#D3450D"/>
            <path
                    d="M12.1198 17.0704C11.1049 17.0704 10.2788 16.2443 10.2788 15.2294C10.2788 14.2145 11.1049 13.3884 12.1198 13.3884C13.1347 13.3884 13.9608 14.2145 13.9608 15.2294C13.9608 16.2443 13.1347 17.0704 12.1198 17.0704ZM12.1198 14.9285C11.9546 14.9285 11.8248 15.0642 11.8248 15.2235C11.8248 15.3828 11.9605 15.5185 12.1198 15.5185C12.2791 15.5185 12.4148 15.3828 12.4148 15.2235C12.4148 15.0642 12.2791 14.9285 12.1198 14.9285Z"
                    fill="#D3450D"/>
            <path
                    d="M7.63539 26.4287C8.22523 26.4287 8.70339 25.9505 8.70339 25.3607C8.70339 24.7708 8.22523 24.2927 7.63539 24.2927C7.04554 24.2927 6.56738 24.7708 6.56738 25.3607C6.56738 25.9505 7.04554 26.4287 7.63539 26.4287Z"
                    fill="#D3450D"/>
            <path
                    d="M7.63541 27.2075C6.62051 27.2075 5.79443 26.3815 5.79443 25.3666C5.79443 24.3517 6.62051 23.5256 7.63541 23.5256C8.65031 23.5256 9.47639 24.3517 9.47639 25.3666C9.47639 26.3815 8.65031 27.2075 7.63541 27.2075ZM7.63541 25.0656C7.4702 25.0656 7.34038 25.2013 7.34038 25.3607C7.34038 25.52 7.4761 25.6557 7.63541 25.6557C7.79473 25.6557 7.93044 25.52 7.93044 25.3607C7.93044 25.2013 7.79473 25.0656 7.63541 25.0656Z"
                    fill="#D3450D"/>
            <path
                    d="M17.9083 26.4287C18.4982 26.4287 18.9763 25.9505 18.9763 25.3607C18.9763 24.7708 18.4982 24.2927 17.9083 24.2927C17.3185 24.2927 16.8403 24.7708 16.8403 25.3607C16.8403 25.9505 17.3185 26.4287 17.9083 26.4287Z"
                    fill="#D3450D"/>
            <path
                    d="M17.9084 27.2075C16.8935 27.2075 16.0674 26.3815 16.0674 25.3666C16.0674 24.3517 16.8935 23.5256 17.9084 23.5256C18.9233 23.5256 19.7493 24.3517 19.7493 25.3666C19.7493 26.3815 18.9233 27.2075 17.9084 27.2075ZM17.9084 25.0656C17.7431 25.0656 17.6133 25.2013 17.6133 25.3607C17.6133 25.52 17.749 25.6557 17.9084 25.6557C18.0677 25.6557 18.2034 25.52 18.2034 25.3607C18.2034 25.2013 18.0677 25.0656 17.9084 25.0656Z"
                    fill="#D3450D"/>
            <path
                    d="M1.84095 36.5658C2.4308 36.5658 2.90896 36.0877 2.90896 35.4978C2.90896 34.908 2.4308 34.4298 1.84095 34.4298C1.25111 34.4298 0.772949 34.908 0.772949 35.4978C0.772949 36.0877 1.25111 36.5658 1.84095 36.5658Z"
                    fill="#D3450D"/>
            <path
                    d="M1.84098 37.3388C0.826081 37.3388 0 36.5127 0 35.4978C0 34.4829 0.826081 33.6569 1.84098 33.6569C2.85588 33.6569 3.68196 34.4829 3.68196 35.4978C3.68196 36.5127 2.85588 37.3388 1.84098 37.3388ZM1.84098 35.2028C1.67576 35.2028 1.54595 35.3385 1.54595 35.4978C1.54595 35.6572 1.68166 35.7929 1.84098 35.7929C2.00029 35.7929 2.13601 35.6572 2.13601 35.4978C2.13601 35.3385 2.00029 35.2028 1.84098 35.2028Z"
                    fill="#D3450D"/>
            <path
                    d="M12.1198 36.5658C12.7096 36.5658 13.1878 36.0877 13.1878 35.4978C13.1878 34.908 12.7096 34.4298 12.1198 34.4298C11.5299 34.4298 11.0518 34.908 11.0518 35.4978C11.0518 36.0877 11.5299 36.5658 12.1198 36.5658Z"
                    fill="#D3450D"/>
            <path
                    d="M12.1198 37.3388C11.1049 37.3388 10.2788 36.5127 10.2788 35.4978C10.2788 34.4829 11.1049 33.6569 12.1198 33.6569C13.1347 33.6569 13.9608 34.4829 13.9608 35.4978C13.9608 36.5127 13.1347 37.3388 12.1198 37.3388ZM12.1198 35.2028C11.9546 35.2028 11.8248 35.3385 11.8248 35.4978C11.8248 35.6572 11.9605 35.7929 12.1198 35.7929C12.2791 35.7929 12.4148 35.6572 12.4148 35.4978C12.4148 35.3385 12.2791 35.2028 12.1198 35.2028Z"
                    fill="#D3450D"/>
        </g>
        <defs>
            <clipPath id="clip0_871_1812">
                <rect fill="white" height="38.7314" width="120"/>
            </clipPath>
        </defs>
    </svg>
</div>
<table class="table">
    <caption>Nouveau raccordements</caption>
    <thead>
    <tr>
        <th>Forfait Raccordement</th>
        <th>Type de raccordement</th>
        <th>Intensité</th>
        <th>Puissance</th>
        <th>Prix</th>
    </tr>
    </thead>
    <tbody>
    {% for forfait in params.forfaits if forfait.Phases != 3 %}
    <tr>
        <td>{{forfait.Label}}</td>
        <td>{% if forfait.Phases == 1 %}Monophasé{% elif forfait.Phases == 4 %}Triphasé avec neutre{% else %}{{ forfait.Phases }}{% endif %}</td>
        <td>{{forfait.Amperage}}&nbsp;{{forfait.AmperageUnit}}</td>
        <td>{{"{:.1f}".format(forfait.Power).replace(".", ",").replace(",0", "")}}&nbsp;{{forfait.PowerUnit}}</td>
        <td>{{"{:,.2f}".format(forfait.Price).replace(",", "X").replace(".", ",").replace("X", ".")}}&nbsp;€</td>
    </tr>
    {%endfor%}
    </tbody>
</table>
</body>

</html>