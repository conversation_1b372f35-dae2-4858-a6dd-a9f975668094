{%- set content_a = params.ContentA -%}
{%- if 'EAN' in content_a -%}
    {%- set content_a = [content_a] -%}
{%- endif -%}
{%- for element in content_a -%}
    {%- if 'SERIAL_NUMBER' in element.ContentB -%}
        {# Set ContentB as list in all case #}
        {%- set _ = element.update({'ContentB': [element.ContentB]}) -%}
    {%- endif %}
{%- endfor -%}
{%- set element_a = content_a.0 -%}
RESA - Compteur ÉLECTRICITÉ {% for element_b in element_a.ContentB %}{% if loop.index > 1 %} / {% endif -%}
    {{ element_b.SERIAL_NUMBER | trim }}{% endfor %}: Le {{ params.Header.DATE_FROM }}, notre agent s'est présenté à votre domicile pour le relevé d’index annuel. En raison de votre absence, il n'a pu réaliser ce relevé. Nous vous invitons à communiquer vos index avant le {{ params.Header.DATE_TO }} via ce lien :
{%- set ean = element_a.EAN -%}
{%- set token = element_a.TOKEN -%}
{%- set sn = element_a.ContentB.0.SERIAL_NUMBER %}
https://{% if 'qta' in params.env %}webresa-qual{% elif 'qla' in params.env %}webresa-qla{% else %}www{% endif %}.resa.be/{{ params.Langue }}/i?{%- if token -%}t=
    {{- token -}}{%- else -%}e={{ ean }}&c={{ sn }}{%- endif -%}&tr=an