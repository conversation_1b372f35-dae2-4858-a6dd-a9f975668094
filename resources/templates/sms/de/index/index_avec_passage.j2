{%- set content_a = params.ContentA -%}
{%- if 'EAN' in content_a -%}
    {%- set multi = False -%}
    {%- set content_a = [content_a] -%}
{%- else -%}
    {%- if content_a|length == 1-%}
        {%- set multi = False -%}
    {%- else -%}
        {%- set multi = True -%}
    {%- endif -%}
{%- endif -%}
RESA – Jährliche Zählerablesungen: Unser Mitarbeiter wird zwischen dem {{params.Header.DATE_FROM}} und {{params.Header.DATE_TO}}vorbeikommen. Sie haben keine Zeit? Tragen Sie Ihre Zählerstände direkt über {% if multi %}diese Links{% else %}diesen Link{% endif %} ein:
{%- for element in content_a -%}
    {%- set ean = element.EAN -%}
    {%- set token = element.TOKEN -%}
    {%- if 'SERIAL_NUMBER' in element.ContentB -%}
        {%- set sn = element.ContentB.SERIAL_NUMBER -%}
    {%- else -%}
        {%- set sn = element.ContentB.0.SERIAL_NUMBER -%}
    {%- endif %}
{% if multi %}{{element.ENERGY_TYPE}} : {% endif %}https://{% if 'qta' in params.env %}webresa-qual{% elif 'qla' in params.env %}webresa-qla{%else%}www{%endif%}.resa.be/{{params.Langue}}/i?{%- if token -%}t={{token}}{%- else -%}e={{ean}}&c={{sn}}{%- endif -%}&tr=an
{%- endfor -%}