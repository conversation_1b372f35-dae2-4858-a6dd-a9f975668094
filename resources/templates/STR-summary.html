<!DOCTYPE html>
<html lang="fr-FR">
<head>
    <meta charset="utf-8">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="width=device-width, initial-scale=1, minimal-ui" name="viewport">
    <meta content="#ffffff" name="theme-color">
    <title>Demande de raccordement - Gaz</title>
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link crossorigin href="https://fonts.gstatic.com" rel="preconnect">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500" rel="stylesheet">
    <style media="screen,print">
	@page {size: A4; margin: 0.5cm 0.75cm;}
	body{font-family: 'Roboto', sans-serif; font-weight:500;font-size:.8em;}
	section:not(:first-child) {page-break-before: always;}
	h1 {font-size:1.2em; font-weight:600; color:#5DAE99; margin:.2em 0 0 0; padding:0}
	h2 {font-size:1em; padding-bottom:.3em; color:#E84E0F}
	a {color:#E84E0F}
	label {display:flex; width:100%; align-items:center; margin-bottom:1em; margin-right:10px}
	input {padding:.5em; width:70%; margin-left: auto; background-color:#F1F1F1; border:0}
	hr {margin-bottom:.5em}
	
	.row {
	  display: flex;
	  flex-direction: row;
	  flex-wrap: wrap;
	}

	.column {
	  display: flex;
	  flex-direction: column;
	  flex-basis: auto;
	  flex: 1;
	  min-width: 0;
	}
	
	.field {
	    display:flex;
	    align-items:center; 
	    margin-bottom:0.7em; 
	    margin-right:10px;
	    font-size:.9em;
    }
	
	.label {
	    margin-right: auto; 
	    min-width: 28%;
	    max-width: 28%;
    }
	  
	.input {
	    padding:.5em; 
	    min-width: 70%;
	    max-width: 70%;
	    margin-left: auto; 
	    background-color:#F1F1F1; 
	    border:0
    }
	  
	.input_full {
	    padding:.5em; 
	    min-width: 100%;
	    max-width: 100%;
	    margin-left: auto; 
	    margin-right: auto; 
	    background-color:#F1F1F1; 
	    border:0
    }
    
    @page {
        @bottom-right {
            content: counter(page) ' / ' counter(pages);
            font-size:.75em;
            margin-bottom: 1em;
            font-family: 'Roboto', sans-serif;
            font-weight:500;
        }
    }
    
    
    </style>
</head>

<!--Set Jinja2 variables-->
{% set vars = namespace() %}
{% set vars.demandeur = {} %}
{% set vars.contact = {} %}
{% set vars.proprietaire = {} %}
{% set vars.elec = false %}
{% set vars.gaz = false %}

{% for partner in params.Data.Partenaire %}
{%if partner.TypePartenaire == "DEMANDEUR"%}
{% set vars.demandeur = partner%}
{%elif partner.TypePartenaire == "CONTACT"%}
{% set vars.contact = partner%}
{%elif partner.TypePartenaire == "PROPRIETAIRE"%}
{% set vars.proprietaire = partner%}
{%endif%}
{%endfor%}

{% for cpt in params.Data.InformationCompteurs.Compteur if cpt.energy=="electricity" %}{% if loop.index == 1 %}
{% set vars.elec = true %}
{%endif%}{%endfor%}
{% for cpt in params.Data.InformationCompteurs.Compteur if cpt.energy=="gaz" %}{% if loop.index == 1 %}
{% set vars.gaz = true %}
{%endif%}{%endfor%}
<!--END Set Jinja2 variables-->

<body>
<section>
    <div class='row'>
        <div class='column'>
            <h1>Nouvelle demande de raccordement -
                {% if vars.elec %}Électricité{%endif%}{% if vars.elec and vars.gaz %} et {%endif%}{% if vars.gaz
                %}Gaz{%endif%}</h1>
        </div>
        <div class='column'>
            <div class="field"><span class="label">Date: </span><span class="input">{{params.date}}</span></div>
        </div>
    </div>
    <div class='row'>
        <div class='column' style="padding-right: 2%">
            <h2>COORDONNÉES DU DEMANDEUR</h2>
            <div class="field"><span class="label">Type:</span><span class="input">{{vars.demandeur.TypeURD}}</span>
            </div>
            <div class="field"><span class="label">Civilité:</span><span class="input">{% if vars.demandeur.CodeCivilite == "0001" %}Mme{% elif vars.demandeur.CodeCivilite == "0002" %}Mr{% elif vars.demandeur.CodeCivilite == "0020" %}Melle{% endif %}</span>
            </div>
            <div class="field"><span class="label">Nom:</span><span class="input">{{vars.demandeur.Nom}}</span></div>
            <div class="field"><span class="label">Prénom:</span><span class="input">{{vars.demandeur.Prenom}}</span>
            </div>
            <div class="field"><span class="label">N° de téléphone:</span><span class="input">{% if vars.demandeur.Gsm != ""%}+32 (0){{vars.demandeur.Gsm}}{% endif %}</span>
            </div>
            <div class="field"><span class="label">E-mail:</span><span class="input">{{vars.demandeur.Email}}</span>
            </div>
            <div class="field"><span class="label">Langue:</span><span class="input">{{vars.demandeur.Langue}}</span>
            </div>
            <div class="field"><span class="label">Rue et numéro:</span><span class="input">{{vars.demandeur.Adresse.Rue}} {{vars.demandeur.Adresse.NumRue}}{{vars.demandeur.Adresse.NumCmpt}}</span>
            </div>
            <div class="field"><span class="label">Code postal:</span><span class="input">{{vars.demandeur.Adresse.CdPostal}}</span>
            </div>
            <div class="field"><span class="label">Localité:</span><span class="input">{{vars.demandeur.Adresse.Localite}}</span>
            </div>
            <div class="field"><span class="label">Pays:</span><span
                    class="input">{{vars.demandeur.Adresse.Pays}}</span></div>
            <div class="field"><span class="label">Etes-vous propriétaire:</span><span class="input">{% if vars.demandeur and vars.proprietaire and vars.demandeur.Nom == vars.proprietaire.Nom and vars.demandeur.Prenom == vars.proprietaire.Prenom  %}oui{%else%}non{%endif%}</span>
            </div>

            <h2>Coordonnées personne contact</h2>
            <div class="field"><span class="label">Nom:</span><span class="input">{{vars.contact.Nom or '/'}}</span>
            </div>
            <div class="field"><span class="label">Prénom:</span><span
                    class="input">{{vars.contact.Prenom or '/'}}</span>
            </div>
            <div class="field"><span class="label">N° de téléphone:</span><span class="input">{% if vars.contact.Gsm != ""%}+32 (0){{vars.contact.Gsm}}{%else%}/{% endif %}</span>
            </div>
            <div class="field"><span class="label">E-mail:</span><span
                    class="input">{{vars.contact.Email or '/'}}</span>
            </div>

            <h2>RÉGIME TVA</h2>
            <div class="field"><span class="label">Assujetti à la tva:</span><span class="input">{% if params.Data.TVA.AssujetiTVA %}Oui{% else %}Non{% endif %}</span>
            </div>
            <div class="field"><span class="label">Vous agissez en tant que:</span><span class="input">{% if params.Data.TVA.Type == 'PROP' %}Propriétaire{% elif params.Data.TVA.Type == 'COMMU' %}Communes{% elif params.Data.TVA.Type == 'SOCIETE' %}Société régionale de logement et société agréée pour le logement social non assujettie{% elif params.Data.TVA.Type == 'INTERN' %}Maison de repos, internat scolaire, home de protection de la jeunesse{% elif params.Data.TVA.Type == 'OTAN' %}OTAN, Ambassade, ESA{% elif params.Data.TVA.Type == 'BATI' %}Bâtiment scolaire{% elif params.Data.TVA.Type == 'AUTO' %}TVA en autoliquidation{% endif %}</span>
            </div>
            <div class="field"><span class="label">Votre logement:</span><span class="input">{% if params.Data.TVA.Logement == 'PRIVE_MOINS' %}Logement privé de moins de 10 ans{% elif params.Data.TVA.Logement == 'DESTRUCTION' %}Logement destiné à une destruction définitive{% elif params.Data.TVA.Logement == 'PRIVE_PLUS' %}Logement privé de plus de 10 ans{% elif params.Data.TVA.Logement == 'RECONSTRUIT' %}Un immeuble qui après démolition est reconstruit lors d’une même opération.{% elif params.Data.TVA.Logement == 'HANDICAP' %}Un logement privé/un établissement d’hébergement adapté pour handicapés.{% else %}/{% endif %}</span>
            </div>
        </div>
        <div class='column'>
            <h2>UTILISATION DU OU DES COMPTEUR(S)</h2>
            <div class="field"><span class="label">Utilisation:</span><span class="input">{{params.Data.TypeBatiment.Usage}}</span>
            </div>
            <div class="field"><span class="label">Type d'immeuble:</span><span class="input">{{params.Data.TypeBatiment.Type}}</span>
            </div>
            <div class="field"><span class="label">Lotissement, habitat groupé ou assimilé:</span><span class="input">{% if params.Data.Lotissement %}Oui{% else %}Non{% endif %}</span>
            </div>

            <h2>ADRESSE DE RACCORDEMENT</h2>
            <div class="field"><span class="label">N° d’habitation connu ?:</span><span class="input">{%if params.Data.Adresse.AdresseConnue%}Oui{%else%}Non{%endif%}</span>
            </div>

            <h2>Adresse de raccordement</h2>
            <div class="field"><span class="label">Rue et numéro:</span><span class="input">{{params.Data.Adresse.Rue}} {{params.Data.Adresse.NumRue}} {{params.Data.Adresse.NumCmpt}}</span>
            </div>
            <div class="field"><span class="label">Code postal:</span><span
                    class="input">{{params.Data.Adresse.CdPostal}}</span></div>
            <div class="field"><span class="label">Localité:</span><span
                    class="input">{{params.Data.Adresse.Localite}}</span></div>
            <div class="field"><span class="label">N° de parcelle:</span><span class="input">{{params.Data.Adresse.ParcelleSpw or '/'}}</span>
            </div>
            <div class="field"><span class="label">Coordonnées GPS:</span><span class="input">{{params.Data.Adresse.CoordGPSX or ''}} / {{params.Data.Adresse.CoordGPSY or ''}}</span>
            </div>
            <div class="field"><span class="label">Emplacement:</span><span class="input">{{params.Data.Adresse.Emplacement or '/'}}</span>
            </div>

            <h2>Date de raccordement</h2>
            <div class="field"><span class="label">Date de raccordement souhaitée:</span><span class="input">{%if params.Data.DateSouhaiteeTrav%}{{params.Data.DateSouhaiteeTrav[4:6]}}/{{params.Data.DateSouhaiteeTrav[:4]}}{%else%}{{params.Data.Remarques.month}}/{{params.Data.Remarques.year}}</span>{%endif%}
            </div>
        </div>
    </div>
</section>
<section>
    <h2>Remarque(s)</h2>
    <div class="field"><span class="input_full">{{params.Data.Remarques.remarkInfos or '/'}}</span></div>
</section>
{% if vars.elec %}
<section>
    <h2>COMPTEUR(S) ÉLECTRIQUE</h2>
    {% for cpt in params.Data.InformationCompteurs.Compteur if cpt.energy=="electricity" %}
    <h2>Compteur électrique n°{{loop.index}}</h2>
    <div class="field"><span class="label">Type de compteur:</span><span class="input">{% if cpt.phasesSlug=="TRI" %}Triphasé{% elif cpt.phasesSlug=="MONO" %}Monophasé{% endif %}</span>
    </div>
    <div class="field"><span class="label">Puissance souhaitée (kVA):</span><span class="input">{% if cpt.power == '+27.7' %}{{cpt.samplingPower}}{%else%}{{cpt.power}}{%endif%}</span>
    </div>
    <div class="field"><span class="label">Ampérage souhaité (A):</span><span class="input">{% if cpt.amperage == '+40' %}{{cpt.injectedPower}}{%else%}{{cpt.amperage}}{%endif%}</span>
    </div>
    <div class="field"><span class="label">Tarif souhaité:</span><span class="input">{% if cpt.tarifSlug=="DT" %}Double tarif{% elif cpt.tarifSlug=="EXN_DT" %}Double tarif avec Exclusif Nuit{% elif cpt.tarifSlug=="ST" %}Simple tarif{% elif cpt.tarifSlug=="EXN_ST" %}Simple tarif avec Exclusif Nuit{% endif %}</span>
    </div>
    {%endfor%}
    <h2>Remarque(s)</h2>
    <div class="field"><span class="input_full">{{params.Data.Remarques.remarkCpts.electricity}}</span></div>
</section>
{%endif%}
{% if vars.gaz == 1 %}
<section>
    <h2>COMPTEUR(S) GAZ</h2>
    {% for cpt in params.Data.InformationCompteurs.Compteur if cpt.energy=="gaz" %}
    <h2>Compteur gaz n°{{loop.index}}</h2>
    <div class="field"><span class="label">Puissance d’installation (kW):</span><span
            class="input">{{cpt.power}}</span>
    </div>
    {%endfor%}
    <h2>Remarque(s)</h2>
    <div class="field"><span class="input_full">{{params.Data.Remarques.remarkCpts.gaz}}</span></div>
</section>
{%endif%}
</body>
</html>