<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            color: #333;
            
        }

        .header-image {
            width: 100%;
            margin-bottom: 20px;
            background-color: #FFF;
        }

        h1 {
            color: #FF6600;
            font-size: 1.2em;
            margin: 20px 0;
            white-space: nowrap;
        }

        .main-table {
            width: 100%;
            border-collapse: collapse;
            display: table;
            
        }
        .main-table td {
            vertical-align: top;
        }

        .section-title{
            font-weight: 700;
            font-size: 14px,
            line-height: 14px;
            padding: 10px 0;
            
        }

        .data-table {
            width: 100%;
            margin: 10px 0;
        }

        .data-table td, p {
            padding: 3px 0;
            font-size: 11px;
            font-weight: 400;
            line-height: 14px;
            text-align: left;
        }

        .data-table td:first-child {
            width: 40%;
            white-space: nowrap;
        }

        .results-box {
            background-color: #FFF;
            border: 2px solid #FF6600;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .results-box p:not(.light-not-bold) {
            font-weight: bold;
        }

        .results-title {
            color: #FF6600;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
        }

        .footer a {
            color: #FF6600;
            text-decoration: none;
        }
    </style>
</head>
<body>
<div class="header-image">
    <svg fill="none" height="142" viewBox="0 0 539 142" width="539" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <rect fill="url(#pattern0_27_2713)" height="142" width="539"/>
        <defs>
            <pattern height="1" id="pattern0_27_2713" patternContentUnits="objectBoundingBox" width="1">
                <use transform="matrix(0.000808407 0 0 0.00307644 0 -0.424001)" xlink:href="#image0_27_2713"/>
            </pattern>
            <image height="463" id="image0_27_2713" width="1237"
                   xlink:href="data:image/png;base64,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"/>
        </defs>
    </svg>
</div>

<h1>Résultat de votre simulation pour une borne de recharge à domicile</h1>

<p>Nous vous remercions pour votre simulation. Vous trouverez ci-dessous le récapitulatif des informations encodées.</p>

<table class="main-table">
    <tr>
        <td valign="top">
            <div class="section-title">Simulation</div>
            <table class="data-table">
                <!--                <tr>-->
                <!--                    <td>Numéro de la simulation :</td>-->
                <!--                    <td>{{params.simulation.UUID}}</td>-->
                <!--                </tr>-->
                <tr>
                    <td>Date de la simulation :</td>
                    <td>{{params.simulation.Date}}</td>
                </tr>
                <tr>
                    <td>EAN :</td>
                    <td>{{params.simulation.Ean}}</td>
                </tr>
                <tr>
                    <td>Compteur :</td>
                    <td>{{params.simulation.Compteur}}</td>
                </tr>
            </table>

            <div class="section-title">Informations concernant le véhicule</div>
            <table class="data-table">
                <tr>
                    <td>Marque :</td>
                    <td>{{params.simulation.Vehicule.Marque}}</td>
                </tr>
                <tr>
                    <td>Modèle :</td>
                    <td>{{params.simulation.Vehicule.Modele}}</td>
                </tr>
                <tr>
                    <td>Version :</td>
                    <td>{{params.simulation.Vehicule.Version}}</td>
                </tr>
                <!--                <tr>-->
                <!--                    <td>Capacité :</td>-->
                <!--                    <td>{{params.simulation.Vehicule.Capacite}}</td>-->
                <!--                </tr>-->
                <tr>
                    <td>Capacité de la batterie :</td>
                    <td>{{params.simulation.Vehicule.CapaciteBatterie}} kWh</td>
                </tr>
                <tr>
                    <td>Type de véhicule :</td>
                    <td>{{params.simulation.Vehicule.Type}}</td>
                </tr>
                <tr>
                    <td>Autonomie :</td>
                    <td>{{params.simulation.Vehicule.Autonomie}} km</td>
                </tr>
                <tr>
                    <td>Consommation :</td>
                    <td>{{params.simulation.Vehicule.Consommation}} kWh/100km</td>
                </tr>
                <tr>
                    <td>Puissance de charge maximale :</td>
                    <td>{{params.simulation.Vehicule.PuissanceChargeMax}} kW (AC)</td>
                </tr>
            </table>

            <div class="section-title">Utilisation du véhicule</div>
            <table class="data-table">
                <tr>
                    <td>Distance type :</td>
                    <td>{{params.simulation.Vehicule.Utilisation.DistanceType}} km</td>
                </tr>
                <tr>
                    <td>Période de recharge :</td>
                    <td>{{params.simulation.Vehicule.Utilisation.PeriodeRecharge}}</td>
                </tr>
                <tr>
                    <td>Branchement moyen :</td>
                    <td>{{params.simulation.Vehicule.Utilisation.BranchementMoyen}} h</td>
                </tr>
                <tr>
                    <td>Parking professionnel avec recharge possible :</td>
                    <td>{%if params.simulation.Vehicule.Utilisation.ParkingElecPro%}Oui{%else%}Non{%endif%}</td>
                </tr>
                <tr>
                    <td>Distance du lieu de travail :</td>
                    <td>{%if params.simulation.Vehicule.Utilisation.DistancePro%}{{params.simulation.Vehicule.Utilisation.DistancePro}}km{%else%}Non renseigné{%endif%}</td>
                </tr>
            </table>

            <div class="section-title">Borne et équipements</div>
            <table class="data-table" style="width:150%">
                <tr>
                    <td>Modèle :</td>
                    <td>{{params.simulation.Borne.Modele}}</td>
                </tr>
                {%if params.simulation.Borne.ModifPuissance%}
                <tr>
                    <td>Modification de puissance :</td>
                    <td>Oui</td>
                </tr>
                <tr>
                    <td>Puissance de départ :</td>
                    <td>{{params.simulation.Borne.PuissanceDepart}} kVA - {{params.simulation.Borne.TypeRaccordementDepart}}</td>
                </tr>
                <tr>
                    <td>Puissance cible :</td>
                    <td>{{params.simulation.Borne.PuissanceCible}} kVA</td>
                </tr>
                {%endif%}
                {%if params.simulation.Borne.ModifTypeRaccordement%}
                <tr>
                    <td>Modification du type de raccordement :</td>
                    <td>oui</td>
                </tr>
                <tr>
                    <td>Type de raccordement de départ :</td>
                    <td>{{params.simulation.Borne.TypeRaccordementDepart}}</td>
                </tr>
                <tr>
                    <td>Type de raccordement cible:</td>
                    <td>{{params.simulation.Borne.TypeRaccordementCible}}</td>
                </tr>
                {%endif%}
                <tr>
                    <td>Coût (partie puissance uniquement) :</td>
                    <td>{%if params.simulation.Borne.Cout and params.simulation.Borne.Cout not in (None, 0, '0')%}{{ params.simulation.Borne.Cout }} €{%else%}Aucun{%endif%}</td>
                </tr>
            </table>
        </td>
        <td valign="top">
            <div class="results-box">
                <div class="results-title">Résultats</div>
                {% if params.simulation.Results == "Compatible" %}
                <p>Votre raccordement est compatible avec la solution de recharge sélectionnée.</p>
                {% elif params.simulation.Results == "compat_80" %}
                <p>Votre raccordement est compatible avec la solution de recharge sélectionnée.</p>
                <p>Vous pouvez conserver votre puissance actuelle en optimisant votre consommation.</p>
                {% elif params.simulation.Results == "not_enough_power" %}
                <p>Votre raccordement actuel n'est pas suffisamment puissant.</p>
                {% elif params.simulation.Results == "mono_to_trio_rec" %}
                <p>Votre raccordement actuel (monophasé) n'est pas compatible.</p>
                <p>Une conversion de raccordement en triphasé est à envisager</p>
                {% elif params.simulation.Results == "trio_no_neutral_to_with" %}
                <p>Votre raccordement actuel (triphasé sans neutre) n'est pas compatible.</p>
                <p>Une conversion de votre raccordement en triphasé avec neutre est à envisager.</p>
                {% elif params.simulation.Results == "mono_to_trio_mand" %}
                <p>Votre raccordement actuel (monophasé) n'est pas compatible.</p>
                <p>Une conversion de votre raccordement en triphasé est nécessaire pour alimenter la borne
                    sélectionnée.</p>
                {% elif params.simulation.Results == "trio_no_neutral_low_borne_high_power" %}
                <p>Votre raccordement actuel (triphasé sans neutre) n'est pas compatible.</p>
                <p>Une conversion de votre raccordement en triphasé avec neutre est à envisager</p>
                <p class="light-not-bold">Attention : La borne sélectionnée (7 kW) se raccorde généralement en
                    monophasé. Etant donné que votre raccordement est triphasé, il serait plus pertinent d’opter
                    plutôt pour une borne triphasée (11 ou 22kW) où la puissance d’alimentation est alors répartie sur
                    les trois phases (plutôt que sur une seule phase).</p>
                {% elif params.simulation.Results == "trio_no_neutral_low_borne_low_power" %}
                <p>Votre raccordement actuel (triphasé sans neutre) n'est pas compatible.</p>
                <p>Une conversion de votre raccordement en triphasé avec neutre est à envisager</p>
                <p class="light-not-bold">Attention : La borne sélectionnée (7 kW) se raccorde généralement en monophasé
                    et nécessite un courant de 32A pour produire sa puissance maximale. Cependant,
                    Votre raccordement triphasé actuel ne permet pas un tel courant sur chacune des phases. Nous vous
                    conseillons donc d’opter plutôt pour la borne triphasée 11 kW qui ne requiert que 16A par phase</p>
                {%endif%}
            </div>
        </td>
    </tr>
</table>

<div class="footer">
    Vous avez une question ?<br>
    Contactez-nous par téléphone au 04/220.12.11 (du lundi au vendredi de 8h à 18h) ou à l'adresse email <a href="mailto:<EMAIL>"><EMAIL></a><br>
    Ce simulateur constitue une simple indication dans votre choix. Nous vous conseillons de contacter votre électricien afin de déterminer l’installation électrique nécessaire
    pour être au plus proche de vos besoins
</div>
</body>
</html>
