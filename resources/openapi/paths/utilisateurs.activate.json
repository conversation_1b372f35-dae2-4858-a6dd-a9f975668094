{"post": {"summary": "WS04: Activer un user ghost ayant validé son mail", "parameters": [{"name": "Token", "in": "query", "description": "The token received from the activation mail", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["Firstname", "Lastname", "Phone", "PhoneFixe", "<PERSON><PERSON><PERSON>", "Password"], "properties": {"Firstname": {"type": "string"}, "Lastname": {"type": "string"}, "Phone": {"type": "string"}, "PhoneFixe": {"type": "string"}, "Password": {"type": "string"}, "Adresse": {"$ref": "#/components/schemas/Adresse"}}, "example": {"Password": "Welcome@2020", "Firstname": "Mini", "Lastname": "Mouse", "Phone": "0000", "Adresse": {"Rue": "mock street", "NumRue": "5", "Localite": "mock city", "Cdpostal": "4000"}}}}}}, "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "description": "Identifie l'utilisateur ayant été activé", "required": ["<PERSON><PERSON>", "Email"], "properties": {"Uid": {"type": "string"}, "Email": {"type": "string"}}}, "example": {"Uid": "Mini.Mouse.6yT3A", "Email": "<EMAIL>"}}}}, "401": {"description": "401 Unauthorized", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"example": {"Error": 401, "Message": "Unkown user `ghost_Fakeuser`"}}}}, "409": {"description": "409 Conflict", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"example": {"Error": 409, "Message": "This username is already taken"}}}}}}}