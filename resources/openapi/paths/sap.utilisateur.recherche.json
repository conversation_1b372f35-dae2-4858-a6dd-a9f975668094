{"get": {"summary": "WS121 : Recherche un user dans SAP", "security": [{"basicAuthorizer": []}], "parameters": [{"name": "BpNom", "in": "query", "required": true, "example": "BROCHARD", "schema": {"type": "string"}}, {"name": "BpPrenom", "in": "query", "required": true, "example": "MICHEL", "schema": {"type": "string"}}, {"name": "BpTelFixe", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "BpTelPortable", "in": "query", "required": false, "example": "0032477353475", "schema": {"type": "string"}}, {"name": "BpEmail", "in": "query", "required": false, "example": "<EMAIL>", "schema": {"type": "string"}}, {"name": "BpRue", "in": "query", "required": false, "example": "RUE DE LA SAPINIERE", "schema": {"type": "string"}}, {"name": "BpNumrue", "in": "query", "required": false, "example": "12", "schema": {"type": "string"}}, {"name": "BpCdpostal", "in": "query", "required": false, "example": 4400, "schema": {"type": "number"}}, {"name": "BpLocalite", "in": "query", "required": false, "example": "FLEMALLE HAUTE", "schema": {"type": "string"}}], "responses": {"200": {"description": "200 success", "content": {"application/json": {"schema": {"type": "object", "required": ["Data"], "properties": {"Data": {"type": "array", "items": {"type": "object", "required": ["BpNom", "BpPrenom", "BpTelFixe", "BpTelPortable", "BpEmail", "BpRue", "BpNumrue", "BpCdpostal", "BpLocalite"], "properties": {"BpNom": {"type": "string"}, "BpPrenom": {"type": "string"}, "BpTelFixe": {"type": "string"}, "BpTelPortable": {"type": "string"}, "BpEmail": {"type": "string"}, "BpRue": {"type": "string"}, "BpNumrue": {"type": "string"}, "BpCdpostal": {"type": "number"}, "BpLocalite": {"type": "string"}}}}}}, "example": {"Data": [{"Bp": "3100950026", "BpNom": "BROCHARD", "BpPrenom": "MICHEL", "BpTelFixe": null, "BpTelPortable": "0032477353475", "BpEmail": "<EMAIL>", "BpRue": "RUE DE LA SAPINIERE", "BpNumrue": "12", "BpCdpostal": "4400", "BpLocalite": "FLEMALLE HAUTE"}]}}}}}}}