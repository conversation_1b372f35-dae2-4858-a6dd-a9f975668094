get:
  summary: "WS198 : Sharepoint WS to get api listingMms"
  parameters:
    - name: Valise
      in: path
      description: "Valise type"
      required: True
      type: string
      example: 'BP'
  responses:
    '200':
      description: Liste des metadata
      content:
        application/json:
          example: [
              {
                "fieldName": "rsaDocumentFlux",
                "values": [
                  {
                    "id": "4c5510ae-f754-4a02-9b56-c2ca79bc7dc5",
                    "label": "AHV (Aide Hivernale)"
                  },
                  {
                    "id": "5e6fa4f0-7811-4813-820a-c3ef908c1376",
                    "label": "DRP"
                  },
                  {
                    "id": "f6926a74-c3da-4932-b5ef-e1f3db542792",
                    "label": "G22 – Relance GRD"
                  },
                  {
                    "id": "54df750d-70b3-4bf0-8bd9-8ff5321fd165",
                    "label": "PRC"
                  }
                ]
              },
            {
              "fieldName": "rsaDocumentSource",
              "values": [
                {
                  "id": "3adc6378-0616-4c1b-826d-5ea1b4f4afed",
                  "label": "Attestation"
                },
                {
                  "id": "9f1ce1a3-e3e6-4665-a728-89dbeffb8b26",
                  "label": "Mig. HGZ"
                }
              ]
              }
            ]
    '400 Type not supported':
      description: Happen if the value provided in the 'valise' parameter is not supported
      content:
        application/json:
          example:
            example: {
              "Message": "Type : {type} not supported",
              "error_code": "UNSUPPORTED_TYPED"
            }
    '400 Valise Missing':
      description: Missing body values.
      content:
        application/json:
          example:
            example: {
              "Message": "Missing valise",
              "error_code": "MISSING_VALISE"
            }
    '500':
      description: Happen if the response from our sharepoint API isn't 200
      content:
        application/json:
          example:
            example: {
              "Message": "An unknown error occurred on with the sharepoint api. Our development team has been notified.",
              "error_code": "UNKNOWN_ERROR_WITH_SHAREPOINT_API"
            }