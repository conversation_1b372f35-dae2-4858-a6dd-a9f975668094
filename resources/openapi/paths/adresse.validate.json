{"post": {"summary": "WS18 : Valider et standardiser une adresse", "requestBody": {"description": "Adresse à valider", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"Rue": {"type": "string"}, "Localite": {"type": "string"}, "Cdpostal": {"type": "string"}}}, "example": {"Rue": "Chem des Amphith", "Localite": "", "Cdpostal": "4000"}}}}, "parameters": [{"name": "<PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string", "example": "FR", "default": "FR"}}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["Liste"], "properties": {"Liste": {"type": "array", "items": {"type": "object", "required": ["Cdpostal", "IdLocalite", "Localite", "IdRue", "Rue"], "properties": {"Cdpostal": {"type": "integer"}, "IdLocalite": {"type": "string"}, "Localite": {"type": "string"}, "IdRue": {"type": "string"}, "Rue": {"type": "string"}}}}}}, "example": {"Liste": [{"Cdpostal": 4000, "IdLocalite": "000000000012", "Localite": "ANGLEUR", "IdRue": "000000005155", "Rue": "CHEMIN DES AMPHITHÉÂTRES"}]}}}}, "400": {"description": "400 Missing Parameter", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"example": {"message": "Missing required request parameters: [Langue]"}}}}, "500": {"description": "500 Internal Server Error", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "example": {"Error": 500, "Message": "Pas inforamtion disponible !"}}}}}}}