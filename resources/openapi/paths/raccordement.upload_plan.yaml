post:
  summary: "WS127 : Lance l'upload des plans réseau lié à un raccordement. Les plans sont uploadés de FME vers la valse SharePoint du dossier raccordement."
  parameters:
    - name: "Authorization"
      in: "header"
      description: "Token FME"
      required: true
      schema:
        type: string
        example: "fmetoken token=0b4c59b39ad59ae416d94aff132eb47a4532e558"
  requestBody:
    description: Donnée à envoyer vers le WS FME
    content:
      application/json:
        schema:
          type: object
          properties:
            Langue:
              type: string
              example: "F"
            NumDossier:
              type: string
              example: "000002001252"
            Rue:
              type: string
              example: "Rue de Meuse"
            IdRue:
              type: string
              example: "000000017590"
            IdRadRue:
              type: string
              example: "147963"
            NumRue:
              type: string
              example: "16"
            CdPostal:
              type: string
              example: "4400"
            Localite:
              type: string
              example: "Flémalle-Grande"
            IdLocalite:
              type: string
              example: "000000000121"
            IdRadLocalite:
              type: string
              example: "112399"
            Pays:
              type: string
              example: "BE"
            Valise:
              type: string
              example: "https://agora-acc.resa.intra/Sites/sap2/Dossier client/SAPFolder17/Dossier Client-2001252-DTA-310"
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: number