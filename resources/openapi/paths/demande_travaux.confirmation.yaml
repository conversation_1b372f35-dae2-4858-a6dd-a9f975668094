get:
  summary: WS109 Réception information devis
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
    - name: NumDossier
      description: Le numéro du dossier
      in: query
      required: false
      schema:
        type: string
        example: '2072557'
    - name: DateAccord
      description: Date d'obtention de l'accord
      in: query
      required: false
      schema:
        type: string
        example: '18/06/2021'
    - name: TypeAction
      example: PRET
      schema:
        type: string
        enum:
          - PRET
          - ACCORD
          - CONFORMITEOK
          - UPLOAD_DOC
          - DEVIS_PAYE
      description: "Type d'action"
      in: query
      required: false
    - name: TypeDoc
      description: "Type de document uploadé, uniquement si TypeAction=UPLOAD_DOC"
      in: query
      required: false
      schema:
        type: string
        example: "61_1"
  responses:
    "200":
      description: 200 OK
      content:
        application/json:
          schema:
            type: object
