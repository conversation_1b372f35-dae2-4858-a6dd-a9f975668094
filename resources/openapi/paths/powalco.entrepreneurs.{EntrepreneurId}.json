{"get": {"summary": "Obtenir les entrepreneurs pour powalco", "security": [{"basicAuthorizer": []}], "parameters": [{"name": "EntrepreneurId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "200 success", "content": {"application/json": {"schema": {"type": "object", "required": ["Id", "Firstname", "Lastname", "Status", "Email", "CreationDate", "LastActivityDate", "DeactivationDate"], "properties": {"Id": {"type": "string"}, "Firstname": {"type": "string"}, "Lastname": {"type": "string"}, "Status": {"type": "string", "enum": ["en attente", "actif", "inactif"]}, "Email": {"type": "string", "format": "email"}, "CreationDate": {"type": "integer", "format": "timestamp"}, "LastActivityDate": {"type": "integer", "nullable": true, "format": "timestamp"}, "DeactivationDate": {"type": "integer", "nullable": true, "format": "timestamp"}}}}}}}}}