get:
  summary: 'WS77 : Uploader un fichier lié à une demande de raccordement'
  description: |
    L'upload du fichier se fait sur l'url S3 pré-signée renvoyée dans la réponse, par un PUT avec un header Content-Type:*
    L'url d'upload n'est valide qu'un certain temps (1h).
    Le document uploader peut être en pdf, png, ou jpg.  
    Dans le cas d'un png ou jpg, il sera automatiquement transformé en pdf.
  security:
  - tokenAuthorizer: []
    ghostAuthorizer: []
  parameters:
  - name: DocumentName
    description: Nom du document à uploader
    in: query
    required: true
    schema:
      type: string
      example: "contrat_de_raccordement.pdf"
  - name: IdDossier
    description: Id du dossier pour le quel le document doit être uploader
    in: query
    required: true
    schema:
      type: string
      example: "123456"
  - name: Submit
    description: si false, le document ne sera pas envoyé a sharepoint avant d'avoir appelé le WS109
    in: query
    required: false
    schema:
      default: true
      type: boolean
      example: false
  responses:
    '200':
      description: OK
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            properties: 
              PutUrl:
                type: string
                description: URL S3 d'upload du fichier
                example: "https://my-resa-api-david-uploads.s3.amazonaws.com/Sherrie.D/12345678/4399da12-b003-41af-9b8f-dd48d10f2129.jpeg?AWSAccessKeyId=AKIAS7HADRZWGOC5WX4C&Signature=soXQwmLBFlOu3XzXJHgxBcLYfhY%3D&content-type=%2A&Expires=1637945854"
              DocumentId: 
                type: string
                example: "7b411104-e9a1-423e-8592-1c7932a1b7f6"
                description: UUID généré pour le document
              DossierId: 
                type: string
                example: "12345678"
                description: Id du dossier
              FileName: 
                type: string
                example: "contratDeRaccordement.pdf"
              FileFormat:
                type: string
                example: "pdf"
              OriginalFileFormat:
                type: string
                example: "jpeg"
              UrlCreationDate:
                type: string
                example: "2021-07-08T16:18:50.258056"
              S3Backup:
                type: boolean
                example: false
