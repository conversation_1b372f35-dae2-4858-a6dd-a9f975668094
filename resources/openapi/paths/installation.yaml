get:
  summary: "WS204 : Obtenir le total de puissance sur une installation"
  description: |
    Ce WS permet d'obtenir le total de puissance électrique sur une installation.<br>
    Pour ce faire, il va utilise l'`Ean` et le `MeterId` fournis afin de retrouver l'adresse de l'installation ainsi que le BP lié à l'Ean.<br>
    <PERSON><PERSON><PERSON>, il va récupérer l'ensemble des compteurs existant sur cette adresse en filtrant sur le BP lié pour éviter de comptabiliser des compteurs voisins appartenant à d'autres clients.
  parameters:
    - name: Ean
      in: query
      required: true
      description: Un des EAN lié au point de raccordement
      schema:
        type: string
        example: '541449020710302969'
    - name: MeterId
      in: query
      required: true
      description: Numéro du compteur lié à l'EAN fournis
      schema:
        type: string
        example: '34175932'
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: object
            properties:
              TotalPowerElec:
                type:
                  - number
                  - null
                description: |
                  Puissance totale d'électricité sur l'installation, en kVA<br>
                  Si la valeur est nulle, c'est que l'Ean n'a pas été trouvé dans la base de données, ou que le couple Ean/MeterId est incorrect.
                example: "27.721"
    '400':
      description: 400 bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: number
                example: 400
              Message:
                type: string
                example: "Le paramètre Ean est requis"
          examples:
            missing_ean:
              value:
                Error: 400
                Message: "Le paramètre Ean est requis"
            missing_meter_id:
              value:
                Error: 400
                Message: "Le paramètre MeterId est requis"
