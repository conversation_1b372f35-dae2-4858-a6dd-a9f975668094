{"get": {"summary": "Obtenir les entrepreneurs pour powalco", "security": [{"tokenAuthorizer": []}], "parameters": [{"name": "PageSize", "description": "set the number of items per page", "in": "query", "schema": {"type": "integer"}, "example": 20}, {"name": "Page", "description": "precise the page to fetch (pagination)", "in": "query", "schema": {"type": "integer"}, "example": 0}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["Data"], "properties": {"Data": {"type": "array", "items": {"type": "object", "required": ["Email", "Id", "FournisseurId"], "properties": {"Email": {"type": "string"}, "Id": {"type": "string"}, "FournisseurId": {"type": "string"}, "Firstname": {"type": ["string", "null"]}, "Lastname": {"type": ["string", "null"]}, "Status": {"type": "string", "enum": ["actif", "inactif", "en attente", "sans compte"]}, "CreationDate": {"type": ["integer", "null"], "format": "timestamp"}, "LastActivityDate": {"type": ["integer", "null"], "format": "timestamp"}, "DeactivationDate": {"type": ["integer", "null"], "format": "timestamp"}}}}}}, "example": {"Data": [{"Id": "2IOPP0292", "FournisseurId": "0003000178", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "inactif", "CreationDate": "23/01/2020", "LastActivityDate": "20/09/2020", "DeactivationDate": "18/10/2020"}, {"Id": "UIOPP12312", "FournisseurId": "0003000179", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "actif", "CreationDate": "02/05/2020", "LastActivityDate": "03/11/2020", "DeactivationDate": null}, {"Id": "8SNJKC21802", "FournisseurId": "0003000174", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "en attente", "CreationDate": "12/03/2020", "LastActivityDate": null, "DeactivationDate": null}, {"Id": "UIOPHJDNFLP12", "FournisseurId": "0003000173", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "sans compte", "CreationDate": null, "LastActivityDate": null, "DeactivationDate": null}], "Pagination": {"PageSize": 20, "CurrentPage": 0, "NbPage": 0}}}}}}}}