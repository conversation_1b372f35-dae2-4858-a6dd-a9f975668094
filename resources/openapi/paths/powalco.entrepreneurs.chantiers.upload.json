{"get": {"summary": "liste les upload de fichier pour chantier powalco", "description": "caller must have entrepreneur permission", "security": [{"tokenAuthorizer": []}], "responses": {"200": {"description": "200 success", "content": {"application/json": {"schema": {"type": "object", "properties": {"Files": {"type": "array", "items": {"type": "object", "properties": {"EntrepreneurId": {"type": "string"}, "Resource": {"type": "string", "description": "act as an ID"}, "Chantier": {"type": "string"}, "Type": {"type": "string", "enum": ["EDLE", "EDLS"]}, "UploadedAt": {"type": "integer", "format": "timestamp"}, "URL": {"type": "string"}}}}}, "required": ["Files"]}}}}}}, "post": {"summary": "Crée un upload de fichier pour chantier powalco", "description": "caller must have entrepreneur permission", "security": [{"tokenAuthorizer": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"FileName": {"type": "string"}, "Chantier": {"type": "string"}, "Type": {"type": "string", "enum": ["EDLE", "EDLS"]}, "PowalcoId": {"type": "string"}}}}}}, "responses": {"200": {"description": "200 success", "content": {"application/json": {"schema": {"type": "object", "properties": {"Item": {"type": "object", "properties": {"EntrepreneurId": {"type": "string"}, "Chantier": {"type": "string"}, "Type": {"type": "string", "enum": ["EDLE", "EDLS"]}, "UploadedAt": {"type": "integer", "format": "timestamp"}}}, "Upload": {"type": "object", "properties": {"url": {"type": "string"}, "fields": {"type": "object"}, "ExpirationTimestamp": {"type": "integer", "format": "timestamp"}}}}, "required": ["<PERSON><PERSON>", "Upload"]}}}}}}, "patch": {"summary": "modifie un upload de fichier pour chantier powalco", "description": "caller must have entrepreneur permission", "security": [{"tokenAuthorizer": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"Resource": {"type": "string"}, "FileName": {"type": "string"}}}}}}, "responses": {"200": {"description": "200 success", "content": {"application/json": {"schema": {"type": "object", "properties": {"Upload": {"type": "object", "properties": {"url": {"type": "string"}, "fields": {"type": "object"}, "ExpirationTimestamp": {"type": "integer", "format": "timestamp"}}}}, "required": ["Upload"]}}}}}}, "delete": {"summary": "supprime un upload de fichier pour chantier powalco", "description": "caller must have entrepreneur permission", "security": [{"tokenAuthorizer": []}], "responses": {"200": {"description": "200 success", "content": {"application/json": {"schema": {"type": "object"}}}}}}}