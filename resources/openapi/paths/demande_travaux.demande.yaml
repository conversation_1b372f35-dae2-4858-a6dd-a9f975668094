post:
  summary: 'WS35 (Async) : <PERSON><PERSON>er un dossier de raccordement'
  description: |
    Meant to be used with /asynchrone endpoint. But working as fine if you provide a unique "MessageId".
    Result and state can be fetched on GET /asynchrone with the proper "MessageId".
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
    - name: MessageId
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
  requestBody:
    description: Il faut fournir les données de contact personnelles, l'objet de raccordement,
      et les caratcéristiques du raccordement
    content:
      application/json:
        schema:
          type: object
          properties:
            Partenaire:
              type: object
              description: Structure contenant les informations du donneur d’ordre
              required:
                - Nom
                - Prenom
                - Langue
                - Email
              properties:
                Nom:
                  type: string
                  example: CHESLET
                Prenom:
                  type: string
                  example: <PERSON>:
                  type: string
                  example: FR
                Gsm:
                  type: string
                  example: '049198232846'
                Tel:
                  type: string
                Email:
                  type: string
                  example: <EMAIL>
                IdPartenaire:
                  type: string
                Adresse:
                  type: object
                  description: Adresse du donneur d’ordre
                  properties:
                    Rue:
                      type: string
                      example: avenue des sillons
                    IdRue:
                      type: string
                      description: Code rue
                    IdRadRue:
                      type: string
                      description: Code rue RAD
                    NumRue:
                      type: string
                      description: N° dans la rue
                      example: 300
                    NumCmpt:
                      type: string
                      description: Complément
                    CdPostal:
                      type: string
                      example: 4100
                    Localite:
                      type: string
                      example: Boncelles
                    IdLocalite:
                      type: string
                      description: Code localoté
                    IdRadLocalite:
                      type: string
                    ParcelleSpw:
                      type: string
                      description: N° de parcelle SPW
                    Pays:
                      type: string
                      description: Nom du pays
            Adresse:
              type: object
              description: Structure contenant les données d’adresse d’exécution des travaux
              properties:
                Rue:
                  type: string
                  example: ruelle de la barriere
                NumRue:
                  type: string
                  description: N° dans la rue
                  example: '9'
                CdPostal:
                  type: string
                  example: '4342'
                Localite:
                  type: string
                  example: Hognoul
                IdRue:
                  type: string
                  description: Code rue
                IdRadRue:
                  type: string
                  description: Code rue RAD
                NumCmpt:
                  type: string
                  description: Complément
                IdLocalite:
                  type: string
                  description: Code localité
                IdRadLocalite:
                  type: string
                ParcelleSpw:
                  type: string
                  description: N° de parcelle SPW
                Pays:
                  type: string
                  description: Nom du pays
            Demande:
              type: object
              description: Table reprenant les demandes de travail
              required:
                - SectActivite
                - TypeTravail
                - NbCompteurs
              properties:
                SectActivite:
                  type: string
                  example: '01'
                  description: Fluide (01 pour Electricité et 02 pour le Gaz)
                Ean:
                  description: Code EAN (obligatoire pour R02)
                NumCpt:
                  description: Numéro de compteur (obligatoire pour R02)
                TypeTravail:
                  type: string
                  example: R01
                  description: R01 (nouveau raccordement/raccordement spécifique) ou R02 (modification raccordement)
                NbCompteurs:
                  type: string
                  example: '1'
                  description: Nombre de compteurs
                Details:
                  type: array
                  description: Liste de détails, voir description pour les valeurs possibles
                  items:
                    CodeDetail:
                      type: string
                      description: |
                        Liste des code possible
                        | code | description |
                        |-|-|
                        | CD_TYPE | Type de travail |
                        | PUISSANCE | Puissance souhaitée |
                        | PROD_SUP_10KVA |  |
                        | TYPE_RACC | Type de raccordement (Pour le Gaz) |
                        | TYPE_IMMEUBLE | Type d’immeuble |
                        | TYPE_UTILISATION | Type d’utilisation |
                        | LOTISSEMENT | Lotissement, habité groupé ou assimilé |
                        | USAGE | Texte avec utilisation et description du raccordement provisoire |
                        | REMARQUE | Texte avec les remarques éventuelles |
                      example: CD_TYPE
                    Valeur:
                      type: string
                      description: |
                        Liste des valeurs possible pour chaque code
                        | code | valeur possible | description |
                        |-|-|-|
                        | CD_TYPE |||
                        || NOUV_RACC | nouveau raccordement |
                        || MODI_INSTAL | Modifier l'installation (puissance, type de compteur, ajout compteur) |
                        || MODI_TARIF | Changer la tarification (ST<>DT) |
                        || MODI_CABLE | Remplacement du câble de liaison entre le compteur et le tableau divisionnaire |
                        || MODI_SMART | Remplacer compteur actuel par compteur communicant |
                        || DEPLA_BRAN | Déplacer le compteur et/ou le branchement existant |
                        || ENLV_CPT | Enlever le(s) compteur(s) et/ou le branchement existant |
                        || NOUV_FORF | Raccordements forfaitaires |
                        || NOUV_RTEC | Raccordements techniques |
                        || NOUV_ARMO | Armoires spécifiques |
                        || NOUV_CCOM | Centres commerciaux |
                        || CHAN_PROVI | Provisoire chantier |
                        || SUPP_BRAN | Suppression du branchement et du ou des compteurs |
                        | PUISSANCE | <nombre> | Puissance souhaitée |
                        | PROD_SUP_10KVA | Y/N ||
                        | TYPE_RACC |||
                        || STANDARD | Standard (<= 70 kW) gratuit |
                        || NON_STANDARD | Non standard (> 70 kW) prix sur devis |
                        | TYPE_IMMEUBLE |||
                        || MAISON | Maison unifamiliale |
                        || IMMEUBLE | Immeuble à appartements |
                        | TYPE_UTILISATION |||
                        || RESI | Résidentielle |
                        || PRO | Professionnelle |
                        || MIXTE | Mixte |
                        | LOTISSEMENT | Y/N ||
                        | USAGE | <Texte libre> | utilisation et description du raccordement provisoire |
                        | REMARQUE | <Texte libre> | remarques éventuelles |
                      example: NOUV_RACC
            Complement:
              type: array
              description: Liste de détails, voir description pour les valeurs possibles
              items:
                Libelle:
                  type: string
                  description: |
                    Code information complémentaire
                    Liste des code possible
                    | code | description |
                    |-|-|
                    | DATESOUHAITEETRAV | Date souhaitée des travaux en YYYYMM |
                    | GDPR | Date acceptation GDPR en YYYYMMJJ |
                    | COORDGPSX | Coordonnée GPS X |
                    | COORDGPSY | Coordonnée GPS Y |
                    | TVA | TVA dans valeur et O dans titre |
                    | TYPE_TVA ||
                    | TYPE_LOGEMENT ||
                    | REMARQUE | Texte avec les remarques éventuelles (partie fin de demande) |
                  example: TYPE_TVA
                Valeur:
                  type: string
                  description: |
                    Valeur information complémentaire
                    Liste des valeurs possible pour chaque code
                    | code | valeur possible | description |
                    |-|-|-|
                    | DATESOUHAITEETRAV | YYYYMM | Date souhaitée des travaux |
                    | GDPR | YYYYMMJJ | Date acceptation GDPR |
                    | COORDGPSX | <nombre> | Coordonnée GPS X |
                    | COORDGPSY | <nombre> | Coordonnée GPS Y |
                    | TVA | | TVA dans valeur et O dans titre |
                    | TYPE_TVA |||
                    || PROP | Propriétaire, locataire, usufruitier, gestionnaire du logement |
                    || COMMU | Commune, SPW, Province , CPAS |
                    || SOCIETE | Société régionale de logement et société agréée pour le logement social non assujetties |
                    || INTERN | Maison de repos, internat scolaire, home de protection de la jeunesse |
                    || OTAN | OTAN, ambassade, ESA |
                    || BATI | Bâtiment scolaire |
                    || AUTO | TVA en autoliquidation |
                    | TYPE_LOGEMENT |||
                    || PRIVE_MOINS | Logement privé de moins de 10 ans |
                    || DESTRUCTION | Logement destiné à une destruction définitive |
                    || AUCUNE | Aucune de ces propositions |
                    || PRIVE_PLUS | Logement privé de plus de 10 ans |
                    || RECONSTRUIT | Un immeuble qui après démolition est reconstruit lors d’une même opération. |
                    || HANDICAP | Un logement privé/un établissement d’hébergement adapté pour handicapés. |
                    | REMARQUE | <Texte libre> | remarques éventuelles |
                  example: PROP
                Titre:
                  type: string
                  description: "[O | N] permettra de structurer le document récapitulatif"
                  example: PROP
        example:
          Partenaire:
            IdPartenaire: '4100427793'
            Nom: Test
            Prenom: Test1
            Langue: FR
            Gsm: '0412345678'
            Email: <EMAIL>
            Adresse:
              Rue: avenue des sillons
              NumRue: '300'
              CdPostal: '4100'
              Localite: Boncelles
          Adresse:
            Rue: avenue des sillons
            NumRue: '302'
            CdPostal: '4100'
            Localite: Boncelles
          Demande:
            SectActivite: '01'
            NumCpt: '1'
            TypeTravail: R01
            Details:
              - CodeDetail: CD_TYPE
                Valeur: NOUV_RACC
              - CodeDetail: PUISSANCE
                Valeur: '20'
              - CodeDetail: PROD_INF_10KVA
                Valeur: N
            NbCompteurs: '1'
  responses:
    '200':
      description: 200 OK
      content:
        application/json: { }
