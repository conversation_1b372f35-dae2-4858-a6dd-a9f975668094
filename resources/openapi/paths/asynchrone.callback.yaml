post:
  summary: "WS115 : Enregistrer le résultat d'une requête asynchrone"
  parameters:
  - name: MessageId
    description: Id du process asynchrone
    in: query
    required: true
    schema:
      type: string
      example: "ABCDE123"
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
          - Response
          - StatusCode
          properties:
            Response:
              type: object
            StatusCode:
              type: number
              example: 200