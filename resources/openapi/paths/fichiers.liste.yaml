get:
  summary: "WS78 : Obtenir les fichiers liés à une demande de raccordement"
  security:
  - tokenAuthorizer: []
    ghostAuthorizer: []
  parameters:
  - name: IdDossier
    description: |
      Id du dossier pour le quel lister les documents.  
      Si il est ommi, la liste de tout les dossiers et leurs documents est retournée sous forme de dictionnaire.
    in: query
    required: false
    schema:
      type: string
      example: "123456"
  responses:
    '404':
      description: Not Found
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            example:
              Error: 404
              Message: "Le dossier '123456' n'a aucun document uploader pour cet utilisateur."
    '200':
      description: OK
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                FileFormat:
                  type: string
                  example: "pdf"
                DocumentId:
                  type: string
                  description: UUID du document
                  example: "7b411104-e9a1-423e-8592-1c7932a1b7f6"
                DossierId: 
                  type: string
                  example: "12345678"
                  description: Id du dossier
                FileName:
                  type: string
                  example: "contratDeRaccordement.pdf"
                OriginalFileFormat:
                  type: string
                  example: "jpeg"
                SendFileTimestamp:
                  type: string
                  description: Moment où le document à été envoyé
                  example: "2021-07-08T16:18:50.258056"
                S3Backup:
                  type: boolean
                  example: true
                S3BackupTimestamp:
                  type: string
                  description: Moment où le document à été envoyé sur le backup S3
                  example: "2021-07-08T16:18:53.378837"
                Sharepoint:
                  type: boolean
                  example: false
                SharepointTimestamp:
                  type: string
                  description: Moment où le document à été envoyé sur le sharepoint
                  example: "2021-07-08T16:18:50.258056"