{"post": {"summary": "WS73 : <PERSON><PERSON><PERSON> un user dans SAP", "security": [{"basicAuthorizer": []}], "requestBody": {"content": {"application/json": {"schema": {}, "example": {"Langue": "FR", "BpNom": "<PERSON>", "BpPrenom": "Mouse", "BpCodePays": "BE", "BpRue": "Unknow", "BpNumrue": "1", "BpCdpostal": "4000", "BpLocalite": "Liege", "BpEmail": "<EMAIL>", "BpTelFixe": 999, "BpTelPortable": 999, "CodePrefe": {"PppPhone": null, "PppMail": "<EMAIL>", "CondGen": null}, "BpPreferences": [{"CodePrefe": "com_panne_sms", "Prefe": false}], "NumCmpt": "<EMAIL>", "IdRadRue": null, "IdRadLocalite": null, "IdAdresse": null}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {}, "example": {"Bp": 3100000011, "IdAdress": "0019010213"}}}}}}}