get:
  summary: "WS220 : Récupération des données de consommation smart pour une commune"
  description: >
    Retourne les données de consommation smart en fonction des paramètres fournis.
    
    - **HOURLY** : Retourne les données avec une granularité horaire pour une journée spécifique. Les dates doivent être le même jour.
    
    - **DAILY** : Retourne les données journalières pour une plage de dates. Les dates doivent avoir au minimum un jour d'écart.
    
    - **MONTHLY** : Retourne les données agrégées par mois pour une plage de dates en se basant sur le premier de chaque mois. Les dates doivent avoir au minimum un mois d'écart. 
    Best use : Prendre du 1er du mois au 1er d'un autre mois.
    
    - **Formats pris en charge :**
      - JSON : Par défaut, les données sont renvoyées en JSON.
      - CSV : Si `Content-Type: text/csv` est spécifié dans les headers, les données seront renvoyées au format CSV.
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: "Authorization"
      in: "header"
      required: true
      schema:
        type: "string"
    - name: "Type"
      in: "query"
      required: false
      schema:
        type: "string"
        enum: [ "HOURLY", "DAILY", "MONTHLY" ]
        default: "MONTHLY"
        description: >
          Type de granularité des données. Les options possibles sont :
          - `HOURLY` : Granularité horaire
          - `DAILY` : Granularité journalière
          - `MONTHLY` : Données agrégées par mois
    - name: "StartDate"
      in: "query"
      required: true
      schema:
        type: "string"
        format: "date-time"
        description: "Date de début au format ISO 8601."
    - name: "EndDate"
      in: "query"
      required: true
      schema:
        type: "string"
        format: "date-time"
        description: "Date de fin au format ISO 8601."
    - name: "Ean"
      in: "query"
      required: true
      schema:
        type: "string"
        description: "Identifiant EAN de la borne de consommation."
    - name: "MeterId"
      in: "query"
      required: true
      schema:
        type: "string"
        description: "Numéro de compteur."
  responses:
    '200':
      description: "Données de consommation récupérées avec succès."
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                MeterId:
                  type: string
                  example: "1SAG1105183360"
                  description: "Identifiant unique du compteur."
                MeasureDateTime:
                  type: string
                  format: "date-time"
                  example: "2024-09-29T00:15:00+02:00"
                  description: "Date et heure de la mesure."
                ConsumptionStartFrame:
                  type: string
                  format: "date-time"
                  example: "2024-09-29T00:00:00+02:00"
                  description: "Date et heure de la mesure précédente, utilisée pour calculer la consommation entre les deux mesures."
                MeasureValue:
                  type: number
                  example: 2046.78
                  description: "Valeur mesurée par le compteur."
                ReadingTypeId:
                  type: string
                  example: "2.8.2"
                  description: "Identifiant du type de lecture (e.g., consommation, production)."
                MeasureState:
                  type: string
                  example: "Valide"
                  description: "État de la mesure (e.g., 'Valide' ou 'Invalide')."
                Ean:
                  type: string
                  example: "541456700002609883"
                  description: "Numéro EAN associé au compteur."
                ReadingFrequency:
                  type: string
                  example: "1D"
                  description: "Fréquence de lecture des données (e.g., '1D' pour quotidienne)."
                StandardizationTimestamp:
                  type: string
                  format: "date-time"
                  example: "2024-09-28T10:15:00+00:00"
                  description: "Horodatage de standardisation des données."
                MeasureUnit:
                  type: string
                  example: "KWH"
                  description: "Unité de mesure (e.g., 'KWH')."
                Consumption:
                  type: number
                  nullable: true
                  example: 15.9
                  description: "Consommation calculée en kWh, si disponible."
        text/csv:
          schema:
            type: string
            example: |
              meterid;measuredatetime;measurevalue;readingtypeid;measurestate;ean;readingfrequency;standardizationtimestamp;measureunit;consumption
              1SAG1105183360;2024-09-29T00:00:00+02:00;2046.78;2.8.2;valide;541456700002609883;1D;2024-09-28T10:15:00+00:00;KWH;15.9
    '400':
      description: "Erreur dans les paramètres de la requête."
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "StartDate must be earlier than EndDate"
              ErrorCode:
                type: string
                example: "INVALID_DATE_RANGE"
              Error:
                type: number
                example: 400
    '400 TYPE_NOT_RECOGNIZED':
      description: "Le paramètre Type est invalide."
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "Type params should be in ['HOURLY', 'DAILY', 'MONTHLY']"
              ErrorCode:
                type: string
                example: "TYPE_NOT_RECOGNIZED"
              Error:
                type: number
                example: 400
    '400 DATE_NOT_ISO8601':
      description: "Le format de la date est incorrect."
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "Date must be in ISO 8601 format"
              ErrorCode:
                type: string
                example: "DATE_NOT_ISO8601"
              Error:
                type: number
                example: 400
    '400 HOURLY_INVALID_DATE_RANGE':
      description: "Pour le type HOURLY, StartDate et EndDate doivent être le même jour."
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "For HOURLY type, StartDate and EndDate must be the same day"
              ErrorCode:
                type: string
                example: "HOURLY_INVALID_DATE_RANGE"
              Error:
                type: number
                example: 400
    '400 DAILY_INVALID_DATE_RANGE':
      description: "Pour le type DAILY, il doit y avoir au moins un jour d'écart entre StartDate et EndDate."
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "For DAILY type, there must be at least 1 day difference between StartDate and EndDate"
              ErrorCode:
                type: string
                example: "DAILY_INVALID_DATE_RANGE"
              Error:
                type: number
                example: 400
    '400 CONTRACT_HAS_NO_ACCESS':
      description: "Le contrat de l'utilisateur n'autorise pas l'accès aux dates demandées."
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "The user's contract has no access to the asked date"
              ErrorCode:
                type: string
                example: "CONTRACT_HAS_NO_ACCESS"
              Error:
                type: number
                example: 400
    '403':
      description: "Accès interdit."
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "This EAN doesn't belong to the given user."
              ErrorCode:
                type: string
                example: "INVALID_EAN_FOR_USER"
              Error:
                type: number
                example: 403
