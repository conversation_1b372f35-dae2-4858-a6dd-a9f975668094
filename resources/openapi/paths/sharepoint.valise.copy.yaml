post:
  summary: "WS182 : Sharepoint ManageDocument.CopyDocument"
  parameters:
    - name: ValiseUid
      in: body
      description: "Transfert de fichier d'une valise a l'autre"
      required: True
      default: false
      type: application/json
      example: '
      {
        "SourceSiteUrl" : "",
        "DestinationSiteUrl" : "",
        "SourceServerRelativeUrl" : "",
        "DestinationServerRelativeUrl" : ""
    }
      '
  responses:
    '200':
      description: Confirmation de copie
    '400 No Valise':
      description: Missing Valise.
      content:
        application/json:
          example:
            example: {
              "Message": "Valise doesn't exist",
              "error_code": "NO_SUCH_NAME_FOR_VALISEID"
            }
    '400 Body Missing':
      description: Missing body values.
      content:
        application/json:
          example:
            example: {
              "Message": "Missing body part in body: <Missing body parts>",
              "error_code": "MISSING_BODY_PART"
            }