get:
  summary: "WS177 : Sharepoint Search Valise. Return toute les infos en Json de la valise spécifié. Faire attention a la syntax de l'UID de la valise"
  parameters:
    - name: ValiseUid
      in: path
      description: "Uid de la valise dont on veut les infos"
      required: True
      default: false
      type: string
      example: Client-*********-QTA-123
  responses:
    '200':
      description: Informations détaillées de la valise au format JSON.
      content:
        application/json:
          example: '
    s:Envelope": {
        "@xmlns:s": "http://schemas.xmlsoap.org/soap/envelope/",
        "s:Body": {
            "SearchResponse": {
                "@xmlns": "http://tempuri.org/",
                "SearchResult": {
                    "@xmlns:a": "http://schemas.datacontract.org/2004/07/SearchValise",
                    "@xmlns:i": "http://www.w3.org/2001/XMLSchema-instance",
                    "a:Comment": "Valisée trouvée",
                    "a:Valises": {
                        "a:Datas": {
                            "a:MetaDatas": [
                                {
                                    "a:TheId": "ContentTypeId",
                                    "a:TheValue": "0x0120D52000E894DA1F47FB9A4A8B494A8E0336694201010026145497B179C94781BE083FA2218244"
                                },
                                {
                                    "a:TheId": "FileLeafRef",
                                    "a:TheValue": "Dossier Client-2108064-QTA-300"
                                },
                                {
                                    "a:TheId": "Modified_x0020_By",
                                    "a:TheValue": "i:0#.w|resa\\svc.spaa.valises"
                                },
                                {
                                    "a:TheId": "HTML_x0020_File_x0020_Type",
                                    "a:TheValue": "Sharepoint.DocumentSet"
                                }
                            ]
                        },
                        "a:Documents": {
                            "a:Document": {
                                "a:Datas": {
                                    "a:MetaDatas": [
                                        {
                                            "a:TheId": "ServerRedirectedEmbedUri",
                                            "a:TheValue": null
                                        },
                                        {
                                            "a:TheId": "ServerRedirectedEmbedUrl",
                                            "a:TheValue": null
                                        },
                                        {
                                            "a:TheId": "ContentTypeId",
                                            "a:TheValue": "0x010100A8A6CE0A6A7BF54DA48535311EDCACC001004A69F704906BE346838B40AFA98C89DA"
                                        }
                                    ]
                                },
                                "a:Nom": "a3f35d48-76dd-4481-8439-89bee1202335.csv",
                                "a:Url": "https://agora-acc.resa.intra/Sites/sap2/Dossier client/SAPFolder18/Dossier Client-2108064-QTA-300/a3f35d48-76dd-4481-8439-89bee1202335.csv"
                            }
                        },
                        "a:Uid": "Dossier Client-2108064-QTA-300",
                        "a:Url": "https://agora-acc.resa.intra/Sites/sap2/Dossier client/SAPFolder18"
                    }
                }
            }
        }
    }
}
'
    '400':
      description: Valise uid n'existe pas.
      content:
        application/json:
          example: {
            "Message": "Valise doesn't exist",
            "error_code": "NO_SUCH_NAME_FOR_VALISEID"
          }
post:
  summary: "WS178 : Sharepoint Create Valise. Crée une valise si elle n'existe pas"
  parameters:
    - name: ValiseUid
      in: path
      description: "Uid de la valise dont qu'on veut créé"
      required: True
      default: false
      type: string
      example: Client-*********-QTA-123
  responses:
    '200':
      description: Valise existe déjà.
      content:
        application/json:
          example: {
            "message": "Valise already exist"
          }

    '200 Valise created':
      description: "Valise crée"
      content:
        application/json:
          example: '
          {
    "s:Envelope": {
        "@xmlns:s": "http://schemas.xmlsoap.org/soap/envelope/",
        "s:Body": {
            "CreateValisesResponse": {
                "@xmlns": "http://tempuri.org/",
                "CreateValisesResult": {
                    "@xmlns:a": "http://schemas.datacontract.org/2004/07/CreateValise",
                    "@xmlns:i": "http://www.w3.org/2001/XMLSchema-instance",
                    "a:Comment": "Valise crée",
                    "a:URL": "https://agora-acc.resa.intra/Sites/sap2/Dossier EAN/SAPFolder83/Dossier EAN-54145670000423093454549-QTA-300"
                }
            }
        }
    }
}
          '