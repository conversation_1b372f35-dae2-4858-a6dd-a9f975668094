get:
  summary: " WS173 : Récupération des informations de Borne Recharge "
  description: "Retourne les informations des bornes associées à un compte en fonction de ses eans."
  parameters:
    - name: "Authorization"
      in: "header"
      required: true
      schema:
        type: "string"
  responses:
    '200':
      description: "OK"
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Ean:
                  type: string
                  example: "541456700002569958"
                Borne:
                  type: object
                  properties:
                    Adresse:
                      type: object
                      properties:
                        Numero:
                          type: string
                          example: "5"
                        Rue:
                          type: string
                          example: "Avenue Victor-Hugo"
                        CodePostal:
                          type: string
                          example: "4000"
                        Commune:
                          type: string
                          example: "LIEGE"
                        Pays:
                          type: string
                          example: "Belgique"
                    Marque:
                      type: string
                      example: "Tesla"
                    Serial:
                      type: string
                      example: "123456"
                    Ean:
                      type: string
                      example: "541456700002569958"
                    Utilisation:
                      type: object
                      properties:
                        Libelle:
                          type: string
                          example: "Privée"
                        Valeur:
                          type: string
                          example: "PRIVATE"
                    Puissance:
                      type: number
                      example: 22.0
                    Photo:
                      type: string
                      example: null
                    Modele:
                      type: string
                      example: "123M"
                    Bidirectionnelle:
                      type: boolean
                      example: false
                    DateDesactivation:
                      type: string
                      example: "2023-11-01"
                    DateActivation:
                      type: string
                      example: "2023-10-01"
                    Active:
                      type: boolean
                      example: false
                Entreprise:
                  type: object
                  properties:
                    FormeJuridique:
                      type: string
                      example: "SPRL"
                    Numero:
                      type: string
                      example: "123456789"
                    Nom:
                      type: string
                      example: "New tesst new life230"
                    Acronyme:
                      type: string
                      example: "NT NL 230"
                DateCreation:
                  type: string
                  example: "2023-12-06T09:46:20.617790"
                Uuid:
                  type: string
                  example: "d77f14866cda410baa685bd31f391ea4"
                Demandeur:
                  type: object
                  properties:
                    Prenom:
                      type: string
                      example: "Jean"
                    Email:
                      type: string
                      example: "<EMAIL>"
                    Telephone:
                      type: string
                      example: "496123456"
                    Nom:
                      type: string
                      example: "Michel"
                Installateur:
                  type: object
                  properties:
                    Telephone:
                      type: string
                      example: null
                    Nom:
                      type: string
                      example: "Solar panel master X"
                    Email:
                      type: string
                      example: null
    '401':
      description: "Non autorisé"
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
    '500':
      description: "Erreur interne du serveur"
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string