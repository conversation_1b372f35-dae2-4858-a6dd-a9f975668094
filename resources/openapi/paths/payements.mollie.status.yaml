get:
  summary: "WS187 : Permet de récupérer les informations de status d'un paiement Mollie"
  description: |
    Fournir soit le PaiementId de Mollie, soit avec le OrderId (numéro de dossier)
  parameters:
    - name: PaymentId
      in: query
      required: false
      description: ID de paiement Mollie
      schema:
        type: string
        example: tr_5B8cwPMGnU6qLbRvo7qEZo
        default: null
    - name: OrderId
      in: query
      required: false
      description: Numéro de dossier
      schema:
        type: string
        example: 0123456789
        default: null
    - name: PaymentType
      in: query
      required: false
      description: Type de paiement
      schema:
        type: string
        enum:
          - 'DEVIS'
          - 'ETUDE'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              MollieStatus:
                type: string
                example: "some_status"
              PaymentId:
                type: string
                example: "mollie_payment_id"
              OrderId:
                type: string
                example: "0012345"
              CreationDate:
                type: string
                format: date-time
                example: "2023-01-01T00:00:00Z"
              PaymentType:
                type: string
                enum:
                  - 'DEVIS'
                  - 'ETUDE'
                example: "DEVIS"
            required:
              - MollieStatus
              - PaymentId
              - OrderId
              - CreationDate
    '400':
      description: BAD REQUEST
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "PaymentId or OrderId + PaymentType is required"
              ErrorCode:
                type: string
                example: "MISSING_QUERY_PARAMS"
    '404':
      description: NOT FOUND
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 404
              Message:
                type: string
                example: "No data with given ID"
              ErrorCode:
                type: string
                example: "NO_DATA_FROM_ID"
post:
  summary: "WS210 : Permet d'enregistrer ou de mettre à jour le statut en waiting d'un paiement Mollie"
  description: |
    Permet d'enregistrer l'état en cours d'un paiement Mollie en fournissant le `PaymentId`.
    Si aucun paiement n'est trouvé, une nouvelle entrée est créée avec le statut `waiting`.
  parameters:
    - name: PaymentId
      in: query
      required: true
      description: ID de paiement Mollie
      schema:
        type: string
        example: tr_5B8cwPMGnU6qLbRvo7qEZo
        default: null
  responses:
    '200':
      description: Succès - Le statut du paiement a été récupéré ou mis à jour.
      content:
        application/json:
          schema:
            type: object
            properties:
              MollieStatus:
                type: string
                example: "waiting"
              PaymentId:
                type: string
                example: "mollie_payment_id"
              OrderId:
                type: string
                example: "0012345"
              CreationDate:
                type: string
                format: date-time
                example: "2023-01-01T00:00:00Z"
              PaymentType:
                type: string
                enum:
                  - 'DEVIS'
                  - 'ETUDE'
                example: "DEVIS"
            required:
              - mollie_status
              - order_id
              - creation_date
    '400':
      description: Requête invalide - pas de PaymentId fournis.
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "PaymentId is required"
              ErrorCode:
                type: string
                example: "MISSING_QUERY_PARAMS"