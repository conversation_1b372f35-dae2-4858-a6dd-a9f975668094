post:
  summary: 'WS09: Permet à un utilisateur de suivre une panne'
  parameters: []
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
          - PanneId
          - Email
          - Sms
          properties:
            PanneId:
              type: string
              description: Id de la panne à la quelle s'abonner
              example: "12345"
            Type:
              type: string
              description: Type d'abonnement, EMAIL ou SMS
              example: "SMS"
            Adresse:
              type: string
              description: Adresse mail ou téléphonique par la quelle reçevoir les informations de suivi de la panne
              example: "+32496123456"
  responses:
    '204':
      description: 204 NO CONTENT
