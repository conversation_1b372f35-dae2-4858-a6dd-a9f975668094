post:
  summary: WS04.4 Validation de la modification d'adresse e-mail
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Callback
      description: the callback URL in the mail - token added in query
      in: query
      required: true
      schema:
        type: string
    - name: Email
      description: the user's new email
      in: query
      required: true
      schema:
        type: string
        format: email
    - name: Sandbox
      in: query
      description: Whether the email should actually be sent or not
      schema:
        type: boolean
      example: 'true'
  responses:
    '200':
      description: OK
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            example:
              Email: <EMAIL>
              Callback: www.test.com