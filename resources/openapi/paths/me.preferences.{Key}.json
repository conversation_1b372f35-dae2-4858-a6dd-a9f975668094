{"patch": {"summary": "Modifier une préférence de l'utilisateur", "parameters": [{"name": "Key", "description": "Préférence à modifier", "in": "path", "required": true, "schema": {"type": "string"}, "example": "<PERSON><PERSON>"}], "requestBody": {"description": "Préférences à modifier", "content": {"application/json": {"schema": {"type": "object"}, "example": {"Langue": "EN"}}}}, "security": [{"tokenAuthorizer": [], "ghostAuthorizer": []}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object"}, "example": {"Langue": "EN"}}}}}}, "delete": {"summary": "WS13 Supprimer une préférence de l'utilisateur", "parameters": [{"name": "Key", "description": "Préférence à supprimer", "in": "path", "required": true, "schema": {"type": "string"}, "example": "<PERSON><PERSON>"}], "security": [{"tokenAuthorizer": [], "ghostAuthorizer": []}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object"}, "example": {"Langue": "FR"}}}}}}}