get:
  summary: "WS218 : <PERSON><PERSON><PERSON><PERSON>rer les historique d'alerts"
  description: Récupère les historique d'alerts pour un utilisateurs et une adresse donné.
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: AddressHash
      in: query
      required: true
      description: Identifiant address_hash de l'utilisateur.
      schema:
        type: string
  responses:
    200:
      description: <PERSON><PERSON>ès, retourne l'historique des alertes.
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Uid:
                  type: string
                  description: Identifiant de l'utilisateur.
                Ean:
                  type: string
                  description: Identifiant EAN.
                MeterId:
                  type: string
                  description: Identifiant du compteur.
                EnergyType:
                  type: string
                  enum: [ "ELEC", "GAZ" ]
                  description: Type d'énergie
                  example: "ELEC"
                ConsoValue:
                  type: number
                  description: Valeur de consommation ayant déclenché l'alerte.
                Limit:
                  type: number
                  description: Seuil programmé pour l'alerte.
                Overhead:
                  type: number
                  description: Dépassement du seuil programmé pour l'alerte.
                AlertType:
                  type: string
                  enum: [ "daily", "monthly" ]
                  description: Type d'alerte (journalière ou mensuelle).
                Date:
                  type: string
                  format: date-time
                  description: Date et heure de l'alerte au format ISO 8601.
                CheckDate:
                  type: string
                  format: date-time
                  description: Date et heure de la donnée qui a lancé l'alerte au format ISO 8601.
                AddressHash:
                  type: string
                  description: Hash d’adresse de l'utilisateur.
                Address:
                  type: object
                  description: Informations sur l'adresse associée à l'alerte.
                  properties:
                    Cdpostal:
                      type: string
                      description: Code postal.
                    CodePays:
                      type: string
                      description: Code pays.
                    Localite:
                      type: string
                      description: Ville/localité.
                    NumRue:
                      type: string
                      description: Numéro de rue.
                    Rue:
                      type: string
                      description: Nom de la rue.
    404:
      description: Aucune alerte trouvée pour cette adresse.