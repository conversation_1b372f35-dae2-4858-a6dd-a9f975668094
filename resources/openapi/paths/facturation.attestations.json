{"get": {"summary": "WS49 : O<PERSON><PERSON><PERSON> les attestations liées à un client authentifié", "parameters": [], "security": [{"tokenAuthorizer": []}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["Customer"], "properties": {"Customer": {"type": "object", "required": ["CustomerNbr", "CustomerId", "Detail", "Address", "Contact"], "properties": {"CustomerNbr": {"type": "string"}, "CustomerId": {"type": "string"}, "Detail": {"type": "object", "required": ["CustomerCategoryCode", "LanguageCode", "Individual", "ConfidentialData"], "properties": {"CustomerCategoryCode": {"type": "string"}, "LanguageCode": {"type": "string"}, "Individual": {"type": "string"}, "ConfidentialData": {"type": "string"}}}, "Company": {"type": "object", "required": ["CompanyName", "VatApplication", "CompanyLegalFormCode"], "properties": {"CompanyName": {"type": "string"}, "VatApplication": {"type": "string"}, "CompanyLegalFormCode": {"type": "string"}}}, "Individual": {"type": ["object", "null"]}, "Address": {"type": "object", "required": ["AddressStructureType", "PostalCode", "TownCode", "TownName", "Street", "StreetNumber", "StreetBox"], "properties": {"AddressStructureType": {"type": "string"}, "PostalCode": {"type": "string"}, "TownCode": {"type": "string"}, "TownName": {"type": "string"}, "Street": {"type": "string"}, "StreetNumber": {"type": "string"}, "StreetBox": {"type": "string"}}}, "Contact": {"type": "object", "required": ["Newsletter", "Marketing"], "properties": {"Newsletter": {"type": "string"}, "Marketing": {"type": "string"}}}}}}}}, "example": [{"Customer": {"CustomerNbr": "*********", "CustomerId": "*********", "Detail": {"CustomerCategoryCode": "CLIENT", "LanguageCode": "FR", "Individual": "false", "ConfidentialData": "false"}, "Company": {"CompanyName": "RESIDENCE GRAK", "VatApplication": "N", "CompanyLegalFormCode": "SA"}, "Individual": null, "Address": {"AddressStructureType": "STRUCTURED", "PostalCode": "4032", "TownCode": "0", "TownName": "<PERSON><PERSON><PERSON><PERSON>", "Street": "PLACE JOSEPH WILLEM", "StreetNumber": "4", "StreetBox": "001"}, "Contact": {"Newsletter": "false", "Marketing": "false"}}}]}}}, "404": {"description": "Not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "examples": {"No data": {"description": "The request completed but no data have been found for this user", "value": {"Error": 404, "Message": "No data found for this user"}}, "No partenaireId": {"description": "No PartenaireId found for this user, impossible to execute the request. Should execute /demande_travaux/demande", "value": {"Error": 404, "Message": "No PartenaireId found for this user"}}}}}}}}}