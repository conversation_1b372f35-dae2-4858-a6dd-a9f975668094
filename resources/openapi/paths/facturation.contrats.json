{"get": {"summary": "WS82 : <PERSON><PERSON><PERSON><PERSON> les contrats d'un utilisateur authentifié", "security": [{"tokenAuthorizer": []}], "parameters": [], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["Contracts"], "properties": {"Contract": {"type": "array", "items": {"type": "object", "required": ["ContractNbr", "Detail", "DeliveryPoints"], "properties": {"ContractNbr": {"type": "string"}, "Detail": {"type": "object", "required": ["Framework", "BillingContract", "EnergyType", "ProductCode", "ContractCategory", "ContractStatusCode", "ContractualStartDate", "DurationCode"], "properties": {"Framework": {"type": "string"}, "BillingContract": {"type": "string"}, "EnergyType": {"type": "string"}, "ProductCode": {"type": "string"}, "ContractCategory": {"type": "string"}, "ContractStatusCode": {"type": "string"}, "ContractualStartDate": {"type": "string", "format": "date"}, "DurationCode": {"type": "string"}}}, "DeliveryPoints": {"type": "object", "required": ["Gsrn", "Address"], "properties": {"Gsrn": {"type": "string"}, "Address": {"type": "object", "required": ["CountryCode", "PostalCode", "TownName", "Street", "StreetNumber"], "properties": {"CountryCode": {"type": "string"}, "PostalCode": {"type": "string"}, "TownName": {"type": "string"}, "Street": {"type": "string"}, "StreetNumber": {"type": "string"}}}}}}}}}}}, "example": [{"Contracts": {"Contract": [{"ContractNbr": "220115305", "Detail": {"Framework": "false", "BillingContract": "false", "EnergyType": "EL", "ProductCode": "EL", "ContractCategory": "FOURNX", "ContractStatusCode": "TERMINE", "ContractualStartDate": "2011-11-06+01:00", "DurationCode": "-1"}, "DeliveryPoints": {"DeliveryPoint": {"Gsrn": "541449020710423206", "Address": {"CountryCode": "BE", "PostalCode": "4000", "TownName": "Liège", "Street": "Rue Saint-Nicolas", "StreetNumber": "492"}}}}, {"ContractNbr": "220000380", "Detail": {"Framework": "false", "BillingContract": "false", "EnergyType": "EL", "ProductCode": "EL", "ContractCategory": "FOURNX", "ContractStatusCode": "TERMINE", "ContractualStartDate": "2007-06-30+02:00", "DurationCode": "-1"}, "DeliveryPoints": {"DeliveryPoint": {"Gsrn": "541456700000291295", "Address": {"CountryCode": "BE", "PostalCode": "4032", "TownName": "<PERSON><PERSON><PERSON><PERSON>", "Street": "PLACE JOSEPH WILLEM", "StreetNumber": "4", "StreetBox": "001"}}}}]}}]}}}, "404": {"description": "Not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "examples": {"No data": {"description": "The request completed but no data have been found for this user", "value": {"Error": 404, "Message": "No data found for this user"}}, "No partenaireId": {"description": "No PartenaireId found for this user, impossible to execute the request. Should execute /demande_travaux/demande", "value": {"Error": 404, "Message": "No PartenaireId found for this user"}}}}}}}}}