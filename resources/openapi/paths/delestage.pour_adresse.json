{"get": {"summary": "WS43 : O<PERSON><PERSON><PERSON> le niveasu de priorité d'une localité en cas de déléstage éléctrique à partir d'informationns sur la gólocalisation", "parameters": [{"name": "<PERSON><PERSON>", "in": "header", "schema": {"type": "string", "example": "FR"}}, {"name": "Localite", "in": "query", "required": true, "description": "Localté", "schema": {"type": "string", "example": "Flône"}}, {"name": "Rue", "in": "query", "required": true, "description": "Rue", "schema": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}}, {"name": "Cdpostal", "in": "query", "required": true, "description": "Code postal", "schema": {"type": "string", "example": "4540"}}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["Liste"], "properties": {"Liste": {"type": "object", "required": ["Tranche", "Message"], "properties": {"Tranche": {"type": "integer"}, "Message": {"type": "string"}}}}}, "example": {"Liste": {"Tranche": 6, "Message": "SITUATION NORMALE,IL Y A SUFFISAMMENT D'ÉLECTRICITÉ DISPONIBLE POUR RÉPONDRE AUX BESOINS DE CONSOMMATION. PAS D'INQUIÉTUDE !"}}}}}, "400": {"description": "400 Bad Request", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "example": {"Error": 400, "Message": "Missing required request parameters: [Cdpostal]"}}}}}}}