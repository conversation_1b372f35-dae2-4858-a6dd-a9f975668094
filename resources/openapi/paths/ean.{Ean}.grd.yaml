get:
  summary: "WS196 : Savoir si l'EAN est detenu par RESA et recupérer le type de fluide (Gaz / Elec)."
  parameters:
    - name: Ean
      in: path
      required: true
      schema:
        type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              ResaIsGRD:
                type: boolean
                description: Indicates if the EAN belongs to Resa GRD.
              EnergyType:
                type: string
                description: Indicates the type of energy (ELEC for electricity or GAZ for gas).
                enum:
                  - ELEC
                  - GAZ
    "404":
      description: 404 NOT FOUND
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: number
                example: 404
              Message:
                type: string
                example: "Aucun résultat retrouvé"