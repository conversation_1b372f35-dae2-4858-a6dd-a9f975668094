get:
  summary: 'WS46 : Obtenir la liste des factures liées à un contrat'
  security:
    - tokenAuthorizer: []
  parameters:
  - name: Ean
    in: query
    required: true
    schema:
      type: integer
      example: 541460900001172286
  - name: Langue
    in: header
    required: false
    schema:
      type: string
      default: FR
    example: FR
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: object
            required:
            - Buspartner
            - TextSolde
            - Solde
            - FactText1
            - FactText2
            - FactText3
            - FactText4
            - FactText5
            - FactText6
            - FactText7
            - Liste
            properties:
              Buspartner:
                type: string
              TextSolde:
                type: string
              Solde:
                type: string
              FactText1:
                type: string
              FactText2:
                type: string
              FactText3:
                type: string
              FactText4:
                type: string
              FactText5:
                type: string
              FactText6:
                type: string
              FactText7:
                type: string
              Liste:
                type: array
                items:
                  $ref: '#/components/schemas/facture'
          example:
            Buspartner: '110147046 '
            TextSolde: 'Solde en votre faveur :'
            Solde: 77,90
            FactText1: Date
            FactText2: Référence
            FactText3: Type et période
            FactText4: Montant total en €
            FactText5: Solde à payer en €
            FactText6: Date d’échéance
            FactText7: Statut
            Liste:
            - DocDate: 01.04.2022
              RefDocNo: '3322032427      '
              Text: Acompte 01/03/2022-31/03/2022
              Amount: 62,10
              AmountOpen: 0,00
              NetDate: 19.04.2022
              Statut: Payée
            - DocDate: 18.02.2022
              RefDocNo: '3322014925      '
              Text: Régularisation 13/01/2021-24/01/2022
              Amount: 170,01
              AmountOpen: 0,00
              NetDate: 08.03.2022
              Statut: Payée
            - DocDate: 24.01.2022
              RefDocNo: '3322007922      '
              Text: Acompte 01/01/2022-31/01/2022
              Amount: 43,64
              AmountOpen: 0,00
              NetDate: 11.02.2022
              Statut: Payée
            - DocDate: 21.12.2021
              RefDocNo: '3321106185      '
              Text: Acompte 01/12/2021-31/12/2021
              Amount: 43,64
              AmountOpen: 0,00
              NetDate: 08.01.2022
              Statut: Payée
            - DocDate: 18.11.2021
              RefDocNo: '3321095553      '
              Text: Acompte 01/11/2021-30/11/2021
              Amount: 43,64
              AmountOpen: 0,00
              NetDate: 06.12.2021
              Statut: Payée