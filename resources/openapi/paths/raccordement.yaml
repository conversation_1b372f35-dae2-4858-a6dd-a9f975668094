post:
  summary: "WS199 : Soumission d'une demande de raccordement"
  description: |
    Ce Endpoint permet de soumettre une demande de raccordement avec les informations du demandeur, de l'adresse, du contact, de la facturation et des compteurs.
    Il va envoyer une demande asynchrone sur demande_travaux/demande, et récupérera un process_id.
    Ensuite va uploader un fichier sur notre S3 avec comme nom de dossier :{process_id}_{sectActivitte}.
    Cela permettra de le récupérer lors du callback et d'envoyer le fichier sur la bonne valise SharePoint.    
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          required:
            - ApplicantType
            - Address
            - Applicant
            - Meters
            - ConfirmRgpd
          properties:
            Synergy:
              type: boolean
              description: Indique si le raccordement est en synergie avec la CILE
              example: true
            Synergrid:
              type: string
              description: Numéro d'agrégation Synergrid
              example: "12.34.56"
            ApplicantType:
              type: string
              description: Type de demandeur (particulier ou société)
              enum:
                - Particulier
                - Independant
                - Societe
              example: "Particulier"
            ActAs:
              type: string
              description: Statut d'action (propriétaire ou locataire)
              enum:
                - PROP
                - LOC
              example: "PROP"
            Address:
              type: object
              description: Adresse du raccordement
              properties:
                Street:
                  type: string
                  description: Rue de l'adresse de raccordement
                  example: "Boulevard d'Avroy"
                Number:
                  type: string
                  description: Numéro de l'adresse de raccordement
                  example: "38"
                Box:
                  type: string
                  description: Boîte de l'adresse de raccordement
                  example: "A"
                Postcode:
                  type: string
                  description: Code postal de l'adresse de raccordement
                  example: "4000"
                City:
                  type: string
                  description: Ville de l'adresse de raccordement
                  example: "Liège"
                Country:
                  type: string
                  default: "BE"
                  description: Pays de l'adresse de raccordement
                  example: "BE"
                Lat:
                  type: number
                  description: Latitude de l'adresse de raccordement si adresse postale inconnue
                  example: 50.6386777
                Lon:
                  type: number
                  description: Longitude de l'adresse de raccordement si adresse postale inconnue
                  example: 5.5677805
                SpwPlot:
                  type: string
                  description: Numéro de parcelle SPW de l'adresse de raccordement si adresse postale inconnue
                  example: "62803B0125/00E000"
                Note:
                  type: string
                  description: Informations complémentaires pour aider à trouver l'adresse de raccordement
                  example: "À droite de l'UCM"
            Applicant:
              type: object
              description: Informations du demandeur
              required:
                - Name
                - Firstname
                - Email
                - Phone
                - Address
              properties:
                Name:
                  type: string
                  description: Nom de famille du demandeur
                  example: "Doe"
                Firstname:
                  type: string
                  description: Prénom du demandeur
                  example: "John"
                Email:
                  type: string
                  description: Email du demandeur
                  example: "<EMAIL>"
                Phone:
                  type: string
                  description: Numéro de téléphone du demandeur
                  example: "+32 11 22 33 44"
                Address:
                  type: object
                  description: Adresse du demandeur
                  required:
                    - Street
                    - Number
                    - Postcode
                    - City
                  properties:
                    Street:
                      type: string
                      description: Rue de l'adresse du demandeur
                      example: "Boulevard d'Avroy"
                    Number:
                      type: string
                      description: Numéro de l'adresse du demandeur
                      example: "38"
                    Box:
                      type: string
                      description: Boîte de l'adresse du demandeur
                      example: "A"
                    Postcode:
                      type: string
                      description: Code postal de l'adresse du demandeur
                      example: "4000"
                    City:
                      type: string
                      description: Ville de l'adresse du demandeur
                      example: "Liège"
                    Country:
                      type: string
                      default: "BE"
                      description: Pays de l'adresse du demandeur
                      example: "BE"
            Contact:
              type: object
              description: Informations de contact
              required:
                - Name
                - Firstname
                - Email
                - Phone
                - Address
              properties:
                Name:
                  type: string
                  description: Nom de famille du contact
                  example: "Doe"
                Firstname:
                  type: string
                  description: Prénom du contact
                  example: "John"
                Email:
                  type: string
                  description: Email du contact
                  example: "<EMAIL>"
                Phone:
                  type: string
                  description: Numéro de téléphone du contact
                  example: "+32 11 22 33 44"
                Address:
                  type: object
                  description: Adresse du contact
                  required:
                    - Street
                    - Number
                    - Postcode
                    - City
                  properties:
                    Street:
                      type: string
                      description: Rue de l'adresse du contact
                      example: "Boulevard d'Avroy"
                    Number:
                      type: string
                      description: Numéro de l'adresse du contact
                      example: "38"
                    Box:
                      type: string
                      description: Boîte de l'adresse du contact
                      example: "A"
                    Postcode:
                      type: string
                      description: Code postal de l'adresse du contact
                      example: "4000"
                    City:
                      type: string
                      description: Ville de l'adresse du contact
                      example: "Liège"
                    Country:
                      type: string
                      default: "BE"
                      description: Pays de l'adresse du contact
                      example: "BE"
            Billing:
              type: object
              description: Adresse de facturation
              required:
                - Street
                - Number
                - Postcode
                - City
              properties:
                Street:
                  type: string
                  description: Rue de l'adresse de facturation
                  example: "Boulevard d'Avroy"
                Number:
                  type: string
                  description: Numéro de l'adresse de facturation
                  example: "38"
                Box:
                  type: string
                  description: Boîte de l'adresse de facturation
                  example: "A"
                Postcode:
                  type: string
                  description: Code postal de l'adresse de facturation
                  example: "4000"
                City:
                  type: string
                  description: Ville de l'adresse de facturation
                  example: "Liège"
                Country:
                  type: string
                  description: Pays de l'adresse de facturation
                  default: "BE"
                  example: "BE"
            Company:
              type: object
              description: Informations de l'entreprise
              required:
                - Name
                - LegalStatus
                - Vat
              properties:
                Name:
                  type: string
                  description: Nom de l'entreprise
                  example: "RESA"
                LegalStatus:
                  type: string
                  description: Statut juridique de l'entreprise
                  example: "SA"
                Vat:
                  type: string
                  description: Numéro de TVA de l'entreprise
                  example: "BE0123456789"
            Meters:
              type: array
              description: Liste des compteurs
              items:
                type: object
                required:
                  - EnergyType
                  - WorkType
                properties:
                  Ean:
                    type: string
                    description: Code EAN
                    example: "5414789632156"
                  Number:
                    type: string
                    description: Numéro compteur
                    example: "4017894"
                  EnergyType:
                    type: string
                    description: Type d'énergie
                    enum:
                      - ELEC
                      - GAZ
                    example: "ELEC"
                  WorkType:
                    type: string
                    description: |
                      Type de travaux.
                      - NOUV_RACC : nouveau raccordement
                      - MODI_INSTAL : Modifier l'installation (puissance, type de compteur, ajout compteur)
                      - MODI_TARIF : Changer la tarification (ST<>DT)
                      - MODI_CABLE : Remplacement du câble de liaison entre le compteur et le tableau divisionnaire
                      - MODI_SMART : Remplacer compteur actuel par compteur communicant
                      - DEPLA_BRAN : Déplacer le compteur et/ou le branchement existant
                      - ENLV_CPT : Enlever le(s) compteur(s) et/ou le branchement existant
                      - NOUV_FORF : Raccordement forfaitaire (abris bus, antenne GSM, bateau)
                      - NOUV_RTEC : Raccordement technique 
                      - NOUV_ARMO : Armoires spécifiques
                      - NOUV_CCOM : Centres commerciaux
                      - CHAN_PROVI : Provisoire chantier
                      - SUPP_BRAN : Suppression du branchement et du ou des compteurs
                      - RACC_FORAIN : Raccordement courte durée (Forains)
                      - MODI_PROD : Ajouter/modifier une production d’électricité
                      - PULSE_DELAY : Demander un report d’impulsion
                    enum:
                      - NOUV_RACC
                      - MODI_INSTAL
                      - MODI_TARIF
                      - MODI_CABLE
                      - MODI_SMART
                      - DEPLA_BRAN
                      - ENLV_CPT
                      - NOUV_FORF
                      - NOUV_RTEC
                      - NOUV_ARMO
                      - NOUV_CCOM
                      - CHAN_PROVI
                      - SUPP_BRAN
                      - RACC_FORAIN
                      - MODI_PROD
                      - PULSE_DELAY
                    example: "MODI_SMART"
                  Quantity:
                    type: integer
                    description: Nombre de compteurs avec cette même configuration
                    default: 1
                    example: 1
                  Power:
                    type: number
                    description: Puissance du compteur
                    example: 9.2
                  Amper:
                    type: number
                    description: Intensité du compteur
                    example: 40
                  PhaseType:
                    type: string
                    description: Nombre/type de phases du compteur
                    enum:
                      - MONO
                      - TRI
                    example: "MONO"
                  Rate:
                    type: string
                    description: |
                      Tarification du compteur
                      - ST : Simple tarif
                      - DT : Double tarif
                      - EXN_ST : Simple tarif exclusif nuit
                      - EXN_DT : Double tarif exclusif nuit
                    enum:
                      - ST
                      - DT
                      - EXN_ST
                      - EXN_DT
                    example: "ST"
                  Allotment:
                    type: boolean
                    description: Lotissement, habité groupé ou assimilé
                    example: false
                  Moving:
                    type: boolean
                    description: Déplacement du compteur
                    example: false
                  Photo:
                    description: photo du compteur
                    type: object
                    required:
                      - Name
                      - Url
                    properties:
                      Name:
                        type: string
                        description: Nom de la photo du compteur
                        example: "photo.jpg"
                      Url:
                        type: string
                        description: Url de la photo du compteur
                        example: "https://photo.url.com/my_photo.jpg"
                  Note:
                    type: string
                    description: Note sur le compteur
                    example: null
            ProductMoreThan10Kva:
              type: boolean
              description: Indique si la production électrique dépasse 10 Kva
              example: false
            ElecNote:
              type: string
              description: Remarque électricité
              example: "Remarque élec"
            GasNote:
              type: string
              description: Remarque gaz
              example: "Remarque gaz"
            GeneralNote:
              type: string
              description: Remarque générale
              example: "Remarque générale"
            DesiredDate:
              type: string
              description: Date souhaitée pour les travaux au format YYYYMM
              pattern: '^\d{4}\d{2}$'
              example: "202305"
            Usage:
              type: string
              description: |
                Usage du compteur
                - RESI : Résidentiel
                - PRO : Professionnel
                - MIXTE : Mixte
              enum:
                - RESI
                - PRO
                - MIXTE
              example: "RESI"
            HouseType:
              type: string
              description: |
                Type de logement
                - PRIVE_MOINS : Logement privé de moins de 10 ans
                - DESTRUCTION : Logement destiné à une destruction définitive
                - AUCUNE : Aucune de ces propositions
                - PRIVE_PLUS : Logement privé de plus de 10 ans
                - RECONSTRUIT : Un immeuble qui après démolition est reconstruit lors d’une même opération
                - HANDICAP : Un logement privé/un établissement d’hébergement adapté pour handicapés
              enum:
                - PRIVE_MOINS
                - DESTRUCTION
                - AUCUNE
                - PRIVE_PLUS
                - RECONSTRUIT
                - HANDICAP
              example: "PRIVE_MOINS"
            SubjectToVat:
              type: boolean
              description: Assujetti à la TVA
              example: true
            VatType:
              type: string
              description: |
                Type de TVA
                - PROP : Propriétaire, locataire, usufruitier, gestionnaire du logement
                - COMMU : Commune, SPW, Province, CPAS
                - SOCIETE : Société régionale de logement et société agréée pour le logement social non assujetti
                - INTERN : Maison de repos, internat scolaire, home de protection de la jeunesse ;
                - OTAN : OTAN, ambassade, ESA
                - BATI : Bâtiment scolaire
                - AUTO : TVA en autoliquidation
              enum:
                - PROP
                - COMMU
                - SOCIETE
                - INTERN
                - OTAN
                - BATI
                - AUTO
              example: "PROP"
            ConfirmVat:
              type: boolean
              description: Confirmation que la TVA est correctement déclarée
              example: true
            ConfirmGdpr:
              type: boolean
              description: Confirmation de l'accord RGPD
              example: true
            NearByStreet:
              type: string
              description: Rue la plus proche lorsque la rue réelle n'est pas encore connue
            Documents:
              type: array
              description: Documents liés à la demande
              items:
                type: object
                required:
                  - Name
                  - Url
                properties:
                  Name:
                    type: string
                    description: Nom du document
                    example: "Document 1.pdf"
                  Url:
                    type: string
                    description: Url du document
                    example: "https://document.url.com/my-DOC.pdf"
  responses:
    '204':
      description: "No contents"
    '400 INVALID_FIELDS':
      description: INVALID_FIELDS
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                description: Error code
                example: 400
              Message:
                type: string
                description: Error message
                example: "Some required fields are missing or have an incorrect type"
              ErrorCode:
                type: string
                description: Specific error code
                example: "INVALID_FIELDS"
              Extra:
                type: array
                description: Additional error details
                items:
                  type: object
                  properties:
                    Field:
                      type: string
                      description: The field that caused the error
                      example: "ActAs"
                    Error:
                      type: string
                      description: Description of the error
                      example: "Input should be 'PROP' or 'LOC'"
                    ErrorType:
                      type: string
                      description: Type of error
                      example: "enum"
                    InputGiven:
                      type: string
                      description: The input that caused the error
                      example: "WRONG"
    '500 DOWNLOAD_ERROR':
      description: Erreur serveur
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                description: Message détaillant l'erreur de téléchargement
                example: "Erreur lors du téléchargement du fichier"
              Error:
                type: string
                description: Code d'erreur HTTP
                example: "500"
              ErrorCode:
                type: string
                description: Code spécifique de l'erreur de téléchargement
                example: "DOWNLOAD_ERROR"
    '500 UPLOAD_ERROR':
      description: Erreur serveur
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                description: Message détaillant l'erreur de téléchargement
                example: "Erreur lors de l'upload du fichier"
              Error:
                type: string
                description: Code d'erreur HTTP
                example: "500"
              ErrorCode:
                type: string
                description: Code spécifique de l'erreur d'upload
                example: "UPLOAD_ERROR"