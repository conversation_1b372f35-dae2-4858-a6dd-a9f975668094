{"get": {"summary": "WS76 : O<PERSON><PERSON><PERSON> les informations liées à tous les EAN et tous les compteurs d'un utilisateur. La liste de EAN et compteurs aura été mise à jour au préalable lors de l'onboarding de l'utilisateur", "description": "", "parameters": [], "security": [{"tokenAuthorizer": [], "ghostAuthorizer": []}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["ListeEan"], "properties": {"ListeEan": {"type": "array", "items": {"type": "object", "required": ["<PERSON><PERSON>", "SectActivite", "Compteurs"], "properties": {"Ean": {"type": "string"}, "SectActivite": {"type": "string"}, "Compteurs": {"type": "array", "items": {"type": "object", "required": ["Historique"], "properties": {"Historique": {"type": "object", "required": ["NomContrat", "DetailContrat"], "properties": {"NomContrat": {"type": "integer"}, "DetailContrat": {"type": "array", "items": {"type": "object", "required": ["DateRel", "DetailCpt", "<PERSON><PERSON><PERSON>"], "properties": {"DateRel": {"type": "integer"}, "DetailCpt": {"type": "array", "items": {"type": "object", "required": ["NumCompteur", "Compteur"], "properties": {"NumCompteur": {"type": "string"}, "Compteur": {"type": "array", "items": {"type": "object", "required": ["Cadran"], "properties": {"Cadran": {"type": "array", "items": {"type": "object", "required": ["CatCadran", "Index", "IndexUnit", "IndexQual"], "properties": {"CatCadran": {"type": "string"}, "Index": {"type": "string"}, "IndexUnit": {"type": "string"}, "IndexQual": {"type": "string"}}}}}}}}}}, "Tarif": {"type": "array", "items": {"type": "object", "required": ["Cat<PERSON>arif", "Qtt", "QttUnit"], "properties": {"CatTarif": {"type": "string"}, "Qtt": {"type": "string"}, "QttUnit": {"type": "string"}}}}}}}}}}}}}}}}}, "example": {"ListeEan": [{"Ean": "541460900000575415", "SectActivite": "02", "DateDebutReleve": null, "DateFinReleve": null, "Compteurs": [{"NumCompteur": null, "Registres": null, "Historique": [{"NomContrat": 2000118964, "DetailContrat": [{"DateRel": 20110218, "DetailCpt": [{"NumCompteur": "000000000021451579", "Compteur": [{"Cadran": [{"CatCadran": "TH", "Index": "11161 0.00000000000000", "IndexUnit": "M3", "IndexQual": "02"}]}]}], "Tarif": [{"CatTarif": "TH", "Qtt": "16918.0000000 ", "QttUnit": "KWH"}]}, {"DateRel": 20120201, "DetailCpt": [{"NumCompteur": "000000000021451579", "Compteur": [{"Cadran": [{"CatCadran": "TH", "Index": "12257 0.00000000000000", "IndexUnit": "M3", "IndexQual": "01"}]}]}], "Tarif": [{"CatTarif": "TH", "Qtt": "12243.0000000 ", "QttUnit": "KWH"}]}, {"DateRel": 20130313, "DetailCpt": [{"NumCompteur": "000000000021451579", "Compteur": [{"Cadran": [{"CatCadran": "TH", "Index": "13959 0.00000000000000", "IndexUnit": "M3", "IndexQual": "03"}]}]}], "Tarif": [{"CatTarif": "TH", "Qtt": "18908.0000000 ", "QttUnit": "KWH"}]}, {"DateRel": 20140203, "DetailCpt": [{"NumCompteur": "000000000021451579", "Compteur": [{"Cadran": [{"CatCadran": "TH", "Index": "15189 0.00000000000000", "IndexUnit": "M3", "IndexQual": "02"}]}]}], "Tarif": [{"CatTarif": "TH", "Qtt": "13326.0000000 ", "QttUnit": "KWH"}]}, {"DateRel": 20150204, "DetailCpt": [{"NumCompteur": "000000000021451579", "Compteur": [{"Cadran": [{"CatCadran": "TH", "Index": "16495 0.62100000000000", "IndexUnit": "M3", "IndexQual": "03"}]}]}], "Tarif": [{"CatTarif": "TH", "Qtt": "14221.9111210 ", "QttUnit": "KWH"}]}, {"DateRel": 20160311, "DetailCpt": [{"NumCompteur": "000000000021451579", "Compteur": [{"Cadran": [{"CatCadran": "TH", "Index": "18046 0.97400000000000", "IndexUnit": "M3", "IndexQual": "03"}]}]}], "Tarif": [{"CatTarif": "TH", "Qtt": "17198.7103756 ", "QttUnit": "KWH"}]}, {"DateRel": 20170313, "DetailCpt": [{"NumCompteur": "000000000021451579", "Compteur": [{"Cadran": [{"CatCadran": "TH", "Index": "19533 0.42700000000000", "IndexUnit": "M3", "IndexQual": "03"}]}]}], "Tarif": [{"CatTarif": "TH", "Qtt": "16618.1368892 ", "QttUnit": "KWH"}]}, {"DateRel": 20180313, "DetailCpt": [{"NumCompteur": "000000000021451579", "Compteur": [{"Cadran": [{"CatCadran": "TH", "Index": "20900 0.22300000000000", "IndexUnit": "M3", "IndexQual": "03"}]}]}], "Tarif": [{"CatTarif": "TH", "Qtt": "15268.8112490 ", "QttUnit": "KWH"}]}]}]}]}, {"Ean": "541456700000601506", "SectActivite": "01", "DateDebutReleve": 20200101, "DateFinReleve": 20200122, "Compteurs": [{"NumCompteur": null, "Registres": null, "HistoriqueErrorMessage": "Aucunes donn\\u00e9es trouv\\u00e9es pour la s\\u00e9lection !"}]}]}}}}}}}