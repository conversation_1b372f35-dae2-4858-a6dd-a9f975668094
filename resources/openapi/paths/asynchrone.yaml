post:
  summary: "WS95 : Exécuter une requete de manière asynchrone"
  parameters: []
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
          - Method
          - Path
          properties:
            ProcessId:
              type:
                - string
                - null
              description: Id du process, si null un nouvel id est créé.
            Method:
              type: string
              enum:
              - get
              - post
              - delete
              - patch
            Path:
              type: string
            Type:
              default: internal
              description: |
                internal | external
                internal : Execute un endpoint de façon asyncrone par un call en interne a la labda liée au endpoint.
                external : Execute directement le endpoint supposé déjà asynchrone en ajoutant un parametre d'url "MessageId". Il est de la rewponsabilité du endpoint appelé d'affecté la réponse par un callback sur /asynchrone/callback.
              type: string
            Params:
              type: object
            PathParameters:
              type: object
            Data:
              type: string
        example:
          Method: GET
          Path: "/pannes/unplanned"
          PathParameters: {}
          Params:
            CdPostal: 4041
          Data:
  responses:
    '200':
      description: '200'
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
            - ProcessId
            properties:
              ProcessId:
                type: string
                description: Id du process, permet de récupérer le résultat sur GET /asynchrone/{ProcessId}.
                minLength: 50
                maxLength: 50
              ExternalStatusCode:
                type: number
                description: Uniquement pour les appel avec Type=external. StatusCode du endpoint appelé.
              ExternalResponse:
                type: object
                description: Uniquement pour les appel avec Type=external. Retour du endpoint appelé.
          example:
            ProcessId: ABCDE123
