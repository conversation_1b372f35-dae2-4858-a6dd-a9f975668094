get:
  summary: "WS133 : Obtenir la liste des dossiers liés à la commune."
  description: L'utilisateur connecté doit soit avoir le rôle 'RACC_CONSU' ou 'RACC_GERER', ou alors être administrateur de la commune.
  security:
    - tokenAuthorizer: [ ]
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Reference:
                  type: string
                  example: "000003054029"
                Type:
                  type: string
                  example: "MODIF"
                Ean:
                  type: string
                  example: "541460900001998534"
                Statut:
                  type: string
                  example: "Visite pour l'élaboration du devis - A planifier"
                Action:
                  type: boolean
                  example: true
                Cloture:
                  type: boolean
                  example: false
                Adresse:
                  type: object
                  properties:
                    Rue:
                      type: string
                      example: "Quai des Ardennes"
                    NumRue:
                      type: string
                      example: "98"
                    NumComp:
                      type: string
                      example: ""
                    CodePostal:
                      type: string
                      example: "4031"
                    Localite:
                      type: string
                      example: "<PERSON><PERSON><PERSON>"