post:
  summary: "WS206 : <PERSON><PERSON><PERSON> ou modifier un profil énergétique"
  description: "Permet de créer ou de mettre à jour un profil énergétique basé sur les données fournies."
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - in: query
      name: AddressHash
      required: false
      schema:
        type: string
      description: "Fournir le has de l'adresse OU l'adresse complete"
      example: "aaefdses123t"
    - in: query
      name: Street
      required: true
      schema:
        type: string
      description: "Rue de l'adresse"
      example: "Rue du test"
    - in: query
      name: Number
      required: true
      schema:
        type: integer
      description: "Numéro de l'adresse"
      example: 2
    - in: query
      name: Postcode
      required: true
      schema:
        type: integer
      description: "Code postal de l'adresse"
      example: 4920
    - in: query
      name: City
      required: true
      schema:
        type: string
      description: "Ville de l'adresse"
      example: "Liège"
    - in: query
      name: Box
      required: false
      schema:
        type: string
      description: "Boîte postale (si applicable)"
      example: "A"
    - in: query
      name: Country
      required: false
      schema:
        type: string
        default: "Belgique"
      description: "Pays de l'adresse (par défaut : Belgique)"
      example: "Belgique"
  requestBody:
    description: "Profil énergétique à créer ou mettre à jour."
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            Bilan:
              type: boolean
              description: "Indique si l'utilisateurs veut recevoir son bilan."
              example: true
            ConsentBilan:
              type: boolean
              description: "Indique si l'utilisateurs consent a l'utilisation de ses donnée pour le bilan (Required if Bilan sended)."
              example: true
            ConsentEnergyAsset:
              type: boolean
              description: "Indique si l'utilisateurs consent a l'utilisation de ses donnée pour le profile energetique (Required if ProfilEnergetique sended)."
              example: true
            EnergyAsset:
              type: object
              description: "Description des caractéristiques énergétiques du foyer."
              properties:
                NumberPeople:
                  type: integer
                  description: "Nombre de personnes vivant dans le foyer."
                  example: 3
                HabitationType:
                  type: string
                  enum: [ "HOUSE", "APARTMENT" ]
                  description: "Type de logement de l'utilisateur, soit une maison ('HOUSE') ou un appartement ('APARTMENT')."
                  example: "HOUSE"
                DomesticHotWater:
                  type: boolean
                  description: "Indique si le foyer utilise l'eau chaude sanitaire."
                  example: true
                PrimaryHeatingSource:
                  type: string
                  enum: [ "PAC", "ELECTRICITY", "GAZ", "OIL" ]
                  description: "Source principale de chauffage du foyer: 'PAC' (pompe à chaleur), 'ELECTRICITY', 'GAZ' ou 'OIL'."
                  example: "PAC"
                SwimmingPool:
                  type: boolean
                  description: "Indique si le foyer possède une piscine."
                  example: true
                Jacuzzi:
                  type: boolean
                  description: "Indique si le foyer possède un jacuzzi."
                  example: false
                ElectricVehiclesChargedHome:
                  type: integer
                  description: "Nombre de véhicules électriques chargés à domicile."
                  example: 1
  responses:
    200:
      description: "Profil énergétique créé ou mis à jour avec succès."
      content:
        application/json:
          schema:
            type: object
            properties:
              EnergyAsset:
                type: object
                properties:
                  NumberPeople:
                    type: integer
                    example: 3
                  HabitationType:
                    type: string
                    enum: [ "HOUSE", "APARTMENT" ]
                    example: "HOUSE"
                  DomesticHotWater:
                    type: boolean
                    example: true
                  PrimaryHeatingSource:
                    type: string
                    enum: [ "PAC", "ELECTRICITY", "GAZ", "OIL" ]
                    example: "PAC"
                  SwimmingPool:
                    type: boolean
                    example: true
                  Jacuzzi:
                    type: boolean
                    example: false
                  ElectricVehiclesChargedHome:
                    type: integer
                    example: 1
    '400 INVALID_FIELDS':
      description: "INVALID_FIELDS"
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                description: "Error code"
                example: 400
              Message:
                type: string
                description: "Error message"
                example: "Some required fields are missing or have an incorrect type"
              ErrorCode:
                type: string
                description: "Specific error code"
                example: "INVALID_FIELDS"
get:
  summary: "WS208 : Récupération des informations du profil énergétique"
  description: "Retourne les informations du profil énergétique de l'utilisateur, y compris la consommation totale."
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - in: query
      name: AddressHash
      required: false
      schema:
        type: string
      description: "Fournir le has de l'adresse OU l'adresse complete"
      example: "aaefdses123t"
    - in: query
      name: Street
      required: true
      schema:
        type: string
      description: "Rue de l'adresse"
      example: "Rue du test"
    - in: query
      name: Number
      required: true
      schema:
        type: integer
      description: "Numéro de l'adresse"
      example: 2
    - in: query
      name: Postcode
      required: true
      schema:
        type: integer
      description: "Code postal de l'adresse"
      example: 4920
    - in: query
      name: City
      required: true
      schema:
        type: string
      description: "Ville de l'adresse"
      example: "Liège"
    - in: query
      name: Box
      required: false
      schema:
        type: string
      description: "Boîte postale (si applicable)"
      example: "A"
    - in: query
      name: Country
      required: false
      schema:
        type: string
        default: "Belgique"
      description: "Pays de l'adresse (par défaut : Belgique)"
      example: "Belgique"
  responses:
    '200':
      description: "OK"
      content:
        application/json:
          schema:
            type: object
            properties:
              Bilan:
                type: boolean
                description: "Indique si l'utilisateurs veut recevoir son bilan."
                example: true
              ConsentEnergyAsset:
                type: boolean
                description: "Indique si l'utilisateurs à consentis à converser ses donnée de profil energétique."
                example: true
              ConsentBilan:
                type: boolean
                description: "Indique si l'utilisateurs a consentis à recevoir son bilan."
                example: true
              EnergyAsset:
                type: object
                properties:
                  NumberPeople:
                    type: integer
                    example: 8
                  HabitationType:
                    type: string
                    example: "HOUSE"
                  DomesticHotWater:
                    type: boolean
                    example: true
                  PrimaryHeatingSource:
                    type: string
                    example: "PAC"
                  SwimmingPool:
                    type: boolean
                    example: true
                  Jacuzzi:
                    type: boolean
                    example: false
                  ElectricVehiclesChargedHome:
                    type: integer
                    example: 1
              TotalConsumption:
                type: number
                example: 22699.0
    '400 INVALID_FIELDS':
      description: "INVALID_FIELDS"
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                description: "Error code"
                example: 400
              Message:
                type: string
                description: "Error message"
                example: "Some required fields are missing or have an incorrect type"
              ErrorCode:
                type: string
                description: "Specific error code"
                example: "INVALID_FIELDS"
    404:
      description: Energy Data not found