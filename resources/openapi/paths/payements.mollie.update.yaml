post:
  summary: 'WS110: Utilisé par mollie pour envoyer un changement de status dans le paiement.'
  description : |
    Reçois l'id du paiment pour pouvoir récupérer son status via un endpoint de mollie.  
    Le status est ensuite mis à jour sur DynamoDB dans la table MollieStatus_<env>.
  requestBody:
    content:
      application/x-www-form-urlencoded:
        schema:
          type: object
          properties:
            id:
              type: string
              example: tr_WDqYK6vllg
          required:
            - id
  responses:
    '200':
      description: OK
    '400':
      description: BAD REQUEST
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "L'id est obligatoire dans la requête"
    '500':
      description: INTERNAL SERVER ERROR
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "Internal server error"
