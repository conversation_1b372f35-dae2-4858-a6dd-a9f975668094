get:
  summary: 'Envoie un e-mail contenant les informations du véhicule.'
  security:
    - tokenAuthorizer: [ ]
  description: |
    Lorsqu'un utilisateur accède à cette URL, le service récupère les informations du véhicule correspondant à l'UUID fourni.
    Un appel au WS68 est ensuite déclenché pour envoyer un e-mail contenant ces informations.
    Support pour les comptes "ghost" (automatiquement générés pour les utilisateurs sans compte réel).
  parameters:
    - in: path
      name: uuid
      schema:
        type: string
      required: true
      description: "UUID du véhicule."
    - in: query
      name: email
      schema:
        type: string
        format: email
      required: false
      description: "<PERSON> fournie, cette adresse e-mail sera celle à laquelle le message sera envoyé."
  responses:
    '204':
      description: "NO CONTENT. L'e-mail a été envoyé avec succès."
    '400':
      description: "BAD REQUEST. Données du véhicule invalides ou manquantes."
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "Données du véhicule invalides ou manquantes"
    '500':
      description: "INTERNAL SERVER ERROR. Erreur interne du serveur."
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "Erreur interne du serveur."
