{"get": {"summary": "WS39 : <PERSON><PERSON><PERSON><PERSON> les détails d'un devis à partir du dossier et/ou du partenaire", "security": [{"tokenAuthorizer": []}], "parameters": [{"name": "Ordre", "description": "Identifiant du dossier", "in": "query", "required": true, "schema": {"type": "integer"}, "example": 3003044}, {"name": "PartenaireId", "description": "Identifiant du partenaire", "in": "query", "required": false, "schema": {"type": "integer"}, "example": 4100026907}], "responses": {"200": {"description": "200 success", "content": {"application/json": {"schema": {"type": "object", "required": ["Liste"], "properties": {"Liste": {"type": "array", "items": {"type": "object", "required": ["<PERSON><PERSON>", "<PERSON><PERSON>", "DtFin", "MontantFinal", "CommStruc"], "properties": {"Devis": {"type": "string"}, "Devise": {"type": "string", "maxLength": 1, "minLength": 1}, "DtFin": {"type": "string"}, "MontantFinal": {"type": "string"}, "CommStruc": {"type": "string"}, "Message": {"type": "string"}, "Url": {"type": "string"}}}}}}, "example": {"Liste": [{"Dossier": "000003003044", "Devis": "2830000134", "Type": "ETUDE", "Devise": "€", "DtFin": "11/12/2015", "MontantFinal": "121.00", "CommStruc": "+++893/2830/00076+++", "Url": "Pas de devis dans la valise SharePoint"}, {"Dossier": "000003003044", "Devis": "3003044", "Type": "DEVIS", "Devise": "€", "DtFin": "26/11/2017", "MontantFinal": "0.00", "CommStruc": "+++893/3003/04486+++", "Url": "Pas de devis dans la valise SharePoint"}]}}}}}}}