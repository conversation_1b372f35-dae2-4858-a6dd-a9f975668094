{"get": {"summary": "WS83 : Obt<PERSON>r les factures d'un client social authentifié", "security": [{"tokenAuthorizer": []}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}}, "example": [{"CustomerType": {"CustomerNbr": "*********", "CustomerId": "*********", "Detail": {"CustomerCategoryCode": "CLIENT", "LanguageCode": "FR", "Individual": "false", "ConfidentialData": "false"}, "Company": {"CompanyName": "DEM  PHOTOGRAVURE", "VatApplication": "N", "CompanyLegalFormCode": "SA"}, "Individual": null, "Address": {"AddressStructureType": "STRUCTURED", "PostalCode": "4030", "TownCode": "1", "TownName": "Grivegnée", "Street": "WAIDE DES DAMES", "StreetNumber": "56"}, "Contact": {"Newsletter": "false", "Marketing": "false"}}}]}}}, "404": {"description": "Not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "examples": {"No data": {"description": "The request completed but no data have been found for this user", "value": {"Error": 404, "Message": "No data found for this user"}}, "No partenaireId": {"description": "No PartenaireId found for this user, impossible to execute the request. Should execute /demande_travaux/demande", "value": {"Error": 404, "Message": "No PartenaireId found for this user"}}}}}}}}}