delete:
  summary: 'WS72 : Supprimer un EAN pour un utilisateur'
  deprecated: true
  parameters:
  - name: Ean
    in: path
    required: true
    description: <PERSON><PERSON> à <PERSON>
    example: 123456
    schema:
      type: integer
  - name: Bp
    in: query
    required: true
    description: Bp de l'utilisateur
    example: 3100018958
    schema:
      type: integer
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              Utilisateur:
                type: string
                description: Uid de l'utilisateur
                example: "Tes.Test.WEpWG"
              ListeEan:
                type: array
                description: Ean de l'utilisateur après suppression
                items:
                  type: object
                  properties:
                    Ean:
                      type: number
                      example: 1234567