get:
  summary: 'WS103: O<PERSON><PERSON><PERSON> le master lié à un Bp'
  parameters:
    - name: IdBp
      in: path
      required: true
      schema:
        type: number
        example: 3100000012
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        default: FR
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: object
            required:
              - Master
            properties:
              Master:
                type: string
          example:
            Master: THIOUX
    '404':
      description: Aucun résultat retrouvé
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 404
              Message:
                type: string
                example: "Aucun résultat retrouvé"
