get:
  summary: 'WS25.1 : Obtenir un dépannage non planifié sur son Id'
  description: 'Obtenir un dépannage non planifié'
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
    - name: Id
      in: path
      required: true
      schema:
        type: string
        example: 000411110401
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - DatePanne
              - Rue
              - CdPostal
              - Localite
              - HeurePriseCharge
              - StatutPanne
              - Id
              - Cause
            properties:
              DatePanne:
                type: integer
                example: 20200727
              Rue:
                type: string
                example: Avenue d'Alès
              RueNum:
                type: string
                example: '70'
              CdPostal:
                type: integer
                example: 4040
              Localite:
                type: string
                example: Herstal
              HeurePriseCharge:
                type: string
                example: 10:55:37
              StatutPanne:
                type: string
                example: Panne en cours
              Id:
                type: string
                example: 000411110401
              DureePanne:
                type: string
                example: 02:58
              Cause:
                type: string
                example: Défaillance(s) technique(s)
              GPS:
                type: object
                properties:
                  Lat:
                    type: number
                    example: 50.67097776498703
                  Long:
                    type: number
                    example: 5.627343799679566