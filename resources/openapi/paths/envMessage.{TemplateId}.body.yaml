get:
  summary: "WS189 : retourne les variables d'une template brevo"
  parameters:
    - name: TemplateId
      in: path
      description: id template
      required: true
      schema:
        type: string
      example: "12345"
  responses:
    '200':
      description: "Variables in the template"
      content:
        application/json:
          schema:
            type: object
            example: {
              "Header": {
                "TEMPLATE_ID": "INDEX_ESTIMATION_COMMUN",
                "EMAIL": "",
                "MOBILE_PHONE": "",
                "PARTNER_ID": "",
                "DATE_FROM": "DATE_FROM",
                "DATE_TO": "DATE_TO",
                "NAME": "NAME",
                "STREET": "STREET",
                "POST_CODE": "POST_CODE",
                "CITY": "CITY"
              },
              "ContentA": [
                {
                  "EAN": "EAN",
                  "ENERGY_TYPE": "ENERGY_TYPE",
                  "TOKEN": "TOKEN",
                  "ContentB": {
                    "SERIAL_NUMBER": "SERIAL_NUMBER"
                  }
                },
                {
                  "EAN": "EAN",
                  "ENERGY_TYPE": "ENERGY_TYPE",
                  "TOKEN": "TOKEN",
                  "ContentB": {
                    "SERIAL_NUMBER": "SERIAL_NUMBER"
                  }
                }
              ]
            }