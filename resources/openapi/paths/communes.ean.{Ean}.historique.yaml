get:
  summary: "WS132 : Obtenir l'historique d'un EAN lié à la commune de l'utilisateur connecté."
  description: L'utilisateur connecté doit soit avoir le rôle 'INDEX_CPT_CONSU' ou 'INDEX_CPT_GERER', ou alors être administrateur de la commune.
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Ean
      in: path
      required: true
      schema:
        type: string
        example: '541460900002617502'
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Date:
                  type: string
                  example: "2022-10-18"
                Index:
                  type: number
                  example: 71930
                Unite:
                  type: string
                  example: "KWH"
                NumCpt:
                  type: string
                  example: "10375117"
                Cadran:
                  type: string
                  example: "HAUT"