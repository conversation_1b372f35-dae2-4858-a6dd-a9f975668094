delete:
  summary: 'WS159 : Supprime un véhicule par UUID'
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - in: path
      name: UUID
      schema:
        type: string
      required: true
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "Véhicule supprimé avec succès"
    '400':
      description: BAD REQUEST
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "UUID invalide ou manquant"
    '500':
      description: INTERNAL SERVER ERROR
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "Erreur interne du serveur"