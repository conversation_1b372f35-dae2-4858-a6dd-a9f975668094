get:
  summary: "WS135 : Obtenir la liste des comptes géré par l'administrateur de la commune."
  security:
    - tokenAuthorizer: [ ]
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Uid:
                  type: string
                  example: "049a9a2c-6b0e-4796-bdd5-427b3b74f063"
                Nom:
                  type: string
                  example: "Nom"
                Prenom:
                  type: string
                  example: "Prénom"
                Departement:
                  type: string
                  example: "Département"
                Actif:
                  type: boolean
                  example: true
                Valide:
                  type: boolean
                  example: true
                Roles:
                  type: array
                  items:
                    type: string
                    example: "RACC_READ"
post:
  summary: "WS137 : Création d'un compte géré par l'administrateur de la commune."
  security:
    - tokenAuthorizer: [ ]
  requestBody:
    content:
      application/json:
        schema:
          type: object
          properties:
            Nom:
              type: string
              example: "Nom"
            Prenom:
              type: string
              example: "Prénom"
            Departement:
              type: string
              example: "Département"
            Fonction:
              type: string
              example: "Fonction"
            Email:
              type: string
              example: "<EMAIL>"
            Tel:
              type: string
              example: "+32496123456"
            Actif:
              type: boolean
              example: true
            Roles:
              type: array
              items:
                type: string
                example: "RACC_READ"
  responses:
    '201':
      description: 201 created
      content:
        application/json:
          schema:
            type: object
            properties:
              Uid:
                type: string
                example: "049a9a2c-6b0e-4796-bdd5-427b3b74f063"
              Nom:
                type: string
                example: "Nom"
              Prenom:
                type: string
                example: "Prénom"
              Departement:
                type: string
                example: "Département"
              Fonction:
                type: string
                example: "Fonction"
              Email:
                type: string
                example: "<EMAIL>"
              Tel:
                type: string
                example: "+32496123456"
              Actif:
                type: boolean
                example: true
              Roles:
                type: array
                items:
                  type: string
                  example: "RACC_READ"