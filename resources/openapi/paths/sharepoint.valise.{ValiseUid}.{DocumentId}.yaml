get:
  summary: "WS180 : Sharepoint Find document. Récupérer un fichier présent sur une valise"
  parameters:
    - name: ValiseUid
      in: path
      description: "Uid de la valise dont on veut les infos"
      required: True
      default: false
      type: string
      example: Client-123456789-QTA-123
    - name: DocumentUid
      in: path
      description: "Nom du document dont on veut les infos"
      required: True
      default: false
      type: string
      example: test-123456.csv
  responses:
    '200':
      description: Contenu du fichier demandé
      content:
        application/json:
          example: '
Hello world ! 
'
    '400 ValiseId':
      description: Valise uid n'existe pas.
      content:
        application/json:
          example: {
            "Message": "Val<PERSON> doesn't exist",
            "error_code": "NO_SUCH_NAME_FOR_VALISEID"
          }
    '400 DocumentId':
      description: Document uid n'existe pas.
      content:
        application/json:
          example: {
            "Message": "Unable to get sharepoint file data",
            "error_code": "DATA_NOT_REACHABLE_IN_SHAREPOINT"
          }
put:
  summary: "WS181 : Sharepoint ManageDocument.PostDocument. Ajoute un document sur la valise spécifiée avec une URL s3 présignée"
  parameters:
    - name: ValiseUid
      in: path
      description: "Uid de la valise dont ou veut upload"
      required: True
      type: string
      example: Client-123456789-QTA-123
    - name: DocumentUid
      in: path
      description: "Nom du document ou veut upload"
      required: True
      type: string
      example: test-123456.csv
  requestBody:
    required: False
    content:
      application/json:
        example:
          {
            "file_data": "url_S3",
            "metadata": { },
            "mimetype": "application/pdf",
            "emplacement": ""
          }
  responses:
    '204':
      description: file uploaded
    '400 ValiseId':
      description: Valise uid n'existe pas.
      content:
        application/json:
          example:
            {
              "Message": "Valise doesn't exist",
              "error_code": "NO_SUCH_NAME_FOR_VALISEID"
            }
    '400 DocumentId':
      description: Document uid n'existe pas.
      content:
        application/json:
          example:
            {
              "Message": "Unable to get sharepoint file data",
              "error_code": "DATA_NOT_REACHABLE_IN_SHAREPOINT"
            }
    '400 Data':
      description: file data et emplacement sont tous les deux vides.
      content:
        application/json:
          example:
            {
              "Message": "file data and emplacement can't be both empty",
              "error_code": "FILEDATA_AND_EMPLACEMENT_EMPTY"
            }