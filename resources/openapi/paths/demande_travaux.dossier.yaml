post:
  summary: >-
    WS59 : Obtenir les informations des dossiers liées à une liste de NumDossier,
    de IdPartenaire, de <PERSON><PERSON> ou d'Adresse
  description: >-
    Une seule liste d'inputs (<PERSON>umD<PERSON>ier, IdPartenaire, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ou UserInfo) doit
    être fournie. <PERSON>, il faut fournir "CdPostal", "Localité", "Rue",
    "NumRue". Si UserInfo, il faut fournir "Lastname", "Firstname", "Phone", "PhoneFixe", "Email", "ContactEmail".
    Il est possible de filtrer les dossiers via "DateFrom" et "DateTo" et d'ignorer les dossiers inférieurs ou
    supérieurs avec "TypeDoss" ("SUP" et "INF" respectivement, "TOUS" par défaut)
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
    - name: Quick
      in: query
      required: false
      description: If True, only return dossier info without actions and sub-dossiers
      schema:
        type: string
        example: "True"
        default: "False"
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
            - NumDossier
          properties:
            NumDossier:
              type: array
              items:
                type: string
              example: [ "645181" ]
            Ean:
              type: array
              items:
                type: string
              example: [ "5414xxxxxxxxxxxxxx" ]
            IdPartenaire:
              type: array
              items:
                type: string
              example: [ "4100635917" ]
            Adresse:
              type: array
              items:
                type: object
              required:
                - CdPostal
                - Localite
                - Rue
                - NumRue
              properties:
                CdPostal:
                  type: string
                  example: "4053"
                Localite:
                  type: string
                  example: "Embourg"
                Rue:
                  type: string
                  example: "Rue de Bleurmont"
                NumRue:
                  type: string
                  example: "35"
            UserInfo:
              type: array
              items:
                type: object
              required:
                - Lastname
                - Firstname
                - Phone
                - PhoneFixe
                - Email
                - ContactEmail
              properties:
                Lastname:
                  type: string
                  example: "DOE"
                Firstname:
                  type: string
                  example: "JOHN"
                Phone:
                  type: string
                  example: "+123456789"
                PhoneFixe:
                  type: string
                  example: "+987654321"
                Email:
                  type: string
                  example: "<EMAIL>"
                ContactEmail:
                  type: string
                  example: "<EMAIL>"
            TypeDoss:
              type: string
              example: "SUP"
            DateFrom:
              type: string
              format: date
              example: "2024-01-01"
            DateTo:
              type: string
              format: date
              example: "2024-12-31"
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          schema:
            type: object
            required:
              - Liste
            properties:
              Liste:
                type: array
                items:
                  type: object
                  required:
                    - NumDossier
                    - NumDossierSup
                    - NumDossierInf
                    - TypeDossier
                    - Ean
                    - IdPartenaire
                    - CdPostal
                    - IdRadRue
                    - IdLocalite
                    - IdRadLocalite
                    - Localite
                    - NumRue
                    - NumComp
                    - NumEtape
                    - LibEtape
                    - CodeStatut
                    - LibStatut
                    - IdAdresse
                    - DateDossier
                    - Coordinateur
                    - Liste
                    - DossierInf
                  properties:
                    NumDossier:
                      type: array
                      items:
                        type: string
                      example: [ "2120564" ]
                    Coordinateur:
                      type: object
                      properties:
                        Nom:
                          type: string
                          example: "LENAERTS"
                        Prenom:
                          type: string
                          example: "CRYSTAL"
                        Tel:
                          type: string
                          example: "+3242201203"
                    NumDossierSup:
                      type: string
                      example: ""
                    NumDossierInf:
                      type: array
                      items:
                        type: string
                      example: [ ]
                    TypeDossier:
                      type: string
                      example: "R01"
                    TypeDossierCode:
                      type: string
                      example: "NOUV"
                    Ean:
                      type: integer
                      example: null
                    IdPartenaire:
                      type: integer
                      example: 4100631581
                    CdPostal:
                      type: integer
                      example: 4845
                    IdRue:
                      type: string
                      example: "10602"
                    IdRadRue:
                      type: integer
                      example: 121573
                    Rue:
                      type: string
                      example: "Chemin de la Fontaine"
                    IdLocalite:
                      type: string
                      example: "306"
                    IdRadLocalite:
                      type: integer
                      example: 113204
                    Localite:
                      type: string
                      example: "Sart-lez-Spa"
                    NumRue:
                      type: integer
                      example: 16
                    NumComp:
                      type: string
                      example: ""
                    NumEtape:
                      type: integer
                      example: 4
                    LibEtape:
                      type: string
                      example: "Devis"
                    CodeStatut:
                      type: integer
                      example: 44
                    LibStatut:
                      type: string
                      example: "Devis réalisé - En attente de votre paiement"
                    IdAdresse:
                      type: string
                      example: "0037661282"
                    DateDossier:
                      type: string
                      example: "02/04/2024"
                    TypeFluide:
                      type: string
                      example: "Elec"
                    WS109Actions:
                      type: array
                      items:
                        type: object
                        properties:
                          DateAccord:
                            type: string
                            example: ""
                          Timestamp:
                            type: number
                            example: 1631028134.197715
                          TypeAction:
                            type: string
                            example: "UPLOAD_DOC"
                    SynergieCile:
                      type: boolean
                      example: false
                    SynergieCmh:
                      type: boolean
                      example: true
                    Liste:
                      type: array
                      items:
                        type: object
                        properties:
                          CodeInfo:
                            type: string
                            example: "44_1"
                          LibInfo:
                            type: string
                            example: "Montant du devis"
                          TypeInfo:
                            type: string
                            example: "MT"
                          ValeurInfo:
                            type: number
                            example: 122.21
                          LibInfoStatut:
                            type: string
                            example: ""
                    DossierInf:
                      type: array
                      items:
                        type: object
                      example: [ ]