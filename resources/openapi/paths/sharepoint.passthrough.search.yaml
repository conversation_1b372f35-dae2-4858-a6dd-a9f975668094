post:
  summary: "WS194 : Passthrough to SharePoint search WS."
  description: |
    Provides search results according to passed JSON request.  
    Source WS Sharepoint documentation : [here](https://dev.azure.com/RESABE/Valises%20SharePoint/_wiki/wikis/Valises-SharePoint.wiki/281/Recherche)
  requestBody:
    content:
      application/json:
        name: IRequest
        description: Search query to performe against SharePoint
        schema:
          type: object
          properties:
            header:
              type: object
              description: Additional metadata information to performe query search.
              required: true
              properties:
                dataActionDescription:
                  type: string
                  description: Short description or naming operation.
                  example: recherche-valise-action
                  required: true
                action:
                  type: string
                  description: Operation to performe with passed query.
                  example: search
                  required: true
                userId:
                  type: string
                  description: Id of user performing operation.
                  example: 183
                  required: true
                timestamp:
                  type: string
                  description: Timestamp when application call the operation.
                  example: '2020-04-17T07:46:02.670Z'
                  required: true
            searchDescription:
              type: object
              description: Description of query to performe.
              required: true
              properties:
                actionId:
                  type: string
                  description: Unique GUID id to identify operation.
                  example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
                  required: true
                pageSize:
                  type: number
                  description: Number of elements to get.
                  example: 10
                  required: true
                criterias:
                  type: object
                  description: Search operation criteria.
                  required: true
                  properties:
                    fieldName:
                      type: string
                      description: Field name to search for.
                      required: true
                      enum:
                        - Title
                        - BPID
                        - DOCUID
                        - DOCCONTEXT
                        - DOCDESC
                        - DOCTYPE
                        - DOCSOURCE
                        - Path
                        - CreatedBy
                        - Created
                        - Modified
                        - ModifiedBy
                    operator:
                      type: string
                      description: Operator to apply.
                      required: true
                      enum:
                        - All
                        - And
                        - Or
                        - eq
                        - Equal
                        - Ne
                        - Le
                        - Lt
                        - Gt
                        - Ge
                        - Contains
                    value:
                      type:
                        - string
                        - number
                        - ISearchCriteria
                      description: Value to search or nested search criteria.
                      required: true
                orderby:
                  type: object
                  description: Order by criteria of how to present results.
                  required: true
                  properties:
                    fieldName:
                      type: string
                      description: Field name to order by on.
                      example: DOCTYPE
                      required: true
                    direction:
                      type: string
                      description: Ascending or descending order to show results.
                      example: ascending
                      required: true
  responses:
    '200':
      description: Search against SharePoint according to passed object query
      content:
        application/json:
          schema:
            type: object
            properties:
              isError:
                type: boolean
                description: True or false if error occurred during a call.
                example: true/false
                required: true
              message:
                type: string
                description: User friendly error message if occurred.
                example: Some useful message
                required: true
              status:
                type: number
                description: Optional if error occurred error number.
                example: 403
                required: false
              results:
                type: array
                description: Optional if no error occurred, results, according to request.
                example:
                  - id: 0
                    m: message
                required: false
            