get:
  summary: "WS197 : Obtenir la liste des cadrans d'EAN liés à la commune de l'utilisateur connecté."
  description: "L'utilisateur connecté doit soit avoir le rôle 'INDEX_CPT_CONSU' ou 'INDEX_CPT_GERER', ou alors être administrateur de la commune."
  security:
    - tokenAuthorizer: [ ]
  responses:
    '200':
      description: Une liste de cadrans
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Ean:
                  type: string
                  example: "541449011000143560"
                NumCpt:
                  type: string
                  example: "000000000064285947"
                EQUNR:
                  type: string
                  example: "000000000501356427"
                LOGIKZW:
                  type: string
                  example: "000000000002456657"
                CatCadran:
                  type: string
                  example: "LO"
                STANZVOR:
                  type: string
                  example: "01"
                STANZNAC:
                  type: string
                  example: "03"
                ZWZUART:
                  type: string
                  nullable: true
                  example: null
                ETDZ_ZWNUMMER:
                  type: string
                  example: "001"
                MATNR:
                  type: string
                  example: "000000000000100940"
                ZWGRUPPE:
                  type: string
                  example: "00000097"
                CodeCadran:
                  type: string
                  example: "1.6.1"
                ANZART:
                  type: string
                  example: "DD"
                ANLAGE:
                  type: string
                  example: "4100746852"
                CatTarif:
                  type: string
                  example: "EDA_LO"
                lv_nb_lines:
                  type: integer
                  example: 6
                ATWRT:
                  type: string
                  example: "E"
                Cadran:
                  type: string
                  example: "NUIT"