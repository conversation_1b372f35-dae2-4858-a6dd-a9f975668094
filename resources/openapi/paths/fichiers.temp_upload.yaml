get:
  summary: 'WS112 : Uploader un fichier temporaire et récupérer son url'
  description: |
    L'upload du fichier se fait sur l'url S3 pré-signée renvoyée dans la réponse, par un PUT avec un header Content-Type:*
    Permet d'uploader un document sur S3 de façon temporaire (1h).  
    L'url retournée n'est valide qu'un certain temps (1h).
  security:
  - tokenAuthorizer: []
    ghostAuthorizer: []
  parameters:
  - name: FileName
    description: Nom du document à uploader
    in: query
    required: true
    schema:
      type: string
      example: "contrat_de_raccordement.pdf"
  - name: ContentType
    description: content-type du fichier
    in: query
    required: false
    schema:
      type: string
      example: "application/pdf"
  responses:
    '201':
      description: CREATED
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            properties: 
              PutUrl:
                type: string
                description: URL d'upload du fichier
                example: "https://my-resa-api-production-temp-storage.s3.amazonaws.com/ws112/aecbaba4-ceda-44b9-91bc-873592b3daa3.pdf?AWSAccessKeyId=AKIAS7HADRZWGOC5WX4C&Signature=W2vu1ts8b7dt4lQjHGxwN%2Bod1r4%3D&Expires=1635180877"
              GetUrl:
                type: string
                description: URL de download du fichier
                example: "https://my-resa-api-production-temp-storage.s3.amazonaws.com/ws112/aecbaba4-ceda-44b9-91bc-873592b3daa3.pdf?AWSAccessKeyId=AKIAS7HADRZWGOC5WX4C&Signature=W2vu1ts8b7dt4lQjHGxwN%2Bod1r4%3D&Expires=1635180877"
