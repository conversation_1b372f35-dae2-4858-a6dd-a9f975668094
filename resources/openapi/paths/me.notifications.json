{"get": {"summary": "WS15 : O<PERSON><PERSON>r les notifications communiquées à un utilisateur", "parameters": [{"name": "<PERSON>ais<PERSON><PERSON>", "in": "query", "description": "Recherche les notifications antérieures ou égales au nombre de jours indiqué", "required": false, "schema": {"type": "integer", "example": "5"}}, {"name": "ReadStatus", "in": "query", "description": "Recherche par état de lecture des notifications", "required": false, "schema": {"type": "integer", "example": 0}}], "security": [{"tokenAuthorizer": []}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "array", "required": [], "items": {"$ref": "#/components/schemas/Notification"}}, "example": [{"Uid": "164c1695-0de6-413e-abc5-c2ea64bca53a", "Link": null, "ReadTimestamp": 1598016764.734614, "Message": "Notification de test.", "Type": "Informative", "Priority": "low", "Timestamp": 1598014064.372304}]}}}}}, "post": {"summary": "Permet de créer une notification associée à un utilisateur authentifié.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["Message"], "properties": {"Message": {"type": "string"}, "Priority": {"type": "string", "enum": ["low", "medium", "high"]}, "Type": {"type": "string"}, "Link": {"type": "string"}}}, "example": {"Message": "Notification de test.", "Priority": "low", "Type": "Informative", "Link": null}}}}, "parameters": [], "security": [{"tokenAuthorizer": []}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}, "example": {"Uid": "164c1695-0de6-413e-abc5-c2ea64bca53a", "Link": null, "ReadTimestamp": null, "Message": "Notification de test.", "Type": "Informative", "Priority": "low", "Timestamp": 1598014064.372304}}}}}}}