post:
  summary: "WS16 : Authentifie un utilisateur déjà enregistré à partir de son username et de son password"
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
            - Username
            - Password
          properties:
            Username:
              type: string
            Password:
              type: string
        example:
          Username: She<PERSON>.<EMAIL>
          Password: Welcome123
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            description: Authentication token for the user
            properties:
              AccessToken:
                type: string
              TokenType:
                type: string
                enum:
                  - bearer
              ExpiresIn:
                type: integer
              Resource:
                type: string
                enum:
                  - urn:microsoft:userinfo
              RefreshToken:
                type: string
              RefreshTokenExpiresIn:
                type: integer
              Scope:
                type: string
                enum:
                  - openid
              IdToken:
                type: string
          example:
            AccessToken: someverylongtoken
            TokenType: bearer
            ExpiresIn: 3600
            Resource: urn:microsoft:userinfo
            RefreshToken: someverylongtoken
            RefreshTokenExpiresIn: 28800
            Scope: openid
            IdToken: someverylongtoken
    '400':
      description: 400 response
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          example:
            Error: invalid_grant
            ErrorDescription: "MSIS9659: Invalid 'username' or 'password'"
