get:
  summary: "WS217 : Récupération des objectifs et de la consommation réelle d'énergie"
  description: "Retourne la consommation cible basée sur le profil énergétique ainsi que la consommation réelle du mois précédent."
  parameters:
    - name: AddressHash
      description: AddressHash used to retrieve all linked ean
      in: query
      required: true
      schema:
        type: string
        example: 9558c458589c6e71868a47fd240f9378ea764b30c45c5404cc8fc747f5ad77a6
  responses:
    '404':
      description: Not Found
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            example:
              Error: 404
              Message: "AddressHash not found in query params"
    200:
      description: "Consommation cible et réelle récupérées avec succès."
      content:
        application/json:
          schema:
            type: object
            properties:
              ProfileTargetLow:
                type: number
                description: "Valeur basse de la cible de consommation d'énergie."
                example: 0
              ProfileTargetHigh:
                type: number
                description: "Valeur haute de la cible de consommation d'énergie (valeur idéale profil * 2)."
                example: 200
              RealConsumption:
                type: number
                description: "Consommation réelle du mois précédent (consommation - injection)."
                example: 100