post:
  summary: 'WS03.2 : Valider un numéro de telephone avec un code reçu par SMS'
  parameters:
    - name: "Token"
      in: "query"
      description: "Le token reçu dans le mail d'activation. Requis so non authentifié."
      required: false
      schema: 
        type: "string"
    - name: "Authorization"
      in: "header"
      description: "Auth token de l'utilisateur. Requis si authentifié."
      required: false
      schema: 
        type: "string"
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
            - Code
          properties:
            Code:
              type: string
              description: Code reçu par SMS
              example: "701510"
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          schema:
            type: object
            properties:
              Type:
                type: string
                description: Texte libre retourné à la validation à des fin de contexte pour le client
                example: "Texte libre"