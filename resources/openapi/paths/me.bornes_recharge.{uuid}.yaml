put:
  summary: WS174 Modifier les détails de Borne Recharge
  description: Met à jour une 'borne' basée sur l'UUID donné après validation des droits d'accès et des règles de mise à jour.
  parameters:
    - in: header
      name: Authorization
      required: true
      schema:
        type: string
      description: Token d'authentification.
    - in: path
      name: uuid
      required: true
      schema:
        type: string
      description: L'UUID de la 'borne' à mettre à jour.
  requestBody:
    description: Détails mis à jour de la 'borne'.
    content:
      application/json:
        schema:
          type: object
          properties:
            Ean:
              type: string
              example: "541456700002569958"
            Borne:
              type: object
              properties:
                Adresse:
                  type: object
                  properties:
                    Numero:
                      type: string
                      example: "5"
                    Rue:
                      type: string
                      example: "Avenue Victor-Hugo"
                    CodePostal:
                      type: string
                      example: "4000"
                    Commune:
                      type: string
                      example: "LIEGE"
                    Pays:
                      type: string
                      example: "Belgique"
                Marque:
                  type: string
                  example: "Tesla"
                Serial:
                  type: string
                  example: "123456"
                Ean:
                  type: string
                  example: "541456700002569958"
                Utilisation:
                  type: object
                  properties:
                    Libelle:
                      type: string
                      example: "Privée"
                    Valeur:
                      type: string
                      example: "PRIVATE"
                Puissance:
                  type: number
                  example: 22.0
                Photo:
                  type: string
                  nullable: true
                Modele:
                  type: string
                  example: "123M"
                Bidirectionnelle:
                  type: boolean
                  example: false
                Active:
                  type: boolean
                  example: true
                DateDesactivation:
                  type: string
                  example: "2024-11-01"
                DateActivation:
                  type: string
                  example: "2023-11-01"
            Entreprise:
              type: object
              properties:
                FormeJuridique:
                  type: string
                  nullable: true
                Numero:
                  type: string
                  nullable: true
                Nom:
                  type: string
                  example: "New tesst new life230"
                Acronyme:
                  type: string
                  nullable: true
            DateCreation:
              type: string
              example: "2023-12-07T08:24:59.599310"
            Uuid:
              type: string
              example: "6aaff24560134572869f50e9c7be1612"
            Demandeur:
              type: object
              properties:
                Nom:
                  type: string
                  example: "Michela"
                Prenom:
                  type: string
                  example: "Jean"
                Email:
                  type: string
                  example: "<EMAIL>"
                Telephone:
                  type: string
                  example: "496123456"
            Installateur:
              type: object
              properties:
                Nom:
                  type: string
                  example: "Solar panel master X"
                Email:
                  type: string
                  nullable: true
                Telephone:
                  type: string
                  nullable: true
  responses:
    200:
      description: Mise à jour réussie.
      content:
        application/json:
          schema:
            type: object
            properties:
              Demandeur:
                type: object
                properties:
                  Nom:
                    type: string
                    example: "Michela"
                  Prenom:
                    type: string
                    example: "Jean"
                  Email:
                    type: string
                    example: "<EMAIL>"
                  Telephone:
                    type: string
                    example: "496123456"
              Borne:
                type: object
                properties:
                  Adresse:
                    type: object
                    properties:
                      Rue:
                        type: string
                        example: "Avenue Victor-Hugo"
                      Numero:
                        type: string
                        example: "5"
                      CodePostal:
                        type: string
                        example: "4000"
                      Commune:
                        type: string
                        example: "LIEGE"
                      Pays:
                        type: string
                        example: "Belgique"
                  Ean:
                    type: string
                    example: "541456700002569958"
                  Marque:
                    type: string
                    example: "Tesla"
                  Modele:
                    type: string
                    example: "123M"
                  Utilisation:
                    type: object
                    properties:
                      Libelle:
                        type: string
                        example: "Privée"
                      Valeur:
                        type: string
                        example: "PRIVATE"
                  Bidirectionnelle:
                    type: boolean
                    example: false
                  Serial:
                    type: string
                    example: "123456"
                  Puissance:
                    type: number
                    example: 22
                  Photo:
                    type: string
                    nullable: true
                  Date:
                    type: string
                    example: "2023-11-01"
              Ean:
                type: string
                example: "541456700002569958"
              Uuid:
                type: string
                example: "a38f4d67b4b748b494c1834b7413fac3"
              Installateur:
                type: object
                properties:
                  Nom:
                    type: string
                    example: "Solar panel master X"
                  Email:
                    type: string
                    nullable: true
                  Telephone:
                    type: string
                    nullable: true
              Entreprise:
                type: object
                properties:
                  Numero:
                    type: string
                    nullable: true
                  Nom:
                    type: string
                    example: "New tesst new life230"
                  Acronyme:
                    type: string
                    nullable: true
                  FormeJuridique:
                    type: string
                    nullable: true
              DateCreation:
                type: string
                example: "2023-12-07T09:45:25.390050"
              Supprimer:
                type: boolean
                example: false
              TypeDemande:
                type: object
                properties:
                  Valeur:
                    type: string
                    example: "ACTIVATE"
                  Libelle:
                    type: string
                    example: "La mise en service d'une borne de recharge"
delete:
  summary: WS175 Retirer une entrée de Borne Recharge
  description: Supprime une 'borne' basée sur l'UUID donné après avoir validé les droits d'accès.
  parameters:
    - in: header
      name: Authorization
      required: true
      schema:
        type: string
      description: Token d'authentification.
    - in: path
      name: uuid
      required: true
      schema:
        type: string
      description: L'UUID de la 'borne' à supprimer.
  responses:
    204:
      description: Suppression réussie. No Content.
    401:
      description: Non autorisé. Token d'authentification manquant ou invalide.
    403:
      description: Interdit. L'EAN ne figure pas dans la liste du compte.
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: NOT_AUTHORIZED
    404:
      description: borne non trouvée.
  tags:
    - Bornes Recharge