{"post": {"summary": "<PERSON><PERSON><PERSON> un compte entrepreneur", "description": "caller must have admin permissions", "security": [{"tokenAuthorizer": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"Email": {"type": "string", "format": "email"}}}}}}, "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"Uid": {"type": "string"}}, "required": ["<PERSON><PERSON>"]}}}}, "409": {"description": "409 Conflict", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"example": {"error": 409, "message": "This email is already taken"}}}}}}}