get:
  security:
    - basicAuthorizer: [ ]
  parameters:
    - name: Id
      description: Id du chantier
      in: path
      required: true
      schema:
        type: string
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: object
            required:
              - Id
            properties:
              Id:
                type: string