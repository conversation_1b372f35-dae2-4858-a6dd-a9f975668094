post:
  summary: 'WS03.1 : Obtenir code de validation du numéro de telephone par SMS'
  parameters:
    - name: "Token"
      in: "query"
      description: "Le token reçu dans le mail d'activation. Requis so non authentifié."
      required: false
      schema: 
        type: "string"
    - name: "Authorization"
      in: "header"
      description: "Auth token de l'utilisateur. Requis si authentifié."
      required: false
      schema: 
        type: "string"
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
            - Phone
          properties:
            Phone:
              type: string
              description: Numéro de téléphone à valider
              example: "+32496908712"
            Type:
              type: string
              description: Texte libre retourné à la validation à des fin de contexte pour le client
              example: "Texte libre"
  responses:
    '200':
      description: 200 OK