get:
  summary: "Génère un fichier PDF à partir des données HTML d'un véhicule spécifique."
  security:
    - tokenAuthorizer: [ ]
  description: |
    Ce service prend un UUID de véhicule en tant que paramètre de chemin, récupère les données correspondantes et génère un PDF. Le PDF est ensuite encodé en Base64 et renvoyé.
  parameters:
    - name: vehicle_uuid
      in: path
      required: true
      schema:
        type: string
      description: L'UUID du véhicule pour lequel générer un PDF.
    - name: 'Accept'
      in: headers
      required: true
      schema:
        type: string
      description: A ajouter, accepté les pdf dans le hearders pour que AWS renvoie le bon format.
      example: 'application/pdf'
  responses:
    '200':
      description: "OK, PDF généré avec succès."
      content:
        application/pdf:
          schema:
            type: string
            format: base64
      headers:
        Content-Type:
          schema:
            type: string
          example: 'application/pdf'
        content-disposition:
          schema:
            type: string
          example: 'attachment; filename=VehicleSummary.pdf'
    '400':
      description: "BAD REQUEST, données HTML invalides ou manquantes."
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "Données du véhicule invalides ou manquantes"
    '404':
      description: "NOT FOUND, chemin non trouvé."
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 404
              Message:
                type: string
                example: "Chemin non trouvé"
    '500':
      description: "INTERNAL SERVER ERROR, une erreur s'est produite pendant la génération du PDF."
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "Erreur interne du serveur"