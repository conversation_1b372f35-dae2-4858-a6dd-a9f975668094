get:
  summary: "WS55 : Obtenir les détails d'un contrat appartenant à un utilisateur authentifié"
  description: "Obtenir les détails d'un contrat appartenant à un utilisateur authentifié"
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Id
      description: Id du contrat
      in: path
      required: true
      schema:
        type: integer
        example: 220115305
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
              - Contract
            properties:
              Contract:
                type: object
                required:
                  - ContractNbr
                  - Detail
                  - DeliveryPoints
                properties:
                  ContractNbr:
                    type:
                      - string
                      - integer
                  Detail:
                    type: object
                    required:
                      - Framework
                      - BillingContract
                      - EnergyType
                      - ProductCode
                      - ContractCategory
                      - ContractStatusCode
                      - ContractualStartDate
                      - DurationCode
                    properties:
                      Framework:
                        type: string
                      BillingContract:
                        type: string
                      EnergyType:
                        type: string
                      ProductCode:
                        type: string
                      ContractCategory:
                        type: string
                      ContractStatusCode:
                        type: string
                      ContractualStartDate:
                        type: string
                      DurationCode:
                        type:
                          - integer
                          - string
                  DeliveryPoints:
                    type: object
          example:
            Contract:
              ContractNbr: '220115305'
              ContractId: '173519'
              CustomerNbr: '110106099'
              Detail:
                Framework: 'false'
                BillingContract: 'false'
                EnergyType: EL
                DeliveryCategoryCode: FOURNX - MOZA
                ProductCode: EL
                ContractCategory: FOURNX
                SubscriptionDate: 2011-11-06+01:00
                ContractStatusCode: TERMINE
                ContractualStartDate: 2011-11-06+01:00
                DurationCode: '-1'
                ContractualEndDate: { }
                InstalmentPeriodicityCode: '0'
                SettlementPeriodicityCode: 'null'
              Invoicing:
                PaymentModeCode: VIR
                InvoicingEmail: null
              PreviousContract:
                PreviousProviderCode: null
                DefaultProvider: 'false'
                TermUndetermined: 'false'
              CustomizedInformations:
                ContractUserField:
                  Code: PreviousSupplierRef
                  Value: '**********'
              Rates:
                RateGroup:
                  ApplicationFromDate: 2011-11-06+01:00
                  StandardRates:
                    StandardRate:
                      RateCode: EL-FOURN-X
              DeliveryPoints:
                DeliveryPoint:
                  Gsrn: '541449020710423206'
                  Direction: CONSUMPTION
                  GridOperatorGln: '5414567999991'
                  GridOperatorCode: RESA S.A.
                  Slp: S11
                  ReadingFrequency: YEAR
                  GridRate: '930'
                  MeterIsOpen: 'true'
                  IsAMove: 'false'
                  EmptyHouse: 'false'
                  BeginDate: 2011-11-06+01:00
                  ContractConfirmed: 'false'
                  Indexes:
                    Index:
                      - MeterNr: 000000000000424228
                        TimeframeCode: TOTAL_HOUR
                        Value: '65229.00'
                        Direction: CONSUMPTION
                        IndexDate: 2014-06-10+02:00
                      - MeterNr: 000000000000424228
                        TimeframeCode: TOTAL_HOUR
                        Value: '64475.00'
                        Direction: CONSUMPTION
                        IndexDate: 2013-09-19+02:00
                      - MeterNr: 000000000000424228
                        TimeframeCode: TOTAL_HOUR
                        Value: '64259.10'
                        Direction: CONSUMPTION
                        IndexDate: 2012-09-03+02:00
                  Intervention: null
                  ExpressPlanning: 'false'
                  SmartMeter: 'false'