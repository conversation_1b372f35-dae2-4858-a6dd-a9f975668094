post:
  summary: 'WS40: Obtenir une date pour un rendez vous ou visite de raccordement'
  parameters:
  - in: "header"
    name: "Langue"
    required: false
    schema:
      type: string
      default: FR
    example: FR
  requestBody:
    description: |
      Le body de la requete doit contenir une liste de demande, avec pour chaque demande :
      |Champ|	Type|	Longueur|	Valeurs|	Mandatory|
      |-|-|-|-|-|
      |Langue|	CHAR|	2|	FR / DE|	Y|
      |NumDossier|	CHAR|	12|	-| 	Y|
      |DatePlanif|	CHAR|	10|	JJ/MM/AAAA|	O|
      |TypeAction|	CHAR|	10|	-|	Y|

      TypeAction doit contenir :
      *	DATE : pour trouver X dates à planifier
      *	DATE_MODIF : pour trouver X dates alors qu’une autre planification est en cours.
      *	PLAN : pour réaliser la planification
      *	MODIF : pour modifier la date de planification
      *	DATE_ANNUL : pour annuler la planification

      DatePlanif doit être mandatory avec TypeAction = DATE_MODIF/PLAN/MODIF/DATE_ANNUL.  
      DatePlanif contiendra la date de planification en cours avec DATE_MODIF/DATE_ANNUL.  
      DatePlanif contiendra la date à planifier avec PLAN/MODIF.
    content:
      application/json:
        schema:
          type: object
        example:
          Liste:
          - NumDossier: '3012427'
            TypeAction: DATE
          - NumDossier: '3015308'
            DatePlanif: 09/07/2021
            TypeAction: PLAN
  responses:
    '200':
      description: |
        |Champ|	Type|	Longueur|	Valeurs|	Mandatory|
        |-----|-----|---------|---------|---------|
        |NumDossier|	CHAR|	12|-|	 	Y|
        |CodeRetour	|CHAR	|10	|NEW_DATE / DATE_PLANIF / DATE_ANNUL|Y|
        |Liste|-|-|-|-|
        |---- Date	|CHAR|	10|	JJ/MM/AAAA|	Y|
      content:
        application/json:
          schema:
            type: object
          example:
            Liste:
              - NumDossier: '3012427'
                CodeRetour: NEW_DATE
                Liste:
                  - Date: 09/07/2021
                  - Date: 12/07/2021
                  - Date: 13/07/2021
                  - Date: 14/07/2021
                  - Date: 15/07/2021
              - NumDossier: '3015308'
                CodeRetour: DATE_PLANIF
                Liste:
                  - Date: 09/07/2021
