{"patch": {"summary": "WS15 : Modifier le status d'une notification envoyée à un utilisateur", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["NotificationUid", "Read"], "properties": {"NotificationUid": {"type": "string"}, "Read": {"type": "string"}}}, "example": {"NotificationUid": "23483e6d-2edd-431b-b712-3bdf8c05e6b9", "Read": "true"}}}}, "parameters": [], "security": [{"tokenAuthorizer": []}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}, "example": {"NotificationUid": "164c1695-0de6-413e-abc5-c2ea64bca53a", "Link": null, "Message": "Notification de test.", "Type": "Informative", "Priority": "low", "Timestamp": 1598014064.372304}}}}}}}