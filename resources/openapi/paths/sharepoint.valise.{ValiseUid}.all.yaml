get:
  summary: "WS178 : Sharepoint List Document. Return une liste de document présent dans la valise"
  parameters:
    - name: Val<PERSON>Uid
      in: path
      description: "Uid de la valise dont on veut les infos"
      required: True
      default: false
      type: string
      example: Client-123456789-QTA-123
  responses:
    '200':
      description: Liste de fichier présent dans la valise.
      content:
        application/json:
          example: '
   ["file1.csv", "file2.txt"]
'
    '400':
      description: Valise uid n'existe pas.
      content:
        application/json:
          example:
            example: {
              "Message": "Val<PERSON> doesn't exist",
              "error_code": "NO_SUCH_NAME_FOR_VALISEID"
            }