{"post": {"summary": "WS03: Envoyer un e-mail de validation de reset du password", "parameters": [{"name": "Callback", "in": "query", "description": "The URL to callback from the mail", "required": true, "schema": {"type": "string"}, "example": "https://my.resa.be/recover-password"}, {"name": "Email", "in": "query", "description": "The email from the user", "required": true, "schema": {"type": "string"}, "example": "<EMAIL>"}, {"name": "Sandbox", "in": "query", "description": "Whether the email should actually be sent or not", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "OK", "headers": {}, "content": {"application/json": {"schema": {"type": "object", "required": ["Email"], "properties": {"Email": {"type": "string"}}}, "example": {"Email": "<EMAIL>"}}}}}}}