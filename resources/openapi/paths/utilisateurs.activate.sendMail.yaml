post:
  summary: "WS04.1 : Envoie un mail de vérification à l'adresse e-mail fournie par l'utilisateur"
  security:
    - ghostAuthorizer: [ ]
  parameters:
    - name: Callback
      in: query
      description: The URL to callback from the mail (or in case of success in ContactEmail validation flow)
      required: true
      schema:
        type: string
      example: https://my.resa.be/activate
    - name: Callback2FA
      in: query
      description: The URL to callback from the mail if 2FA needed
      required: false
      schema:
        type: string
      example: https://my.resa.be/validation-sms
    - name: ErrorCallback
      in: query
      description: The URL to callback in case of error
      required: false
      schema:
        type: string
      example: https://my.resa.be/validation-error
    - name: ContactEmail
      in: query
      description: To activate ContactEmail validation flow
      schema:
        type: boolean
      example: 'false'
    - name: Sandbox
      in: query
      description: Whether the email should actually be sent or not
      schema:
        type: boolean
      example: 'true'
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
            - Email
          properties:
            Email:
              type: string
              format: email
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            description: Identifie l'utilisateur a qui le mail d'activation a ete
              envoye
            required:
              - SessionId
              - Email
            properties:
              SessionId:
                type: string
              Email:
                type: string
                format: email
          example:
            SessionId: ghost_265502893
            Email: <EMAIL>
    '401':
      description: 401 Unauthorized
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          example:
            Error: 401
            Message: Unkown user `ghost_Fakeuser`