post:
  summary: 'WS79 : Envoyer une demande via formulaire'
  description: La nature de la demande dépend du `Type` et de la `Section` / `SousSection`
  externalDocs:
    url: J:/IT/DOCUMENTATION/SERVICES%20ET%20APPLICATIONS/API/LIS-CFG-templates.xlsx
  parameters:
    - name: Sandbox
      in: query
      description: Le mail n'est pas envoyé en mode sandbox
      required: false
      schema:
        type: boolean
        example: true
        default: false
    - name: Type
      in: query
      description: |
        Conditionne la façon dont le formulaire est enregistré.  
        MAIL : Le contenu du formulaire est envoyé par mail à l'adresse donnée dans le champ "Destinataire"  
        SF : Le contenu du formulaire est envoyé dans un ticket sur SalesForce  
        DB : Le contenu du formulaire est enregistré dans une DB pour usage futur
      required: true
      schema:
        type: string
        enum:
          - MAIL
          - SF
          - DB
        example: MAIL
        default: MAIL
  requestBody:
    content:
      application/json:
        schema:
          type: object
          properties:
            Section:
              example: Information sur mes paiements
            SousSection:
              example: Information sur mes paiements
            Destinataire:
              example: <EMAIL>
              description: <PERSON><PERSON>, ignore le choix de mail selon section/sous-section
            Reference:
              example: 0
            Ean:
              example: 0
            Nom:
              example: ''
            Prenom:
              example: ''
            Email:
              example: ''
            Phone:
              example: ''
            Description:
              example: ''
            Files:
              type: array
              items:
                type: object
                properties:
                  FileData:
                    example: zjfjkfhfh==
                    description: File data in B64, only if no FileUrl
                  FileUrl:
                    example: https://file.somewhere.com
                    description: File url, only if no FileData
                  DocumentName:
                    example: foo
                  Extension:
                    example: pdf
            CustomsData:
              type: object
              description: Dictionnaire de données personnalisées qui sera remis en HTML dans le formulaire final
              example: {
                "Adresse": {
                  "Rue": "Champs-Élysées",
                  "Numero": "123",
                  "Autre": [ "1","2" ],
                  "Test2": "hello"
                },
                "Nom": "Doe",
                "Prenom": "John"
              }
  responses:
    '201':
      description: OK
