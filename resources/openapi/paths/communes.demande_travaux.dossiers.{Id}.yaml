get:
  summary: "WS134 : Obtenir les informations d'un dossier liées à une commune sur base de son id"
  description: L'utilisateur connecté doit soit avoir le rôle 'RACC_CONSU' ou 'RACC_GERER', ou alors être administrateur de la commune.
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Id
      in: path
      required: true
      description: Id of the wanted dossier
      schema:
        type: string
        example: 000123456789
        default: False
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          schema:
            required:
              - NumDossier
              - NumDossierSup
              - NumDossierInf
              - TypeDossier
              - Ean
              - IdPartenaire
              - CdPostal
              - IdRadRue
              - IdLocalite
              - IdRadLocalite
              - Localite
              - NumRue
              - NumComp
              - NumEtape
              - LibEtape
              - CodeStatut
              - LibStatut
              - IdAdresse
              - DateDossier
              - DossierInf
            properties:
              NumDossier:
                type: array
                items:
                  type: string
              NumDossierSup:
                type: string
              NumDossierInf:
                type: array
                items:
                  type: string
              TypeDossier:
                type: string
              TypeDossierCode:
                type: string
              Ean:
                type: integer
              IdPartenaire:
                type: integer
              CdPostal:
                type: integer
              IdRue:
                type: string
              IdRadRue:
                type: integer
              Rue:
                type: string
              IdLocalite:
                type: string
              IdRadLocalite:
                type: integer
              Localite:
                type: string
              NumRue:
                type: integer
              NumComp:
                type: string
              NumEtape:
                type: integer
              LibEtape:
                type: string
              CodeStatut:
                type: integer
              LibStatut:
                type: string
              IdAdresse:
                type: string
              DateDossier:
                type: string
              TypeFluide:
                type: string
              DossierInf:
                type: array
                decription: Liste de données des dossiers inférieur (Même format que celle de ce dossier).
                items:
                  type: object
              WS109Actions:
                type: array
                items:
                  type: object
                  required:
                    - DateAccord
                    - Timestamp
                    - TypeAction
                  properties:
                    DateAccord:
                      type: string
                    Timestamp:
                      type: string
                    TypeAction:
                      type: string
              Liste:
                type: array
                items:
                  type: object
                  required:
                    - CodeInfo
                    - LibInfo
                    - TypeInfo
                  properties:
                    CodeInfo:
                      type: string
                    LibInfo:
                      type: string
                    LibInfoStatut:
                      type: string
                    TypeInfo:
                      type: string
          example:
            NumDossier:
              - '645181'
            NumDossierSup: ''
            NumDossierInf:
              - '000000645708'
              - '000000100142'
            TypeDossier: RT1
            DateDossier: 24/11/2015
            IdPartenaire: 4100080118
            Ean: null
            IdAdresse: '0001140184'
            CdPostal: 4420
            IdLocalite: '237'
            IdRadLocalite: 112908
            Localite: Montegnée
            IdRue: '19125'
            IdRadRue: 166590
            Rue: Rue Hector Denis
            NumRue: 114
            NumComp: ''
            NumEtape: 6
            LibEtape: En attente dossier complet
            CodeStatut: 61
            LibStatut: En attente dossier complet
            TypeFluide: Elec
            DossierInf: [ ]
            WS109Actions:
              - DateAccord: ''
                Timestamp: 1631028134.197715
                TypeAction: UPLOAD_DOC
            Liste:
              - CodeInfo: '61_3'
                LibInfo: ' Formulaire "Demande de réalisation"'
                LibInfoStatut: ' En attente de réception'
                TypeInfo: DOC
                ValeurInfo: null
