get:
  summary: "WS200 : Récupérer les informations des compteurs liés à un EAN"
  description: |
    Récupère les informations des compteurs liés à un EAN depuis la DB HANA.
    
    - Pour les utilisateurs authentifiés (et si pas d'EAN ou MeterId spécifié) :
      - Un header Authorization contenant le JWT de l'utilisateur est nécessaire.
      - La réponse contiendra les données EAN et compteur pour cet utilisateur.

    - Pour les utilisateurs non authentifiés :
      - Un Ean et MeterId sont requis en query parameters.
      - L'EAN est utilisé pour récupérer des informations spécifiques sur les compteurs.
      - L'ID du compteur est utilisé pour valider que la paire Ean/MeterId est correcte.
  parameters:
    - name: Ean
      in: query
      description: Code EAN du compteur.
      required: false
      schema:
        type: string
        example: "541449020710302952"
    - name: MeterId
      in: query
      description: ID du compteur.
      required: false
      schema:
        type: string
        example: "000000000034175962"
    - name: Authorization
      in: header
      description: JWT de l'utilisateuré uniquement si pas d'EAN ou MeterId spécifié.
      required: false
      schema:
        type: string
        example: "Bearer <token>"
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Address:
                  type: object
                  description: Informations d'adresse
                  properties:
                    Street:
                      type: string
                      description: Nom de la rue
                      example: "Rue Lairesse"
                    Number:
                      type: string
                      description: Numéro de rue
                      example: "32"
                    Postcode:
                      type: string
                      description: Code postal
                      example: "4020"
                    City:
                      type: string
                      description: Nom de la ville
                      example: "Liège"
                Eans:
                  type: array
                  description: Liste des EAN associés à l'adresse
                  items:
                    type: object
                    properties:
                      Ean:
                        type: string
                        description: Code EAN
                        example: "541449020710302952"
                      Meters:
                        type: array
                        description: Liste des compteurs associés à l'EAN
                        items:
                          type: object
                          properties:
                            Type:
                              type: string
                              description: Type de compteur (électricité ou gaz)
                              enum: [ "elec", "gaz" ]
                              example: "elec"
                            Number:
                              type: string
                              description: Numéro de compteur
                              example: "000000000034175962"
                            Tarif:
                              type: string
                              description: Catégorie tarifaire
                              example: "Double tarif"
                            Rate:
                              type: string
                              description: Forfaits catégorisant le compteur selon sa configuration.
                              example: "Essentiel"
                            Phase:
                              type: string
                              description: Nombre de phase du compteur.
                              enum: [ "1N", "2", "3", "3N" ]
                              example: "3"
                            PhaseType:
                              type: string
                              description: Indique le type de configuration de phase du compteur.
                              enum: [ "MONO", "MONO_N", "TRI", "TRI_N" ]
                              example: "TRI"
                            Smart:
                              type: boolean
                              description: Indicateur de compteur intelligent
                              example: false
                            Power:
                              type:
                                - "number"
                                - "null"
                              description: Puissance du compteur
                              example: 5.8
                            Amper:
                              type:
                                - "number"
                                - "null"
                              description: Ampérage du compteur
                              example: 25.5
                            Production:
                              type:
                                - "number"
                                - "null"
                              description: Détails de production
                              example: null
    '400 INCORRECT_PARAMETERS':
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                description: HTTP Error code
                example: 400
              Message:
                type: string
                description: Error message
                example: "The given Ean doesn't match the MeterId"
              ErrorCode:
                type: string
                description: Application error code
                example: "INCORRECT_PARAMETERS"
    '400 MISSING_PARAMETERS':
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                description: HTTP Error code
                example: 400
              Message:
                type: string
                description: Error message
                example: "Missing mandatory query string when non auth: [Ean, MeterId]"
              ErrorCode:
                type: string
                description: Application error code
                example: "MISSING_PARAMETERS"
    '401 INVALID_TOKEN':
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                description: HTTP Error code
                example: 401
              Message:
                type: string
                description: Error message
                example: "Token de connexion invalide"
              ErrorCode:
                type: string
                description: Application error code
                example: "INVALID_TOKEN"
