get:
  summary: 'WS45 : Obtenir le solde d''un contrat, à partir du numéro de contrat'
  security:
    - tokenAuthorizer: []
  parameters:
  - name: Ean
    in: query
    required: true
    schema:
      type: integer
      example: 541460900001172286
  - name: Langue
    in: header
    required: false
    schema:
      type: string
      default: FR
    example: FR
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: object
            required:
            - Buspartner
            - Devise
            - Solde
            properties:
              Buspartner:
                type: string
              TextSolde:
                type: string
              Devise:
                type: string
              Solde:
                type: string
          example:
            Buspartner: '110147046 '
            TextSolde: 'Solde en votre faveur :'
            Devise: €
            Solde: 77,90