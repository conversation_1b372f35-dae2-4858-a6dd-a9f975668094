{"title": "<PERSON><PERSON>", "description": "this error structure mean the resa web service received your request but something went wrong", "type": "object", "properties": {"Standard": {"type": "object", "properties": {"FaultText": {"type": "string"}}}, "Addition": {"type": "object", "properties": {"Fault": {"type": "object", "properties": {"Type": {"type": "string"}, "ID": {"type": "string"}, "Number": {"type": "integer"}, "Message": {"type": "string"}, "LogNo": {"type": "string"}, "LogMsgNo": {"type": "string"}, "MessageV1": {"type": "string"}, "MessageV2": {"type": "string"}, "MessageV3": {"type": "string"}, "MessageV4": {"type": "string"}, "Parameter": {"type": "string"}, "Row": {"type": "string"}, "Field": {"type": "string"}, "System": {"type": "string"}}}}}}}