schema:
  type: object
  required:
    - Header
  properties:
    Langue:
      type: string
      description: La langue à utiliser si l'utilisateur n'a pas de préférence.
    Header:
      type: array
      items:
        type: object
      description: Attributs permettant de configurer l'en-tête du message.
        Il doit au minimum contenir TEMPLATE_ID afin d'identifier le template à utiliser
        et EAN, EMAIL ou MOBILE_PHONE afin d'identifier l'utilisateur.
example:
  Langue: DE
  Header:
    - Type: PARTNER_ID
      Valeur: 3100000036
    - Type: DATE_FROM
      Valeur: 01/08/2019
    - Type: DATE_TO
      Valeur: 31/08/2019
    - Type: NAME
      Valeur: LAMBERT-XHONNEUX RAYMOND
    - Type: STREET
      Valeur: Avenue François Bovesse 1
    - Type: POST_CODE
      Valeur: 4053
    - Type: CITY
      Valeur: Embourg
    - Type: EMAIL
      Valeur: <EMAIL>
    - Type: MOBILE_PHONE
      Valeur: "+32496123456"
    - Type: TEMPLATE_ID
      Valeur: INDEX_COMMUN_AVEC_PASSAGE
  ContentA:
    - Data:
        - Type: EAN
          Valeur: 541460900001609400
        - Type: ENERGY_TYPE
          Valeur: Gaz
      ContentB:
        Data:
          Type: SERIAL_NUMBER
          Valeur: 27518046
    - Data:
        - Type: EAN
          Valeur: 541456700002312960
        - Type: ENERGY_TYPE
          Valeur: Elec
      ContentB:
        Data:
          Type: SERIAL_NUMBER
          Valeur: 53102597M1