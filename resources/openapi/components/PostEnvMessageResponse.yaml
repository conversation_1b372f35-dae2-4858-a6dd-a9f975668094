type: object
properties:
  StatusCode:
    type: integer
    description: "Code de status http global. Il prend la plus basse valeur entre les MessageStatus"
    example: 502
  Messages:
    type: array
    description: "Liste des messages envoyés par utilisateur avec leurs status."
    items:
      type: object
      properties:
        Uid: 
          type: string
          description: "Uid de l'utilisateur concerné par le message"
          example: "Tes.Test.JGG22"
        MessageStatus: 
          type: integer
          description: "Code de status http du message. Il prend la plus basse valeur entre EmailStatusCode et SmsStatusCode est donc à 200 si l'un des deux est à 200"
          example: 502
        MessageResponse:
          type: object
          nullable: false
          properties:
            EmailStatusCode: 
              type: integer
              description: "Code de status de l'email."
              example: 200
              nullable: false
            Email:
              type:
                - string
                - list
              example: "<EMAIL>"
              nullable: false
            EmailErrorMessage:
              type: string
              nullable: true
              description: "Message d'erreur si echec de l'envois"
              example: null
            SmsStatusCode: 
              type: integer
              description: "Code de status http global. Il prend la plus haute valeur entre EmailStatusCode et SmsStatusCode et n'est donc à 200 que si les deux sont à 200"
              example: 502
              nullable: false
            Phone: 
              type: string
              example: "+32412345678"
              nullable: false
            SmsErrorMessage:
              type: string
              example: "SMS server error"
              description: "Message d'erreur si echec de l'envois"
              nullable: true