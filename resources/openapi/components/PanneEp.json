{"title": "Panne d'un équipement EP", "description": "Panne d'un équipement d'éclairage publique déclarée par un client de Résa", "type": "object", "properties": {"Id": {"type": "string"}, "DateCreation": {"type": "string", "pattern": "^([0-2][0-9]|(3)[0-1])(/)(((0)[0-9])|((1)[0-2]))(/)\\d{4}$"}, "DateCloture": {"type": ["string", "null"], "pattern": "^([0-2][0-9]|(3)[0-1])(/)(((0)[0-9])|((1)[0-2]))(/)\\d{4}$"}, "DernierStatut": {"$ref": "#/components/schemas/StatutEp"}, "Addresse": {"$ref": "#/components/schemas/AddresseEp"}, "EquipementEp": {"$ref": "#/components/schemas/EquipementEp"}}, "required": ["DateCreation", "DernierStatut", "Id"]}