type: object
properties:
  Langue:
    type: "string"
    description: "La langue a utiliser si l'utilisateur n'a pas de préférence."
    nullable: false
    example: "FR"
    enum: ["FR", "NL", "DE"]
  Header:
    type: object
    description: "Attributs permettant de configurer l'en-tête du message. Il doit au minimum contenir TEMPLATE_ID afin d'identifier le template à utiliser et un (ou plusieurs) entre EAN, EMAIL et MOBILE_PHONE afin d'identifier l'utilisateur."
    nullable: false
    properties:
      TEMPLATE_ID:
        type: string
        example: "MY_TEST_TEMPLATE"
        description: "Id du template à utiliser."
        nullable: false
      EAN:
        type: string
        example: "541460900000575415"
        description: "EAN de l'utilisateur"
        nullable: true
      EMAIL:
        type:
          - string
          - list
        example: "<EMAIL>"
        description: "Email de l'utilisateur"
        nullable: true
      ATTACHMENTS:
        type: array
        description: "Liste des informations des fichiers à télécharger"
        items:
          type: object
          properties:
            URL:
              type: string
              example: "https://s3.amazonaws.com/bucket_name/file_name"
              description: "URL complète du fichier"
              nullable: false
            FILENAME:
              type: string
              example: "document.pdf"
              description: "Nom du fichier à utiliser lors de l'attachement"
              nullable: false
        example:
          - URL: "https://s3.amazonaws.com/bucket_name/file1.pdf"
            FILENAME: "file1.pdf"
          - URL: "https://s3.amazonaws.com/bucket_name/file2.pdf"
            FILENAME: "file2.pdf"
      MOBILE_PHONE:
        type: string
        example: "+32412345678"
        description: "Numéro de téléphone de l'utilisateur au format E.164"
        nullable: true
      CCI:
        type:
          - string
          - list
          - null
        example: "<EMAIL>"
        description: "Email pour envois d'une copie du mail en CCI/BCC"
        nullable: true
      CC:
        type:
          - string
          - list
          - null
        example: "<EMAIL>"
        description: "Email pour envois d'une copie du mail en CC"
        nullable: true
      VALISE:
        type: string
        example: "https://agora-acc.resa.intra/Sites/sap2/Dossier client/SAPFolder17/Dossier Client-3048365-QTA-300"
        description: "Valise dans laquelle une copie du mail sera placée."
        nullable: true
      NO_USER_CHECK:
        type: string
        enum: [ "N", "Y" ]
        example: 'N'
        description: "Cette option permet de ne pas utiliser les champs EMAIL et MOBILE_PHONE comme clé de recherche d'un utilisateur, mais comme valeurs réelles pour l'envoi de message.\nAttention : La vérification des préférences utilisateur est donc également désactivée."
        nullable: true
  Content:
    type: object
    description: "Liste d'attributs à envoyer dans le corps du message"
    nullable: true
    properties:
      SomeTemplateVar:
        type: string
        description: "Attribut utilisé dans le template"
        nullable: true
        example: "Could be anything"
      SomeTemplateDict:
        type: "object"
        description: "Sous contenu hierarchique utilisé dans le template"
        nullable: true
        properties:
          SomeTemplateVar:
            type: string
            description: "Attribut utilisé dans le template"
            nullable: true
            example: "Could be anything"
      SomeTemplateList:
        type: "array"
        description: "Liste d'éléments utilisé dans le template"
        nullable: true
        items: 
          type: string
          example: "Could be anything, event another dict or list"