import base64
import json
from os import environ

from requests import request
from simple_salesforce import Salesforce, SalesforceMalformedRequest

from utils.aws_utils import get_resource_key, get_secret, load_s3_file
from utils.dict_utils import first
from utils.models.sf_case_request_model import SFCaseRequest


# @aws_lambda_handler
def handler(event, context):
    for record in event.get("Records", []):
        payload = json.loads(record["body"])
        _send_to_salesforce(SFCaseRequest.recursive_construct(**payload))


def _send_to_salesforce(case_request: SFCaseRequest):
    sf_cred = get_secret(environ["SALESFORCE_API"])
    mapping_file = json.loads(load_s3_file(environ["BUCKET"], get_resource_key("mapping_ws79_salesforce.json")))

    mapping = mapping_file.get(case_request.data.section.lower())
    if mapping:
        if "SpecialType" in mapping:
            special_type = mapping["SpecialType"].get(case_request.data.sous_section.lower())
            if special_type:
                mapping["Type"] = special_type
    else:
        mapping = {"RecordType": case_request.data.section, "Type": case_request.data.sous_section}

    sf = Salesforce(
        username=sf_cred["username"],
        password=sf_cred["password"],
        security_token=sf_cred["security_token"],
        client_id="MyResaAPI",
        consumer_key=sf_cred["client_id"],
        consumer_secret=sf_cred["client_secret"],
        version=sf_cred["api_version"],
        domain="login" if environ["STAGE"] == "production" else "test",
    )

    description = ""
    if case_request.data.description:
        description += case_request.data.description
    if case_request.data.remarque:
        if description:
            description += "\n"
        description += case_request.data.remarque

    case = {
        "Description": description,
        "Type": mapping["Type"],
        "Subject": case_request.data.subject or case_request.data.sous_section,
        "SuppliedEmail": case_request.client_email,
        "SuppliedName": f"{case_request.data.nom} {case_request.data.prenom}",
        "SuppliedPhone": case_request.data.phone,
        "Origin": "Web",
        "Z_SuppliedAddress__City__s": case_request.data.adresse.commune,
        "Z_SuppliedAddress__CountryCode__s": "BE",
        "Z_SuppliedAddress__Street__s": f"{case_request.data.adresse.rue} {case_request.data.adresse.numero}",
        "Z_SuppliedAddress__PostalCode__s": case_request.data.adresse.code_postal,
        "Z_PreferredContactMethod__c": case_request.data.preference,
        "RecordType": {"Name": mapping["RecordType"]},
    }
    if case_request.data.ean:
        case["Z_ServicePoint__r"] = {"vlocity_cmt__MarketIdentifier__c": case_request.data.ean}
    if case_request.data.customs_html:
        case["Z_AdditionalInformation__c"] = case_request.data.customs_html
    if case_request.data.status:
        case["Status"] = case_request.data.status
    if case_request.data.reason:
        case["Z_CloseReason__c"] = case_request.data.reason

    try:
        case = sf.Case.create(case)
    except SalesforceMalformedRequest as sf_ex:
        ex_content = first(sf_ex.content)
        if ex_content and "vlocity_cmt__ServicePoint__c" in ex_content.get("message") and ex_content.get("errorCode") == "INVALID_FIELD":
            case.pop("Z_ServicePoint__r")
            case["Z_AdditionalInformation__c"] = ""
            if case_request.data.ean:
                case["Z_AdditionalInformation__c"] += f'<div style="margin-top:0.7rem;"><strong>EAN</strong>: {case_request.data.ean}</div>'
            if case_request.data.customs_html:
                case["Z_AdditionalInformation__c"] += case_request.data.customs_html
            case = sf.Case.create(case)
        else:
            raise

    for file in case_request.files:
        response = request("GET", str(file.file_url), timeout=30)
        b64_file = base64.b64encode(response.content).decode()

        content_version = sf.ContentVersion.create(
            {
                "title": file.get("DocumentName"),
                "PathOnClient": file.get("DocumentName"),
                "VersionData": b64_file,
            },
        )

        content_version_details = sf.ContentVersion.get(content_version.get("id"))

        sf.contentDocumentLink.create(
            {
                "ContentDocumentId": content_version_details.get("ContentDocumentId"),
                "LinkedEntityId": case.get("id"),
                "ShareType": "I",
                "Visibility": "AllUsers",
            },
        )
