from panne_check_request import PanneCheckRequest
from panne_check_response import PanneCheckResponse
from utils.api import api_caller
from utils.aws_handler_decorator import aws_lambda_handler
from utils.dict_utils import get
from utils.errors import NotFoundError, BadRequestError


@aws_lambda_handler
def handler(event, context):
    data: PanneCheckRequest = PanneCheckRequest.from_dict(get(event, "queryStringParameters", {}))

    if not data.equi_id and not data.have_address():
        raise BadRequestError("Accepted parameters are either 'EquiId' or 'Rue' + 'Numero' + 'Zipcode' or/and 'Ville'")

    panne_id = check_if_already_exist(data)
    if not panne_id:
        raise NotFoundError()

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": PanneCheckResponse(panne_id).to_json(),
    }


def check_if_already_exist(data: PanneCheckRequest) -> str:
    """
    Check if an open interruption exist for this EP.
    @param data: The request data
    @return: Interruption ID if an open interruption already exist.
    """
    panne_id = None

    if data.equi_id:
        try:
            resp = api_caller("get", f"/ep/{data.equi_id}")
        except:
            pass
        else:
            if resp and get(resp, "Panne", {}, default_on_empty=True).get("DernierStatut", {}).get("Descr", "ACLO") != "ACLO":
                panne_id = resp["Panne"]["Id"]
    else:
        try:
            url = f"/pannes?Rue={data.rue}&Numero={data.numero}&EnCours=true&PageSize=100"
            if data.zipcode:
                url += f"&Zipcode={data.zipcode}"
            if data.ville:
                url += f"&Ville={data.ville}"
            resp = api_caller("get", url)
        except:
            pass
        else:
            for panne in resp.get("Data", []):
                if not panne.get("DateCloture"):
                    panne_id = panne["Id"]
                    break

    return panne_id
