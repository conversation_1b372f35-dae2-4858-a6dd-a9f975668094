from dataclasses import dataclass, field

from dataclasses_json import dataclass_json, LetterCase


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class PanneCheckRequest:
    equi_id: str = field(default=None)
    rue: str = field(default=None)
    zipcode: str = field(default=None)
    ville: str = field(default=None)
    numero: str = field(default=None)

    def have_address(self) -> bool:
        return bool(self.rue and (self.zipcode or self.ville) and self.numero)
