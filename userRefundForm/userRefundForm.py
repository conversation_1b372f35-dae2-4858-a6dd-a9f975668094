import json
from datetime import datetime
from os import environ

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_dynamodb_table, get_secret
from utils.db_connector import make_connection
from utils.dict_utils import get, first
from utils.errors import BadRequestError


def check_iban(iban: str):
    if int(f"{iban[4:]}{iban[:4]}".replace("BE", "1114")) % 97 != 1:
        raise BadRequestError("Invalid IBAN", error_code="BAD_IBAN")


def check_contract(contract: str):
    check_contract_query = f"""
    SELECT CASE WHEN count(VKONT) > 0 THEN true ELSE false END AS exist
    FROM {environ["HANA_SCHEMA"]}.FKKVKP
    WHERE VKONT = lpad(:contract, 12, '0')"""

    cnx = None
    curs = None
    result = None
    try:
        cnx = make_connection(get_secret(environ["HANA_SECRET"]))
        curs = cnx.cursor()
        curs.execute(check_contract_query, {"contract": contract})
        result = first(curs.fetchone())
    finally:
        if curs:
            curs.close()
        if cnx:
            cnx.close()

    if not result:
        raise BadRequestError("Incorrect contract number", error_code="BAD_CONTRACT")


@aws_lambda_handler
def handler(event, context):
    table = get_dynamodb_table(environ["FORM_DYNAMO_TABLE"])
    data = json.loads(get(event, "body", "{}"))

    iban = get(data, "IBAN", err=BadRequestError("IBAN is mandatory")).replace(" ", "")
    contract = get(data, "NumContrat", err=BadRequestError("NumContrat is mandatory"))
    check_iban(iban)
    check_contract(contract)

    table.put_item(
        Item={
            "lastname": get(data, "Nom", err=BadRequestError("Nom is mandatory")),
            "firstname": get(data, "Prenom", err=BadRequestError("Prenom is mandatory")),
            "contract": contract,
            "iban": iban,
            "date": datetime.utcnow().isoformat(),
        }
    )

    return {"statusCode": 201}
