<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="gateway QTA/acc" type="PythonConfigurationType" factoryName="Python" folderName="Gateway">
    <module name="AWS_API Web" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="PYTHONUNBUFFERED" value="1" />
      <env name="AD_SECRET" value="MyResaAPI/ActiveDirectory/qta" />
      <env name="ADFS_SECRET" value="MyResaAPI/ADFS/dev" />
      <env name="ADFS_WEB" value="MyResaWeb/ADFS/Qual" />
      <env name="API_REGION" value="eu-west-1" />
      <env name="API_STAGE" value="v0" />
      <env name="API_URL" value="https://127.0.0.1:8011" />
      <env name="API_VERSION" value="v0" />
      <env name="AsynchroneProcessesTable" value="AsynchroneProcesses_qta" />
      <env name="AsyncProcessLambda" value="arn:aws:lambda:eu-west-1:204480941676:function:AsyncProcess-qta" />
      <env name="AWS_DEFAULT_REGION" value="eu-west-1" />
      <env name="AWS_PROFILE" value="resa" />
      <env name="BornesRechargeTable" value="BornesRechargeTable_qta" />
      <env name="BUCKET" value="my-resa-api-qta" />
      <env name="BUCKET_NAME" value="my-resa-api-qta-uploads" />
      <env name="BUCKET_NAME_SHAREPOINT_UPLOADS" value="my-resa-api-qta-sharepoint-uploads" />
      <env name="BUCKET_NAME_UPLOADS" value="my-resa-api-qta-uploads" />
      <env name="CURL_CA_BUNDLE" value="" />
      <env name="DIGACERT_STORE_CERT_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:digacertStoreCert-qta" />
      <env name="DOMAIN_NAME" value="127.0.0.1:8011" />
      <env name="DossierConfActionsTable" value="DossierConfirmationActions_qta" />
      <env name="DYNAMODB" value="MyResaUser_qta" />
      <env name="DYNAMODB_TOKEN_INVALIDATION" value="TokenInvalidation" />
      <env name="EmailSendsTable" value="EmailSends" />
      <env name="ENV_FILE" value="resources/v0/env.qta.json" />
      <env name="ENV_MESSAGE_LOG_BUCKET" value="env-message-logs-qta" />
      <env name="EXTERNAL_API_URL" value="https://localhost:8011" />
      <env name="FORMULAIRE" value="MyResaFormulaire_qta" />
      <env name="FormulairesTable" value="FormulairesTable_qta" />
      <env name="FunctionName" value="passThrough-qta" />
      <env name="HTML_TO_PDF_LAMBDA" value="arn:aws:lambda:eu-west-1:204480941676:function:transformHtmlToPdf-qta:v0" />
      <env name="IS_PROD" value="true" />
      <env name="JWK_BUCKET" value="my-resa-api-qta" />
      <env name="JWK_KEY" value="resources/AD_publickey.json" />
      <env name="LOCAL" value="true" />
      <env name="MAPPING_BUCKET_FILE" value="resources/v0/linking.json" />
      <env name="MAPPING_BUCKET_NAME" value="my-resa-api-qta" />
      <env name="MD_TABLE" value="MyResaMD_qta" />
      <env name="MOLLIE_TOKEN" value="MyResaAPI/Mollie/DEV" />
      <env name="PANNE_DYNAMODB_TABLE" value="UserPanneSubscription_qta" />
      <env name="PANNE_LOCATION_TABLE" value="PanneLocation_qta" />
      <env name="PASSTHROUGH_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:passThrough-qta:web-resa-proj" />
      <env name="PayementTable" value="PayementTable_qta" />
      <env name="SALESFORCE_QUEUE_URL" value="https://sqs.eu-west-1.amazonaws.com/204480941676/sendToSalesForce-qta_queue" />
      <env name="SHAREPOINT_SECRET" value="MyResaAPI/Sharepoint/QTA" />
      <env name="SharepointUpload_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:sharepointUpload-qta:94" />
      <env name="SMS_CODES" value="MyResaSMSCodes_qta" />
      <env name="STAGE" value="qta" />
      <env name="STAGE_TAG" value="QTA" />
      <env name="USER_ACTIVATION_SQS_URL" value="https://sqs.eu-west-1.amazonaws.com/204480941676/userActivation-qta_queue" />
      <env name="USER_SYNC_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:userSynchronisation-qta" />
      <env name="RACCORDEMENT_DRAFT_TABLE" value="RaccordementDraft_QTA" />
      <env name="LANG" value="FR" />
      <env name="CACHE_BASE_DIR" value="/tmp/MyResa" />
      <env name="UserEnergyProfiles" value="UserEnergyProfiles_QTA" />
      <env name="SmartConsoAlerts" value="SmartConsoAlerts_QTA" />
      <env name="HistoricAlerts" value="HistoricAlerts_QTA" />
    </envs>
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <EXTENSION ID="software.aws.toolkits.jetbrains.core.execution.PythonAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </EXTENSION>
    <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tools/local_flask/gateway.py" />
    <option name="PARAMETERS" value="" />
    <option name="SHOW_COMMAND_LINE" value="false" />
    <option name="EMULATE_TERMINAL" value="true" />
    <option name="MODULE_MODE" value="false" />
    <option name="REDIRECT_INPUT" value="false" />
    <option name="INPUT_FILE" value="" />
    <method v="2" />
  </configuration>
</component>