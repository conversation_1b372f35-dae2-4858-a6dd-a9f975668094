import os

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_dynamodb_table
from utils.errors import UnauthorizedError


@aws_lambda_handler
def handler(event, context):
    print(event)
    try:
        mobile = event["queryStringParameters"]["Mobile"]
        validation_code = event["queryStringParameters"]["ValidationCode"]
        allow = True
        verifier_code_sms(validation_code, mobile)
    except Exception as e:
        print("basicAuth failed with an error ", e)
        allow = False
    return {
        "principalId": "user",
        "policyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Action": "execute-api:Invoke",
                    "Effect": ("Allow" if allow else "Deny"),
                    "Resource": "*",
                }
            ],
        },
    }


def are_mobile_same(mobile_1, mobile_2):
    if mobile_1.replace(" ", "") == mobile_2.replace(" ", ""):
        return True
    return False


def verifier_code_sms(code, mobile):
    table = get_dynamodb_table(os.environ["SMS_CODES"])
    response = table.get_item(Key={"code": code})
    if not response or not response["Item"]:
        raise UnauthorizedError("Code de validation invalide")
    item = response["Item"]
    trailed_item_mobile = item["mobile"].replace(" ", "")
    trailed_request_mobile = mobile.replace(" ", "")
    if not item["mobile"]:
        raise UnauthorizedError("Code de falidation invalide")
    if not are_mobile_same(item["mobile"], mobile):
        raise UnauthorizedError("Code de validation invalide")
    if item["validated"]:
        raise UnauthorizedError("Code de validation déjà utilisé")
    table.update_item(
        Key={"code": code},
        UpdateExpression="SET #validated = :validated",
        ExpressionAttributeNames={
            "#validated": "validated",
        },
        ExpressionAttributeValues={":validated": True},
    )
    return
