import json
from os import environ
from typing import List, Set, Dict

from boto3.dynamodb.conditions import Key

from models.user import User
from models.users_incoherence import UsersIncoherence
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import scanAll, publishSNS
from utils.ldap_utils import LDAP


def get_dynamodb_users_uid() -> [str]:
    dynamo_users = scanAll(
        environ["DYNAMODB"],
        {
            "ProjectionExpression": "phone,firstname,lastname,uid,email",
            "FilterExpression": ~(Key("uid").begins_with("ghost_") | Key("uid").begins_with("Tes.User.")),
        },
    )
    return [User(**user) for user in dynamo_users]


def get_ldap_users_uid() -> [str]:
    ldap = LDAP.loadFromSecret(environ["AD_SECRET"])
    ldap_users = ldap.list_users_common_info()
    return [
        User(
            phone=user.get("telephoneNumber"),
            firstname=user.get("givenName"),
            lastname=user.get("sn"),
            uid=user.get("uid"),
            email=user.get("mail"),
            pwd_last_set=user.get("pwdLastSet"),
        )
        for user in ldap_users
    ]


def check_not_existing_users(
    incoherence: UsersIncoherence,
    dynamodb_users_uid: Set[str],
    ldap_users_uid: Set[str],
):
    incoherence.not_exist_on_dynamodb = list(dynamodb_users_uid - ldap_users_uid)
    incoherence.not_exist_on_ad = list(ldap_users_uid - dynamodb_users_uid)


def check_password_set(incoherence: UsersIncoherence, ldap_users: List[User]):
    incoherence.password_not_set = [user.uid for user in ldap_users if not user.pwd_last_set]


def check_incoherence(
    incoherence: UsersIncoherence,
    dynamodb_users: Dict[str, User],
    ldap_users: Dict[str, User],
):
    for user_uid in dynamodb_users:
        if ldap_users.get(user_uid) and dynamodb_users[user_uid] != ldap_users[user_uid]:
            incoherence.incoherent_attributes.append(user_uid)


def notify_incoherence(incoherence: UsersIncoherence):
    if incoherence.total_count() > 0:
        publishSNS(
            environ["ErrorSNS"],
            json.dumps(
                {
                    "Version": environ.get("API_VERSION"),
                    "Environment": environ.get("STAGE"),
                    "Incoherence": incoherence.__dict__,
                }
            ),
            subject=f"User coherence check : {incoherence.total_count()} incoherence detected",
        )


@aws_lambda_handler
def handler(event, context):
    incoherence = UsersIncoherence()
    dynamodb_users = {user.uid: user for user in get_dynamodb_users_uid()}
    ldap_users = {user.uid: user for user in get_ldap_users_uid()}

    # Check for user that exist only in one place
    check_not_existing_users(incoherence, set(dynamodb_users.keys()), set(ldap_users.keys()))

    # Check every AD user have its pwd_last_set set
    check_password_set(incoherence, list(ldap_users.values()))

    # Check for incoherence
    check_incoherence(incoherence, dynamodb_users, ldap_users)

    # notify results
    print(incoherence)
    notify_incoherence(incoherence)


if __name__ == "__main__":
    handler({}, None)
