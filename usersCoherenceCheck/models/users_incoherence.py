import json
from dataclasses import dataclass, field
from typing import List


@dataclass
class UsersIncoherence:
    not_exist_on_ad: List[str] = field(default_factory=list)
    not_exist_on_dynamodb: List[str] = field(default_factory=list)
    password_not_set: List[str] = field(default_factory=list)
    incoherent_attributes: List[str] = field(default_factory=list)

    def total_count(self):
        return len(self.not_exist_on_ad) + len(self.not_exist_on_dynamodb) + len(self.password_not_set) + len(self.incoherent_attributes)

    def __repr__(self) -> str:
        return json.dumps(self.__dict__)
