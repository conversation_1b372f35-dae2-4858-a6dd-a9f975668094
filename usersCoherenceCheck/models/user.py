from dataclasses import dataclass


@dataclass
class User:
    uid: str
    phone: str = None
    firstname: str = None
    lastname: str = None
    email: str = None
    pwd_last_set: str = None

    def __eq__(self, o: object) -> bool:
        return isinstance(o, User) and self.phone == o.phone and self.firstname == o.firstname and self.lastname == o.lastname and self.uid == o.uid and self.email == o.email
