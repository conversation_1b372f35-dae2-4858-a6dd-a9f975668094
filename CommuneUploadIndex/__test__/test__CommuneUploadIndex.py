import io
import json
import unittest
from copy import deepcopy
from unittest.mock import patch, Mock

from openpyxl.reader.excel import load_workbook

from utils.api import api_caller
from utils.mocking import get_mock_commune_user_token, mock_api

tmp_result = [
    {
        "Ean": "541449020710372856",
        "Type": "Elec",
        "NumCpt": "11003946",
        "Smart": False,
        "EQUNR": "000000000501020901",
        "TRIGSTAT": "1",
        "EAN": "541449020710372856",
        "NOMBREGAUCHE": "05",
        "NOMBREDROITE": "00",
        "Index": '[{"CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"","DateRel":"20240607","EQUI_EQUNR":"000000000501020901","ETDZ_ZWNUMMER":"002","Ean":"541449020710372856","IndexQual":"02","IndexUnit":"KWH","Index_Decimal":"0.00000000000000","Index_Integer":"15235","LOGIKZW":"000000000001383511","MATNR":"000000000000120129","NumCompteur":"11003946","Smart":false,"lv_nb_lines":2},{"CatCadran":"HI","CatTarif":"ECA_HI","CodeCadran":"","DateRel":"20240607","EQUI_EQUNR":"000000000501020901","ETDZ_ZWNUMMER":"001","Ean":"541449020710372856","IndexQual":"02","IndexUnit":"KWH","Index_Decimal":"0.00000000000000","Index_Integer":"14553","LOGIKZW":"000000000001383510","MATNR":"000000000000120129","NumCompteur":"11003946","Smart":false,"lv_nb_lines":2}]',
        "CadranInfo": '[{"ANLAGE":"4100687391","ANZART":"DD","CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"SU/L/N","EQUNR":"000000000501020901","ETDZ_ZWNUMMER":"002","Ean":"541449020710372856","LOGIKZW":"000000000001383511","LV_NB_LINES":2,"MATNR":"000000000000120129","NumCpt":"11003946","STANZNAC":"00","STANZVOR":"05","ZWGRUPPE":"00000189","ZWZUART":"04"},{"ANLAGE":"4100687391","ANZART":"DD","CatCadran":"HI","CatTarif":"ECA_HI","CodeCadran":"NU/H/D","EQUNR":"000000000501020901","ETDZ_ZWNUMMER":"001","Ean":"541449020710372856","LOGIKZW":"000000000001383510","LV_NB_LINES":2,"MATNR":"000000000000120129","NumCpt":"11003946","STANZNAC":"00","STANZVOR":"05","ZWGRUPPE":"00000189","ZWZUART":"04"}]',
        "Periode": '[{"ADATSOLL":"20241031","TARIFTYP":"EY","ZUORDDAT":"20241103"}]',
        "Adresse": {"Rue": "Rue des Lilas", "NumRue": "12A", "NumComp": "Box 3", "CodePostal": "1000", "Localite": "Bruxelles"},
    },
    {
        "Ean": "541449020710372857",
        "Type": "Gaz",
        "NumCpt": "21003947",
        "Smart": True,
        "EQUNR": "000000000501020902",
        "TRIGSTAT": "0",
        "EAN": "541449020710372857",
        "NOMBREGAUCHE": "03",
        "NOMBREDROITE": "01",
        "Index": '[{"CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"","DateRel":"20240608","EQUI_EQUNR":"000000000501020902","ETDZ_ZWNUMMER":"003","Ean":"541449020710372857","IndexQual":"03","IndexUnit":"M3","Index_Decimal":"0.00000000000000","Index_Integer":"25235","LOGIKZW":"000000000001383611","MATNR":"000000000000120229","NumCompteur":"21003947","Smart":true,"lv_nb_lines":1}]',
        "CadranInfo": '[{"ANLAGE":"4100687392","ANZART":"DD","CatCadran":"LO","CatTarif":"GAS_LO","CodeCadran":"DU/G","EQUNR":"000000000501020902","ETDZ_ZWNUMMER":"003","Ean":"541449020710372857","LOGIKZW":"000000000001383611","LV_NB_LINES":1,"MATNR":"000000000000120229","NumCpt":"21003947","STANZNAC":"01","STANZVOR":"03","ZWGRUPPE":"00000289","ZWZUART":"05"}]',
        "Periode": '[{"ADATSOLL":"20251031","TARIFTYP":"GY","ZUORDDAT":"20251103"}]',
        "Adresse": {"Rue": "Avenue Louise", "NumRue": "46", "NumComp": "Etage 1", "CodePostal": "1050", "Localite": "Ixelles"},
    },
    {
        "Ean": "541449020710372858",
        "Type": "Elec",
        "NumCpt": "11003948",
        "Smart": True,
        "EQUNR": "000000000501020903",
        "TRIGSTAT": "0",
        "EAN": "541449020710372858",
        "NOMBREGAUCHE": "04",
        "NOMBREDROITE": "02",
        "Index": '[{"CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"","DateRel":"20240609","EQUI_EQUNR":"000000000501020903","ETDZ_ZWNUMMER":"004","Ean":"541449020710372858","IndexQual":"02","IndexUnit":"KWH","Index_Decimal":"0.00000000000000","Index_Integer":"12000","LOGIKZW":"000000000001383612","MATNR":"000000000000120130","NumCompteur":"11003948","Smart":true,"lv_nb_lines":1}]',
        "CadranInfo": '[{"ANLAGE":"4100687393","ANZART":"DD","CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"SU/L/N","EQUNR":"000000000501020903","ETDZ_ZWNUMMER":"004","Ean":"541449020710372858","LOGIKZW":"000000000001383612","LV_NB_LINES":1,"MATNR":"000000000000120130","NumCpt":"11003948","STANZNAC":"02","STANZVOR":"04","ZWGRUPPE":"00000190","ZWZUART":"04"}]',
        "Periode": '[{"ADATSOLL":"20261031","TARIFTYP":"EZ","ZUORDDAT":"20261103"}]',
        "Adresse": {"Rue": "Chaussée de Gand", "NumRue": "55", "NumComp": "", "CodePostal": "1080", "Localite": "Molenbeek-Saint-Jean"},
    },
    {
        "Ean": "541449020710372859",
        "Type": "Gaz",
        "NumCpt": "21003949",
        "Smart": False,
        "EQUNR": "000000000501020904",
        "TRIGSTAT": "1",
        "EAN": "541449020710372859",
        "NOMBREGAUCHE": "02",
        "NOMBREDROITE": "02",
        "Index": '[{"CatCadran":"HI","CatTarif":"GAS_HI","CodeCadran":"","DateRel":"20240610","EQUI_EQUNR":"000000000501020904","ETDZ_ZWNUMMER":"005","Ean":"541449020710372859","IndexQual":"03","IndexUnit":"M3","Index_Decimal":"0.00000000000000","Index_Integer":"15000","LOGIKZW":"000000000001383613","MATNR":"000000000000120231","NumCompteur":"21003949","Smart":false,"lv_nb_lines":1}]',
        "CadranInfo": '[{"ANLAGE":"4100687394","ANZART":"DD","CatCadran":"HI","CatTarif":"GAS_HI","CodeCadran":"NU/H","EQUNR":"000000000501020904","ETDZ_ZWNUMMER":"005","Ean":"541449020710372859","LOGIKZW":"000000000001383613","LV_NB_LINES":1,"MATNR":"000000000000120231","NumCpt":"21003949","STANZNAC":"02","STANZVOR":"02","ZWGRUPPE":"00000291","ZWZUART":"05"}]',
        "Periode": '[{"ADATSOLL":"20271031","TARIFTYP":"GZ","ZUORDDAT":"20271103"}]',
        "Adresse": {"Rue": "Boulevard du Midi", "NumRue": "14B", "NumComp": "2e Etage", "CodePostal": "1060", "Localite": "Saint-Gilles"},
    },
]


@mock_api
@patch("CommuneUtils.api_caller_32_async", new=Mock(return_value={"ProcessId": "Unittest"}))
@patch("controllers.Controller.Controller.to_response", new=Mock(side_effect=lambda x, y: deepcopy(tmp_result)))
@patch("CommuneUtils.get_commune_ean", new=Mock(return_value=[res["Ean"] for res in tmp_result]))
class TestCommuneUploadIndex(unittest.TestCase):
    @classmethod
    @patch("controllers.Controller.Controller.to_response", new=Mock(side_effect=lambda x, y: deepcopy(tmp_result)))
    def setUpClass(cls):
        cls.token = get_mock_commune_user_token()

        cls.response_get = api_caller(
            "GET",
            "/communes/index?FullEan=false",
            headers={
                "Authorization": "Bearer " + cls.token,
                "Accept": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "X-Api-Key": "mock",
            },
            raw=True,
        )

        file = io.BytesIO(cls.response_get.content)
        workbook = load_workbook(file)
        cls.workbook = workbook

    def get_copied_workbook(self):
        temp_file = io.BytesIO()
        self.workbook.save(temp_file)
        temp_file.seek(0)
        return load_workbook(temp_file)

    def test_send_excel_commune_index(self):
        self.assertEqual(self.response_get.status_code, 200, "Failed to download the Excel file")

        upload_response = api_caller(
            "POST",
            "/communes/index",
            body=self.response_get.content,
            headers={
                "Authorization": "Bearer " + self.token,
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "X-Api-Key": "mock",
            },
            raw=True,
        )

        self.assertEqual(upload_response.status_code, 204, "Failed to upload the Excel file")

    def test_send_wrong_file_type(self):
        json_content = json.dumps({"key": "value"}).encode("utf-8")

        headers = {
            "Authorization": "Bearer " + self.token,
            "Content-Type": "application/json",
            "X-Api-Key": "mock",
        }

        # Envoyer le fichier JSON
        upload_response = api_caller(
            "POST",
            "/communes/index",
            body=json_content,
            headers=headers,
            raw=True,
        )

        self.assertEqual(upload_response.status_code, 400, "The API did not handle the JSON file correctly")

        response_body = upload_response.json()
        self.assertEqual(response_body.get("ErrorCode"), "WRONG_TYPE", "Unexpected error code for JSON file")

    def test_send_excel_with_wrong_headers(self):
        workbook = self.get_copied_workbook()
        sheet = workbook.active

        sheet.cell(row=1, column=1, value="WrongHeader")

        modified_file = io.BytesIO()
        workbook.save(modified_file)
        modified_file.seek(0)

        # Encoder le fichier modifié en base64
        modified_file_content = modified_file.read()

        # 3. Renvoyer le fichier pour validation
        upload_response = api_caller(
            "POST",
            "/communes/index",
            body=modified_file_content,
            headers={
                "Authorization": "Bearer " + self.token,
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "X-Api-Key": "mock",
            },
            raw=True,
        )

        self.assertEqual(upload_response.status_code, 400, "Expected status code 400 for wrong headers")
        response_body = upload_response.json()
        self.assertEqual(response_body.get("ErrorCode"), "WRONG_HEADERS", "Expected error code 'WRONG_HEADERS'")
        self.assertIn("En-têtes inattendus : WrongHeader", response_body.get("Message"), "Expected error message to mention 'WrongHeader'")

    def test_send_excel_with_missing_headers(self):
        workbook = self.get_copied_workbook()
        sheet = workbook.active

        sheet.delete_cols(2)
        modified_file = io.BytesIO()
        workbook.save(modified_file)
        modified_file.seek(0)

        modified_file_content = modified_file.read()

        upload_response = api_caller(
            "POST",
            "/communes/index",
            body=modified_file_content,
            headers={
                "Authorization": "Bearer " + self.token,
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "X-Api-Key": "mock",
            },
            raw=True,
        )

        # Vérifier que le code de statut de la réponse est 400 et que l'erreur est 'WRONG_HEADERS'
        self.assertEqual(upload_response.status_code, 400, "Expected status code 400 for missing headers")
        response_body = upload_response.json()
        self.assertEqual(response_body.get("ErrorCode"), "WRONG_HEADERS", "Expected error code 'WRONG_HEADERS'")
        self.assertIn("En-têtes manquants", response_body.get("Message"), "Expected error message to mention 'En-têtes manquants'")

    def test_wrong_number_columns(self):
        workbook = self.get_copied_workbook()
        sheet = workbook.active

        sheet.insert_cols(2)
        for row in sheet.iter_rows(min_row=1, max_row=1, values_only=False):
            row[1].value = "Rue"  # Nommer cette colonne 'Rue'

        # 3. Ajouter une ligne avec des données incorrectes
        extra_data_row = ["ExtraData"] * len(sheet[1])  # Créer une ligne avec des données supplémentaires
        sheet.append(extra_data_row)

        # Sauvegarder les modifications dans un nouveau fichier
        modified_file = io.BytesIO()
        workbook.save(modified_file)
        modified_file.seek(0)

        # Encoder le fichier modifié en base64
        modified_file_content = modified_file.read()

        # 4. Renvoyer le fichier pour validation
        upload_response = api_caller(
            "POST",
            "/communes/index",
            body=modified_file_content,
            headers={
                "Authorization": "Bearer " + self.token,
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "X-Api-Key": "mock",
            },
            raw=True,
        )

        # Vérifier que le code de statut de la réponse est 400 et que l'erreur est 'WRONG_NUMBER_COL'
        self.assertEqual(upload_response.status_code, 400, "Expected status code 400 for wrong number of columns")
        response_body = upload_response.json()
        self.assertEqual(response_body.get("ErrorCode"), "WRONG_NUMBER_COL", "Expected error code 'WRONG_NUMBER_COL'")
        self.assertIn("Nombre de colonnes incorrect", response_body.get("Message"), "Expected error message to mention 'Nombre de colonnes incorrect'")
