import base64
import os
from datetime import datetime
from zipfile import Bad<PERSON>ipF<PERSON>

from openpyxl import load_workbook

from CommuneUtils import add_result_for_ean, api_caller_32_async
from utils.api import api_caller
from utils.auth_utils import getUserData
from utils.aws_utils import upload_s3_file
from utils.errors import BadRequestError
from utils.log_utils import LogTime
from utils.message_utils import send_in_blue_mail, get_template_config
from utils.sentry.sentry_utils import capture_exception_in_sentry
from utils.string_utils import format_number


class CommuneUploadObj:
    def __init__(self, commune_ean_list, user_email, file, context, event, authorization_header, api_key, change_four=False):
        self.api_key = api_key
        self.commune_ean_list = commune_ean_list
        self.user_email = user_email
        self.file = file
        self.commune_data = getUserData(event)
        try:
            self.workbook = load_workbook(filename=self.file, keep_vba=False, data_only=True, keep_links=False)
        except BadZipFile:
            raise BadRequestError(message="Invalid Excel file", error_code="INVALID_FILE_TYPE")
        except Exception:
            raise BadRequestError(message="Failed to process the file", error_code="PROCESSING_ERROR")

        self.sheet = self.workbook.active
        self.context = context
        self.event = event
        self.bearer_token = authorization_header
        self.change_four = change_four

    def validate_content(self):
        headers = [cell for cell in next(self.sheet.iter_rows(values_only=True))]
        expected_headers = [
            "Rue",
            "NumRue",
            "NumCompl",
            "CodePostal",
            "Localite",
            "Ean",
            "NumCpt",
            "Type",
            "Cadran",
            "CodeCadran",
            "NumCadran",
            "DatePrec",
            "IndexPrec",
            "NouvIndex",
            "DateNouvIndex",
            "DateDebutRel",
            "DateFinRel",
            "FondEchelle",
        ]
        # Trouver les en-têtes manquants et inattendus
        missing_headers = [header for header in expected_headers if header not in headers]
        unexpected_headers = [str(header) for header in headers if header not in expected_headers]

        # Construire un message d'erreur détaillé si nécessaire
        if missing_headers or unexpected_headers:
            error_messages = []
            if missing_headers:
                error_messages.append(f"En-têtes manquants : {', '.join(missing_headers)}")
            if unexpected_headers:
                error_messages.append(f"En-têtes inattendus : {', '.join(unexpected_headers)}")

            raise BadRequestError(
                "Erreur dans les en-têtes. " + " ".join(error_messages),
                error_code="WRONG_HEADERS",
            )
        for row in self.sheet.iter_rows(min_row=2, values_only=True):  # Commencer à partir de la 2e ligne
            if len(row) != len(expected_headers):
                # Convertir la ligne en liste pour une meilleure représentation dans l'erreur
                row_values = list(row)
                raise BadRequestError(
                    f"Nombre de colonnes incorrect. {row_values}",
                    error_code="WRONG_NUMBER_COL",
                )

    def process_rows(self):
        with LogTime("process_rows"):
            rows = list(self.sheet.iter_rows(min_row=2, values_only=True))
            list_report = self.review_and_add_data_in_row(rows)
        with LogTime("process_and_upload_report"):
            self.process_and_upload_report(list_report)

    def verify_nouv_index(self, row):
        try:
            nouv_index = str(row[13]).replace(",", ".")

            # Verification que on n'est bien sur des chiffres et pas sur une data type "abcdefg"
            _ = float(nouv_index)
            return True
        except Exception:
            return False

    def get_communes_ean_cadrans(self):
        response = api_caller(
            "GET",
            "/communes/ean/cadrans",
            headers={"Authorization": self.bearer_token},
            raw=True,
        )
        self.list_commune_ean_info = response.json()

    def review_and_add_data_in_row(self, rows):
        list_report = []
        list_compteur_already_processed = []
        result_by_num_cadran = {}
        self.get_communes_ean_cadrans()
        for row in rows:
            record_message = None
            ean = row[5]
            date_nouv_index = row[14]
            num_cadran = row[10]
            if date_nouv_index:
                if isinstance(date_nouv_index, str):
                    try:
                        datetime.strptime(date_nouv_index, "%Y/%m/%d")
                        date_nouv_index = date_nouv_index.replace("/", "")
                    except ValueError:
                        record_message = "Mauvais format de date (Format attendu : YYYY/mm/dd)"
                else:
                    record_message = "Mauvais format de date (Format attendu : YYYY/mm/dd)"
            else:
                date_nouv_index = datetime.now().strftime("%Y%m%d")

            if row[13] is None:
                record_message = "Pas de relevé"
            elif self.verify_nouv_index(row) is False:
                record_message = "Mauvais NouvIndex (pas un nombre)"
            else:
                compteur = self.retrieve_ean_compteurs(ean, row, num_cadran)
                if not isinstance(compteur[0], dict):
                    record_message = compteur[0][1]

            if record_message is None:
                num_cpt = compteur[0]["NumCompteur"]
                num_equip = compteur[0]["NumEquip"]
                record_message = "En attente"
                if (num_cpt, num_equip) not in list_compteur_already_processed:
                    list_compteur_already_processed.append((num_cpt, num_equip))
                    dict_releve = {
                        "Ean": ean,
                        "Email": "",
                        "CodeConfirm": "X",
                        "ChangeFour": "X" if self.change_four else "",
                        "Informatif": "",
                        "CatReleve": "COMMUNE",
                        "Compteurs": compteur,
                        "DateIndex": date_nouv_index,
                    }

                    list_report.append(dict_releve)
                else:
                    for releve in list_report:
                        if releve["Compteurs"][0]["NumCompteur"] == num_cpt and releve["Compteurs"][0]["NumEquip"] == num_equip:
                            releve["Compteurs"][0]["IndexReleve"].append(compteur[0]["IndexReleve"][0])
                            break
                record_message += f" |  {compteur[0]['IndexReleve'][0]['IndexReleve']}"
            result_by_num_cadran[num_cadran] = record_message
        with LogTime("add_result_for_ean"):
            self.file = add_result_for_ean(self.file, result_by_num_cadran)
        return list_report

    def process_and_upload_report(self, list_report):
        message_id = file_dir_key = None
        if len(list_report) > 0:
            try:
                file_dir_key = f"CommuneUploadIndexMasse/{self.commune_data['Commune']['Id']}-{self.commune_data['Commune']['Localite']}"
                response_32_async = api_caller_32_async(list_report, self.user_email, self.bearer_token, self.api_key, self.commune_data["Commune"]["Localite"], file_dir_key)
                message_id = response_32_async["ProcessId"]
            except Exception as e:
                capture_exception_in_sentry(e)
            file_key = f"{file_dir_key}/{message_id}.xlsx"
            upload_s3_file(
                os.environ["BUCKET"],
                file_key,
                self.file.getvalue(),
            )
        else:
            self.send_mail()

    def send_mail(self):
        file_content = self.file.getvalue()
        attachments = [
            {
                "content": base64.b64encode(file_content).decode(),
                "name": "Resultat_enregistrement_des_index.xlsx",
            }
        ]
        send_in_blue_mail(
            template_id=get_template_config("COMMUNE_ENREGISTREMENT_INDEX")["EmailId"][os.environ["LANG"]],
            template_data={"commune_name": self.commune_data["Commune"]["Localite"]},
            email=self.user_email,
            attachments=attachments,
        )

    def retrieve_ean_compteurs(self, ean, row, num_cadran):
        pos_cadran = row[8]
        nouv_index = str(row[13]).replace(",", ".")
        fond_echelle = str(row[17]).replace(",", ".")

        nb_decimal = int(fond_echelle.split(".")[1]) if "." in fond_echelle else 0
        nb_chiffre_entier = int(fond_echelle.split(".")[0])

        if "." in nouv_index:
            chiffre_entier, decimal = nouv_index.split(".")
        else:
            if nb_decimal != 0 and len(nouv_index) <= nb_chiffre_entier + nb_decimal:
                chiffre_entier = nouv_index[:-nb_decimal]
                decimal = nouv_index[-nb_decimal:]
            else:
                chiffre_entier = nouv_index
                decimal = ""

        nouv_index = format_number(chiffre_entier, decimal, nb_chiffre_entier, nb_decimal, ",")

        matching_compteur = None
        if str(ean) not in self.commune_ean_list:
            compteur = [[False, "EAN non lié à la commune"]]
        else:
            for dict_compteur in self.list_commune_ean_info:
                if dict_compteur["LOGIKZW"] == num_cadran:
                    matching_compteur = dict_compteur
                    break
            if matching_compteur:
                compteur = [
                    {
                        "NumCompteur": matching_compteur["NumCpt"],
                        "NumEquip": matching_compteur["EQUNR"],
                        "IndexReleve": [
                            {
                                "NbChiffre": matching_compteur["STANZVOR"],
                                "NbDecimales": matching_compteur["STANZNAC"],
                                "NumCadran": matching_compteur["LOGIKZW"],
                                "PosCadran": pos_cadran,
                                "TypeCadran": matching_compteur["CatCadran"],
                                "IndexReleve": nouv_index,
                                "isSubmitted": False,
                            }
                        ],
                    }
                ]
            else:
                compteur = [[False, "Compteur non lié à la commune"]]

        return compteur
