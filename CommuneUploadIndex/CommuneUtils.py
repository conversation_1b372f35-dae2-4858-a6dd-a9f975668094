import base64
import io

import openpyxl
from openpyxl import load_workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>, Font, PatternFill
from openpyxl.utils import get_column_letter

from utils.api import api_caller
from utils.aws_handler_decorator import commune_handler
from utils.errors import BadRequestError


@commune_handler(roles=["INDEX_CPT_GERER"])
def get_user_email(event: dict, logged_user=None) -> str:
    # This function is only an excuse to reuse the commune_handler logic and validations
    return logged_user.email


def add_result_for_ean(file, result_by_num_cadran):
    file.seek(0)
    workbook = openpyxl.load_workbook(filename=file, keep_vba=False, data_only=True, keep_links=False)
    sheet = workbook.active

    headers = [cell.value for cell in next(sheet.iter_rows(max_row=1))]
    if "resultat" not in headers:
        resultat_column = len(headers) + 1  # to edit excel file

        header_fill = PatternFill(start_color="FF8517", end_color="FF8517", fill_type="solid")
        header_font = Font(name="Deja<PERSON>u Sans", bold=True, color="000000")
        align_center = Alignment(horizontal="center", vertical="center")

        cell = sheet.cell(row=1, column=resultat_column)
        cell.value = "resultat"
        cell.alignment = align_center
        cell.fill = header_fill
        cell.font = header_font
        sheet.column_dimensions[get_column_letter(resultat_column)].width = 50

        resultat_column -= 1  # To continue with python list
    else:
        resultat_column = headers.index("resultat")

    if "NumCadran" in headers:
        num_cadran_column = headers.index("NumCadran")
    else:
        raise ValueError("La colonne 'NumCadran' n'existe pas.")

    for row in sheet.iter_rows(min_row=2):
        num_cadran = row[num_cadran_column].value
        if result_by_num_cadran.get(num_cadran):
            row[resultat_column].value = result_by_num_cadran[num_cadran]

    excel_file = io.BytesIO()
    workbook.save(excel_file)
    excel_file.seek(0)

    return excel_file


def get_commune_ean(request_headers):
    response_147 = api_caller(
        method="get",
        path="/communes/index?FullEan=true",
        headers=request_headers,
        raw=True,
    )
    content_base64 = response_147.content
    content_binary = base64.b64decode(content_base64)
    virtual_file = io.BytesIO(content_binary)

    try:
        workbook = load_workbook(virtual_file, keep_vba=False)
    except:
        raise BadRequestError(message=response_147.content)

    sheet = workbook.active
    ean_list = [str(row[5]) for row in sheet.iter_rows(min_row=2, values_only=True) if row[5] is not None]

    return ean_list


def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        return False


def api_caller_32_async(list_releve, commune_mail, bearer_token, api_key, commune_name, file_dir_key):
    response_32 = api_caller(
        method="post",
        path="/asynchrone",
        body={
            "Method": "POST",
            "Path": "/index/bulk",
            "Data": list_releve,
            "Type": "external",
            "ExtraData": {
                "Mail": commune_mail,
                "CommuneName": commune_name,
                "FileDirKey": file_dir_key,
            },
        },
        headers={"Authorization": bearer_token, "x-api-key": api_key},
    )
    return response_32
