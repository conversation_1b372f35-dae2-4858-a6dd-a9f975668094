import base64
import io

from CommuneObject import CommuneUploadObj
from CommuneUtils import get_commune_ean, get_user_email
from utils.aws_handler_decorator import aws_lambda_handler
from utils.errors import BadRequestError
from utils.log_utils import LogTime


@aws_lambda_handler
def handler(event, context):
    content = event["body"]
    headers = event.get("headers", {})
    authorization_header = headers.get("Authorization", "")
    api_key_header = headers.get("X-Api-Key", "") or headers.get("x-api-key", "")
    if not api_key_header:
        raise BadRequestError(message="X-Api-Key not found ! (Care, it's case sensitive)", error_code="X_API_KEY_NOT_FOUND")
    change_four = False
    if event.get("queryStringParameters"):
        if event["queryStringParameters"].get("change_four"):
            change_four = True
    try:
        if event.get("isBase64Encoded", False):
            content = base64.b64decode(content)
        file = io.BytesIO(content)
    except TypeError:
        raise BadRequestError(message="Wrong type for the file", error_code="WRONG_TYPE")

    with LogTime("get_commune_ean"):
        commune_ean_list = get_commune_ean(event.get("headers", {}))

    user_email = get_user_email(event)
    commune_obj = CommuneUploadObj(commune_ean_list, user_email, file, context, event, authorization_header, api_key_header, change_four)

    with LogTime("validate_content"):
        commune_obj.validate_content()
    commune_obj.process_rows()

    return {"statusCode": 204}
