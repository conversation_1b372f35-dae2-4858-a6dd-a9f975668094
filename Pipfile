[[source]]
name = "pypi"
url = "https://pypi.org/simple"
verify_ssl = true

[dev-packages]

[packages]
python-jose = {extras = ["cryptography"],version = "*"}
boto3 = "*"
requests = "*"
xmltodict = "*"
simplejson = "*"
aws-psycopg2 = "*"
html-testrunner = "*"
jsonschema = "*"
ldap3 = "*"
asyncio = "*"
unidecode = "*"
pytest = "*"
moto = "*"
validate-email = "*"
botocore = "*"
mandrill = "*"
parameterized = "*"
pyping = "*"
dbapi = "*"
mysql-connector-python = "*"
pymssql = "*"
pyproj = "*"
python-memcached = "*"
pymemcache = "*"
murmurhash3 = "*"
hdbcli = "*"
requests-toolbelt = "*"
gtar = "*"

[requires]
python_version = "3.7"
