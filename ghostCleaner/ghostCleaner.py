#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_dynamodb_table, scan_all_generator_parallel
from utils.log_utils import log_info, log_err
from utils.userdata import Preferences


@aws_lambda_handler
def handler(event, context):
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    lower_date = int(time.time() - (60 * 60 * 24 * 30)) * 1000
    ghosts = scan_all_generator_parallel(
        os.environ["DYNAMODB"],
        {
            "ProjectionExpression": "uid, preferences",
            "FilterExpression": "(attribute_not_exists(valide) or valide = :valide) and (attribute_not_exists(createdAt) or createdAt < :lowerDate) and (attribute_not_exists(email) or email = :email)",
            "ExpressionAttributeValues": {
                ":valide": False,
                ":lowerDate": lower_date,
                ":email": None,
            },
        },
    )
    nbr_cleaned = 0
    for ghost in ghosts:
        try:
            prefs = Preferences(ghost).get()
            # Do not delete ghost withb`email` and `com_encod_index_mail` in preferences
            if prefs.get("email") and prefs["com_encod_index_mail"] is True:
                log_info(f"{ghost['uid']} is not deleted because it has `email` and `com_encod_index_mail` = true")
            else:
                table.delete_item(Key={"uid": ghost["uid"]})
                nbr_cleaned += 1
        except Exception as e:
            log_err(f"Failed to delete ghost {ghost['uid']}", e)
    log_info(nbr_cleaned, "ghosts cleaned")


if __name__ == "__main__":
    handler(None, None)
