#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
import time
from os import environ

import boto3
import requests

from Controller import Controller
from pinpoint_msg import PinpointMsg
from request_data import RequestData
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_secret_string, get_resource_key
from utils.errors import UnauthorizedError
from utils.log_utils import log_err
from utils.sentry.sentry_utils import (
    capture_exception_in_sentry,
    capture_message_in_sentry,
)


def validate_phone_number(phone: str):
    controller = Controller(None, {"secret": "{HANA_SECRET}"})

    sql_statement = controller.load_sql_file(get_resource_key("sqlStatements/gaz_meter_pin_validate_phone.sql"))
    sql_statement = controller.apply_hana_env(sql_statement)

    cursor = controller.execute_query(sql_statement, {"phone": phone})
    data = cursor.fetchone()
    if data[0] == 0:
        raise UnauthorizedError("The sending phone number is not authorized to perform this request.")


def get_pin_code(request_data: RequestData, event) -> str:
    # check if SN format is correct
    if re.match("^7(?:FLO|SAG|AMX)[0-9]{10}$", request_data.sn, flags=re.I):
        res = requests.get(
            url=f"{environ['API_URL']}/gazMeter/{request_data.sn}/pin",
            headers={"apikey": get_secret_string("MyResaAPI/GazPinAPIKey")},
        )
        if res.status_code == 404:
            response = "Aucun compteur trouvé."
        elif res.status_code == 200:
            response = res.json().get("PIN")
        else:
            # Unknown error while fetching gaz pin
            log_err(res.text)
            capture_message_in_sentry(res.text, {"event": event}, "fatal")
            response = "Une erreur est survenue."
    else:
        # Bad format
        response = "Format du numéro de série incorrect."

    return response


def get_request_data_from_pinpoint(event) -> RequestData:
    pinpoint_msg = PinpointMsg.from_json(event["Records"][0]["Sns"]["Message"])
    return RequestData(
        sn=pinpoint_msg.message_body.strip(),
        caller_phone=pinpoint_msg.origination_number,
        pinpoint_phone=pinpoint_msg.destination_number,
    )


def send_respone_to_pinpoint(response: str, destination: str, origin: str):
    pinpoint = boto3.client("pinpoint")

    result = pinpoint.send_messages(
        ApplicationId=environ["PINPOINT_APP_ID"],
        MessageRequest={
            "Addresses": {destination: {"ChannelType": "SMS"}},
            "MessageConfiguration": {
                "SMSMessage": {
                    "Body": "RESA gaz pin : " + response,
                    "MessageType": "TRANSACTIONAL",
                    "OriginationNumber": origin,
                }
            },
        },
    )

    if result.get("MessageResponse", {}).get("Result", {}).get(destination, {}).get("StatusCode") != 200:
        log_err(result)
        capture_message_in_sentry("Pinpoint delivery failure", {"result": result}, "fatal")(result)


@aws_lambda_handler
def handler(event, context):
    start = time.time()
    print(event)

    try:
        request_data = get_request_data_from_pinpoint(event)

        try:
            # check phone number
            validate_phone_number(request_data.caller_phone)

            # ge the pin code from the API
            response = get_pin_code(request_data, event)
        except UnauthorizedError:
            # Phone number not allowed
            response = "Vous n'êtes pas autorisé à effectuer cette requête."
        except Exception as e:
            # Unknown error while cheking phone number or fetching gaz pin
            log_err(e)
            capture_exception_in_sentry(e)
            response = "Une erreur est survenue."

        # Send response SMS
        send_respone_to_pinpoint(response, request_data.caller_phone, request_data.pinpoint_phone)
    except Exception as e:
        # Unknown error with decoding request or sending response
        log_err(e)
        capture_exception_in_sentry(e)

    end = time.time()
    print(f"Total handler duration : {end - start}")


if __name__ == "__main__":
    handler(
        {
            "Records": [
                {
                    "EventSource": "aws:sns",
                    "EventVersion": "1.0",
                    "EventSubscriptionArn": "arn:aws:sns:eu-west-1:204480941676:gaz_pin_sms_production:71ba93a9-ad1a-4a2e-a484-234284feb129",
                    "Sns": {
                        "Type": "Notification",
                        "MessageId": "b8ebf877-1cac-5d95-924e-a19fbb4a0903",
                        "TopicArn": "arn:aws:sns:eu-west-1:204480941676:gaz_pin_sms_production",
                        "Subject": None,
                        "Message": '{"originationNumber":"+32496908712","destinationNumber":"+32460216956","messageKeyword":"KEYWORD_204480941676","messageBody":"7FLO2120247311","previousPublishedMessageId":"f49dac4eclvsakp6o9hiq3ccer0b7vhb4tkv6og0","inboundMessageId":"a97e8654-c7b2-46d1-a93c-68f73124776a"}',
                        "Timestamp": "2021-10-19T11:01:58.605Z",
                        "SignatureVersion": "1",
                        "Signature": "AtfMBSOG7KIdgt3vgZ/kY3Rst/lhgueCTcwVALSWWN9nJwmrQJZOI4I8RwWiCbvLUe6EOEKAII1JuWXn4t47ESyGEaFILWaEP3SGoNKodQLGxpxEDwJxKUe5SUKKqlsYQ8EH7vMEsl0vpG5aoTKcyLafdEtTiOrg7h4pGvj1GfctfiOa8JnaiI157Rx3SQ04eEz+DWM3QRm5GikER2z0j2wJ47uwVjQ1vLokPNA4q4nMjVf5lR2aHIOOcWiWS+sxffLFybuyVDbZWmxciftEEKAGeCcjcagn6GPpe8CCcS2TChntUHzFt9LuwPAEl0Fd6q8T9rinjg/MN2tgXHnuGA==",
                        "SigningCertUrl": "https://sns.eu-west-1.amazonaws.com/SimpleNotificationService-7ff5318490ec183fbaddaa2a969abfda.pem",
                        "UnsubscribeUrl": "https://sns.eu-west-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-1:204480941676:gaz_pin_sms_production:71ba93a9-ad1a-4a2e-a484-234284feb129",
                        "MessageAttributes": {},
                    },
                }
            ]
        },
        None,
    )
