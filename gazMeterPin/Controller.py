import json
import os

import psycopg2

import utils.resa_api_constants as constants
from utils.aws_utils import load_s3_file, get_secret
from utils.db_connector import make_connection
from utils.dict_utils import get
from utils.errors import Http<PERSON><PERSON>r, BadRequestError


def default_row_processor(cursor, row):
    """
    return the row as key-value dictionnary
    """
    resource = {}
    for i in range(len(cursor.description)):
        column = cursor.description[i][0]
        resource[column] = row[i]
    return resource


class Controller:
    def __init__(self, event, linking):
        self.event = event
        self.linking = linking
        self.row_processor = default_row_processor

    def transform_type(self, var):
        """
        transorm the value to a sql readable format
        """
        ret = var
        if isinstance(var, list):
            ret = json.dumps(var)
        elif isinstance(var, dict):
            ret = json.dumps(var)
        return ret

    def load_sql_file(self, filename):
        """
        return the sql statement
        """
        return load_s3_file(os.environ["MAPPING_BUCKET_NAME"], filename)

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        db_credentials = get_secret(self.get_secret())
        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        if get(db_credentials, "RDBMS") == "hana":
            sql_statement = sql_statement.format(HanaSchema=env_file["HANA_SCHEMA"])
        return sql_statement

    def get_secret(self):
        env = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        return self.linking["secret"].format(**env)

    def db_connection(self):
        """
        return a db connection cursor
        """

        db_credentials = get_secret(self.get_secret())
        cnx = make_connection(db_credentials)
        cursor = cnx.cursor()
        return cursor

    def execute_query(self, sql_statement, request_params):
        try:
            cursor = self.db_connection()
        except Exception:
            raise HttpError(500, constants.errmsg_dict["connection"])
        try:
            cursor.execute(sql_statement, request_params)
        except psycopg2.errors.DataException as e:
            raise BadRequestError(str(e.diag.message_primary))
        return cursor
