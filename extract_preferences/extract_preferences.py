import json
import os
from datetime import datetime

from utils.DecimalEncoder import DecimalEncoder
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import write_s3_file, scanAll
from utils.dict_utils import get
from utils.log_utils import LogTime
from utils.userdata import <PERSON><PERSON>, Dossier, Preferences


@aws_lambda_handler
def handler(event, context):
    print("extract preferences")
    users = scanAll(os.environ["DYNAMODB"])
    # users = users[0:3000]

    with LogTime("aggregateUsers"):
        pass
        # users_agg = aggregateUsers(users)

    user_mapped = [mapUser(user) for user in users]

    struct = {
        "date": int(datetime.now().timestamp() * 1000),
        "environnement": os.environ["STAGE"],
        "table": os.environ["DYNAMODB"],
        "users": user_mapped,
    }

    txt = json.dumps(struct, indent=4, cls=DecimalEncoder)
    write_s3_file(os.environ["BUCKET"], "output/extracted_users_preferences.json", txt)


def mapUser(user):
    prefs = Preferences(user)
    dossiers = Dossier(user)
    ean = Ean(user)
    return {
        "uid": get(user, "uid"),
        "ghost": not get(user, "valide"),
        "email": get(user, "email"),
        "phone": get(user, "phone"),
        "firstname": get(user, "firstname"),
        "lastname": get(user, "lastname"),
        "adresse": get(user, "adresse"),
        "eans": ean.get(enrich_data=False),
        "dossiers": dossiers.get(enrich_data=False),
        "preferences": prefs.get(),
        "createdAt": get(user, "createdAt"),
    }


def aggregateUsers(users):
    bests = {}

    def inBests(user):
        return get(user, "uid") in bests.keys()

    for i, user in enumerate(users):
        print(i, " : ", len(users))
        for user_ in users[i + 1 :]:
            if isSameUser(user, user_):
                bests[get(user, "uid")] = None
                bests[get(user_, "uid")] = None
                best = bestUserToKeep(user, user_)
                bests[get(best, "uid")] = best
    return [best for best in bests.values() if best and (get(best, "email") or get(best, "adresse"))]


def isSameUser(user1, user2):
    """ghosts are same if they share email, phone or adresse"""
    if get(user1, "valide") and get(user2, "valide"):
        return False
    return (
        (
            # same email
            get(user1, "email") and get(user2, "email") and get(user1, "email") == get(user2, "email")
        )
        or (
            # same phone
            get(user1, "phone") and get(user2, "phone") and get(user1, "phone") == get(user2, "phone")
        )
        or (
            # same adresse
            get(user1, "adresse") and get(user2, "adresse") and hash(frozenset(get(user1, "adresse"))) == hash(frozenset(get(user2, "adresse")))
        )
    )


def bestUserToKeep(user1, user2):
    """select the best user to keep is they identify the same user"""
    if get(user1, "valide"):
        return user1
    if get(user2, "valide"):
        return user2
    if get(user1, "createdAt", 0) >= get(user2, "createdAt", 0):
        return user1
    else:
        return user2


if __name__ == "__main__":
    handler({}, {})
