import json
import os
from decimal import Decimal
from os import environ

import boto3
from jinja2 import Template

from utils.DecimalEncoder import DecimalEncoder
from utils.api import api_caller, basic_auth_headers
from utils.auth_utils import load_env_file, getUserData
from utils.aws_utils import (
    load_s3_file,
    get_resource_key,
)
from utils.errors import BadRequestError, NotFoundError, InternalServerError
from utils.log_utils import log_info
from utils.validation import is_valid_email


def connect_to_dynamo(table_name):
    dynamodb = boto3.resource("dynamodb")
    return dynamodb.Table(table_name)


def convert_decimals_to_floats(d):
    for key, value in d.items():
        if isinstance(value, Decimal):
            d[key] = float(value)
        elif isinstance(value, dict):
            convert_decimals_to_floats(value)


def send_mail_vehicles(event, response, vehicle_uuid):
    filtered_results = [item for item in response["Items"] if item["UUID"] == vehicle_uuid]
    if len(filtered_results) == 0:
        raise NotFoundError("Vehicle not found with UUID", error_code="VEHICLE_NOT_FOUND")
    simulation = filtered_results[0]

    query_params = event.get("queryStringParameters", {}) if event else {}
    email = query_params.get("email") if query_params else None
    if not email or not is_valid_email(email):
        email = getUserData(event)["email"]
    log_info(f"Send to : {email}")
    log_info(f"With data : {simulation}")

    api_caller(
        method="post",
        path="/envMessage",
        body={
            "Langue": os.environ["LANG"],
            "Header": {
                "TEMPLATE_ID": "VEHICLE_SUMMARY",
                "EMAIL": email,
                "NO_USER_CHECK": "Y",
            },
            "Content": simulation,
        },
        headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
    )


def get_vehicle_info(event, response):
    try:
        simulations = response.get("Items", [])

        for simulation in simulations:
            vehicle = simulation["Vehicule"]

            # Convertir les valeurs Decimal en float pour le dictionnaire de premier niveau
            convert_decimals_to_floats(vehicle)

            # Convertir les valeurs Decimal en float pour le sous-dictionnaire 'Utilisation'
            if "Utilisation" in vehicle:
                convert_decimals_to_floats(vehicle["Utilisation"])

            if "Borne" in simulation:
                convert_decimals_to_floats(simulation["Borne"])

        return simulations
    except Exception as e:
        raise BadRequestError(e, error_code="NO_VEHICLES")


def template_mail_html(response, vehicle_uuid):
    filtered_results = [item for item in response["Items"] if item["UUID"] == vehicle_uuid]
    if len(filtered_results) == 0:
        raise NotFoundError("Vehicle not found with UUID", error_code="VEHICLE_NOT_FOUND")
    try:
        simulation = json.loads(json.dumps(filtered_results[0], cls=DecimalEncoder))
        simulation["Borne"]["Modele"] = " ".join(simulation["Borne"]["Modele"].rsplit(" ")[-2:])

        TRIPHASE_NEUTRE = "Triphasé avec neutre"
        TRIPHASE_SANS_NEUTRE = "Triphasé sans neutre"
        CONFORT_MODELE = "7.4 kW"

        puissance_cible = float(simulation["Borne"]["PuissanceCible"])
        puissance_depart = float(simulation["Borne"]["PuissanceDepart"])
        modif_racc = simulation["Borne"]["ModifTypeRaccordement"]
        modif_puissance = simulation["Borne"]["ModifPuissance"]
        racc_cible = simulation["Borne"]["TypeRaccordementCible"]
        racc_depart = simulation["Borne"]["TypeRaccordementDepart"]
        modele_borne = simulation["Borne"]["Modele"]

        rules = {
            "compat_80": 0.8 * puissance_cible <= puissance_depart and not modif_puissance and not modif_racc,
            "not_enough_power": 0.8 * puissance_cible > puissance_depart and not modif_puissance and not modif_racc,
            "mono_to_trio_rec": (
                0.8 * puissance_cible <= puissance_depart
                and modif_racc
                and racc_depart not in [TRIPHASE_NEUTRE, TRIPHASE_SANS_NEUTRE]
                and racc_cible in [TRIPHASE_NEUTRE, TRIPHASE_SANS_NEUTRE]
            ),
            "mono_to_trio_mand": (modif_racc and racc_depart not in [TRIPHASE_NEUTRE, TRIPHASE_SANS_NEUTRE] and racc_cible in [TRIPHASE_NEUTRE, TRIPHASE_SANS_NEUTRE]),
            "trio_no_neutral_to_with": (modif_racc and racc_depart == TRIPHASE_SANS_NEUTRE and racc_cible == TRIPHASE_NEUTRE),
            "trio_no_neutral_low_borne_high_power": (racc_depart == TRIPHASE_SANS_NEUTRE and modele_borne == CONFORT_MODELE and puissance_depart >= 32),
            "trio_no_neutral_low_borne_low_power": (racc_depart == TRIPHASE_SANS_NEUTRE and modele_borne == CONFORT_MODELE and puissance_depart < 32),
        }

        simulation["Results"] = "Compatible"
        for result, condition in rules.items():
            if condition:
                simulation["Results"] = result
                break

        html_content = Template(load_s3_file(environ["BUCKET"], get_resource_key("templates/bornes_results_ws171.html"))).render({"params": {"simulation": simulation}})
        return html_content
    except Exception as e:
        raise InternalServerError("Impossible to interact with template", error_code="TEMPLATE_ERROR") from e
