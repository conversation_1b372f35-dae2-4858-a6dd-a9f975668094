from dataclasses import dataclass, field
from decimal import Decimal
from typing import Optional, Union

from dataclasses_json import dataclass_json, LetterCase, config


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Utilisation:
    distance_type: Union[float, Decimal]
    periode_recharge: str  # Jour / Nuit
    branchement_moyen: Union[float, Decimal]
    parking_elec_pro: bool
    distance_pro: Union[float, Decimal]


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Vehicule:
    marque: str
    modele: str
    version: str
    capacite_batterie: Union[float, Decimal]
    vehicle_type: str = field(metadata=config(field_name="Type"))
    autonomie: Union[float, Decimal]
    consommation: Union[float, Decimal]
    puissance_charge_max: Union[float, Decimal]
    capacite: Union[float, Decimal]
    utilisation: Utilisation


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Borne:
    modele: str
    modif_puissance: bool
    puissance_depart: Union[float, Decimal]
    puissance_cible: Union[float, Decimal]
    modif_type_raccordement: bool
    type_raccordement_depart: str
    type_raccordement_cible: str
    cout: Optional[str] = None


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Simulation:
    vehicule: Vehicule
    ean: str
    compteur: str
    borne: Borne
    UID: Optional[str] = None  # Ceci sera généré côté serveur
    UUID: Optional[str] = None  # Ceci sera généré côté serveur
    date: Optional[str] = None  # Ceci sera généré côté serveur
