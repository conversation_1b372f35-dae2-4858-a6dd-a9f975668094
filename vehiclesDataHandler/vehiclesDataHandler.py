import json
import os
import uuid
from datetime import datetime

import boto3
import pytz
from boto3.dynamodb.conditions import Attr
from functions_utils import connect_to_dynamo, send_mail_vehicles, template_mail_html
from models.simulation import Simulation

from utils.auth_utils import getUserId
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import invoke_lambda
from utils.DecimalEncoder import DecimalEncoder
from utils.errors import BadRequestError
from utils.type_utils import format_decimal


@aws_lambda_handler
def handler(event, context):
    http_method = event["httpMethod"].lower()  # Convertir en minuscule pour garantir la correspondance
    dic_methods_func = {"post": post_lambda, "get": get_lambda, "delete": delete_lambda}

    if http_method in dic_methods_func:
        return dic_methods_func[http_method](event)
    else:
        raise BadRequestError("Methode non reconnue", error_code="BAD_REQUEST_ERROR")


def post_lambda(event):
    # Récupérer le corps de la requête
    data = event.get("body")
    if not data:
        raise BadRequestError("pas de donnee pour le vehicule", error_code="NO_DATA_VEHICLE")

    simulation_data_obj = Simulation.from_json(data)

    # Générer un UUID pour le véhicule et l'ajouter à l'objet
    simulation_data_obj.UUID = str(uuid.uuid4())

    # Récupérer l'UID de l'utilisateur du token d'authentification et l'ajouter à l'objet
    simulation_data_obj.UID = getUserId(event)

    # Ajouter la date actuelle au format ISO à l'objet
    simulation_data_obj.date = datetime.now(tz=pytz.timezone("Europe/Brussels")).strftime("%d-%m-%Y %H:%M")

    # Convertir l'objet VehicleData en dictionnaire pour l'ajouter à DynamoDB
    simulation_data = simulation_data_obj.to_dict()

    # Essayer d'ajouter les données du véhicule à la table DynamoDB
    try:
        environ = os.environ["STAGE_TAG"]
        table = connect_to_dynamo(f"UserVehicles_{environ}")
        vehicule_data = format_decimal(simulation_data)
        table.put_item(Item=vehicule_data)
        return {"statusCode": 201, "body": simulation_data_obj.to_json()}
    except Exception as e:
        # Note : Considérez l'utilisation d'un code d'erreur différent pour une erreur côté serveur.
        raise BadRequestError(f"exception : {e}", error_code="UNABLE_TO_INTERACT_WITH_DB")


def get_lambda(event):
    user_uid = getUserId(event)

    environ = os.environ["STAGE_TAG"]
    table = connect_to_dynamo(f"UserVehicles_{environ}")
    response = table.query(
        KeyConditionExpression=boto3.dynamodb.conditions.Key("UID").eq(user_uid),
        IndexName="UID-index",
    )

    if event["pathParameters"]:
        event_parameters = event["pathParameters"]
        vehicle_uuid = event_parameters.get("UUID", None)

        if event["path"].endswith("/sendMail"):
            send_mail_vehicles(event, response, vehicle_uuid)
            return {"statusCode": 204}

        elif event["path"].endswith("/pdf"):
            html_template = template_mail_html(response, vehicle_uuid)
            response = invoke_lambda(
                FunctionName=os.environ["HTML_TO_PDF_LAMBDA"],
                InvocationType="RequestResponse",
                Payload=json.dumps({"body": html_template}),
            )
            response_dict = json.loads(response["Payload"].read())

            if response_dict.get("errorMessage"):
                raise BadRequestError(response_dict["errorMessage"], error_code="PAYLOAD_PROBLEMS")

            payload_str = response_dict["results"]
            return {
                "statusCode": 200,
                "isBase64Encoded": True,
                "body": payload_str,
                "headers": {
                    "Content-Type": "application/pdf",
                    "content-disposition": "attachment; filename=VehicleSummary.pdf",
                },
            }
        else:
            raise BadRequestError("Path not found", error_code="PATH_NOT_FOUND")

    else:
        # Query pour récupérer tous les véhicules pour un UID utilisateur spécifique
        vehicles = response.get("Items", [])  # get_vehicle_info(event, response)
        vehicles_objs = [Simulation.from_dict(vehicle) for vehicle in vehicles]
        vehicles_objs.sort(key=lambda x: (datetime.strptime(x.date, "%d-%m-%Y %H:%M")), reverse=True)
        vehicles_json = json.dumps([obj.to_dict() for obj in vehicles_objs], cls=DecimalEncoder)
    return {"statusCode": 200, "body": vehicles_json}


def delete_lambda(event):
    try:
        # Récupérer l'UUID du véhicule depuis les paramètres de l'URL
        vehicle_uuid = event["pathParameters"]["UUID"]
        print(f"v: {vehicle_uuid}")
        if not vehicle_uuid:
            raise BadRequestError("No UUID provided", error_code="NO_UUID")

        # Récupérer l'UID de l'utilisateur du token d'authentification
        user_uid = getUserId(event)
        if not user_uid:
            raise BadRequestError("No user found with this UID", error_code="NO_USER_FOUND")
        print(f"u : {user_uid}")
        # Connexion à DynamoDB et référence à la table
        environ = os.environ["STAGE_TAG"]
        table = connect_to_dynamo(f"UserVehicles_{environ}")
        print("connected")
        # Supprimer l'élément correspondant à l'UUID et à l'UID de l'utilisateur
        response = table.delete_item(
            Key={
                "UUID": vehicle_uuid,
            },
            ConditionExpression=Attr("UID").eq(user_uid),
        )
        # Vérifier que l'opération de suppression a réussi
        if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
            return {
                "statusCode": 200,
                "body": json.dumps({"message": "Vehicle successfully deleted"}),
            }

    except Exception as e:
        # Loguer l'exception et retourner une erreur 400
        raise BadRequestError(f"exception : {e}", error_code="UNABLE_TO_DELETE_ITEM")
