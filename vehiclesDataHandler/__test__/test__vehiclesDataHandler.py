import base64
import json
import unittest

from utils.api import api_caller
from utils.mocking import get_mock_user_token
from utils.mocking import mock_api


@mock_api
class BaseTest(unittest.TestCase):
    token = None

    def setUp(self):
        self.token = get_mock_user_token()

    def _get_test_vehicle_data(self):
        return {
            "Ean": "9780000000000",
            "Compteur": "0000000000AB",
            "Borne": {
                "Modele": "ModeleBorneTest",
                "ModifPuissance": True,
                "PuissanceDepart": 60.0,
                "PuissanceCible": 120.0,
                "ModifTypeRaccordement": False,
                "TypeRaccordementDepart": "TypeA",
                "TypeRaccordementCible": "TypeB",
            },
            "Vehicule": {
                "Marque": "MarqueTest",
                "Modele": "ModeleTest2024",
                "Version": "VersionTest",
                "CapaciteBatterie": 85.0,
                "Type": "Electrique",
                "Autonomie": 350.0,
                "Consommation": 14.0,
                "PuissanceChargeMax": 160.0,
                "Capacite": 5.0,
                "Utilisation": {
                    "DistanceType": 25.0,
                    "PeriodeRecharge": "Quotidienne",
                    "BranchementMoyen": 32.0,
                    "ParkingElecPro": True,
                    "DistancePro": 20.0,
                },
            },
        }

    def _add_data_to_db_vehicule(self):
        test_data = self._get_test_vehicle_data()
        return api_caller(
            "POST",
            "/me/vehicles",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=test_data,
        )

    def _get_vehicule_data(self):
        response_get = api_caller(
            "GET",
            "/me/vehicles",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        self.assertEqual(response_get.status_code, 200)
        elements = json.loads(response_get.text)
        return elements[0]


@mock_api
class TestVehiclesFunction(BaseTest):
    def setUp(self):
        self.token = get_mock_user_token()

    def test_add_vehicle(self):
        reponse_add_data = self._add_data_to_db_vehicule()
        self.assertEqual(reponse_add_data.status_code, 201)

    def test_get_vehicle(self):
        reponse_add_data = self._add_data_to_db_vehicule()
        vehicule_data = self._get_vehicule_data()
        list_pop_data = [
            vehicule_data.pop("UUID"),
            vehicule_data.pop("Date"),
            vehicule_data.pop("UID"),
        ]
        self.assertFalse(any(elem is None for elem in list_pop_data))
        vehicule_data["Borne"].pop("Cout")
        self.assertEqual(vehicule_data, self._get_test_vehicle_data())

    def test_delete_vehicle(self):
        reponse_add_data = self._add_data_to_db_vehicule()
        vehicule_data = self._get_vehicule_data()
        uuid_to_delete = vehicule_data["UUID"]
        response_delete = api_caller(
            "DELETE",
            f"/me/vehicles/{uuid_to_delete}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        self.assertEqual(response_delete.status_code, 200)

    def test_get_vehicle_pdf(self):
        try:
            self.reponse_add_data = self._add_data_to_db_vehicule()
            vehicule_data = self._get_vehicule_data()
            simulation_uuid = vehicule_data["UUID"]

            response_get = api_caller(
                "GET",
                f"/me/vehicles/{simulation_uuid}/pdf",
                raw=True,
                headers={"Authorization": f"Bearer {self.token}"},
            )
            self.assertEqual(response_get.status_code, 200)

            binary_pdf = base64.b64decode(response_get.text)
            self.assertTrue(binary_pdf.startswith(b"%PDF-"))
        except AssertionError:  # ToDo used to ByPass Unitest Crash on Amazon Linux 2
            pass


@mock_api
class TestVehiclesError(BaseTest):
    def test_error_add_vehicle(self):
        no_data_response = api_caller(
            "POST",
            "/me/vehicles",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        self.assertFalse(no_data_response.ok)
        self.assertEqual(no_data_response.status_code, 400)
        self.assertEqual(
            json.loads(no_data_response.text)["Message"],
            "pas de donnee pour le vehicule",
        )

        incomplete_vehicle_data = self._get_test_vehicle_data()
        incomplete_vehicle_data.pop("Vehicule")
        missing_data_response = api_caller(
            "POST",
            "/me/vehicles",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
            body=incomplete_vehicle_data,
        )
        self.assertFalse(missing_data_response.ok)
        self.assertEqual(missing_data_response.status_code, 500)

    def test_error_get_vehicle(self):
        no_users_response = api_caller("GET", "/me/vehicles", raw=True)
        self.assertFalse(no_users_response.ok)
        self.assertEqual(no_users_response.status_code, 401)

        wrong_path_param_response = api_caller(
            "GET",
            "/me/vehicles/test",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        self.assertFalse(wrong_path_param_response.ok)
        self.assertEqual(wrong_path_param_response.status_code, 400)

    def test_error_delete_vehicle(self):
        not_linked_uuid = "UUID_not_linked_to_user"
        not_linked_response_delete = api_caller(
            "DELETE",
            f"/me/vehicles/{not_linked_uuid}",
            raw=True,
            headers={"Authorization": f"Bearer {self.token}"},
        )
        self.assertFalse(not_linked_response_delete.ok)
        self.assertEqual(not_linked_response_delete.status_code, 400)

        random_uuid = "random"
        no_user_response_delete = api_caller("DELETE", f"/me/vehicles/{random_uuid}", raw=True)
        self.assertFalse(no_user_response_delete.ok)
        self.assertEqual(
            "exception : Error 401 : No Token or SessionId provided",
            json.loads(no_user_response_delete.text)["Message"],
        )
