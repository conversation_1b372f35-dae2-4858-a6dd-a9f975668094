import base64
import distutils
import json
import os
import shutil
from copy import copy
from distutils.dir_util import copy_tree
from os import environ

from flask import Flask, Request, request
from flask_cors import CORS

from utils.aws_utils import get_resource_key, load_s3_file

app = Flask(__name__)

CORS(app, resources={r"/*": {"origins": "*"}})
global_headers = {
    "Cache-Control": "no-cache, no-store, must-revalidate",
    "Pragma": "no-cache",
    "Expires": "0",
    "Content-Type": "application/json",
}

binary_types = ["application/pdf", "image/png", "image/jpeg", "image/gif", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]


def call_with_aws_event(request: Request, handler, path_parameters=None):
    aws_event = {
        "httpMethod": request.method,
        "resource": request.url_rule.rule.replace("<", "{").replace(">", "}"),
        "path": request.path,
        "queryStringParameters": request.args.to_dict(),
        "headers": dict(request.headers),
        "pathParameters": path_parameters or {},
        "requestContext": {
            "resourcePath": request.url_rule.rule.replace("<", "{").replace(">", "}"),
            "path": request.path,
            "domainName": request.host,
            "stage": environ["API_VERSION"],
        },
    }
    # Force header to be SessionId instead of Sessionid
    if aws_event["headers"].get("Sessionid"):
        aws_event["headers"]["SessionId"] = aws_event["headers"]["Sessionid"]

    os.environ["FunctionName"] = handler.__module__

    if request.method in ["POST", "PUT", "PATCH"]:
        if request.content_type in ("application/json", "text/plain"):
            aws_event["body"] = request.data.decode("utf-8")
        elif request.content_type and request.content_type in binary_types:
            aws_event["body"] = base64.b64encode(request.data).decode("utf-8")
            aws_event["isBase64Encoded"] = True
        else:
            aws_event["body"] = request.data

    answer_dict = handler(aws_event, None)
    status_code = answer_dict.get("statusCode", 200)
    headers = copy(global_headers)
    headers.update(answer_dict.get("headers", {}))
    if headers.get("Content-Type") and headers.get("Content-Type") in binary_types and request.headers.get("Accept") == headers.get("Content-Type"):
        body = base64.b64decode(answer_dict.get("body", ""))
    else:
        body = answer_dict.get("body", "")
    return body, status_code, headers


def start_local_flask(port, ressource_path="./resources"):
    cache_path = f"{os.environ.get('CACHE_BASE_DIR', '/tmp/MyResa')}/s3/{os.environ['BUCKET']}/resources/{os.environ['API_VERSION']}"
    # Remove local cache
    try:
        shutil.rmtree(cache_path)
        distutils.dir_util._path_created = {}
    except FileNotFoundError:
        pass
    # Set local files in cache
    copy_tree(ressource_path, cache_path)

    # Autoconfigure flask routes based on linking.json file
    linking = json.loads(load_s3_file(environ["BUCKET"], get_resource_key("linking.json")))

    for endpoint, methods in linking.items():
        for method, config in methods.items():

            def func(config):
                return lambda **kwargs: call_with_aws_event(
                    request=request,
                    handler=getattr(__import__(config["type"]), "handler"),
                    path_parameters=kwargs,
                )

            endpoint = endpoint.replace("{", "<").replace("}", ">")
            app.add_url_rule(
                rule=endpoint,
                endpoint=f"{endpoint}_{method}",
                methods=[method],
                view_func=func(config),
            )

    # Disable ssl cert check
    environ["REQUESTS_CA_BUNDLE"] = ""
    environ["CURL_CA_BUNDLE"] = ""

    # Run the app with adhoc ssl
    app.run(host="127.0.0.1", port=port, ssl_context="adhoc", threaded=True)


if __name__ == "__main__":
    start_local_flask(8011)
