name: MyResaAPI unittest - ${{  split(variables['Build.SourceBranchName'], '/')[1]  }}
trigger: none
pool: AZSELFHOSTED_Agent_SDVIP-DEVOPS01
variables:
  PROFILE: resa
resources:
  containers:
    - container: myresaapi-cicd
      image: myresaapi-cicd
      endpoint: resaado.azurecr.io
      options: -u root
stages:
  - stage: Validate
    jobs:
      - job: Unittest
        workspace:
          clean: all
        container: myresaapi-cicd
        steps:
          - task: DownloadSecureFile@1
            name: AwsConfig
            displayName: 'Download AWS credentials'
            inputs:
              secureFile: 'aws_config'
          - script: |
              mkdir ~/.aws
              mv $(AwsConfig.secureFilePath) ~/.aws/config
            displayName: 'Set AWS credentials'
          - script: |
              python -m venv venv
              source venv/bin/activate
              for f in */*requirements.txt; do
                  pip install -r "$f";
              done
              pip install pytest pytest-azurepipelines pytest-cov pytest-xdist moto==4.2.14 boto3 flask flask_cors weasyprint==52.5
            displayName: 'Install dependencies'
          - script: |
              source venv/bin/activate
              set -a && source unittest.env && set +a
              export PYTHONPATH="$PWD"
              for dir in "$PWD"/*; do
                  if [ -d "$dir" ]; then
                      export PYTHONPATH="$PYTHONPATH:$dir";
                  fi;
              done
              pytest
            displayName: 'Run Python Unit Tests'
  - stage: Cleanup
    dependsOn:
      - Validate
    condition: always()
    jobs:
      - job: Cleanup
        workspace:
          clean: all
        steps:
          - script: docker system prune -a --force
            displayName: 'Clean up docker container / volumes / images / caches'