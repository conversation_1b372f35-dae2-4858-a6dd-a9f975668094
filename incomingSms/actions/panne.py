from os import environ

import boto3
import requests

from ringring_msg import RingRingMsg
from utils.api import basic_auth_headers

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(environ["PANNE_DYNAMODB_TABLE"])


def unsubscribe(content: RingRingMsg, event, config):
    tel = content.from_num if "+" in content.from_num else f"+{content.from_num}"
    panne_id = f"41{content.message.split(' ')[-1]}".zfill(12)

    item = table.get_item(Key={"panne": panne_id, "adresse": tel if "+" in tel else f"+{tel}"}).get("Item")

    if item:
        table.delete_item(Key={"panne": panne_id, "adresse": tel if "+" in tel else f"+{tel}"})

        lang = item.get("langue") or "FR"

        if lang == "DE":
            message = "Ihr Abonnement für Benachrichtigungen wurde gelöscht"
        else:
            message = "Votre abonnement aux notifications à bien été supprimé"

        requests.post(
            url=f"{environ['API_URL']}/sms?Sandbox=false",
            json={"to": tel, "message": message},
            headers=basic_auth_headers("MyResaAPI/AWS/BasicAuthPassword"),
        )

    return {"isBase64Encoded": False, "statusCode": 204}
