from actions import panne
from ringring_msg import RingRingMsg
from utils.aws_handler_decorator import aws_lambda_handler
from utils.dict_utils import get, safe_list_get
from utils.errors import NotFoundError

actions = {"SP": panne.unsubscribe}


@aws_lambda_handler
def handler(event, context):
    content = {}
    action = ""

    if '"To":"8810"' in get(event, "body", "").replace(" ", "").replace("\n", ""):
        content = RingRingMsg.from_json(event["body"])
        action = safe_list_get(content.message.split(" "), 1, "")

    if action not in actions:
        raise NotFoundError(f"No actions found for keyword `{action}`")
    return actions[action](content, event, context)
