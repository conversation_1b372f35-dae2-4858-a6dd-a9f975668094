from dataclasses import dataclass, field
from typing import Optional

from dataclasses_json import dataclass_json, LetterCase, config


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class RingRingMsg:
    message_id: str
    reference: Optional[str]
    from_num: str = field(metadata=config(field_name="From"))
    to_num: str = field(metadata=config(field_name="To"))
    message: str
    country: str
    time_received: str
