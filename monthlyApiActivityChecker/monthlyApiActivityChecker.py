import os
from datetime import datetime, timedelta

import boto3

from utils.api import api_caller
from utils.aws_handler_decorator import aws_lambda_handler


def get_api_gateway_stages(api_id):
    client = boto3.client("apigateway")
    response = client.get_stages(restApiId=api_id)
    return [stage["stageName"] for stage in response["item"]]


def get_last_request_time(stage_name, api_name):
    cloudwatch = boto3.client("cloudwatch")
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(days=500)

    metrics = cloudwatch.get_metric_data(
        MetricDataQueries=[
            {
                "Id": "m1",
                "MetricStat": {
                    "Metric": {
                        "Namespace": "AWS/ApiGateway",
                        "MetricName": "Count",
                        "Dimensions": [
                            {"Name": "ApiName", "Value": api_name},
                            {"Name": "Stage", "Value": stage_name},
                        ],
                    },
                    "Period": 3600,
                    "Stat": "Sum",
                },
                "ReturnData": True,
            }
        ],
        StartTime=start_time,
        EndTime=end_time,
    )
    last_request_timestamp = None

    # Parcourir les résultats de métriques
    for metric in metrics["MetricDataResults"]:
        for point in metric["Timestamps"]:
            if last_request_timestamp is None or point > last_request_timestamp:
                last_request_timestamp = point

    return last_request_timestamp.strftime("%d-%m-%Y") if last_request_timestamp else "No Data"


@aws_lambda_handler
def handler(event, context):
    env_name = os.environ.get("STAGE_TAG", None)
    api_id = os.environ.get("API_ID", None)
    api_name = os.environ.get("API_NAME", None)
    stages = get_api_gateway_stages(api_id)
    no_last_requests = [f"{stage + ' : No metrics' if get_last_request_time(stage, api_name) == 'No Data' else ''}" for stage in stages]
    last_requests = [f"{stage + ' : ' + get_last_request_time(stage, api_name) if get_last_request_time(stage, api_name) != 'No Data' else ''}" for stage in stages]

    mail_data = {"no_last_requests": no_last_requests, "last_requests": last_requests, "api_id": api_id, "api_name": api_name, "env_name": env_name}
    api_caller(
        method="post",
        path="/envMessage",
        body={
            "Langue": os.environ["LANG"],
            "Header": {"TEMPLATE_ID": "API_CHECKER_TEMPLATE", "EMAIL": "<EMAIL>", "NO_USER_CHECK": "Y"},
            "Content": mail_data,
        },
    )
    return {"statusCode": 204}
