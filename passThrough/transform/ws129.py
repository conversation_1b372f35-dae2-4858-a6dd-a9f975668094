import json

from utils.dict_utils import get


def refine_result(response, params):
    content = json.loads(response.body)
    refined_content = []

    for location in get(content, "data", [], default_on_empty=True):
        opening_times = get(location, "opening_times", {}, default_on_empty=True)
        coordinates = get(location, "coordinates", {}, default_on_empty=True)

        if location.get("country") == "BEL":
            refined_content.append(
                {
                    "Id": location.get("id"),
                    "Address": {
                        "Road": location.get("address"),
                        "City": location.get("city"),
                        "PostalCode": location.get("postal_code"),
                        "Country": location.get("country"),
                        "Coordinates": {
                            "Latitude": coordinates.get("latitude"),
                            "Longitude": coordinates.get("longitude"),
                        },
                    },
                    "LastUpdated": location.get("last_updated"),
                    "TwentyFourSeven": opening_times.get("twentyfourseven"),
                    "Evses": [
                        {
                            "Uid": evse.get("uid"),
                            "Status": evse.get("status"),
                            "Connectors": [
                                {
                                    "Id": connector.get("id"),
                                    "Standard": connector.get("standard"),
                                    "Format": connector.get("format"),
                                    "PowerType": connector.get("power_type"),
                                    "Pricing": connector.get("pricing_id"),
                                    "Power": connector.get("max_power") or get(connector, "voltage", 0) * get(connector, "amperage", 0) or None,
                                }
                                for connector in get(evse, "connectors", [], default_on_empty=True)
                            ],
                        }
                        for evse in get(location, "evses", [], default_on_empty=True)
                    ],
                }
            )

    response.body = json.dumps(refined_content)
    response.headers = {"Content-Type": "application/json"}
    return response
