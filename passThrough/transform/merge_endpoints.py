import json

from classes.Response import Response
from utils.api import APICaller
from utils.dict_utils import get


def merge_ws27_ws33(response, params):
    liste_ean = get(json.loads(response.body), "ListeEan", [])

    result_dict = {}
    thread_list = [
        APICaller(
            job_name=get(item, "Ean"),
            method="get",
            path="/index/passage",
            params={"Ean": str(get(item, "Ean"))},
            body=None,
            headers={"Langue": "FR"},
            throw=True,
            propagate=False,
            result_dict=result_dict,
        )
        for item in liste_ean
    ]

    for thread in thread_list:
        thread.start()

    for thread in thread_list:
        thread.join()

    result = [{**item, **result_dict[get(item, "Ean", "")]} for item in liste_ean]
    final_response = Response(
        request=response.request,
        isBase64Encoded=response.isBase64Encoded,
        statusCode=200,
        headers=response.headers,
        body=json.dumps({"ListeEan": result}),
    )

    return final_response
