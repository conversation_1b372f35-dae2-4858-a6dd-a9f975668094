import json
from os import environ

import boto3

from classes import Request
from classes import Response
from utils.ep import adapt_ep_equi_id
from utils.geo_utils import Address

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(environ["PANNE_LOCATION_TABLE"])


def adapt_equi_id(request: Request, params):
    data = json.loads(request.data)
    equi_id = data.get("EquiId")

    if equi_id:
        data["EquiId"] = adapt_ep_equi_id(equi_id)
        request.data = json.dumps(data)

    return request


def save_coords(response: Response, params):
    response_data = json.loads(response.body)
    request_data = json.loads(response.request.data)

    address = Address(
        road=request_data.get("Rue"),
        number=request_data.get("Numero"),
        city=request_data.get("Commune"),
        zip_code=request_data.get("Cp"),
    )
    address.geocode()

    table.put_item(
        Item={
            "panne_id": response_data.get("NumDossier"),
            "equi_id": request_data.get("EquiId") or "None",
            "road": request_data.get("Rue"),
            "number": request_data.get("Numero"),
            "city": request_data.get("Commune"),
            "zip_code": request_data.get("Cp"),
            "lat": address.lat,
            "lng": address.lng,
        }
    )

    return response
