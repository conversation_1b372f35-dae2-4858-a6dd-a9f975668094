from utils.api import api_caller
from utils.dict_utils import get


def get_bp_hgz(request, params):
    parameters = request.params

    response = api_caller(method="get", path=f"/ean/{parameters.get('Ean', '0')}", headers=request.headers)

    parameters[get(params, "set", "BpHgz", default_on_empty=True)] = get(response, "BpHgz", "0000000000")

    return request


def get_cc_hgz(request, params):
    parameters = request.params

    response = api_caller(method="get", path=f"/ean/{parameters.get('Ean', '0')}", headers=request.headers)

    parameters[get(params, "set", "CcHgz", default_on_empty=True)] = get(response, "CcHgz", "000000000")

    return request
