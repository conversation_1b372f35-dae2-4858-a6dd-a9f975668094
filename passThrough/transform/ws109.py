from datetime import datetime
from decimal import Decimal
from os import environ

from boto3.dynamodb.conditions import Key

from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import get
from utils.errors import ForbiddenError
from utils.models.user import User
from utils.sharepoint import submit_document_to_sharepoint


def validate_dossier(request, params):
    user = User.from_event(request.__dict__)

    parameters = request.params
    if parameters.get("NumDossier"):
        if parameters.get("NumDossier") not in user.dossiers_ids and not user.commune_id:
            raise ForbiddenError(
                "This dossier doesn't belong to the given user.",
                error_code="INVALID_DOSSIER_FOR_USER",
            )

    return request


def save_to_dynamo(response, params):
    num_dossier = response.request.params.get("NumDossier")
    if response.statusCode == 200 and num_dossier:
        table = get_dynamodb_table(environ["DossierConfActionsTable"])

        table.update_item(
            Key={"Id": Decimal(num_dossier)},
            UpdateExpression="SET #actions = list_append(if_not_exists(#actions, :empty_list), :actions)",
            ExpressionAttributeNames={"#actions": "Actions"},
            ExpressionAttributeValues={
                ":actions": [
                    {
                        "TypeAction": response.request.params.get("TypeAction"),
                        "DateAccord": response.request.params.get("DateAccord"),
                        "Timestamp": Decimal(datetime.now().timestamp()),
                    }
                ],
                ":empty_list": [],
            },
        )

    return response


def send_documents_to_sharepoint(response, params):
    type_action = response.request.params.get("TypeAction", "")
    num_dossier = response.request.params.get("NumDossier", "")
    if response.statusCode == 200 and type_action.upper() == "UPLOAD_DOC":
        table = get_dynamodb_table(environ["MD_TABLE"])

        documents = get(
            table.query(
                IndexName="dossier_id-index",
                KeyConditionExpression=Key("dossier_id").eq(num_dossier),
                FilterExpression=Key("submitted").eq(False),
            ),
            "Items",
            [],
        )
        for document in documents:
            submit_document_to_sharepoint(document["document_id"])

    return response
