import json

from utils.dict_utils import get


def addUCodeLibelle(response, params):
    asJSON = None
    try:
        asJSON = json.loads(response.body)
    except ValueError:
        return response

    UCode = get(asJSON, "Ucode", "Warning: Ucode not found !")
    labels = {
        "U00": "Simple tarif",
        "U10": "Simple tarif",
        "U13": "Simple tarif",
        "U20": "Simple tarif + Chauffage(Exclusif nuit)",
        "U26": "Chauffage(Exclusif nuit)",
        "U30": "Simple tarif",
        "U31": "Simple tarif",
        "U33": "Simple tarif",
        "U40": "Double tarif",
        "U50": "Double tarif + Chauffage(Exclusif nuit)",
        "U60": "Double tarif",
        "U61": "Double tarif",
        "U62": "Double tarif",
        "U63": "Double tarif",
        "U69": "Double tarif + Chauffage(Exclusif nuit)",
        "U71": "Double tarif",
        "U72": "Double tarif",
        "U73": "Double tarif",
        "U80": "Double tarif + tarif en pointe",
        "U90": "Double tarif + tarif en pointe",
        "U91": "Double tarif + tarif en pointe",
        "U93": "Double tarif + tarif en pointe",
        "U96": "Simple tarif + Double tarif",
        "U98": "Double tarif + tarif en pointe",
        "U102": "Trihoraire",
        "U104": "Double tarif + chauffage(Effacement Heures Pointe)",
        "U106": "Simple tarif + chauffage(Effacement Heures Pointe)",
        "U107": "Chauffage(Effacement Heures Pointe)",
        "U200": "Simple tarif",
        "U202": "Simple tarif",
    }
    UCodeLabel = get(labels, UCode, "")
    asJSON["UCodeLibelle"] = UCodeLabel
    setattr(response, "body", json.dumps(asJSON))

    return response
