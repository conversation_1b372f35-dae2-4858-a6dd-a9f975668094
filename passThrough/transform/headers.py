from utils.aws_utils import get_secret


def headers(req_resp, params):
    req_resp.headers.update(params["headers"])
    return req_resp


def remove_apigw_headers(req, params):
    new_headers = {}
    for k, v in req.headers.items():
        if not k.lower().startswith("x-"):
            new_headers[k] = v
    req.headers = new_headers
    return req


def auth(request, params):
    BEARER = "Bearer "
    secret_name = params["secret"]
    credentials = get_secret(secret_name)
    header_args = request.headers
    header_args["Authorization"] = BEARER + credentials["API_KEY"]
    setattr(request, "headers", header_args)
    return request
