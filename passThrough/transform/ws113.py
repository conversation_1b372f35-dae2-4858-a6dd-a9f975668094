import base64
import json
from os import environ
from uuid import uuid4

import xmltodict

from utils.aws_utils import create_s3_presigned_url, invoke_lambda, upload_s3_file
from utils.log_utils import log_err_json, log_info
from utils.type_utils import to_list

TMP_BUCKET = f"{environ['BUCKET']}-temp-storage"


def launch_pdf_upload(element, ean, type):
    pdf_data = element.get("Pdf", {}).get("PdfFr")

    if pdf_data:
        creation_date = element.get("CreatedOn")
        cert_number = element.get("Number")

        tmp_file = f"ws113/{uuid4()}/{cert_number}.pdf"
        upload_s3_file(TMP_BUCKET, tmp_file, base64.b64decode(pdf_data.encode()))
        cert_url = create_s3_presigned_url(TMP_BUCKET, tmp_file)

        log_info(f"STARTING digacertStoreCert for EAN {ean}")
        invoke_lambda(
            FunctionName=environ["DIGACERT_STORE_CERT_LAMBDA_ARN"],
            InvocationType="Event",
            Payload=json.dumps(
                {
                    "creation_date": creation_date,
                    "cert_number": cert_number,
                    "cert_url": cert_url,
                    "ean": ean,
                    "type": type,
                },
            ).encode("utf8"),
        )

        # Remove pdf_data b64
        element["Pdf"]["PdfFr"] = cert_url
        element["Pdf"]["PdfNl"] = "--- Removed by MyResa API ---"

    # Remove attachment files
    for attachments in to_list(element.get("Attachments")):
        for attachment in to_list(attachments.get("Attachment", {})):
            if attachment.get("Base64Binary"):
                attachment["Base64Binary"] = "--- Removed by MyResa API ---"

    # Remove IsometricScheme files
    for iso_schemes in to_list(element.get("IsometricSchemes")):
        for iso_scheme in to_list(iso_schemes.get("IsometricScheme", {})):
            if iso_scheme.get("Base64Binary"):
                iso_scheme["Base64Binary"] = "--- Removed by MyResa API ---"


def store_cert(response, params):
    try:
        content = xmltodict.parse(response.body)
        get_approved_documents_reply = content.get("SOAP-ENV:Envelope", {}).get("SOAP-ENV:Body", {}).get("ns3:GetApprovedDocumentsReply")
        if get_approved_documents_reply:
            for ean_data in to_list(get_approved_documents_reply.get("Ean")):
                ean = ean_data.get("ns3:EanNumber")
                if ean:
                    for control_report in to_list(ean_data.get("ControlReport")):
                        launch_pdf_upload(control_report, ean, "ControlReport")

                    for conformity_certificate in to_list(ean_data.get("ConformityCertificate")):
                        launch_pdf_upload(conformity_certificate, ean, "ConformityCertificate")

            response.body = xmltodict.unparse(content)
    except Exception as e:
        log_err_json({"Exception store_cert": e})
    finally:
        return response
