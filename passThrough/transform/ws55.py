import json

from utils.api import api_caller
from utils.errors import ForbiddenError
from utils.models.user import User
from utils.parallel import concurrently


def validate_user_ean(response, params):
    user = User.from_event(response.request.__dict__)

    tmp = concurrently(
        *[
            lambda ean=ean: api_caller(
                method="get",
                path=f"/ean/{ean}",
                headers={"Authorization": response.request.headers.get("Authorization")},
            ).get("BpHgz")
            for ean in user.ean_ids
        ]
    )
    haugazel_id_list = list(filter(None, set(tmp)))

    data = json.loads(response.body)
    haugazel_id = data.get("Contract", {}).get("CustomerNbr")

    if int(haugazel_id) not in haugazel_id_list:
        raise ForbiddenError(
            "This contract doesn't belong to the given user.",
            error_code="INVALID_GHZ_CONTRACT_FOR_USER",
        )

    response.headers.pop("Transfer-Encoding", None)
    return response
