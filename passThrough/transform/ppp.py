import json
import os

from boto3.dynamodb.conditions import Key

from utils.DecimalEncoder import DecimalEncoder
from utils.auth_utils import getUserData
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import first, get
from utils.errors import HttpError
from utils.string_utils import strOrNone
from utils.userdata import Ean
from utils.validation import validateAdresse


def prepare_ws64(request, params):
    request.params["Token"] = str(get(request.params, "Token", "")).upper()
    user_data = getUserData({"headers": request.headers}, allow_ghost=True)
    bp = get(user_data, "bp")
    mode = 3 if user_data["valide"] else (2 if bp else 1)
    request.params["Mode"] = mode
    request.params["Bp"] = bp
    return request


def add_account_info_from_token(response, params):
    user_data = getUserData({"headers": response.request.headers}, allow_ghost=True)
    if response.statusCode != 200:
        raise HttpError(500, "Cannot load token")
    origin = json.loads(response.body) or {}

    table = get_dynamodb_table(os.environ["DYNAMODB"])
    user_with_token_email = None
    email = get(origin, "BpEmail", get(user_data, "email"))
    if email:
        user_with_token_email = first(
            [
                user
                for user in get(
                    table.query(
                        KeyConditionExpression=Key("email").eq(email),
                        IndexName="email-index",
                    ),
                    "Items",
                    [],
                )
                if user["valide"]
            ]
        )
    exist = user_with_token_email and (user_with_token_email["uid"] != user_data["uid"])  # user find in ddb with different uid than current
    if not user_data["valide"] and response.request.params["Mode"] == 1:
        origin["EmailExist"] = not not exist
        ## Token  info
        user_data["firstname"] = get(origin, "BpPrenom", get(user_data, "firstname"))
        user_data["lastname"] = get(origin, "BpNom", get(user_data, "lastname"))
        user_data["email"] = get(origin, "BpEmail", get(user_data, "email"))
        user_data["phone"] = get(origin, "BpTel", get(user_data, "phone"))
        rue = get(origin, "BpRue")
        num_rue = get(origin, "BpNumRue")
        localite = get(origin, "BpLocalite")
        cd_postal = get(origin, "BpCdPostal")
        code_pays = get(origin, "BpCodePays")
        try:
            adresse = validateAdresse(
                {
                    "Rue": rue,
                    "NumRue": strOrNone(num_rue),
                    "Localite": localite,
                    "Cdpostal": strOrNone(cd_postal),
                    "CodePays": code_pays,
                }
            )
        except:
            adresse = get(user_data, "adresse")
        user_data["adresse"] = adresse
        token = get(response.request.params, "Token")

        update_expression = "SET adresse = :adresse, firstname = :firstname, lastname = :lastname, token_ppp = :token_ppp"
        expression_attribute_values = {
            ":adresse": user_data["adresse"],
            ":firstname": user_data["firstname"],
            ":lastname": user_data["lastname"],
            ":token_ppp": token,
        }
        if user_data["email"]:
            update_expression += ", email = :email"
            expression_attribute_values[":email"] = user_data["email"]
        if user_data["phone"]:
            update_expression += ", phone = :phone"
            expression_attribute_values[":phone"] = user_data["phone"]

        print(update_expression)
        print(expression_attribute_values)

        table.update_item(
            Key={"uid": user_data["uid"]},
            UpdateExpression=update_expression,
            ExpressionAttributeValues=expression_attribute_values,
        )
    ean = get(origin, "Ean")
    if ean:
        Ean(user_data).addEan(ean)
    response.body = json.dumps(origin, cls=DecimalEncoder)
    print("add_account_info_from_token finished")
    return response
