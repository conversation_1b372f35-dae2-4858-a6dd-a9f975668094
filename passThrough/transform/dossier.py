from utils.api import api_caller
from utils.dict_utils import get, first


def get_id_partenaire(request, params):
    parameters = request.params

    response = api_caller(
        method="post",
        path="/demande_travaux/dossier",
        body=f'{{"NumDossier": [{parameters.get("NumDossier", "0")}]}}',
        headers=request.headers,
    )

    parameters[get(params, "set", "IdPartenaire", default_on_empty=True)] = get(first(get(response, "Liste", []), {}), "IdPartenaire", "0000000000")

    return request
