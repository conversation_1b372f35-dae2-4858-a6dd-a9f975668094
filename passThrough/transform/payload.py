import json


##_______________________________________________________________||
def convertToPdf(response, params=None):
    if params is None:
        params = {}
    response_body = response.body
    print("Response is : ")

    response_dict = json.loads(response_body)
    response.body = response_dict["pdfBase64"]
    response.isBase64Encoded = True
    response.headers = {
        "Content-Type": "application/pdf",
        "content-disposition": "attachment; filename=facturation.pdf",
    }

    return response


##_______________________________________________________________||
