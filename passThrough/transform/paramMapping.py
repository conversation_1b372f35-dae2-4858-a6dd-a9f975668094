import json

from utils.api import api_caller
from utils.dict_utils import get


def pathParameterToQueryString(request, params):
    pp = get(request.pathParameters, params["pathParameter"])
    request.params[params["queryString"]] = pp
    request.pathParameters.pop(params["pathParameter"], None)
    return request


def mapParameters(request, params):
    input_ = params["input"]
    output_ = params["output"]

    default = get(input_, "default")
    case = get(output_, "case")

    inVal = get(
        getattr(request, input_["source"]),
        input_["name"],
        get(getattr(request, input_["source"]), input_["name"].lower(), default),
    )
    if case == "upper":
        inVal = inVal.upper()
    elif case == "lower":
        inVal = inVal.lower()
    getattr(request, output_["source"])[output_["name"]] = inVal
    getattr(request, input_["source"]).pop(input_["name"], None)
    return request


def mapValue(request, params):
    source = params["input"]["source"]
    name = params["input"]["name"]
    val = str(get(getattr(request, source), name))
    default = get(params, "default")
    newVal = get(params["map"], val, default)
    getattr(request, source)[name] = newVal
    return request


def insert(request, params):
    source = params["output"]["source"]
    src = getattr(request, source)
    if source == "data":
        if src:
            src = json.loads(src)
        else:
            src = {}
    name = params["output"]["name"]
    value = get(params, "value")
    src[name] = value
    if source == "data":
        src = json.dumps(src)
    setattr(request, source, src)
    return request


def nomPrenomParameterToQueryString(request, params):
    source = params["input"]["source"]
    print("params", params)
    print("request", getattr(request, source))
    MeterId = getattr(request, source)[params["input"]["MeterId"]]
    response = api_caller("get", "/ean", params={"Ean": MeterId})
    print(response)
    for item in response["ListeEan"]:
        if str(item["Ean"]) == MeterId:
            request.params["Nom"] = item["Nom"]
            request.params["Prenom"] = item["Prenom"]
    return request
