import json

from utils.api import APICaller
from utils.dict_utils import first, get


def enrich_geoloc(response, params):
    origin = json.loads(response.body)
    print("Response from pannes")
    print(origin)
    result_dict = {}

    calls = [
        APICaller(
            job_name=panne["Rue"] + panne["Localite"],
            method="post",
            path="/adresse/match",
            params={},
            body=json.dumps(
                {
                    "Rue": panne["Rue"],
                    "Cdpostal": str(panne["CdPostal"]) if panne["CdPostal"] else None,
                }
            ),
            throw=False,
            result_dict=result_dict,
        )
        for panne in origin["Liste"]
    ]

    for call in calls:
        call.start()

    for call in calls:
        call.join()

    origin["Liste"] = [
        {
            **panne,
            "GPS": get(
                get(
                    first(result_dict[panne["Rue"] + panne["Localite"]]["Data"], {}),
                    "GPS",
                    {},
                ),
                "MilieuBatiment",
            ),
        }
        for panne in origin["Liste"]
    ]

    response.body = json.dumps(origin)

    return response
