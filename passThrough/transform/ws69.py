import json
import os

from utils.aws_utils import get_dynamodb_items
from utils.errors import BadRequestError

cached_data = None


def retrieve_phone_number(request):
    request_data = request.data
    request_data_dict = json.loads(request_data)
    to = request_data_dict.get("to")

    return to


def check_locked_number(request, _):
    global cached_data
    phone_number = retrieve_phone_number(request)
    if cached_data is None:
        cached_data = get_dynamodb_items(f"MyResaAPI_MISC_{os.environ['STAGE_TAG']}", {"Key": "LockedPhoneNumber"})["Numbers"]
    print(phone_number, cached_data)
    if phone_number in cached_data:
        raise BadRequestError(
            "The provided phone number is in the locked phone list",
            error_code="LockedPhoneNumber",
        )

    return request
