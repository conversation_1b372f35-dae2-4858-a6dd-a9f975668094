import json
import os
from datetime import datetime

import requests

from utils.aws_utils import get_secret, get_secret_string, load_s3_file
from utils.dict_utils import get
from utils.errors import INVALID_EAN_FOR_USER, BadRequestError, ForbiddenError
from utils.log_utils import LogTime
from utils.models.user import User


class ExpirableToken:
    def __init__(self, token, expires_in):
        self.token = token
        self.expires_in = expires_in - 5  # remove 5sec to be safe in case of response delay
        self.local_timestamp = datetime.now().timestamp()
        self.exp_timestamp = self.local_timestamp + self.expires_in

    def is_expired(self):
        return datetime.now().timestamp() > self.exp_timestamp

    def __str__(self):
        return f"{self.token} - {self.expires_in}s"


def auth(request, params):
    env = json.loads(load_s3_file(os.environ["MAPPING_BUCKET_NAME"], os.environ["ENV_FILE"]))

    secret_name = params["secret"].format(**env)
    credentials = get_secret(secret_name)

    request.auth = (credentials["user"], credentials["password"])
    return request


_cached_tokens: dict[str, ExpirableToken] = {}


def _get_oauth_client_credentials_token(url, payload):
    token = None
    cache_key = f"{url}:{payload}"

    if _cached_tokens.get(cache_key):
        cached_token = _cached_tokens[cache_key]
        if not cached_token.is_expired():
            token = cached_token.token

    if not token:
        response = requests.post(
            url=url,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            data=payload,
        )
        response_json = response.json()

        token = f"{response_json['token_type']} {response_json['access_token']}"
        _cached_tokens[cache_key] = ExpirableToken(token, 5)  # response_json['expires_in'])

    return token


def oauth_client_credentials(request, params):
    with LogTime("get OAuth Client Credentials"):
        secret_name = params["secret"].format(**os.environ)
        credentials = get_secret(secret_name)

        payload = f"grant_type=client_credentials&client_id={credentials['ClientID']}&client_secret={credentials['ClientSecret']}&scope={credentials['Scope']}"

        request.headers["Authorization"] = _get_oauth_client_credentials_token(credentials["AccessTokenURL"], payload)

        return request


def validate_user_ean(request, params):
    user = User.from_event(request.__dict__)

    parameters = request.params
    if not parameters.get("Ean"):
        raise BadRequestError("EAN field missing.", error_code="MISSING_EAN")
    if parameters.get("Ean", "0") not in user.ean_ids:
        raise INVALID_EAN_FOR_USER

    return request


def validate_user_num_dossier(request, params):
    user = User.from_event(request.__dict__)

    parameters = request.params
    if not parameters.get("NumDossier"):
        raise BadRequestError("NumDossier field missing.", error_code="MISSING_NUM_DOSSIER")
    if int(parameters.get("NumDossier", "0")) not in user.dossiers_ids and not user.commune_id:
        raise ForbiddenError(
            "This NumDossier doesn't belong to the given user.",
            error_code="INVALID_NUM_DOSSIER_FOR_USER",
        )

    return request


def insertSecret(request, params):
    source = params["output"]["source"]
    src = getattr(request, source)
    if source == "data":
        if src:
            src = json.loads(src)
        else:
            src = {}
    name = params["output"]["name"]
    secret = get(params, "secret")
    value = get_secret_string(secret)
    src[name] = value
    if source == "data":
        src = json.dumps(src)
    setattr(request, source, src)
    return request
