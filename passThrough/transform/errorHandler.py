import json
from json import JSONDecodeError

from utils.dict_utils import capitalizeKeys, get
from utils.errors import HttpError
from utils.log_utils import log_err


def resaError(response, params):
    """
    Handle SAP errors messages
    """
    if 200 <= response.statusCode < 300:
        return response

    try:
        as_json = capitalizeKeys(json.loads(response.body))
        log_err(as_json)
        fault = get(get(as_json, "Addition", {}), "Fault", {})
        error = get(as_json, "Error", {})
        message = error_code = None
        if fault:
            message = get(fault, "Message")
            error_code = get(fault, "Number")
        elif error:
            message = get(error, "Message")
            error_code = get(error, "Code")
        if message and error_code:
            raise HttpError(response.statusCode, message, error_code=f"SAP-{error_code}")
    except JSONDecodeError:
        pass

    return response


def haugazelError(response, params):
    if response.statusCode == 200:
        return response
    err = None
    try:
        asJSON = capitalizeKeys(json.loads(response.body))
        log_err(asJSON)
        message = asJSON["Errors"]["ErrorFor"]["Error"]["Message"]
        err = HttpError(response.statusCode, message)
    except Exception as e:
        log_err(e)
        pass
    if err:
        raise err
    return response
