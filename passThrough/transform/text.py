import json


def removeChar(response, params=None):
    if params is None:
        params = {}
    try:
        toRemove = params["char"]
        key = params["key"]
        body = json.loads(response.body)
        print(body, key, toRemove)
        body[key] = body[key].replace(toRemove, "")
        response.body = json.dumps(body)
    except Exception as e:
        print("remove<PERSON>har failed", e)
        pass
    return response
