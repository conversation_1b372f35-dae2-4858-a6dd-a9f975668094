import json
from collections import OrderedDict

import xmltodict

from utils.dict_utils import capitalizeKeys, remove_json_keys
from utils.dict_utils import get


def namespace(elt, namespaces=None):
    if namespaces is None:
        namespaces = {}
    if isinstance(elt, OrderedDict) or isinstance(elt, dict):
        elt = dict(elt)
        for key, value in elt.items():
            namespaces = namespace(value, namespaces)
        return namespaces
    elif isinstance(elt, list):
        for ns in list(map(lambda elt_: namespace(elt_, namespaces), elt)):
            namespaces.update(ns)
        return namespaces
    else:
        if isinstance(elt, str):
            namespaces.update({elt: None})
        return namespaces


def xmlToJson(response, params=None):
    if params is None:
        params = {}
    print("xml", response.body)
    namespaces = namespace(xmltodict.parse(response.body))
    dict_ = xmltodict.parse(response.body, process_namespaces=True, namespaces=namespaces)
    dict_ = remove_json_keys(dict_, get(params or {}, "removeKeys", []))
    dict_ = capitalizeKeys(dict_)
    json_data = json.dumps(dict_)
    setattr(response, "body", json_data)
    getattr(response, "headers")["Content-Type"] = "application/json"
    getattr(response, "headers")["Content-Length"] = len(json_data)
    return response
