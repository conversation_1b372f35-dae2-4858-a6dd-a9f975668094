#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio
import json
import os

from apply_transform import apply_transform
from classes.Request import Request
from passthrough_mock import mock_passthrough_response
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_resource_key, load_s3_file
from utils.dict_utils import get


def apply_transform_type(linking, obj, type_):
    if "transform" in linking:
        transorm_configs = filter(lambda x: x["type"] == type_, linking["transform"])
        obj = apply_transform(obj, transorm_configs)
    return obj


@aws_lambda_handler
def handler(event, context):
    method = event["httpMethod"]
    path = event["resource"]

    env = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))

    linking = json.loads(load_s3_file(os.environ["BUCKET"], get_resource_key("linking.json")))[path][method.upper()]

    # Remove x-api-key before calling the target endpoint
    event["headers"].pop("x-api-key", None)

    request = Request(
        method=get(linking, "method", method),
        url=linking["URL"].format(**env),
        params=event["queryStringParameters"] or {},
        headers=event["headers"] or {},
        pathParameters=event["pathParameters"] or {},
        data=event.get("body") or "",
        auth=(),
    )

    request = apply_transform_type(linking, request, "request")
    if get(request.params, "Sandbox") == "true":
        response = mock_passthrough_response(request, method.lower() + path)
    else:
        response = asyncio.run(request.execute())
    response = apply_transform_type(linking, response, "response")

    response.headers.pop("Content-Encoding", None)
    response.headers.pop("Transfer-Encoding", None)

    response = response.toDict()
    return response


if __name__ == "__main__":
    print(
        handler(
            {
                "httpMethod": "POST",
                "requestContext": {
                    "domainName": "pqx8lp3l98.execute-api.eu-west-1.amazonaws.com",
                    "stage": "v0",
                },
                "resource": "/token",
                "queryStringParameters": {"Sandbox": "true"},
                "pathParameters": None,
                "body": json.dumps({"Email": "<EMAIL>", "Password": "Welcome1234"}),
                "headers": {"Token": "WhateverToken", "Langue": "FR"},
            },
            None,
        )
    )
