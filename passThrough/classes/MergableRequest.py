from .Request import Request


class MergableRequest(Request):
    def __init__(self, requests, onMerge=(lambda a, b: a)):
        super().__init__(None, None)
        self.requests = requests
        self.onMerge = onMerge

    def transform(self, function, args=None):
        if args is None:
            args = {}
        self.requests = [req.transform(function, args) for req in self.requests]
        return self

    async def execute(self):
        resps = []
        for req in self.requests:
            resps.append(req.execute())
        resps = [(await resp) for resp in resps]
        return self.onMerge(resps)
