class Response:
    def __init__(self, request, isBase64Encoded, statusCode, headers, body):
        self.request = request
        self.isBase64Encoded = isBase64Encoded
        self.statusCode = statusCode
        self.headers = headers
        self.body = body

    def transform(self, function, args=None):
        if args is None:
            args = {}
        return function(self, args)

    def toDict(self):
        return {
            "isBase64Encoded": self.isBase64Encoded,
            "statusCode": self.statusCode,
            "headers": self.headers,
            "body": self.body,
        }
