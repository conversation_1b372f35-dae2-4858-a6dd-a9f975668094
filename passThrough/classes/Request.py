import json
from urllib.parse import urlparse

from requests import request

from utils.DecimalEncoder import DecimalEncoder
from .Response import Response


class Request:
    def __init__(
        self,
        method,
        url,
        params=None,
        headers=None,
        pathParameters=None,
        data="",
        auth=(),
    ):
        if params is None:
            params = {}
        if headers is None:
            headers = {}
        if pathParameters is None:
            pathParameters = {}
        self.method = method
        self.url = url
        self.params = params
        self.headers = headers
        self.pathParameters = pathParameters
        self.data = data
        self.auth = auth

    def transform(self, function, args=None):
        if args is None:
            args = {}
        return function(self, args)

    async def execute(self):
        url = self.url.format(**self.pathParameters)
        print(
            "execute request : ",
            self.method,
            " ",
            url,
            "\nparams : ",
            self.params,
            "\nheaders : ",
            self.headers,
            "\ndata : ",
            self.data,
        )

        try:
            json_data = json.loads(self.data)
        except:
            json_data = None

        resp = request(
            method=self.method.upper(),
            url=url,
            params=self.params,
            headers={**self.headers, "Host": urlparse(url).netloc},
            data=self.data if not json_data else None,
            json=json_data,
            auth=self.auth,
            verify=False,
        )
        print("resp.encoding", resp.encoding)
        headers = json.loads(json.dumps(dict(resp.headers), cls=DecimalEncoder))
        headers["content-encoding"] = None  # Quick fix ?
        ret = Response(self, False, resp.status_code, headers, resp.text)
        resp.close()
        return ret
