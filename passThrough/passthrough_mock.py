import json

from classes.Response import Response
from utils.errors import BadRequestError


def mock_passthrough_response(request, path):
    """
    Mock response of passthrough services that can be used only once
    """
    mock_responses = {
        "post/token": {
            "Master": "MASTER",
            "Root": 124,
            "Ean": 541460900000575415,
            "Bp": 2101017538,
            "BpCodePays": "BE",
            "BpNom": "PECHEUX",
            "BpPrenom": "VINCENT",
            "BpRue": "RUE DU BARON",
            "BpNumRue": 20,
            "BpCdPostal": 4000,
            "BpLocalite": "LIEGE",
            "IdAdresse": "5424894",
        }
    }
    if path not in mock_responses:
        raise BadRequestError("No mock response defined for that passthrough service")
    response = Response(request, False, 200, {}, json.dumps(mock_responses[path]))
    return response
