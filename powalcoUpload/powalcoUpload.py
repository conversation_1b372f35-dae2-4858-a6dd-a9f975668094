import json
import os
from datetime import datetime
from decimal import Decimal

import boto3
from boto3.dynamodb.conditions import Key

from PowalcoSite import PowalcoSite
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import first
from utils.log_utils import log_info, log_err


def handler(event, context):
    log_info(event)
    for record in event["Records"]:
        record_handler(json.loads(record["body"]), context)


def record_handler(payload, context):
    try:
        key = first(payload["Records"])["s3"]["object"]["key"]
        bucket = first(payload["Records"])["s3"]["bucket"]["name"]
        resource = "s3://{}/{}".format(bucket, key)
        prefix = os.environ["PrefixUrl"]
        public_bucket_url = f"{prefix}/{key}"
        # setting object acl to public read
        s3 = boto3.resource("s3")
        object_acl = s3.ObjectAcl(bucket, key)
        object_acl.put(ACL="public-read")

        log_info("resource : " + str(resource))
        table = get_dynamodb_table(os.environ["PowalcoFiles"])
        file_data = first(table.query(KeyConditionExpression=Key("resource").eq(resource))["Items"])
        if "powalcoUpload" not in file_data or file_data["powalcoUpload"] is None or ("powalcoUpload" in file_data and file_data["powalcoUpload"]["success"] == False):
            type_ = str(file_data["type"]).upper()
            edl_type = None
            if type_ == "EDLE":
                edl_type = "Entry"
            elif type_ == "EDLS":
                edl_type = "Exit"
            else:
                raise Exception("Unknown Type `{}`".format(type_))

            print("edl_type")
            print(edl_type)
            site_id = file_data["powalco_id"]
            powalco_random_identifier = file_data["powalco_random_identifier"]
            print("site_id")
            print(site_id)
            public_url = f"{prefix}/{powalco_random_identifier}/.pdf"
            if "amazonaws" in prefix:
                file_url = public_bucket_url
            else:
                file_url = public_url
            print("file_url")
            print(file_url)
            file_upload(site_id, file_url, edl_type)
            table.update_item(
                Key={"resource": resource},
                UpdateExpression="SET powalcoUpload = :powalcoUpload",
                ExpressionAttributeValues={
                    ":powalcoUpload": {
                        "success": True,
                        "timestamp": Decimal(int(datetime.now().timestamp() * 1000)),
                    }
                },
            )
    except Exception as e:
        log_err(e)
        table.update_item(
            Key={"resource": resource},
            UpdateExpression="SET powalcoUpload = :powalcoUpload",
            ExpressionAttributeValues={
                ":powalcoUpload": {
                    "success": False,
                    "timestamp": Decimal(int(datetime.now().timestamp() * 1000)),
                    "error": str(e),
                }
            },
        )
        raise e


def file_upload(site_id, file_url, edl_type):
    print("file upload")
    site = PowalcoSite(site_id)
    print("1")
    has_de = site.check_de_exists()
    print("2")
    if not has_de:
        print("a")
        site.create_de()
    print("3")
    site.check_if_gdv_competent_set()
    print("4")
    if edl_type == "Entry":
        print("edl entry")
        print("a")
        edl_id = site.create_edl(edl_type)
        print("b")
        d = site.create_item_url(edl_id, file_url, "EDLE")
        print("c")
    elif edl_type == "Exit":
        print("edl exit")
        de = site.get_de()
        print("a")
        if de["Fields"]["Status"] != "EndWorks":
            site.set_de_status("EndWorks")
        print("b")
        edls = site.get_edls()
        print("c")
        site_data = site.get_site_data()
        print("d")
        endworks_owned_edls = list(
            filter(
                lambda edl: edl["Status"] == "EndWorks" and edl["OrganisationID"] == site_data["Fields"]["OrganisationID"],
                edls,
            )
        )
        if len(endworks_owned_edls) == 0:
            print("xwll create edl")
            edl_id = site.create_edl(edl_type)
        else:
            edl_id = endworks_owned_edls[0]
        print("ah?")
        t = site.create_item_url(edl_id, file_url, "EDLS")
        print("oh?")
