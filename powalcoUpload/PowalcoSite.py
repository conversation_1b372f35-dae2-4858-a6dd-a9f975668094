import json
import os

import requests
from requests_toolbelt import MultipartEncoder

from utils.aws_utils import get_secret_string

API_BASE_URL = os.environ["PowalcoApi"]
API_ORG_TOKEN = get_secret_string(os.environ["PowalcoApiKey"])


class PowalcoSite(object):
    def __init__(self, site_id):
        print("API_ORG_TOKEN")
        print(API_ORG_TOKEN)
        print("API_BASE_URL")
        print(API_BASE_URL)
        session_url = f"{API_BASE_URL}Authorization/GetToken?type=Site&id={site_id}"
        organisation_url = f"{API_BASE_URL}Authorization/GetToken?type=Organisation"
        headers = {"Authorization": f"bearer {API_ORG_TOKEN}"}
        api_response = requests.get(session_url, headers=headers)
        print("api_response")
        print(api_response)
        api_response_json = api_response.json()
        print("api_response_json")
        print(api_response_json)
        site_token = api_response_json["Token"]
        organisation_token_response = requests.get(organisation_url, headers=headers)
        organisation_token_response_json = organisation_token_response.json()
        organisation_token = organisation_token_response_json["Token"]
        session = requests.session()
        organisation_session = requests.session()
        session.headers = {"Authorization": f"bearer {site_token}"}
        organisation_session.headers = {"Authorization": f"bearer {organisation_token}"}
        self.session = session
        self.organisation_session = organisation_session
        self.api_url = API_BASE_URL
        self.site_token = site_token
        self.site_id = site_id

    def get_site_data(self):
        res = self.session.get(f"{self.api_url}sites/GetData")
        return res.json()

    def get_de(self):
        response = self.session.get(f"{self.api_url}de/get")
        de = response.json()
        return de

    def check_de_exists(self):
        try:
            de = self.get_de()
            return "ID" in de
        except:
            return False

    def create_de(self):
        response = self.session.post(f"{self.api_url}de/create")
        return response.json()

    def set_de_status(self, status):
        payload = {"Status": status}
        response = self.session.post(f"{self.api_url}de/update", data=payload)
        updated_de = response.json()
        return updated_de

    def get_gdvs(self):
        response = self.session.get(f"{self.api_url}de/getgdvs")
        gdvs = response.json()
        return gdvs

    def check_if_gdv_competent_set(self):
        gdvs = self.get_gdvs()
        print("gdvs")
        print(gdvs)
        gdv_competent = list(filter(lambda gdv: gdv["IsCompetent"] == True, gdvs))
        if len(gdv_competent) == 0:
            print("no gdv competent set")
            gdv_id = gdvs[0]["OrganisationID"]
            self.set_competent_gdv(gdv_id)
        else:
            print("gdv competent found")
        return

    def set_competent_gdv(self, gdv_id):
        return self.session.post(f"{self.api_url}de/setgdvcompetent/{gdv_id}")

    def create_edl(self, edl_type):
        # ID: 0 indicates we are creating
        site_data = self.get_site_data()
        organisation = site_data["Fields"]["OrganisationID"]
        edl_params = {"ID": 0, "Type": edl_type, "Organisation": organisation}
        multipart_data = MultipartEncoder(fields={"parameters": json.dumps(edl_params)})
        try:
            edl_response = self.session.post(
                f"{self.api_url}edl/update",
                data=multipart_data,
                headers={"Content-Type": multipart_data.content_type},
            )
            edl = edl_response.json()
            return edl["ID"]
        except Exception as err:
            print("err")
            print(err)

    def get_edls(self):
        edls_response = self.session.post(f"{self.api_url}edl/get")
        return edls_response.json()

    def create_item_url(self, item_id, url, description=""):
        payload = {
            "Urls": [
                {"Url": url, "Description": description},
            ]
        }
        attached_link_response = self.organisation_session.post(
            f"{self.api_url}attachments/createitemurl/{item_id}",
            data=json.dumps(payload),
            headers={"Content-Type": "application/json"},
        )
        attached_link = attached_link_response.json()
        print("attached_link")
        print(attached_link)
        if attached_link_response.status_code >= 300:
            raise Exception(f"{self.api_url}attachments/createitemurl/{item_id} faild with status {attached_link_response.status_code}")
        return attached_link
