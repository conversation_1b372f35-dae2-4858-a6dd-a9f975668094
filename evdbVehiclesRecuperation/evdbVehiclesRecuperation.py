import json
import os

import requests

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import upload_s3_file
from utils.dict_utils import first


def process_vehicle(json_data, type_):
    default_consommation = None
    # Prevent divison by 0
    if json_data.get("Range_Real", 0) != 0:
        default_consommation = (json_data["Battery_Capacity_Useable"] * 100) / json_data["Range_Real"]
    default_puissance_charge_max = 3.7 if type_ == "Hybride" else 7.4

    return {
        "Marque": json_data["Vehicle_Make"],
        "Modele": json_data["Vehicle_Model"],
        "Version": json_data.get("Vehicle_Model_Version", None),
        "CapaciteBatterie": json_data["Battery_Capacity_Useable"],
        "Type": type_,
        "Autonomie": json_data["Range_Real"],
        # /10 to convert from Wh/km to kWh/100m
        "Consommation": json_data.get("Efficiency_Consumption_Real", default_consommation) / 10,
        "PuissanceChargeMax": json_data.get("Charge_Standard_Power", default_puissance_charge_max),
        "CarImages": first(json_data.get("Images")),
    }


@aws_lambda_handler
def handler(event, context):
    # Liste des URL et types à récupérer
    urls_and_types = [
        (
            "https://ev-database.org/export_v30/m1/bev/10387461/MV9fycFP98ydzEr2rZvXxT",
            "Electrique",
        ),
        (
            "https://ev-database.org/export_v30/m1/phev/10387461/MV9fycFP98ydzEr2rZvXxT",
            "Hybride",
        ),
    ]

    # Liste pour stocker les données extraites
    combined_data = []
    # Parcourir chaque URL pour récupérer les données
    for url, type_ in urls_and_types:
        response = requests.get(url)
        if response.status_code == 200:  # Vérifier si la requête a réussi
            json_array = response.json()  # Convertir la réponse en format JSON
            # Ajouter au tableau combiné
            combined_data.extend([process_vehicle(json_data, type_) for json_data in json_array])
    if len(combined_data) != 0:
        upload_s3_file(os.environ["BUCKET"], "evdb.json", json.dumps(combined_data))
