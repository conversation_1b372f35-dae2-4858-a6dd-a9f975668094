import json
import uuid

from utils.errors import BadRequestError


def generate(event, context):
    query_string_parameters = event.get("queryStringParameters", {})

    if not query_string_parameters:
        return {"statusCode": 200, "body": json.dumps(str(uuid.uuid1()))}
    num = query_string_parameters.get("Num")
    if num is None:
        raise BadRequestError("Invalid params in queryStringParameters", error_code="NUM_MISSING")

    error_message = "Num must be an integer > 0"
    error_code = "NUM_INVALID"

    list_of_uuid = []
    try:
        num = int(num)
        if num < 1:
            raise BadRequestError(error_message, error_code=error_code)
        for _ in range(num):
            list_of_uuid.append(str(uuid.uuid1()))
    except ValueError:
        raise BadRequestError(error_message, error_code=error_code)

    return {"statusCode": 200, "body": json.dumps(list_of_uuid)}
