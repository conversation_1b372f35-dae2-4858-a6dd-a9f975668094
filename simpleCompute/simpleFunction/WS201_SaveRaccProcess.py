import os
import time
import uuid

import requests
from marshmallow import ValidationError

from utils.api import api_caller, basic_auth_headers
from utils.auth_utils import load_env_file
from utils.aws_utils import create_s3_presigned_url, get_dynamodb_table, upload_s3_file
from utils.dict_utils import get
from utils.errors import BadRequestError, InternalServerError
from utils.models.connection_model import Connection
from utils.validation import check_callback


def create_raccordement_draft(event, context):
    query_string_parameters = get(event, "queryStringParameters", {})
    resume_url = query_string_parameters.get("ResumeUrl")
    check_callback(resume_url)
    body = event.get("body")
    table = get_dynamodb_table(os.environ["RACCORDEMENT_DRAFT_TABLE"])

    retention_value = 30 * 24 * 60 * 60  # 30days in seconds
    dynamo_dict = {}
    uuid_draftid = str(uuid.uuid4())
    dynamo_dict["UUID"] = uuid_draftid
    ttl = int(time.time()) + retention_value
    dynamo_dict["ttl"] = ttl

    if not resume_url:
        raise BadRequestError("Missing Resume Url in querry string parameters", error_code="MISSING_RESUMEURL")
    try:
        connection_data = Connection.model_validate_json(body)
    except ValidationError as e:
        raise BadRequestError("Some required fields are missing or have an incorrect type", error_code="INVALID_FIELDS", extra=e.messages) from e

    if connection_data.documents:
        for doc in connection_data.documents:
            response = requests.get(doc.url)
            response.raise_for_status()
            file_content = response.content

            s3_object_name = f"{uuid.uuid4()}{doc.extension}"
            bucket_name = f"{os.environ['BUCKET']}-temp-storage-one-month"
            upload_s3_file(bucket_name, s3_object_name, file_content)

            presigned_url = create_s3_presigned_url(
                bucket_name=bucket_name,
                object_name=s3_object_name,
                expiration=retention_value,
            )
            doc.url = presigned_url
    dynamo_dict["Data"] = connection_data.model_dump_json()

    try:
        response = table.put_item(Item=dynamo_dict)
    except Exception as e:
        raise InternalServerError(f"An unexpected error occurred: {e!s}", error_code="UNEXPECTED_ERROR") from e
    if response["ResponseMetadata"]["HTTPStatusCode"] != 200:
        raise InternalServerError("PUT in DynamoDB problem", error_code="DYNAMODB_PROBLEM", extra=response["ResponseMetadata"])

    url_with_uuid = f"{resume_url}?DraftId={uuid_draftid}"

    api_caller(
        method="post",
        path="/envMessage",
        body={
            "Langue": os.environ["LANG"],
            "Header": {"TEMPLATE_ID": "REPRISE_RACCORDEMENT", "EMAIL": connection_data.applicant.email, "NO_USER_CHECK": "Y"},
            "url_reprise": url_with_uuid,
        },
        headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
    )
    return {"statusCode": 200, "body": {"DraftId": uuid_draftid}}
