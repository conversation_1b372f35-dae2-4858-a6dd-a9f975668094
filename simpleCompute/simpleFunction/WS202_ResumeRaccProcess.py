import os

from boto3.dynamodb.conditions import Key
from marshmallow import ValidationError

from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import get
from utils.errors import NotFoundError, InternalServerError


def resume_raccordement_draft(event, context):
    query_string_parameters = get(event, "queryStringParameters", {})
    draft_id = query_string_parameters.get("DraftId")
    table = get_dynamodb_table(os.environ["RACCORDEMENT_DRAFT_TABLE"])

    if not draft_id:
        raise NotFoundError("Missing DraftId in querry string parameters", error_code="MISSING_DRAFTID")
    try:
        query = table.query(KeyConditionExpression=Key("UUID").eq(draft_id))["Items"]
    except ValidationError as e:
        raise InternalServerError("Interal problems with the DynamoDB", error_code="DYNAMODB_PROBLEMS", extra=e.messages) from e

    resumed_racc_data = query[0]["Data"]

    return {"statusCode": 200, "body": resumed_racc_data}
