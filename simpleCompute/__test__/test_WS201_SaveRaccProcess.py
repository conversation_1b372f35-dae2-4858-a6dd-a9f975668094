import unittest

from simpleFunction.WS201_SaveRaccProcess import create_raccordement_draft
from utils.api import api_caller
from utils.errors import BadRequestError
from utils.mocking import mock_api


@mock_api
class TestCreateRaccordementDraft(unittest.TestCase):
    def test_draft_success(self):
        response = api_caller(
            "POST",
            "/raccordement/draft?ResumeUrl=https://resa.be/404",
            body={
                "ApplicantType": "Particulier",
                "ActAs": "PROP",
                "Address": {"Street": "avenue des sillons", "Number": "302", "Box": "A", "Postcode": "4100", "City": "Boncelles"},
                "Applicant": {
                    "Name": "Do<PERSON>",
                    "Firstname": "<PERSON>",
                    "Email": "<EMAIL>",
                    "Phone": "+32 11 22 33 44",
                    "Address": {"Street": "avenue des sillons", "Number": "302", "Box": "A", "Postcode": "4100", "City": "Boncelles", "Country": "Belgique"},
                },
                "Contact": {
                    "Name": "Doe",
                    "Firstname": "<PERSON>",
                    "Email": "<EMAIL>",
                    "Phone": "+32 11 22 33 44",
                    "Address": {"Street": "avenue des sillons", "Number": "302", "Box": "A", "Postcode": "4100", "City": "Boncelles", "Country": "Belgique"},
                },
                "Billing": {"Street": "avenue des sillons", "Number": "302", "Box": "A", "Postcode": "4100", "City": "Boncelles", "Country": "Belgique"},
                "Company": {"Name": "RESA", "LegalStatus": "SA", "Tva": "xxxxx"},
                "Meters": [
                    {
                        "Ean": "5414789632156",
                        "Number": "4017894",
                        "EnergyType": "ELEC",
                        "WorkType": "MODI_SMART",
                        "Photo": {"Name": "photo.jpg", "Url": "https://photo.url.com/my_photo.jpg"},
                    }
                ],
                "ConfirmGdpr": True,
            },
        )
        self.assertIn("DraftId", response, "Le champ 'DraftId' est absent de la réponse")

        self.assertIsNotNone(response["DraftId"], "Le 'DraftId' ne doit pas être nul")

    def test_create_raccordement_draft_missing_resume_url(self):
        event = {
            "queryStringParameters": {},
            "body": '{"EnergyType": "ELEC", "WorkType": "MODI_SMART"}',
        }
        context = {}

        with self.assertRaises(BadRequestError) as ex:
            create_raccordement_draft(event, context)

        self.assertEqual(ex.exception.error_code, "MISSING_RESUMEURL")
