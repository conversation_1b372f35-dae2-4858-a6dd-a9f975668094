import io
import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

from pydantic import ValidationError

from utils.errors import InternalServerError
from utils.mocking import mock_environnement


class TestConnectionCreate(unittest.TestCase):
    @mock_environnement
    @patch("simpleFunction.connection.api_caller")
    @patch("requests.put", new=Mock())
    def test_handler_success_ws35(self, mock_api_caller):
        from simpleFunction.connection import create

        # Mock the necessary external dependencies
        event = {
            "body": """
{
  "Synergy": true,
  "ApplicantType": "Particulier",
  "ActAs": "PROP",
  "Address": {
    "Street": "Boulevard d'Avroy",
    "Number": "38",
    "Box": "A",
    "Postcode": "4000",
    "City": "Liège",
    "Country": "BE",
    "Lat": 50.6386777,
    "Lon": 5.5677805,
    "SpwPlot": "62803B0125/00E000",
    "Note": "À droite de l'UCM"
  },
  "Applicant": {
    "Name": "Doe",
    "Firstname": "<PERSON>",
    "Email": "<EMAIL>",
    "Phone": "+32 11 22 33 44",
    "Address": {
      "Street": "Boulevard d'Avroy",
      "Number": "38",
      "Box": "A",
      "Postcode": "4000",
      "City": "Liège",
      "Country": "BE"
    }
  },
  "Contact": {
    "Name": "Doe",
    "Firstname": "John",
    "Email": "<EMAIL>",
    "Phone": "+32 11 22 33 44",
    "Address": {
      "Street": "Boulevard d'Avroy",
      "Number": "38",
      "Box": "A",
      "Postcode": "4000",
      "City": "Liège",
      "Country": "BE"
    }
  },
  "Billing": {
    "Street": "Boulevard d'Avroy",
    "Number": "38",
    "Box": "A",
    "Postcode": "4000",
    "City": "Liège",
    "Country": "BE"
  },
  "Company": {
    "Name": "RESA",
    "LegalStatus": "SA",
    "Vat": "BE0123456789"
  },
  "Meters": [
    {
      "Ean": "5414789632156",
      "Number": "4017894",
      "EnergyType": "ELEC",
      "WorkType": "MODI_SMART",
      "Quantity": 1,
      "Power": 9.2,
      "Amper": 40,
      "PhaseType": "MONO",
      "Rate": "ST",
      "Allotment": false,
      "Moving": false,
      "Photo": {
        "Name": "photo.jpg",
        "Url": "https://photo.url.com/my_photo.jpg"
      }
    }
  ],
  "ProductMoreThan10Kva": false,
  "ElecNote": "Remarque élec",
  "GasNote": "Remarque gaz",
  "GeneralNote": "Remarque générale",
  "DesiredDate": "202305",
  "Usage": "RESI",
  "HouseType": "PRIVE_MOINS",
  "SubjectToVat": true,
  "VatType": "PROP",
  "ConfirmVat": true,
  "ConfirmGdpr": true,
  "Documents": [
    {
      "Name": "Document 1",
      "Url": "https://document.url.com/my-DOC.pdf"
    }
  ]
}
            """,
            "headers": {"Accept-Language": "fr", "SessionId": "test"},
        }

        context = {}

        # Mock api_caller responses
        mock_api_caller.side_effect = [
            {},  # patch mail to user ghost account
            {},  # patch user preference
            {"PutUrl": "https://localhost/file1"},  # Upload temp S3 file meter photo
            {"PutUrl": "https://localhost/file2"},  # Upload temp S3 file document
            {"ProcessId": "12345"},  # Make async request
            {},  # Send confirmation email
        ]

        # Call handler
        response = create(event, context)

        self.assertEqual(response["statusCode"], 204)
        mock_api_caller.assert_called()
        self.assertEqual(mock_api_caller.call_count, 6)
        self.assertEqual(mock_api_caller.mock_calls[4].args, ("post", "/asynchrone"))

    @mock_environnement
    @patch("simpleFunction.connection.api_caller")
    def test_handler_success_ws35_more_than_5_meter(self, mock_api_caller):
        from simpleFunction.connection import create

        # Mock the necessary external dependencies
        event = {
            "body": """
{
  "Synergy": true,
  "Synergrid": "12.34.56",
  "ApplicantType": "Particulier",
  "ActAs": "PROP",
  "Address": {
    "Street": "Boulevard d'Avroy",
    "Number": "38",
    "Box": "A",
    "Postcode": "4000",
    "City": "Liège",
    "Country": "BE",
    "Lat": 50.6386777,
    "Lon": 5.5677805,
    "SpwPlot": "62803B0125/00E000",
    "Note": "À droite de l'UCM"
  },
  "Applicant": {
    "Name": "Doe",
    "Firstname": "John",
    "Email": "<EMAIL>",
    "Phone": "+32 11 22 33 44",
    "Address": {
      "Street": "Boulevard d'Avroy",
      "Number": "38",
      "Box": "A",
      "Postcode": "4000",
      "City": "Liège",
      "Country": "BE"
    }
  },
  "Contact": {
    "Name": "Doe",
    "Firstname": "John",
    "Email": "<EMAIL>",
    "Phone": "+32 11 22 33 44",
    "Address": {
      "Street": "Boulevard d'Avroy",
      "Number": "38",
      "Box": "A",
      "Postcode": "4000",
      "City": "Liège",
      "Country": "BE"
    }
  },
  "Billing": {
    "Street": "Boulevard d'Avroy",
    "Number": "38",
    "Box": "A",
    "Postcode": "4000",
    "City": "Liège",
    "Country": "BE"
  },
  "Company": {
    "Name": "RESA",
    "LegalStatus": "SA",
    "Vat": "BE0123456789"
  },
  "Meters": [
    {
      "Ean": "5414789632156",
      "Number": "4017894",
      "EnergyType": "ELEC",
      "WorkType": "MODI_SMART",
      "Quantity": 5,
      "Power": 9.2,
      "Amper": 40,
      "PhaseType": "MONO",
      "Rate": "ST",
      "Allotment": false,
      "Moving": false,
      "Photo": {
        "Name": "photo.jpg",
        "Url": "https://photo.url.com/my_photo.jpg"
      }
    }
  ],
  "ProductMoreThan10Kva": false,
  "ElecNote": "Remarque élec",
  "GasNote": "Remarque gaz",
  "GeneralNote": "Remarque générale",
  "DesiredDate": "202305",
  "Usage": "RESI",
  "HouseType": "PRIVE_MOINS",
  "SubjectToVat": true,
  "VatType": "PROP",
  "ConfirmVat": true,
  "ConfirmGdpr": true,
  "Documents": [
    {
      "Name": "Document 1.pdf",
      "Url": "https://document.url.com/my-DOC.pdf"
    }
  ]
}
            """,
            "headers": {"Accept-Language": "fr", "SessionId": "test"},
        }

        context = {}

        # Mock api_caller responses
        mock_api_caller.side_effect = [
            {},  # patch mail to user ghost account
            {},  # patch user preference
            {"PutUrl": "https://localhost/file1"},  # Upload temp S3 file meter photo
            {"PutUrl": "https://localhost/file2"},  # Upload temp S3 file document
            {"ProcessId": "12345"},  # Make async request
            {},  # Send confirmation email
        ]

        # Call handler
        response = create(event, context)

        self.assertEqual(response["statusCode"], 204)
        mock_api_caller.assert_called()
        self.assertEqual(mock_api_caller.call_count, 6)
        self.assertEqual(mock_api_caller.mock_calls[4].args, ("post", "/asynchrone"))

    @mock_environnement
    @patch("simpleFunction.connection.api_caller")
    def test_handler_success_ws35_if_synergrid(self, mock_api_caller):
        from simpleFunction.connection import create

        # Mock the necessary external dependencies
        event = {
            "body": """
    {
      "Synergy": true,
      "Synergrid": "12.34.56",
      "ApplicantType": "Particulier",
      "ActAs": "PROP",
      "Address": {
        "Street": "Boulevard d'Avroy",
        "Number": "38",
        "Box": "A",
        "Postcode": "4000",
        "City": "Liège",
        "Country": "BE",
        "Lat": 50.6386777,
        "Lon": 5.5677805,
        "SpwPlot": "62803B0125/00E000",
        "Note": "À droite de l'UCM"
      },
      "Applicant": {
        "Name": "Doe",
        "Firstname": "John",
        "Email": "<EMAIL>",
        "Phone": "+32 11 22 33 44",
        "Address": {
          "Street": "Boulevard d'Avroy",
          "Number": "38",
          "Box": "A",
          "Postcode": "4000",
          "City": "Liège",
          "Country": "BE"
        }
      },
      "Contact": {
        "Name": "Doe",
        "Firstname": "John",
        "Email": "<EMAIL>",
        "Phone": "+32 11 22 33 44",
        "Address": {
          "Street": "Boulevard d'Avroy",
          "Number": "38",
          "Box": "A",
          "Postcode": "4000",
          "City": "Liège",
          "Country": "BE"
        }
      },
      "Billing": {
        "Street": "Boulevard d'Avroy",
        "Number": "38",
        "Box": "A",
        "Postcode": "4000",
        "City": "Liège",
        "Country": "BE"
      },
      "Company": {
        "Name": "RESA",
        "LegalStatus": "SA",
        "Vat": "BE0123456789"
      },
      "Meters": [
        {
          "Ean": "5414789632156",
          "Number": "4017894",
          "EnergyType": "ELEC",
          "WorkType": "NOUV_FORF",
          "Quantity": 0
        }
      ],
      "ProductMoreThan10Kva": false,
      "ElecNote": "Remarque élec",
      "GasNote": "Remarque gaz",
      "GeneralNote": "Remarque générale",
      "DesiredDate": "202305",
      "Usage": "RESI",
      "HouseType": "PRIVE_MOINS",
      "SubjectToVat": true,
      "VatType": "PROP",
      "ConfirmVat": true,
      "ConfirmGdpr": true,
      "Documents": [
        {
          "Name": "Document 1",
          "Url": "https://document.url.com/my-DOC.pdf"
        }
      ]
    }
            """,
            "headers": {"Accept-Language": "fr", "SessionId": "test"},
        }

        context = {}

        # Mock api_caller responses
        mock_api_caller.side_effect = [
            {},  # patch mail to user ghost account
            {},  # patch user preference
            {"PutUrl": "https://localhost/file2"},  # Upload temp S3 file document
            {"ProcessId": "12345"},  # Make async request
        ]

        # Call handler
        response = create(event, context)

        self.assertEqual(response["statusCode"], 204)
        mock_api_caller.assert_called()
        self.assertEqual(mock_api_caller.call_count, 4)
        self.assertEqual(mock_api_caller.mock_calls[3].args, ("post", "/asynchrone"))

    def test_verify_body_elems_missing_fields(self):
        from simpleFunction.connection import create

        with self.assertRaises(ValidationError) as ex:
            create(
                {
                    "body": '{"EnergyType": "ELEC", "WorkType": "MODI_SMART", "ConfirmGdpr":false}',
                    "headers": {"Accept-Language": "fr", "SessionId": "test"},
                },
                {},
            )
        self.assertListEqual(
            [err["loc"][0] for err in ex.exception.errors()],
            [
                "ApplicantType",
                "Address",
                "Applicant",
                "Meters",
                "ConfirmGdpr",
            ],
        )

    def test_verify_body_elems_all_fields(self):

        from utils.models.connection_model import Connection
        body = """
{
  "Synergy": true,
  "Synergrid": "12.34.56",
  "ApplicantType": "Particulier",
  "ActAs": "PROP",
  "Address": {
    "Street": "Boulevard d'Avroy",
    "Number": "38",
    "Box": "A",
    "Postcode": "4000",
    "City": "Liège",
    "Country": "BE",
    "Lat": 50.6386777,
    "Lon": 5.5677805,
    "SpwPlot": "62803B0125/00E000",
    "Note": "À droite de l'UCM"
  },
  "Applicant": {
    "Name": "Doe",
    "Firstname": "John",
    "Email": "<EMAIL>",
    "Phone": "+32 11 22 33 44",
    "Address": {
      "Street": "Boulevard d'Avroy",
      "Number": "38",
      "Box": "A",
      "Postcode": "4000",
      "City": "Liège",
      "Country": "BE"
    }
  },
  "Contact": {
    "Name": "Doe",
    "Firstname": "John",
    "Email": "<EMAIL>",
    "Phone": "+32 11 22 33 44",
    "Address": {
      "Street": "Boulevard d'Avroy",
      "Number": "38",
      "Box": "A",
      "Postcode": "4000",
      "City": "Liège",
      "Country": "BE"
    }
  },
  "Billing": {
    "Street": "Boulevard d'Avroy",
    "Number": "38",
    "Box": "A",
    "Postcode": "4000",
    "City": "Liège",
    "Country": "BE"
  },
  "Company": {
    "Name": "RESA",
    "LegalStatus": "SA",
    "Vat": "BE0123456789"
  },
  "Meters": [
    {
      "Ean": "5414789632156",
      "Number": "4017894",
      "EnergyType": "ELEC",
      "WorkType": "MODI_SMART",
      "Quantity": 1,
      "Power": 9.2,
      "Amper": 40,
      "PhaseType": "MONO",
      "Rate": "ST",
      "Allotment": false,
      "Moving": false,
      "Photo": {
        "Name": "photo.jpg",
        "Url": "https://photo.url.com/my_photo.jpg"
      }
    }
  ],
  "ProductMoreThan10Kva": false,
  "ElecNote": "Remarque élec",
  "GasNote": "Remarque gaz",
  "GeneralNote": "Remarque générale",
  "DesiredDate": "202305",
  "Usage": "RESI",
  "HouseType": "PRIVE_MOINS",
  "SubjectToVat": true,
  "VatType": "PROP",
  "ConfirmVat": true,
  "ConfirmGdpr": true,
  "Documents": [
    {
      "Name": "Document 1.pdf",
      "Url": "https://document.url.com/my-DOC.pdf"
    }
  ]
}
        """
        Connection.model_validate_json(body)  # This should not raise an exception

    @patch("requests.put")
    def test_upload_file_in_memory_error(self, mock_put):
        from simpleFunction.connection import upload_file_in_memory

        # Mock the response of requests.put to simulate an error
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_put.return_value = mock_response

        file_in_memory = io.BytesIO(b"file content")

        with self.assertRaises(InternalServerError):
            upload_file_in_memory("https://example.com/put", file_in_memory)
