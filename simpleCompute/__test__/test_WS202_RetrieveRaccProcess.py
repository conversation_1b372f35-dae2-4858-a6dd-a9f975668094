import unittest

from utils.api import api_caller
from utils.mocking import mock_api


@mock_api
class TestRetrieveRaccordementDraft(unittest.TestCase):
    def test_retrieve_draft_success(self):
        response = api_caller("GET", "/raccordement/draft?DraftId=testUUID", raw=True)
        self.assertEqual(response.status_code, 200, "Le code statut doit être 200 pour un succès")
        self.assertTrue("testData" in response.text)

    def test_retrieve_raccordement_draft_missing_draft_id(self):
        response = api_caller("GET", "/raccordement/draft", raw=True)
        self.assertEqual(response.status_code, 404)
