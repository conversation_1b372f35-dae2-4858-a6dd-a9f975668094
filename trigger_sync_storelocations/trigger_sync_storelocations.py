import json
import os
import traceback
from datetime import date, timedelta

import boto3
import pysftp

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_secret, invoke_lambda
from utils.log_utils import log_err, log_info


def get_sftp_filenames():
    log_info("Retrieve SFTP credentials")
    sftp_credentials = get_secret("RESA_SFTP")

    try:
        log_info("Connect to SFTP")
        cnopts = pysftp.CnOpts()
        cnopts.hostkeys = None
        sftp = pysftp.Connection(
            host=sftp_credentials["RESA_SFTP_HOSTNAME"],
            username=sftp_credentials["RESA_SFTP_USERNAME"],
            password=sftp_credentials["RESA_SFTP_PASSWORD"],
            cnopts=cnopts,
        )
    except BaseException:
        errmsg = "ERROR: 500 Error dans la connexion avec le serveur SFTP"
        log_err(traceback.format_exc())
        raise Exception(errmsg)

    try:
        log_info("List files in SFTP folder")
        sftp.cwd(sftp_credentials["RESA_SFTP_PATH"])
        path_content = sftp.listdir_attr()
        print(path_content)
        list_of_filenames = [f.filename for f in path_content]
        print(list_of_filenames)
    except BaseException:
        errmsg = "ERROR: 500 Le fichier n a pas ete trouve dans le serveur SFTP"
        log_err(traceback.format_exc())
        raise Exception(errmsg)
    finally:
        try:
            sftp.close()
        except:
            pass

    return list_of_filenames


def get_matching_filename(sftp_filenames):
    if len(sftp_filenames) > 0:
        log_info("Search for recent store location files")
        yesterday = (date.today() - timedelta(days=1)).strftime("%Y%m%d")
        today = date.today().strftime("%Y%m%d")
        tomorrow = (date.today() + timedelta(days=1)).strftime("%Y%m%d")
        list_of_candidates = [yesterday, today, tomorrow]
        matching_filenames = [f for f in sftp_filenames if len(f) > 11 and f[:8] in list_of_candidates and f[-4:] == ".csv"]
        if len(matching_filenames) > 0:
            matching_filenames.sort(reverse=True)
            return matching_filenames[0]

    return ""


def trigger_required(sftp_filename):
    if len(sftp_filename) > 0:
        log_info("List files recently uploaded")
        try:
            s3 = boto3.client("s3")
            sftp_bucket_name = os.environ["sftp_bucket_name"]
            previous_versions_bucket = s3.list_objects(Bucket=sftp_bucket_name)
        except BaseException:
            errmsg = "ERROR: 500 Erreur lors de la connexion a s3"
            log_err(traceback.format_exc())
            raise Exception(errmsg)
        if "Contents" in previous_versions_bucket:
            previous_versions_objects = previous_versions_bucket["Contents"]
            in_previous_versions = [s3_object["Key"] == sftp_filename for s3_object in previous_versions_objects]
            return True not in in_previous_versions
        else:
            return True
    else:
        return False


def upload_to_s3(sftp_filename):
    log_info("Retrieve SFTP credentials")
    sftp_credentials = get_secret("RESA_SFTP")

    try:
        log_info("Connect to SFTP")
        cnopts = pysftp.CnOpts()
        cnopts.hostkeys = None
        sftp = pysftp.Connection(
            host=sftp_credentials["RESA_SFTP_HOSTNAME"],
            username=sftp_credentials["RESA_SFTP_USERNAME"],
            password=sftp_credentials["RESA_SFTP_PASSWORD"],
            cnopts=cnopts,
        )
    except BaseException:
        errmsg = "ERROR: 500 Error dans la connexion avec le serveur SFTP"
        log_err(traceback.format_exc())
        raise Exception(errmsg)

    try:
        log_info("Read file to be uploaded")
        sftp_file = sftp.open(sftp_credentials["RESA_SFTP_PATH"] + "/" + sftp_filename)
    except Exception:
        errmsg = "ERROR: 500 Error dans la connexion avec le fichier"
        log_err(traceback.format_exc())
        raise Exception(errmsg)

    try:
        sftp_file_data = sftp_file.read()
        sftp.close()
    except Exception:
        errmsg = "ERROR: 500 Error dans la connexion avec le fichier"
        log_err(traceback.format_exc())
        sftp.close()
        raise Exception(errmsg)

    try:
        log_info("Upload file")
        s3 = boto3.resource("s3")
        sftp_bucket_name = os.environ["sftp_bucket_name"]
        storelocation_obj = s3.Object(sftp_bucket_name, sftp_filename)
        storelocation_obj.put(Body=sftp_file_data)
    except Exception:
        errmsg = "ERROR: 500 Error dans le download de SFTP vers S3"
        log_err(traceback.format_exc())
        raise Exception(errmsg)


def delete_from_sftp(sftp_filename):
    if sftp_filename and len(sftp_filename) > 0:
        log_info("Retrieve SFTP credentials")
        sftp_credentials = get_secret("RESA_SFTP")

        try:
            log_info("Connect to SFTP")
            cnopts = pysftp.CnOpts()
            cnopts.hostkeys = None
            sftp = pysftp.Connection(
                host=sftp_credentials["RESA_SFTP_HOSTNAME"],
                username=sftp_credentials["RESA_SFTP_USERNAME"],
                password=sftp_credentials["RESA_SFTP_PASSWORD"],
                cnopts=cnopts,
            )
        except Exception:
            errmsg = "ERROR: 500 Error dans la connexion avec le serveur SFTP"
            log_err(traceback.format_exc())
            raise Exception(errmsg)

        try:
            sftp.remove(sftp_credentials["RESA_SFTP_PATH"] + "/" + sftp_filename)
            sftp.close()
        except Exception:
            errmsg = "ERROR: 500 Error deleting file"
            log_err(traceback.format_exc())
            sftp.close()
            raise Exception(errmsg)


def do_trigger(matching_filename):
    log_info("Trigger store location data synchronization")
    print(matching_filename)
    payload = json.dumps({"sftp_filename": matching_filename})
    function_name = "sync_storelocations" + "-" + os.environ["environment_name"]
    print("invoking sync")
    response = invoke_lambda(FunctionName=function_name, InvocationType="Event", Payload=payload)
    print("sync response")
    print(response)
    return response


@aws_lambda_handler
def handler(event, context):
    headers = {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": True,
    }

    if os.environ["environment_name"] == "production":
        try:
            sftp_filenames = get_sftp_filenames()
            matching_filename = get_matching_filename(sftp_filenames)
            is_trigger_required = trigger_required(matching_filename)

            if is_trigger_required:
                upload_to_s3(matching_filename)
                do_trigger(matching_filename)
                delete_from_sftp(matching_filename)

            response = {
                "statusCode": 200,
                "isBase64Encoded": False,
                "headers": headers,
                "body": json.dumps({"trigger_sync": is_trigger_required}),
            }
        except Exception as e:
            response = {
                "statusCode": 500,
                "isBase64Encoded": False,
                "headers": headers,
                "body": json.dumps({"error_message": str(e)}),
            }
    else:
        response = {
            "statusCode": 200,
            "isBase64Encoded": False,
            "headers": headers,
            "body": json.dumps({"trigger_sync": False}),
        }

    return response


if __name__ == "__main__":
    print(handler(None, None))
