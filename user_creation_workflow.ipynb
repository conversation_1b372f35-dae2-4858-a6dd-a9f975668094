#%%
firstname = "La maison liégoise"
lastname = " "
email = "<EMAIL>"
phone = None
phonefixe = ""
adresse = {
    "Cdpostal": "4000",
    "Localite": "Liege",
    "Rue": "string",
    "NumRue": "string",
    "CodePays": "BE",
}

VERSION = "latest"
API_URL = "https://slk9gfimga.execute-api.eu-west-1.amazonaws.com/v0"  # f"https://api-acceptance.resa.be/{VERSION}"
headers = {"Content-Type": "application/json", "accept": "application/json"}
#%%
import requests
import json

response = requests.post(
    API_URL + "/utilisateurs",
    data=json.dumps(
        {
            "Firstname": firstname,
            "Lastname": lastname,
            "Email": email,
            "ContactEmail": email,
            "Phone": phone,
            "PhoneFixe": phonefixe,
            "Adresse": adresse,
        }
    ),
    headers=headers,
).json()

try:
    ghost = response["SessionId"]
    headers["SessionId"] = ghost
    headers["Authorization"] = None
    print(ghost)
except:
    print(response)
#%%
import requests
import json
import webbrowser
import urllib.parse

password = "Welcome@2022"

callback = "https://my.resa.be/activate"
response = requests.post(
    API_URL + "/utilisateurs/activate/sendMail",
    params={"Callback": callback},
    data=json.dumps({"Email": email}),
    headers=headers,
)
print(response.json())
assert str(response.status_code).startswith("2")
webbrowser.open(f"https://yopmail.com/fr/?{urllib.parse.quote_plus(email.split('@')[0])}")
link = input("Email link")
token = link.split("Token=")[1]
response = requests.post(
    API_URL + "/utilisateurs/activate",
    params={"Token": token},
    data=json.dumps({"Password": password}),
    headers=headers,
)
print(response.json())
assert str(response.status_code).startswith("2")
#%%
response = requests.post(
    API_URL + "/utilisateurs/authenticate",
    data=json.dumps({"Username": email, "Password": password}),
    headers=headers,
)
print(response.json().get("IdToken"))