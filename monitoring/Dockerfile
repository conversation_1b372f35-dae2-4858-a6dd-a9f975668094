FROM amazonlinux:2018.03

RUN ulimit -n 1024 && yum -y update && yum -y install \
	gcc \
	openssl-devel \
	bzip2-devel \
	libffi-devel \
	zip \
	wget \
	python3-devel \
	openldap-devel \      
	&& yum clean all

ENV LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH

WORKDIR /opt
RUN wget https://www.python.org/ftp/python/3.7.7/Python-3.7.7.tgz
RUN tar xzf Python-3.7.7.tgz \
	&& cd Python-3.7.7 \
	&& ./configure --enable-optimizations \
	&& make altinstall \
	&& cd .. \
	&& rm -rf Python-3.7.7

WORKDIR /myresa
ENV WORKDIR /myresa

COPY monitoring/setup.py /myresa/

RUN mkdir -p packages
RUN pip3.7 install . -t packages --no-cache-dir

COPY monitoring/lambda_function.py "$WORKDIR/packages/lambda_function.py"
COPY monitoring/tools.py "$WORKDIR/packages/tools.py"
COPY monitoring/env.json "$WORKDIR/packages/env.json"
COPY monitoring/notification.py "$WORKDIR/packages/notification.py"
COPY monitoring/notification_template.html "$WORKDIR/packages/notification_template.html"
COPY utils "$WORKDIR/packages/utils"
COPY resources "$WORKDIR/packages/resources"
COPY test "$WORKDIR/packages/test"

RUN ls packages

RUN cd packages && \
	zip -r $WORKDIR/monitoring.zip * lambda_function.py env.json

CMD ["/bin/bash"]
