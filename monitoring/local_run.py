import json
from datetime import datetime

from monitoring.lambda_function import run_tests
from monitoring.tools import test_modules, load_env


##_________________________________________________________________________||
def handler():
    start_time = datetime.now()

    env_data = load_env("./monitoring/local.env.json")
    env = env_data["ENV"]
    env_content = {env: env_data}
    TEST_MODULES = test_modules()

    test_results = {}
    test_env_result = {}
    logs_before = {}

    run_tests(TEST_MODULES, env, env_content, test_env_result, logs_before, test_results)

    print("failures")
    for test in test_results["failures"]:
        print("    " + str(test))
    print("errors")
    for test in test_results["errors"]:
        print("    " + str(test))

    print(f"Execution time : {datetime.now() - start_time}")
    return {"status": 200, "body": json.dumps({})}


if __name__ == "__main__":
    handler()
