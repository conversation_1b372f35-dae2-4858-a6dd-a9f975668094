{"dev": {"ENV": "dev", "URL": "https://pqx8lp3l98.execute-api.eu-west-1.amazonaws.com/v0", "API_URL": "https://pqx8lp3l98.execute-api.eu-west-1.amazonaws.com/v0", "DYNAMODB": "MyResaUser_dev", "AD_SECRET": "MyResaAPI/ActiveDirectory/qta", "SAP_URL": "153.89.248.95:44301", "ADFS_SECRET": "MyResaAPI/ADFS/dev", "AGORA_SECRET": "MyResaAPI/Agora/dev", "SHAREPOINT_SECRET": "MyResaAPI/Sharepoint/QTA", "ErrorSNS": "arn:aws:sns:eu-west-1:204480941676:MyResaAPI-failure-dev", "SAP_BasicAuth": "MyResaAPI/BasicAuth/QTA", "DIGACERT_URL": "https://test.digacert.be/app/services/digacert-api"}, "qta": {"ENV": "qta", "URL": "https://api-acceptance.resa.be/latest", "API_URL": "https://api-acceptance.resa.be/latest", "DYNAMODB": "MyResaUser_qta", "AD_SECRET": "MyResaAPI/ActiveDirectory/qta", "SAP_URL": "153.89.248.95:44301", "ADFS_SECRET": "MyResaAPI/ADFS/dev", "AGORA_SECRET": "MyResaAPI/Agora/dev", "SHAREPOINT_SECRET": "MyResaAPI/Sharepoint/QTA", "ErrorSNS": "arn:aws:sns:eu-west-1:204480941676:MyResaAPI-failure-dev", "SAP_BasicAuth": "MyResaAPI/BasicAuth/QTA", "DIGACERT_URL": "https://test.digacert.be/app/services/digacert-api", "API_KEY": "uAvIGTqpdgexRie3DHW0hXNkECc0GuLH"}, "QLA": {"ENV": "QLA", "API_URL": "https://api-qla.resa.be/latest", "URL": "https://api-qla.resa.be/latest", "DYNAMODB": "MyResaUser_qla", "AD_SECRET": "MyResaAPI/ActiveDirectory/QLA", "SAP_URL": "153.89.248.126:44302", "ADFS_SECRET": "MyResaAPI/ADFS/QLA", "AGORA_SECRET": "MyResaAPI/Agora/dev", "SHAREPOINT_SECRET": "MyResaAPI/SharepointQTE", "ErrorSNS": "arn:aws:sns:eu-west-1:204480941676:MyResaAPI-failure-dev", "SAP_BasicAuth": "MyResaAPI/BasicAuth/QLA", "DIGACERT_URL": "https://test.digacert.be/app/services/digacert-api"}, "production": {"ENV": "production", "API_URL": "https://api.resa.be/latest", "URL": "https://api.resa.be/latest", "DYNAMODB": "MyResaUser_prd", "AD_SECRET": "MyResaAPI/ActiveDirectory/prd", "SAP_URL": "153.89.248.109:44325", "ADFS_SECRET": "MyResaAPI/ADFS/prd", "AGORA_SECRET": "MyResaAPI/Agora/prod", "SHAREPOINT_SECRET": "MyResaAPI/Sharepoint/PRD", "ErrorSNS": "arn:aws:sns:eu-west-1:204480941676:MyResaAPI-failure-prod", "SAP_BasicAuth": "MyResaAPI/BasicAuth/PRD", "DIGACERT_URL": "https://digacert.be/app/services/digacert-api"}}