import json
import os
from multiprocessing import Process, Manager

import ldap3

from notification import handleNotification, fetchLogs, set_environement
from tools import send_to_slack, mergeResult, launch_suite, test_modules, load_env
from utils.aws_utils import get_dynamodb_table, get_secret, scanAll
from utils.dict_utils import get
from utils.ldap_utils import LDAP
from utils.log_utils import CloudwatchLogger
from utils.tools_testing import TestUserBuilder

##_________________________________________________________________________||

HOOK_URL = get(os.environ, "HOOK")


def run_tests(test_modules, env, env_content, test_env_result, logs_before, test_results=None):
    if test_results is None:
        test_results = {}

    should_log = get(os.environ, "should_log")
    logs_before[env] = fetchLogs("monitoring-" + env)
    set_environement(env, env_content)
    _test_results = launch_suite(
        test_modules,
        {
            "getTestUserBuilder": (lambda: TestUserBuilder(get_secret(os.environ["AD_SECRET"]), os.environ["DYNAMODB"])),
            "getLDAP": (lambda: LDAP.loadFromSecret(os.environ["AD_SECRET"])),
        },
    )

    test_results.update(_test_results)
    logger = CloudwatchLogger("monitoring-" + env if should_log else None)
    mergeResult(env, test_results, logger, test_env_result)
    logger.flush()


##_________________________________________________________________________||
def lambda_handler(event, context):
    env_content = load_env()
    TEST_MODULES = test_modules()

    manager = Manager()
    test_env_result = manager.dict()
    logs_before = manager.dict()

    processes = []
    for env in env_content.keys():
        if not env.startswith("__"):
            processes.append(
                Process(
                    name=env,
                    target=run_tests,
                    args=(TEST_MODULES, env, env_content, test_env_result, logs_before),
                )
            )
    for process in processes:
        process.start()
    for process in processes:
        process.join()

    cleaning(env_content)

    if HOOK_URL:
        send_to_slack(test_env_result, HOOK_URL)

    if "COGNITO_POOL_ID" in os.environ:
        handleNotification(logs_before)

    return {"status": 200, "body": json.dumps({})}


def cleaning(env_content):
    for env in env_content.keys():
        if not env.startswith("__"):
            # CLEANING AD
            ldap = LDAP.loadFromSecret(env_content[env]["AD_SECRET"])
            test_users = ldap.conn.extend.standard.paged_search(
                search_base=ldap.folder_dn,
                search_filter="(CN=Tes.User*)",  # WARNING !!! TOUCHY LINE
                search_scope=ldap3.SUBTREE,
                attributes="*",
                generator=True,
            )
            for test_user in test_users:
                user_dn = test_user["raw_dn"].decode("utf-8")
                ldap.conn.delete(user_dn)
            # CLEANING DDB
            table = get_dynamodb_table(env_content[env]["DYNAMODB"])
            tests = scanAll(
                env_content[env]["DYNAMODB"],
                {
                    "FilterExpression": "begins_with(uid, :x)",
                    "ExpressionAttributeValues": {":x": "Tes.User"},
                },
            )  # WARNING !!! TOUCHY LINE
            for test in tests:
                table.delete_item(Key={"uid": test["uid"]})
