#!/bin/bash
LAMBDA_NAME=${1:-"MyResaAPI-monitoring"}
./bin/genOpenapi.py
DOCKER_BUILDKIT=1 docker build --network host -t myresa-monitoring -f monitoring/Dockerfile . 
docker run --network host myresa-monitoring /bin/bash
docker cp `docker ps -a | sed -n 2p | awk '{print $1}'`:/myresa/monitoring.zip .
aws s3 cp monitoring.zip  s3://my-resa-api-monitoring/lambdas/monitoring.zip --region eu-west-1  
aws lambda update-function-code --function-name $LAMBDA_NAME --s3-bucket my-resa-api-monitoring --s3-key lambdas/monitoring.zip --region eu-west-1 > /dev/null
aws lambda invoke --function-name $LAMBDA_NAME /dev/null &