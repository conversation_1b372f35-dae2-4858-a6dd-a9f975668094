import json
import os
import unittest
from threading import Lock, Thread

import requests

from utils.dict_utils import get
from utils.tools_testing import writeTestLog


##_________________________________________________________________________||


def test_modules():
    TEST_MODULES = []
    for r, d, f in os.walk("./test"):
        for file in f:
            if file.startswith("test_") and file.endswith(".py"):
                module_name = r.replace(".", "").replace("/", ".") + "." + file.replace(".py", "")
                TEST_MODULES.append(module_name[1:])
    return TEST_MODULES


def load_env(file="env.json"):
    f = open(file, "r")
    env_content = json.loads(f.read())
    f.close()
    return env_content


def mergeResult(env_name, result, logger, test_env_results):
    def handleTest(testcase, status, logs=None):
        test_name = testcase
        if test_name not in test_env_results:
            test_env_results[test_name] = {}
        # test_env_result[test_name][env_name] = {...} doesn't work with multiprocess dict
        test_env_result = test_env_results[test_name]
        test_env_result[env_name] = {
            "testcase": testcase,
            "status": status,
            "logs": logs,
        }
        test_env_results[test_name] = test_env_result
        writeTestLog(testcase, status, logger)

    for error in result["errors"]:
        handleTest(error[0], "ERROR", error[1])
    for failure in result["failures"]:
        handleTest(failure[0], "FAIL", failure[1])
    for successes in result["successes"]:
        handleTest(successes, "SUCCEED")


def set_environement(env_name, env_content):
    os.environ = {**os.environ, **env_content[env_name]}


def iterate_tests(test_suite_or_case):
    """Iterate through all of the test cases in 'test_suite_or_case'."""
    try:
        suite = iter(test_suite_or_case)
    except TypeError:
        yield test_suite_or_case
    else:
        for test in suite:
            for subtest in iterate_tests(test):
                yield subtest


class AsyncTest(Thread):
    __lock = Lock()

    def __init__(self, test_case, result_dict):
        Thread.__init__(self)
        self.test_case = test_case
        self.result_dict = result_dict

    def run(self):
        # module = __import__(self.module_name)
        runner = unittest.TextTestRunner(resultclass=TextTestResultWithSuccesses)
        test_result = runner.run(self.test_case)
        self.result_dict[id(self.test_case)] = test_result


def launch_suite(suite, dependenciesInjections=None):
    if dependenciesInjections is None:
        dependenciesInjections = {}
    result_dict = {}
    threads = []
    result = {
        "failures": [],
        "errors": [],
        "skipped": [],
        "successes": [],
        "testsRun": 0,
    }
    test_suite = unittest.defaultTestLoader.loadTestsFromNames(suite)

    for test_case in iterate_tests(test_suite):
        for name, value in dependenciesInjections.items():
            setattr(test_case, name, value)
        t = AsyncTest(test_case, result_dict)
        threads.append(t)
        t.start()
    for t in threads:
        t.join()
        test_result = get(result_dict, id(t.test_case))
        if test_result:
            for key in result.keys():
                result_type = getattr(test_result, key)

                if isinstance(result_type, (list, tuple)):
                    _result = []
                    for test_result_val in result_type:
                        if isinstance(test_result_val, (list, tuple)):
                            _result.append((test_result_val[0]._testMethodName, test_result_val[1]))
                        else:
                            _result.append(test_result_val._testMethodName)
                    result_type = _result

                result[key] += result_type

    return result


##_________________________________________________________________________||
def send_to_slack(result_value, url):
    print("Sending to Slack")

    def statusIcon(status):
        if status == "SUCCEED":
            return ":white_check_mark:"
        elif status == "FAIL":
            return ":x:"
        elif status == "ERROR":
            return ":bangbang:"
        return ":grey_question:"

    logs = []
    summary = []
    cloudwatch_link = "https://eu-west-1.console.aws.amazon.com/cloudwatch/home?region=eu-west-1#logsV2:log-groups/log-group/$252Faws$252Flambda$252FMyResaAPI-monitoring"

    for testname, envs in result_value.items():
        logs_ = ["*" + env + "* : " + result_data["logs"] for env, result_data in envs.items() if result_data["logs"]]
        if logs_:
            logs.append({"title": testname, "value": "\n".join(logs_), "short": False})
        summary.append(
            {
                "title": testname,
                "value": "\n".join([statusIcon(result_data["status"]) + " : " + env for env, result_data in envs.items()]),
                "short": True,
            }
        )
    slack_data = {
        "username": "NotificationBot",
        "icon_emoji": ":satellite:",
        # "text": "<!channel>" if logs else ""
        "attachments": [
            {"color": "#777777", "title": "Summary", "fields": summary},
            {
                "color": "#ee3333",
                "pretext": "----------------------",
                "author_name": "CloudWatch Logs",
                "author_link": cloudwatch_link,
                "title": "Logs",
                "fields": logs,
            },
        ],
    }

    h = {"Content-Type": "application/json"}

    requests.post(url, data=json.dumps(slack_data), headers=h)


##_________________________________________________________________________||
class TextTestResultWithSuccesses(unittest.TextTestResult):
    def __init__(self, *args, **kwargs):
        super(TextTestResultWithSuccesses, self).__init__(*args, **kwargs)
        self.successes = []

    def addSuccess(self, test):
        super(TextTestResultWithSuccesses, self).addSuccess(test)
        self.successes.append(test)
