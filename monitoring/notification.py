import functools
import json
import os

import boto3

from utils.aws_utils import publishSNS
from utils.dict_utils import first
from utils.dict_utils import get, merge


def set_environement(env_name, env_content):
    os.environ = {**os.environ, **env_content[env_name]}


def getUsers():
    client = boto3.client("cognito-idp")
    return [user for user in client.list_users(UserPoolId=os.environ["COGNITO_POOL_ID"])["Users"]]


def getUserAttribute(user, attribute):
    return first([attr["Value"] for attr in user["Attributes"] if attr["Name"] == attribute])


def isUserInterested(user, scenario):
    subs = []
    try:
        subs = json.loads(getUserAttribute(user, "custom:subscriptions"))
    except:
        pass
    return scenario in subs


def fetchLogs(logGroupName):
    cloudwatch = boto3.client("logs")
    try:
        stream = cloudwatch.describe_log_streams(logGroupName=logGroupName, orderBy="LastEventTime", descending=True, limit=1)
        streamName = get(first(stream["logStreams"]), "logStreamName")
        events = cloudwatch.get_log_events(logGroupName=logGroupName, logStreamName=streamName)["events"]
        return [event["message"] for event in events]
    except cloudwatch.exceptions.ResourceNotFoundException:
        return ""


def structureDependencyLogs(logsArray):
    dependencyLogs = [log for log in logsArray if "[DEPENDENCY]" in log]

    def reduce_(struct, log):
        split_ = log.split(" ")
        tags = split_[0]
        object_ = split_[1]
        scenario_ = split_[2]
        succeed = "[SUCCEED]" in log
        scenario = scenario_.replace("(", "").replace(")", "")
        struct[object_] = merge(get(struct, object_, {}), {scenario: succeed})
        return struct

    return functools.reduce(reduce_, dependencyLogs, {})


def structureEndpointLogs(logsArray):
    endpointLogs = [log for log in logsArray if "[ENDPOINT]" in log]

    def reduce_(struct, log):
        split_ = log.split(" ")
        tags = split_[0]
        method = split_[1]
        endpoint = split_[2].replace('"', "")
        scenario = method + " " + endpoint
        fail = "[FAIL]" in log
        existing = get(struct, endpoint, {})
        if not get(existing, scenario, True):
            fail = True
        struct[endpoint] = merge(get(struct, endpoint, {}), {scenario: not fail})
        return struct

    return functools.reduce(reduce_, endpointLogs, {})


def structureLogs(logsArray):
    endpointLogs = structureEndpointLogs(logsArray)
    dependencyLogs = structureDependencyLogs(logsArray)

    return merge(dependencyLogs, endpointLogs)


def difference(old, new):
    """return a list of scenario name"""
    ret = []
    for cat, scenarios in new.items():
        for scenario, status in scenarios.items():
            status2 = get(get(old, cat, {}), scenario)
            print("in difference", cat, scenario, status, status2)
            if (status2 is not None) and (status == False) and (status2 != status):
                # if change and not successful
                ret.append(scenario)
    return ret


def failings(logs):
    """return a list of scenario name"""
    ret = []
    for cat, scenarios in logs.items():
        for scenario, status in scenarios.items():
            if not status:
                ret.append(scenario)
    return ret


def buildTemplate(newly, interested, others):
    f = open("notification_template.html", "r")
    template = f.read()
    f.close()
    return template.format(
        newly_failing="".join(["<li>" + line + "</li>" for line in newly]),
        display_other_responsabilities="block" if interested else "none",
        other_responsabilities="".join(["<li>" + line + "</li>" for line in interested]),
        display_other="block" if others else "none",
        other="".join(["<li>" + line + "</li>" for line in others]),
    )


def sendMail(email, subject, body):
    print("sendmail", email, body)
    client = boto3.client("ses")
    client.send_email(
        Source=os.environ["SENDER_EMAIL"],
        Destination={"ToAddresses": [email]},
        Message={
            "Subject": {"Data": subject, "Charset": "UTF-8"},
            "Body": {"Html": {"Data": body, "Charset": "UTF-8"}},
        },
    )


def handleNotification(logStruct):
    """logStruct = {env : { before|after : [logs] }}"""
    print("handleNotification", logStruct)
    newly_scenarios = {}
    failings_scenario = {}

    f = open("env.json", "r")
    env_content = json.loads(f.read())
    f.close()

    for env in logStruct.keys():
        print(env)
        set_environement(env, env_content)
        before = structureLogs(logStruct[env])
        after = structureLogs(fetchLogs("monitoring-" + env))
        print("before", before)
        print("after", after)
        diff = difference(before, after)  # newly failing tests
        fails = failings(after)
        print("diff", diff)
        print("fails", fails)
        for scenario in diff:
            envs = get(newly_scenarios, scenario, [])
            newly_scenarios[scenario] = envs + [env]
        for scenario in fails:
            envs = get(failings_scenario, scenario, [])
            failings_scenario[scenario] = envs + [env]

        diff_failing = [test for test in diff if test in fails]
        if diff_failing:
            publishSNS(
                os.environ["ErrorSNS"],
                json.dumps({"Environnement": env, "Status": diff_failing}),
                subject=f"Monitoring regression ({env})",
            )

    print("newly_scenarios", newly_scenarios)

    for user in getUsers():
        interested_by = [scenario for scenario in newly_scenarios.keys() if isUserInterested(user, scenario)]
        print("user", user, interested_by)
        if interested_by:
            print(user, "is interested")
            other_responsabilities = [test for test in failings_scenario.keys() if isUserInterested(user, test) and test not in interested_by]
            other = [test for test in failings_scenario.keys() if not isUserInterested(user, test)]
            body = buildTemplate(
                [test + " in " + "/".join(newly_scenarios[test]) for test in interested_by],
                [test + " in " + "/".join(failings_scenario[test]) for test in other_responsabilities],
                [test + " in " + "/".join(failings_scenario[test]) for test in other],
            )
            email = getUserAttribute(user, "email")
            if email:
                sendMail(email, "MyResaAPI monitoring", body)
