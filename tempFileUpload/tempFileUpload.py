import json
import os
from uuid import uuid4

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import create_s3_presigned_url

TMP_BUCKET = f"{os.environ['BUCKET']}-temp-storage"


@aws_lambda_handler
def handler(event, *args, **kwargs):
    tmp_file = f"ws112/{uuid4()}.{event['queryStringParameters']['FileName'].split('.')[-1]}"
    content_type = event["queryStringParameters"].get("ContentType", "*")
    get_url = create_s3_presigned_url(TMP_BUCKET, tmp_file)
    put_url = create_s3_presigned_url(TMP_BUCKET, tmp_file, method="put", ContentType=content_type)

    return {
        "headers": {
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/json",
        },
        "isBase64Encoded": False,
        "statusCode": 201,
        "body": json.dumps({"GetUrl": get_url, "PutUrl": put_url}),
    }
