resource "aws_sns_topic" "General_CloudWatch_Alarms_Topic" {
  name = "General_CloudWatch_Alarms_Topic"
}

resource "aws_cloudwatch_metric_alarm" "pinpoint_sms_limit_alert" {
  alarm_name          = "Pinpoint SMS account spending limit alert"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "SMSMonthToDateSpentUSD"
  namespace           = "AWS/SNS"
  period              = "60"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = <<EOF
 The SMS monthly cost is close to the set spending limit (80%).
 The spending limit should maybe be increased before it reach the limit.
 See docs for how to increase it : https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-sms-awssupport-spend-threshold.html
 EOF
  alarm_actions       = [aws_sns_topic.General_CloudWatch_Alarms_Topic.arn]
}
