resource "aws_cloudwatch_event_target" "check_updateActiveDirectoryPublicKey" {
  rule = aws_cloudwatch_event_rule.every_5min.name
  arn  = module.updateActiveDirectoryPublicKey.lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_updateActiveDirectoryPublicKey" {
  statement_id  = "AllowExecutionFromCloudWatch-updateActiveDirectoryPublicKey"
  action        = "lambda:InvokeFunction"
  function_name = module.updateActiveDirectoryPublicKey.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.every_5min.arn
}

resource "aws_cloudwatch_event_rule" "every_day_at_8" {
  name                = "every-day-at-8-${local.workspace["stage"]}"
  description         = "Fires every day at 7 o'clock (UTC) - 8 o'clock (UTC +1)"
  schedule_expression = "cron(0 7 * * ? *)"
  tags = {
    Service = "MyResaAPI"
  }
}

resource "aws_cloudwatch_event_target" "check_alertConsoDailyAt8" {
  rule = aws_cloudwatch_event_rule.every_day_at_8.name
  arn  = module.sqlDrivenLambda.lambda.arn

  input_transformer {
    input_paths = {
      "original_event" = "$"
    }

    input_template = <<EOF
    {
      "task": "check_alert",
      "original_event": <original_event>
    }
    EOF
  }
}

resource "aws_lambda_permission" "allow_cloudwatch_check_alertConsoDailyAt8" {
  statement_id = "AllowExecutionFromCloudWatch-check_alertConsoDailyAt8"
  action        = "lambda:InvokeFunction"
  function_name = module.sqlDrivenLambda.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.every_day_at_8.arn
}

resource "aws_cloudwatch_event_target" "check_BilanConsoMonthlyAt8" {
  rule = aws_cloudwatch_event_rule.third_of_month_at_8.name
  arn  = module.sqlDrivenLambda.lambda.arn

  input_transformer {
    input_paths = {
      "original_event" = "$"
    }

    input_template = <<EOF
    {
      "task": "check_bilan",
      "original_event": <original_event>
    }
    EOF
  }
}

resource "aws_lambda_permission" "allow_cloudwatch_check_BilanConsoMonthlyAt8" {
  statement_id  = "AllowExecutionFromCloudWatch-check_BilanConsoMonthlyAt8"
  action        = "lambda:InvokeFunction"
  function_name = module.sqlDrivenLambda.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.third_of_month_at_8.arn
}

resource "aws_cloudwatch_event_target" "check_ghostCleaner" {
  rule = aws_cloudwatch_event_rule.every_week.name
  arn  = module.ghostCleaner.lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_ghostCleaner" {
  statement_id  = "AllowExecutionFromCloudWatch-ghostCleaner"
  action        = "lambda:InvokeFunction"
  function_name = module.ghostCleaner.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.every_week.arn
}

resource "aws_cloudwatch_event_rule" "every_one_month" {
  name                = "every_one_month-${local.workspace["stage"]}"
  description         = "Fires every one month"
  schedule_expression = "cron(0 0 1 * ? *)"
  tags = {
    Service = "MyResaAPI"
  }
}

resource "aws_cloudwatch_event_rule" "first_of_month_at_8" {
  name                = "first-of-month-at-8-${local.workspace["stage"]}"
  description         = "Fires on the first day of every month at 7 o'clock (UTC) - 8 o'clock (UTC +1)"
  schedule_expression = "cron(0 7 1 * ? *)"
  tags = {
    Service = "MyResaAPI"
  }
}
resource "aws_cloudwatch_event_rule" "third_of_month_at_8" {
  name = "third-of-month-at-8-${local.workspace["stage"]}"
  description         = "Fires on the thirds day of every month at 7 o'clock (UTC) - 8 o'clock (UTC +1)"
  schedule_expression = "cron(0 7 3 * ? *)"
  tags = {
    Service = "MyResaAPI"
  }
}

resource "aws_cloudwatch_event_rule" "first_of_month_at_18" {
  name                = "first-of-month-at-18-${local.workspace["stage"]}"
  description = "Fires on the first day of every month at 17 o'clock (UTC) - 18 o'clock (UTC +1)"
  schedule_expression = "cron(0 17 1 * ? *)"
  tags = {
    Service = "MyResaAPI"
  }
}

resource "aws_cloudwatch_event_rule" "every_week" {
  name                = "every-week-${local.workspace["stage"]}"
  description         = "Fires every week"
  schedule_expression = "rate(7 days)"
  tags = {
    Service = "MyResaAPI"
  }
}

resource "aws_cloudwatch_event_rule" "every_15days" {
  name                = "every-15days-${local.workspace["stage"]}"
  description         = "Fires every 15 days"
  schedule_expression = "rate(15 days)"
  tags = {
    Service = "MyResaAPI"
  }
}

resource "aws_cloudwatch_event_rule" "every_5min" {
  name                = "every-5minutes-${local.workspace["stage"]}"
  description         = "Fires every 5 minutes"
  schedule_expression = "rate(5 minutes)"
  tags = {
    Service = "MyResaAPI"
  }
}

resource "aws_cloudwatch_event_target" "check_every_week" {
  rule = aws_cloudwatch_event_rule.every_week.name
  arn  = module.trigger_sync_storelocations.lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_weekly_check" {
  statement_id  = "AllowExecutionFromCloudWatch-trigger_sync_storelocations"
  action        = "lambda:InvokeFunction"
  function_name = module.trigger_sync_storelocations.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.every_week.arn
}

resource "aws_cloudwatch_event_rule" "every_day" {
  name                = "every-day-${local.workspace["stage"]}"
  description         = "Fires every day"
  schedule_expression = "rate(1 day)"
  tags = {
    Service = "MyResaAPI"
  }
}

resource "aws_cloudwatch_event_target" "trigger_evdbRecup_daily" {
  rule = aws_cloudwatch_event_rule.every_day.name
  arn  = module.evdbVehiclesRecuperationLambda.lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_daily_check" {
  statement_id  = "AllowExecutionFromCloudWatch-evdbVehiclesRecuperation"
  action        = "lambda:InvokeFunction"
  function_name = module.evdbVehiclesRecuperationLambda.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.every_day.arn
}