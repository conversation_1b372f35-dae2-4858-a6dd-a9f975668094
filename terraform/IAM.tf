resource "aws_lambda_permission" "apigw_lambda" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = module.passThroughLambda.lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = aws_api_gateway_rest_api.MyResaAPI.execution_arn
}

resource "aws_iam_role" "api_gateway" {
  name = "api-gateway-role-${local.workspace["stage"]}"
  path = "/"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Principal": {
        "Service": "apigateway.amazonaws.com"
      },
      "Action":"sts:AssumeRole",
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF

}

resource "aws_iam_role_policy" "lambda_invoke" {
  name = "lambda_invoke-${local.workspace["stage"]}"
  role = aws_iam_role.api_gateway.id

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
        "Sid": "VisualEditor0",
        "Effect": "Allow",
        "Action": ["lambda:InvokeFunction"],
        "Resource": ["*"]
    }
  ]
}
EOF

}
