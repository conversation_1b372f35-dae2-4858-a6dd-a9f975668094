terraform {
  # remote state
  backend "s3" {
    bucket = "my-resa-api-terraform-remote-state"
    key    = "MyResaAPI.tfstate"
    region = "eu-west-1"
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.11"
    }
  }
}

# Configure the AWS Provider
provider "aws" {
  region = "eu-west-1"
}

resource "aws_api_gateway_rest_api" "MyResaAPI" {
  name = local.workspace["api"]

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  binary_media_types = [
    "application/pdf",
    "image/png",
    "image/jpg",
    "image/jpeg",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ]
}

resource "aws_api_gateway_authorizer" "tokenAuthorizer" {
  name                             = "tokenAuthorizer"
  rest_api_id                      = aws_api_gateway_rest_api.MyResaAPI.id
  authorizer_uri                   = module.tokenAuthorizer.lambda_alias.invoke_arn
  type                             = "TOKEN"
  authorizer_credentials           = aws_iam_role.api_gateway.arn
  identity_validation_expression   = "^Bearer [A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+\\.?[A-Za-z0-9-_.+/=]*$" #JWT token
  authorizer_result_ttl_in_seconds = 0
}

resource "aws_api_gateway_authorizer" "basicAuthorizer" {
  name                             = "basicAuthorizer"
  rest_api_id                      = aws_api_gateway_rest_api.MyResaAPI.id
  authorizer_uri                   = module.basicAuthorizer.lambda_alias.invoke_arn
  type                             = "REQUEST"
  authorizer_credentials           = aws_iam_role.api_gateway.arn
  identity_validation_expression   = "^Basic [A-Za-z0-9-_=]*$" #Base64 password
  authorizer_result_ttl_in_seconds = 0
}

resource "aws_api_gateway_authorizer" "signedUrlAuthorizer" {
  name                             = "signedUrlAuthorizer"
  rest_api_id                      = aws_api_gateway_rest_api.MyResaAPI.id
  authorizer_uri                   = module.signedUrlAuthorizer.lambda_alias.invoke_arn
  type                             = "REQUEST"
  authorizer_credentials           = aws_iam_role.api_gateway.arn
  authorizer_result_ttl_in_seconds = 0
  identity_source                  = "method.request.querystring.Salt,method.request.querystring.Signature"
}

resource "aws_api_gateway_authorizer" "smsAuthorizer" {
  name                             = "smsAuthorizer"
  rest_api_id                      = aws_api_gateway_rest_api.MyResaAPI.id
  authorizer_uri                   = module.smsAuthorizer.lambda_alias.invoke_arn
  type                             = "REQUEST"
  authorizer_credentials           = aws_iam_role.api_gateway.arn
  authorizer_result_ttl_in_seconds = 0
  identity_source                  = "method.request.querystring.Mobile,method.request.querystring.ValidationCode"
}


resource "aws_api_gateway_request_validator" "validator" {
  name                        = "MyResaAPI request validator"
  rest_api_id                 = aws_api_gateway_rest_api.MyResaAPI.id
  validate_request_body       = false
  validate_request_parameters = true
}

locals {
  responses_type = {
    ACCESS_DENIED                  = 403,
    API_CONFIGURATION_ERROR        = 500,
    AUTHORIZER_CONFIGURATION_ERROR = 500,
    AUTHORIZER_FAILURE             = 500,
    BAD_REQUEST_PARAMETERS         = 400,
    BAD_REQUEST_BODY               = 400,
    DEFAULT_4XX                    = null,
    DEFAULT_5XX                    = null,
    EXPIRED_TOKEN                  = 403,
    INTEGRATION_FAILURE            = 504,
    INTEGRATION_TIMEOUT            = 504,
    INVALID_API_KEY                = 403,
    INVALID_SIGNATURE              = 403,
    MISSING_AUTHENTICATION_TOKEN   = 403,
    QUOTA_EXCEEDED                 = 429,
    REQUEST_TOO_LARGE              = 413,
    RESOURCE_NOT_FOUND             = 404,
    THROTTLED                      = 429,
    UNAUTHORIZED                   = 401,
    UNSUPPORTED_MEDIA_TYPE         = 415,
    WAF_FILTERED                   = 403
  }
}

resource "aws_api_gateway_gateway_response" "gateway_response" {
  rest_api_id   = aws_api_gateway_rest_api.MyResaAPI.id
  for_each      = local.responses_type
  response_type = each.key

  response_templates = {
    "application/json" = join("", ["{ \"Error\" : ", (each.value != null ? each.value : "$context.responseOverride.status"), ", \"Message\" : $context.error.messageString }"])
  }


  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Origin"      = "'*'"
    "gatewayresponse.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

module "MyResaAPI_private_proxy" {
  source      = "./PrivateProxy"
  name        = local.workspace["api"]
  domain_name = local.workspace["domain_name"]
  is_prod     = local.workspace["prod"]
}
# terraform apply -target=module.MyResaAPI_private_proxy