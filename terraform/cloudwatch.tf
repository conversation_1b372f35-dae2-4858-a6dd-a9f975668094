

locals {
  widgets = flatten([
    for value in local.methods : [
      {
        "type" : "metric",
        "x" : 0,
        "y" : index(local.methods, value) * 6,
        "width" : 20,
        "height" : 6,
        "properties" : {
          "metrics" : [
            ["AWS/ApiGateway", "Count", "ApiName", "MyResaAPI", "Resource", value.resource.path, "Stage", local.workspace["api_stage"], "Method", value.method],
            ["AWS/ApiGateway", "5XXError", "ApiName", "MyResaAPI", "Resource", value.resource.path, "Stage", local.workspace["api_stage"], "Method", value.method],
            ["AWS/ApiGateway", "4XXError", "ApiName", "MyResaAPI", "Resource", value.resource.path, "Stage", local.workspace["api_stage"], "Method", value.method]
          ],
          "period" : 300,
          "stat" : "Average",
          "region" : "eu-west-1",
          "title" : "${value.method} ${value.resource.path}"
        }
      },
      {
        "type" : "metric",
        "x" : 20,
        "y" : index(local.methods, value) * 6,
        "width" : 3,
        "height" : 3,
        "properties" : {
          "view" : "singleValue",
          "metrics" : [
            ["AWS/ApiGateway", "Latency", "ApiName", "MyResaAPI", "Resource", value.resource.path, "Stage", local.workspace["api_stage"], "Method", value.method]
          ],
          "period" : 300,
          "stat" : "Average",
          "region" : "eu-west-1",
          "title" : "Latency"
        }
      },
      {
        "type" : "metric",
        "x" : 20,
        "y" : index(local.methods, value) * 6 + 3,
        "width" : 3,
        "height" : 3,
        "properties" : {
          "view" : "singleValue",
          "metrics" : [
            ["AWS/ApiGateway", "Latency", "ApiName", "MyResaAPI", "Resource", value.resource.path, "Stage", local.workspace["api_stage"], "Method", value.method]
          ],
          "period" : 300,
          "stat" : "Maximum",
          "region" : "eu-west-1",
          "title" : "Max Latency"
        }
      }
    ]
  ])
}

resource "aws_cloudwatch_dashboard" "dashboard" {
  dashboard_name = "MyResaAPI-${local.workspace["stage"]}"

  dashboard_body = jsonencode({
    "widgets" : local.widgets
  })
}
