variable "vpc_id" {
  type    = string
  default = "vpc-02c6e3c694fe4a155"
}

variable "subnets" {
  default = [
    "subnet-0b37792a32cda2f86",
    "subnet-04f1be7c08d3e2a4e",
  ]
}

variable "securityGroups" {
  default = [
    "sg-08f50a198a2085a39",
    "sg-09ebcff0c2cdfb46b",
  ]
}

variable "region" {
  default = "eu-west-1"
}

variable "version_stage" {
  description = "API version stage (doesn't apply for development)"
  type        = string
  default     = "v0"
}

data "aws_caller_identity" "current" {}

locals {
  account_id = data.aws_caller_identity.current.account_id
  env = {
    default = {
      stage                     = terraform.workspace
      stage_tag                 = "DEV"
      api_stage                 = "v0"
      api_version               = var.version_stage
      S3Bucket                  = "my-resa-api-${terraform.workspace}"
      api                       = "MyResaAPI-${terraform.workspace}"
      dynamodb                  = "MyResaUser_dev"
      sms_codes                 = "MyResaSMSCodes_dev"
      prod                      = false
      dynamodb_md               = "MyResaMD_dev"
      storelocation_db_secret   = "RESA_STORELOCATION_DB/DEV"
      ad_secret                 = "MyResaAPI/ActiveDirectory/qta"
      adfs_secret               = "MyResaAPI/ADFS/dev"
      domain_name               = ""
      env_file                  = "resources/${var.version_stage}/env.qta.json"
      sharepoint_secret         = "MyResaAPI/Sharepoint/QTA"
      sharepoint_system         = "QTA"
      PayementTable             = "PayementTable_dev"
      PowalcoFiles              = "PowalcoFiles-dev"
      PowalcoBucket             = "powalco-files-${terraform.workspace}"
      PowalcoApiKey             = "PowalcoApiKey/dev"
      PowalcoApi                = "https://services-acc2.powalco.be/api/"
      PowalcoUrl                = null
      SNSErrors                 = "MyResaAPI-failure-dev"
      WAF_name                  = "MyResaAPI-dev-WAF"
      CognitoMailAttribute      = "custom:receiveNotification"
      CognitoPoolId             = "eu-west-1_03nJTXjhD"
      NewDeploySenderMail       = "<EMAIL>"
      envMessageLogBucket       = "env-message-logs"
      mollieToken               = "MyResaAPI/Mollie/DEV"
      DossierConfActionsTable   = "DossierConfirmationActions_dev"
      AsynchroneProcesses_table = "AsynchroneProcesses_dev"
      ADFSWeb                   = "MyResaWeb/ADFS/Qual"
      form_dynamo_table         = "UserRefundForm_dev"
      panne_dynamodb_table      = "UserPanneSubscription_dev"
      panne_location_table      = "PanneLocation_dev"
      BornesRechargeTable       = "BornesRechargeTable_dev"
      FormulairesTable          = "FormulairesTable_dev"
      cache_base_dir            = "/tmp/MyResa"
      UserEnergyProfiles = "UserEnergyProfiles_dev"
      SmartConsoAlerts = "SmartConsoAlerts_dev"
      HistoricAlerts = "HistoricAlerts_dev"
    }
    sandbox-cyril = {
      domain_name = "dmbrh5mv06.execute-api.eu-west-1.amazonaws.com"
    }
    sandbox-david = {
      domain_name = "prps0w5am8.execute-api.eu-west-1.amazonaws.com"
    }
    dev = {
      stage                   = "dev"
      stage_tag               = "DEV"
      api_stage               = "v0"
      S3Bucket                = "my-resa-api-dev"
      api                     = "MyResaAPI-dev"
      sms_codes               = "MyResaSMSCodes_dev"
      dynamodb                = "MyResaUser_dev"
      prod                    = false
      dynamodb_md             = "MyResaMD_dev"
      storelocation_db_secret = "RESA_STORELOCATION_DB/DEV"
      PowalcoFiles            = "PowalcoFiles-dev"
      ad_secret               = "MyResaAPI/ActiveDirectory/qta"
      adfs_secret             = "MyResaAPI/ADFS/dev"
      env_file                = "resources/${var.version_stage}/env.qta.json"
      PayementTable           = "PayementTable_dev"
      PowalcoBucket           = "powalco-files-dev"
      PowalcoUrl              = "powalco-qual.resa.be"
      WAF_name                = "MyResaAPI-dev-WAF"
      DossierConfActionsTable = "DossierConfirmationActions_dev"
      UserEnergyProfiles = "UserEnergyProfiles_dev"
      SmartConsoAlerts = "SmartConsoAlerts_dev"
      HistoricAlerts = "HistoricAlerts_dev"
    }
    qta = {
      stage                     = "qta"
      stage_tag                 = "QTA"
      api_stage                 = replace(var.version_stage, ".", "-")
      S3Bucket                  = "my-resa-api-qta"
      api                       = "MyResaAPI-qta"
      dynamodb                  = "MyResaUser_qta"
      sms_codes                 = "MyResaSMSCodes_qta"
      PowalcoFiles              = "PowalcoFiles-dev"
      prod                      = true
      dynamodb_md               = "MyResaMD_qta"
      storelocation_db_secret   = "RESA_STORELOCATION_DB/DEV"
      ad_secret                 = "MyResaAPI/ActiveDirectory/qta"
      adfs_secret               = "MyResaAPI/ADFS/dev"
      domain_name               = "api-acceptance.resa.be"
      env_file                  = "resources/${var.version_stage}/env.qta.json"
      PayementTable             = "PayementTable_qta"
      PowalcoBucket             = "powalco-files-qta"
      WAF_name                  = "MyResaAPI-dev-WAF"
      DossierConfActionsTable   = "DossierConfirmationActions_qta"
      AsynchroneProcesses_table = "AsynchroneProcesses_qta"
      form_dynamo_table         = "UserRefundForm_qta"
      panne_dynamodb_table      = "UserPanneSubscription_qta"
      panne_location_table      = "PanneLocation_qta"
      BornesRechargeTable       = "BornesRechargeTable_qta"
      FormulairesTable          = "FormulairesTable_qta"
      UserEnergyProfiles = "UserEnergyProfiles_QTA"
      SmartConsoAlerts = "SmartConsoAlerts_QTA"
      HistoricAlerts = "HistoricAlerts_QTA"

    }
    qla = {
      stage                     = "qla"
      stage_tag                 = "QLA"
      api_stage                 = replace(var.version_stage, ".", "-")
      S3Bucket                  = "my-resa-api-qla"
      api                       = "MyResaAPI-QLA"
      dynamodb                  = "MyResaUser_qla"
      sms_codes                 = "MyResaSMSCodes_qla"
      prod                      = true
      dynamodb_md               = "MyResaMD_qla"
      storelocation_db_secret   = "RESA_STORELOCATION_DB/QLA"
      ad_secret                 = "MyResaAPI/ActiveDirectory/QLA"
      adfs_secret               = "MyResaAPI/ADFS/QLA"
      PowalcoFiles              = "PowalcoFiles-dev"
      domain_name               = "api-qla.resa.be"
      env_file                  = "resources/${var.version_stage}/env.qla.json"
      sharepoint_secret         = "MyResaAPI/Sharepoint/QLA"
      sharepoint_system         = "QLA"
      PayementTable             = "PayementTable_qla"
      PowalcoBucket             = "powalco-files-qla"
      WAF_name                  = "MyResaAPI-dev-WAF"
      DossierConfActionsTable   = "DossierConfirmationActions_qla"
      AsynchroneProcesses_table = "AsynchroneProcesses_qla"
      ADFSWeb                   = "MyResaWeb/ADFS/QLA"
      form_dynamo_table         = "UserRefundForm_qla"
      panne_dynamodb_table      = "UserPanneSubscription_qla"
      panne_location_table      = "PanneLocation_qla"
      BornesRechargeTable       = "BornesRechargeTable_qla"
      FormulairesTable          = "FormulairesTable_qla"
      UserEnergyProfiles = "UserEnergyProfiles_QLA"
      SmartConsoAlerts = "SmartConsoAlerts_QLA"
      HistoricAlerts = "HistoricAlerts_QLA"
    }
    production = {
      stage                     = "production"
      stage_tag                 = "PRD"
      api_stage                 = replace(var.version_stage, ".", "-")
      S3Bucket                  = "my-resa-api-production"
      api                       = "MyResaAPI-production"
      dynamodb                  = "MyResaUser_prd"
      sms_codes                 = "MyResaSMSCodes_prd"
      prod                      = true
      PowalcoFiles              = "PowalcoFiles-prd"
      dynamodb_md               = "MyResaMD_prd"
      storelocation_db_secret   = "RESA_STORELOCATION_DB/PRD"
      ad_secret                 = "MyResaAPI/ActiveDirectory/prd"
      adfs_secret               = "MyResaAPI/ADFS/prd"
      domain_name               = "api.resa.be"
      env_file                  = "resources/${var.version_stage}/env.production.json"
      sharepoint_secret         = "MyResaAPI/Sharepoint/PRD"
      sharepoint_system         = "PTE"
      PayementTable             = "PayementTable_prd"
      PowalcoFiles              = "PowalcoFiles-prd"
      PowalcoBucket             = "powalco-files-${terraform.workspace}"
      PowalcoApiKey             = "PowalcoApiKey/prd"
      PowalcoApi                = "https://services.powalco.be/api/"
      PowalcoUrl                = "powalco.resa.be"
      PowalcoBucket             = "powalco-files-prd"
      SNSErrors                 = "MyResaAPI-failure-prod"
      WAF_name                  = "MyResaAPI-dev-WAF"
      mollieToken               = "MyResaAPI/Mollie/PRD"
      DossierConfActionsTable   = "DossierConfirmationActions_prd"
      AsynchroneProcesses_table = "AsynchroneProcesses_prd"
      ADFSWeb                   = "MyResaWeb/ADFS/PRD"
      form_dynamo_table         = "UserRefundForm_prd"
      panne_dynamodb_table      = "UserPanneSubscription_prd"
      panne_location_table      = "PanneLocation_prd"
      BornesRechargeTable       = "BornesRechargeTable_prd"
      FormulairesTable          = "FormulairesTable_prd"
      UserEnergyProfiles = "UserEnergyProfiles_PRD"
      SmartConsoAlerts = "SmartConsoAlerts_PRD"
      HistoricAlerts = "HistoricAlerts_PRD"
    }
  }
  tmp_workspace_name = split("_", terraform.workspace)[0]
  workspace_name     = contains(keys(local.env), local.tmp_workspace_name) ? local.tmp_workspace_name : "default"
  workspace          = merge(local.env["default"], local.env[local.workspace_name])
}

# access with stage = "${local.workspace["stage"]}"
