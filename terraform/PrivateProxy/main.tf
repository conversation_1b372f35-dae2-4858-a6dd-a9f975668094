variable "proxy_vpc_id" {
  type    = string
  default = "vpce-032c2076bd424d172"
}
variable "name" {
  type = string
}
variable "domain_name" {
  type = string
}
variable "is_prod" {
  type = bool
}

resource "aws_api_gateway_rest_api" "Private_proxy" {
  count = var.is_prod ? 1 : 0
  name  = "${var.name}-private-proxy"

  endpoint_configuration {
    types            = ["PRIVATE"]
    vpc_endpoint_ids = [var.proxy_vpc_id]
  }
}

resource "aws_api_gateway_rest_api_policy" "Private_proxy_policy" {
  count       = var.is_prod ? 1 : 0
  depends_on  = [aws_api_gateway_rest_api.Private_proxy.0]
  rest_api_id = aws_api_gateway_rest_api.Private_proxy.0.id
  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : "*",
        "Action" : "execute-api:Invoke",
        "Resource" : "${aws_api_gateway_rest_api.Private_proxy.0.execution_arn}/Default/*",
        "Condition" : {
          "StringEquals" : {
            "aws:SourceVpce" : var.proxy_vpc_id
          }
        }
      }
    ]
  })
}

resource "aws_api_gateway_resource" "ApiProxyResource" {
  count       = var.is_prod ? 1 : 0
  depends_on  = [aws_api_gateway_rest_api.Private_proxy.0]
  rest_api_id = aws_api_gateway_rest_api.Private_proxy.0.id
  parent_id   = aws_api_gateway_rest_api.Private_proxy.0.root_resource_id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_integration" "ApiProxyIntegration" {
  count                   = var.is_prod ? 1 : 0
  depends_on              = [aws_api_gateway_rest_api.Private_proxy.0]
  rest_api_id             = aws_api_gateway_rest_api.Private_proxy.0.id
  resource_id             = aws_api_gateway_resource.ApiProxyResource.0.id
  http_method             = aws_api_gateway_method.ApiProxyMethod.0.http_method
  type                    = "HTTP_PROXY"
  integration_http_method = "ANY"
  uri                     = "https://${var.domain_name}/{proxy}"
  request_parameters = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
}

resource "aws_api_gateway_method" "ApiProxyMethod" {
  count         = var.is_prod ? 1 : 0
  depends_on    = [aws_api_gateway_rest_api.Private_proxy.0]
  rest_api_id   = aws_api_gateway_rest_api.Private_proxy.0.id
  resource_id   = aws_api_gateway_resource.ApiProxyResource.0.id
  http_method   = "ANY"
  authorization = "NONE"
  request_parameters = {
    "method.request.path.proxy" = true
  }
}

resource "aws_api_gateway_deployment" "ApiDeployment" {
  count       = var.is_prod ? 1 : 0
  depends_on  = [aws_api_gateway_rest_api.Private_proxy.0, aws_api_gateway_method.ApiProxyMethod.0, aws_api_gateway_rest_api_policy.Private_proxy_policy.0]
  rest_api_id = aws_api_gateway_rest_api.Private_proxy.0.id
}

resource "aws_api_gateway_stage" "ApiStage" {
  count         = var.is_prod ? 1 : 0
  depends_on    = [aws_api_gateway_rest_api.Private_proxy.0, aws_api_gateway_method.ApiProxyMethod.0, aws_api_gateway_deployment.ApiDeployment.0]
  rest_api_id   = aws_api_gateway_rest_api.Private_proxy.0.id
  deployment_id = aws_api_gateway_deployment.ApiDeployment.0.id
  stage_name    = "Default"
}
