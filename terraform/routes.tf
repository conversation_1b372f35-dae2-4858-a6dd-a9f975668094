// asynchrone
module "asynchrone" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "asynchrone"
}
module "POST_asynchrone" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.asynchrone.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "asynchrone_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.asynchrone.resource.id
  path   = "{Id}"
}
module "GET_asynchrone_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.asynchrone_x.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "asynchrone_callback" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.asynchrone.resource.id
  path   = "callback"
}
module "POST_asynchrone_callback" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.asynchrone_callback.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.MessageId" = true,
  }
}
// utilisateurs
module "utilisateurs" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "utilisateurs"
}
module "POST_utilisateurs" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "PATCH_utilisateurs" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "PATCH"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "utilisateurs_authenticate" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs.resource.id
  path   = "authenticate"
}
module "POST_utilisateurs_authenticate" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_authenticate.resource
  lambda               = module.getADTokenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    //"method.request.body.Username"           = true,
    //"method.request.body.Password"           = true
  }
}
module "utilisateurs_authenticate_logout" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_authenticate.resource.id
  path   = "logout"
}
module "POST_utilisateurs_authenticate_logout" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_authenticate_logout.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}

//utilisateurs/activate
module "utilisateurs_activate" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs.resource.id
  path   = "activate"
}
module "POST_utilisateurs_activate" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_activate.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Token" = true
  }
}

//utilisateurs/activate/sendMail
module "utilisateurs_activate_sendMail" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_activate.resource.id
  path   = "sendMail"
}
module "POST_utilisateurs_activate_sendMail" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_activate_sendMail.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Authorization"      = false
    "method.request.header.SessionId"          = false
    "method.request.querystring.Callback"      = true
    "method.request.querystring.Callback2FA"   = false
    "method.request.querystring.CallbackError" = false
  }
}

//utilisateurs/activate/sendSms
module "utilisateurs_activate_sendSms" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_activate.resource.id
  path   = "sendSms"
}
module "POST_utilisateurs_activate_sendSms" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_activate_sendSms.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false
    "method.request.header.Authorization" = false
    "method.request.querystring.Token"    = false
  }
}

//utilisateurs/activate/checkSms
module "utilisateurs_activate_checkSms" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_activate.resource.id
  path   = "checkSms"
}
module "POST_utilisateurs_activate_checkSms" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_activate_checkSms.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false
    "method.request.header.Authorization" = false
    "method.request.querystring.Token"    = false
  }
}

//utilisateurs/activate/checkMail
module "utilisateurs_activate_checkMail" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_activate.resource.id
  path   = "checkMail"
}
module "POST_utilisateurs_activate_checkMail" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_activate_checkMail.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Token" = true
  }
}

# /uuid
module "uuid" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "uuid"
}

module "GET_uuid" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.uuid.resource
  lambda               = module.simpleComputeLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "envMessage" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "envMessage"
}

module "POST_utilisateurs_envMessage" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.envMessage.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}
module "envMessage_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.envMessage.resource.id
  path   = "{TemplateId}"
}
module "envMessage_x_body" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.envMessage_x.resource.id
  path   = "body"
}

module "GET_envMessage_x_body" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.envMessage_x_body.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}

module "utilisateurs_updateMail" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs.resource.id
  path   = "updateMail"
}
module "POST_utilisateurs_updateMail" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_updateMail.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Token" = true
  }
}

module "utilisateurs_updateMail_sendMail" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_updateMail.resource.id
  path   = "sendMail"
}
module "POST_utilisateurs_updateMail_sendMail" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_updateMail_sendMail.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.querystring.Callback" = true,
    "method.request.querystring.Email"    = true
  }
}

//utilisateurs/reset
module "utilisateurs_reset" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs.resource.id
  path   = "reset"
}
module "POST_utilisateurs_reset" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_reset.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Token" = true
  }
}
module "utilisateurs_reset_sendMail" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_reset.resource.id
  path   = "sendMail"
}
module "POST_utilisateurs_reset_sendMail" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_reset_sendMail.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Callback" = true,
    "method.request.querystring.Email"    = true
  }
}

//utilisateurs/updatePassword
module "utilisateurs_updatePassword" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs.resource.id
  path   = "updatePassword"
}
module "POST_utilisateurs_updatePassword" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_updatePassword.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}

//utilisateurs/edit
module "utilisateurs_edit" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs.resource.id
  path   = "edit"
}
//utilisateurs/edit/{Uid}
module "utilisateurs_edit_uid" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_edit.resource.id
  path   = "{Uid}"
}

//utilisateurs/edit/{Uid}/preferences
module "utilisateurs_edit_uid_preferences" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.utilisateurs_edit_uid.resource.id
  path   = "preferences"
}
module "GET_utilisateurs_edit_uid_preferences" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.utilisateurs_edit_uid_preferences.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.signedUrlAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Preference" = false,
    "method.request.querystring.Value"      = false
  }
}

// me
module "me" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "me"
}
module "DELETE_me" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "DELETE"
  role                 = aws_iam_role.api_gateway
  resource             = module.me.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Token" = true
  }
}

module "me_energy_profile" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path = "energy_profile"
}
module "POST_me_energy_profile" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_energy_profile.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}
module "GET_me_energy_profile" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "GET"
  role     = aws_iam_role.api_gateway
  resource = module.me_energy_profile.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}

module "me_alerts" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "alerts"
}
module "POST_me_alerts" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_alerts.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}
module "GET_me_alerts" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "GET"
  role     = aws_iam_role.api_gateway
  resource = module.me_alerts.resource
  lambda   = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}
module "me_alerts_historic" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_alerts.resource.id
  path   = "historic"
}
module "GET_me_alerts_historic" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_alerts_historic.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}

module "me_energy_profile_target" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_energy_profile.resource.id
  path   = "target"
}
module "GET_me_energy_profile_target" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_energy_profile_target.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}

module "me_bornesRecharge" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "bornes_recharge"
}


module "GET_me_bornesRecharge" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_bornesRecharge.resource
  lambda               = module.bornesRechargeDataHandlerLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}
module "me_bornesRecharge_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_bornesRecharge.resource.id
  path   = "{uuid}"
}
module "PUT_me_bornesRecharge_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "PUT"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_bornesRecharge_x.resource
  lambda               = module.bornesRechargeDataHandlerLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}
module "DELETE_me_bornesRecharge_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "DELETE"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_bornesRecharge_x.resource
  lambda               = module.bornesRechargeDataHandlerLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}
// me/vehicles
module "me_vehicles" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "vehicles"
}
module "POST_me_vehicles" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_vehicles.resource
  lambda               = module.vehiclesDataHandlerLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}
module "GET_me_vehicles" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_vehicles.resource
  lambda               = module.vehiclesDataHandlerLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}
module "me_vehicles_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_vehicles.resource.id
  path   = "{UUID}"
}
module "DELETE_me_vehicles" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "DELETE"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_vehicles_x.resource
  lambda               = module.vehiclesDataHandlerLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}
module "me_vehicles_x_sendMail" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_vehicles_x.resource.id
  path   = "sendMail"
}
module "GET_me_vehicles_sendMail" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_vehicles_x_sendMail.resource
  lambda               = module.vehiclesDataHandlerLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
}
module "me_vehicles_x_pdf" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_vehicles_x.resource.id
  path   = "pdf"
}

module "GET_me_vehicles_pdf" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_vehicles_x_pdf.resource
  lambda               = module.vehiclesDataHandlerLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  response_models = {
    "application/pdf" = "Empty"
  }
}
// me/preferences
module "me_preferences" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "preferences"
}

module "GET_me_preferences" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_preferences.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}
module "POST_me_preferences" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_preferences.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
    //"method.request.body"                  = true
  }
}
module "me_preferences_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_preferences.resource.id
  path   = "{Key}"
}
module "DELETE_me_preferences_X" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "DELETE"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_preferences_x.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}
module "PATCH_me_preferences_X" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "PATCH"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_preferences_x.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}
// /me/objet_raccordement
module "me_objet_raccordement" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "objet_raccordement"
}

module "POST_me_objet_raccordement" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_objet_raccordement.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false,
    //"method.request.body.DossierId"        = true
  }
}

module "me_objet_raccordement_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_objet_raccordement.resource.id
  path   = "{OrId}"
}

module "DELETE_me_objet_raccordement_x" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "DELETE"
  role     = aws_iam_role.api_gateway
  resource = module.me_objet_raccordement_x.resource
  lambda   = module.getDynamoDBLambda.lambda_alias
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}
// me/dashboard
module "me_dashboard" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "dashboard"
}

module "GET_me_dashboard" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_dashboard.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false,
    "method.request.querystring.Quick"    = false
  }
}

// me/notifications
module "me_notifications" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "notifications"
}
module "POST_me_notifications_create" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_notifications.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}
module "GET_me_notifications" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_notifications.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"     = false,
    "method.request.header.Authorization" = false
  }
}

//region /me//me/consentements
module "me_consentements" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "consentements"
}
module "POST_me_consentements" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_consentements.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
module "GET_me_consentements" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_consentements.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region me/delete
module "me_delete" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me.resource.id
  path   = "delete"
}
module "GET_me_delete" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_delete.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Token" = true
  }
}
//endregion

//region me/delete/sendMail
module "me_delete_sendMail" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_delete.resource.id
  path   = "sendMail"
}
module "POST_me_delete_sendMail" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_delete_sendMail.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.querystring.Callback" = true
  }
}
//endregion
//newregion
module "bp" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "bp"
}
module "bp_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.bp.resource.id
  path   = "{BP}"
}
module "bp_x_myre" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.bp_x.resource.id
  path   = "myre"
}
module "GET_bp_x_myre" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "GET"
  role     = aws_iam_role.api_gateway
  resource = module.bp_x_myre.resource
  lambda   = module.sqlDrivenLambda.lambda_alias
}
//endregion
module "ppp" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "ppp"
}

module "ppp_master" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ppp.resource.id
  path   = "master"
}
module "ppp_master_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ppp_master.resource.id
  path   = "{IdBp}"
}
module "GET_ppp_master_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ppp_master_x.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}

module "me_notifications_status" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.me_notifications.resource.id
  path   = "status"
}
module "PATCH_me_notifications_status" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "PATCH"
  role                 = aws_iam_role.api_gateway
  resource             = module.me_notifications_status.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
  }
}

// adresse
module "adresse" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "adresse"
}
module "GET_adresse" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.adresse.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Langue"        = false
    "method.request.querystring.Cdpostal" = true
  }
}
// adresse/validate
module "adresse_validate" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.adresse.resource.id
  path   = "validate"
}
module "POST_adresse_validate" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.adresse_validate.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Langue" = false
    /*"method.request.body.Cdpostal"      = true,
    "method.request.body.Rue"           = true,
    "method.request.body.Localite"      = false*/
  }
}
// adresse/match
module "adresse_match" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.adresse.resource.id
  path   = "match"
}
module "POST_adresse_match" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.adresse_match.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
// adresse/pays
module "adresse_pays" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.adresse.resource.id
  path   = "pays"
}
module "GET_adresse_pays" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.adresse_pays.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
// adresse/distCourte
module "adresse_distCourte" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.adresse.resource.id
  path   = "distCourte"
}
module "POST_adresse_distCourte" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.adresse_distCourte.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
// adresse/ean
module "adresse_ean" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.adresse.resource.id
  path   = "ean"
}
module "GET_adresse_ean" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.adresse_ean.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Langue"   = false
    "method.request.querystring.Ean" = true
  }
}
// adresse/services
module "adresse_services" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.adresse.resource.id
  path   = "services"
}
module "GET_adresse_services" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.adresse_services.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Cdpostal"  = false,
    "method.request.querystring.Rue"       = false,
    "method.request.querystring.Numero"    = false,
    "method.request.querystring.Localite"  = false,
    "method.request.querystring.IdCommune" = false
  }
}

// adresse/cdpostaux
module "adresse_cdpostaux" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.adresse.resource.id
  path   = "cdpostaux"
}
module "GET_adresse_cdpostaux" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.adresse_cdpostaux.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.grdElec" = false,
    "method.request.querystring.grdGaz"  = false
  }
}

// /jobs
module "jobs" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "jobs"
}

module "GET_jobs" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "GET"
  role     = aws_iam_role.api_gateway
  resource = module.jobs.resource
  lambda   = module.passThroughLambda.lambda_alias
  request_parameters = {}
}

// /tarifs
module "tarifs" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "tarifs"
}

module "POST_tarifs" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "POST"
  role     = aws_iam_role.api_gateway
  resource = module.tarifs.resource
  lambda   = module.sqlDrivenLambda.lambda_alias
}

// delestage
module "delestage" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "delestage"
}

// delestage/adresse
module "delestage_adresse" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.delestage.resource.id
  path   = "pour_adresse"
}

module "GET_delestage_adresse" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.delestage_adresse.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Langue"        = false,
    "method.request.querystring.Localite" = true,
    "method.request.querystring.Rue"      = true,
    "method.request.querystring.Cdpostal" = true
  }
}

//ep
module "ep" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "ep"
}

module "GET_ep" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ep.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Lat0"          = false
    "method.request.querystring.Lat1"          = false
    "method.request.querystring.Long0"         = false
    "method.request.querystring.Long1"         = false
    "method.request.querystring.FiltreEnPanne" = false
    "method.request.querystring.PageSize"      = false
    "method.request.querystring.Page"          = false
    "method.request.querystring.DateDebut"     = false
    "method.request.querystring.DateFin"       = false
  }
  lambda = module.sqlDrivenLambda.lambda_alias
}

//ep/{Id}
module "ep_id" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ep.resource.id
  path   = "{Id}"
}

module "GET_ep_id" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "GET"
  role     = aws_iam_role.api_gateway
  resource = module.ep_id.resource
  lambda   = module.sqlDrivenLambda.lambda_alias
}

// ep/communes
module "ep_communes" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ep.resource.id
  path   = "communes"
}
module "GET_ep_communes" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ep_communes.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}

//portP1
module "portP1" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "portP1"
}
module "GET_portP1" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.portP1.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.passThroughLambda.lambda_alias
  request_parameters = {
    "method.request.querystring.Ean"    = false
    "method.request.querystring.NumCpt" = false
  }
}
module "portP1_demande" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.portP1.resource.id
  path   = "demande"
}

module "POST_portP1_demande" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.portP1_demande.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.passThroughLambda.lambda_alias
  request_parameters = {
    "method.request.querystring.NumCpt" = false
    "method.request.querystring.Action" = true
  }
}

//pannes
module "pannes" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "pannes"
}
module "GET_pannes" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
}

//pannes/active
module "pannes_active" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes.resource.id
  path   = "active"
}
module "GET_pannes_active" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_active.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.PanneCheckerLambda.lambda_alias
}

//pannes/ep
module "pannes_ep" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes.resource.id
  path   = "ep"
}
module "POST_pannes_ep" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_ep.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.passThroughLambda.lambda_alias
}

//pannes/planned
module "pannes_planned" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes.resource.id
  path   = "planned"
}
module "GET_pannes_planned" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_planned.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.header.Langue"             = false
    "method.request.querystring.IdRue"         = false
    "method.request.querystring.IdRadRue"      = false
    "method.request.querystring.IdRadLocalite" = false
    "method.request.querystring.IdLocalite"    = false
    "method.request.querystring.CdPostal"      = false
    "method.request.querystring.Localite"      = false
    "method.request.querystring.Rue"           = false
    "method.request.querystring.Lat0"          = false
    "method.request.querystring.Lat1"          = false
    "method.request.querystring.Long0"         = false
    "method.request.querystring.Long1"         = false
    "method.request.querystring.PageSize"      = false
    "method.request.querystring.Page"          = false
  }
}
//pannes/unplanned
module "pannes_unplanned" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes.resource.id
  path   = "unplanned"
}
module "GET_pannes_unplanned" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_unplanned.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.header.Langue"             = false
    "method.request.querystring.IdRue"         = false
    "method.request.querystring.IdRadRue"      = false
    "method.request.querystring.IdRadLocalite" = false
    "method.request.querystring.IdLocalite"    = false
    "method.request.querystring.CdPostal"      = false
    "method.request.querystring.Localite"      = false
    "method.request.querystring.DateDebut"     = false
    "method.request.querystring.DateFin"       = false
    "method.request.querystring.Rue"           = false
    "method.request.querystring.Lat0"          = false
    "method.request.querystring.Lat1"          = false
    "method.request.querystring.Long0"         = false
    "method.request.querystring.Long1"         = false
    "method.request.querystring.PageSize"      = false
    "method.request.querystring.Page"          = false
    "method.request.querystring.EnCours"       = false
  }
}

//pannes/unplanned/:id
module "pannes_unplanned_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes_unplanned.resource.id
  path   = "{Id}"
}
module "GET_pannes_unplanned_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_unplanned_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.header.Langue" = false
  }
}

//pannes/:id
module "pannes_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes.resource.id
  path   = "{Id}"
}

module "GET_pannes_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.path.Id" = true
  }
}

//pannes/tad
module "pannes_tad" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes.resource.id
  path   = "tad"
}

module "GET_pannes_tad" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_tad.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.querystring.PageSize" = false
    "method.request.querystring.Page"     = false
  }
}

//pannes/smart
module "pannes_smart" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes.resource.id
  path   = "smart"
}

module "GET_pannes_smart" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_smart.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.getDynamoDBLambda.lambda_alias
}

module "POST_pannes_smart" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes_smart.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.getDynamoDBLambda.lambda_alias
}

//ean
module "ean" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "ean"
}
module "GET_ean" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.header.Langue"        = false
    "method.request.header.Authorization" = false
    "method.request.querystring.Ean"      = false
    "method.request.querystring.NumCpt"   = false
    "method.request.querystring.Localite" = false
    "method.request.querystring.Cdpostal" = false
    "method.request.querystring.Rue"      = false
    "method.request.querystring.NumRue"   = false
  }
}
module "ean_cpt" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean.resource.id
  path   = "cpt"
}
module "GET_ean_cpt" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_cpt.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.smsAuthorizer.id
  request_parameters = {
    "method.request.header.Langue"              = false
    "method.request.querystring.NumCompteur"    = true
    "method.request.querystring.Mobile"         = true
    "method.request.querystring.ValidationCode" = true
  }
}
module "ean_validation" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean.resource.id
  path   = "validation"
}
module "GET_ean_validation" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_validation.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.eanFormatCheckerLambda.lambda_alias
  request_parameters = {
    "method.request.querystring.Ean" = true
  }
}
module "ean_recherche" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean.resource.id
  path   = "recherche"
}
module "GET_ean_recherche" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_recherche.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
  request_parameters = {
    "method.request.querystring.Lastname"     = true
    "method.request.querystring.Firstname"    = true
    "method.request.querystring.Phone"        = false
    "method.request.querystring.PhoneFixe"    = false
    "method.request.querystring.Email"        = false
    "method.request.querystring.ContactEmail" = false
  }
}

//region /ean/meter
module "ean_meter" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean.resource.id
  path   = "meter"
}
module "GET_ean_meter" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_meter.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
  }
}
//endregion

module "ean_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean.resource.id
  path   = "{Ean}"
}
module "GET_ean_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.path.Ean"             = true
    "method.request.querystring.NumCpt"   = false
    "method.request.header.Authorization" = false
  }
}
module "ean_x_grd" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean_x.resource.id
  path   = "grd"
}
module "GET_ean_x_grd" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_x_grd.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.path.Ean" = true
  }
}
module "ean_x_meter" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean_x.resource.id
  path   = "meter"
}
module "GET_ean_x_meter" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_x_meter.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.path.Ean"             = true
    "method.request.querystring.PostCode" = true
    "method.request.querystring.Street"   = true
  }
}
module "ean_x_history" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean_x.resource.id
  path   = "history"
}
module "GET_ean_x_history" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_x_history.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer = aws_api_gateway_authorizer.tokenAuthorizer.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.path.Ean" = true
  }
}

//region /ean/:Ean/lockStatus
module "ean_x_lockStatus" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.ean_x.resource.id
  path   = "lockStatus"
}
module "GET_ean_x_lockStatus" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.ean_x_lockStatus.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_parameters = {
    "method.request.path.Ean" = true
  }
}
//endregion

//index
module "index" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "index"
}
module "GET_index" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.index.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.passThroughLambda.lambda_alias
  request_parameters = {
    "method.request.header.Langue"           = false
    "method.request.querystring.Ean"         = false
    "method.request.querystring.NumCompteur" = false
    "method.request.querystring.Token"       = false
  }
}
module "POST_index" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.index.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.passThroughLambda.lambda_alias
  request_parameters = {
    /*"method.request.body.Ean"                               = true
    "method.request.body.Email"                             = true
    "method.request.body.CodeConfirm"                       = true
    "method.request.body.ChangeFour"                        = true
    "method.request.body.Compteurs"                         = true
    "method.request.body.Compteurs.NumCompteur"             = true
    "method.request.body.Compteurs.NumEquip"                = true
    "method.request.body.Compteurs.IndexReleve"             = true
    "method.request.body.Compteurs.IndexReleve.NumCadran"   = true
    "method.request.body.Compteurs.IndexReleve.IndexReleve" = true
    "method.request.body.Compteurs.IndexReleve.TypeCadran"  = true
    "method.request.body.Compteurs.IndexReleve.NbChiffre"   = true
    "method.request.body.Compteurs.IndexReleve.NbDecimales" = true
    "method.request.body.Compteurs.IndexReleve.PosCadran"   = true*/
  }
}

//index/bulk
module "index_bulk" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.index.resource.id
  path   = "bulk"
}

module "POST_index_bulk" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.index_bulk.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.passThroughLambda.lambda_alias
}

//index/ean
module "index_passage" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.index.resource.id
  path   = "passage"
}
module "GET_index_passage" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.index_passage.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.header.Langue"           = false
    "method.request.querystring.Ean"         = false
    "method.request.querystring.NumCompteur" = false
  }
}
module "index_historique" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.index.resource.id
  path   = "historique"
}

module "GET_index_historique_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.index_historique.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.header.Langue"    = false
    "method.request.path.Ean"         = false
    "method.request.path.NumCompteur" = false
  }
}

module "index_smart" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.index.resource.id
  path   = "smart"
}

module "GET_index_smart" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.index_smart.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.querystring.EndDate"   = false
    "method.request.querystring.StartDate" = false
    "method.request.querystring.Type"      = false
    "method.request.querystring.Ean"       = false
    "method.request.querystring.MeterId"   = false
  }
}

// /index/preparer_encodage
module "index_preparer_encodage" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.index.resource.id
  path   = "preparer_encodage"
}
module "GET_index_preparer_encodage" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.index_preparer_encodage.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_parameters = {
    "method.request.header.SessionId"     = false
    "method.request.header.Authorization" = false
  }
}

//cab/point_rechargement
module "cab" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "cab"
}
module "cab_point_rechargement" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.cab.resource.id
  path   = "point_rechargement"
}
module "GET_cab_point_rechargement" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.cab_point_rechargement.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_parameters = {
    "method.request.querystring.Lat0"     = false
    "method.request.querystring.Lat1"     = false
    "method.request.querystring.Long0"    = false
    "method.request.querystring.Long1"    = false
    "method.request.querystring.Page"     = false
    "method.request.querystring.PageSize" = false
  }
}

//facturation
module "facturation" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "facturation"
}
//facturation/factures
module "facturation_factures" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.facturation.resource.id
  path   = "factures"
}
module "GET_facturation_factures" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.facturation_factures.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.passThroughLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.header.Langue"   = false
    "method.request.querystring.Ean" = true
  }
}
module "facturation_factures_hgz" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.facturation_factures.resource.id
  path   = "hgz"
}
module "GET_facturation_factures_hgz" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.facturation_factures_hgz.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  lambda               = module.passThroughLambda.lambda_alias
}
//facturation/attestations
module "facturation_attestations" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.facturation.resource.id
  path   = "attestations"
}
module "GET_facturation_attestations" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.facturation_attestations.resource
  lambda               = module.passThroughLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Authorization" = true
  }
}
//facturation/pdf
module "facturation_pdf" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.facturation.resource.id
  path   = "pdf"
}
module "GET_facturation_pdf" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.facturation_pdf.resource
  lambda               = module.passThroughLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Ean"      = true
    "method.request.querystring.RefDocNo" = true
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
    "method.response.header.Content-Type"                = true
  }
  response_models = {
    "application/pdf" = "Empty"
  }
}
//facturation/contrats
module "facturation_contrats" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.facturation.resource.id
  path   = "contrats"
}
module "GET_facturation_contrats" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.facturation_contrats.resource
  lambda               = module.passThroughLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Authorization" = true
  }
}
module "facturation_contrats_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.facturation_contrats.resource.id
  path   = "{Id}"
}
module "GET_facturation_contrats_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.facturation_contrats_x.resource
  lambda               = module.passThroughLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Authorization" = true
    "method.request.path.Id"              = true
  }
}

//facturation/devis
module "facturation_devis" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.facturation.resource.id
  path   = "devis"
}
module "GET_facturation_devis" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.facturation_devis.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.header.Langue"          = false
    "method.request.querystring.NumDossier" = true
  }
}
//facturation/balance
module "facturation_balance" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.facturation.resource.id
  path   = "balance"
}
module "GET_facturation_balance" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.facturation_balance.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.header.Langue"   = false
    "method.request.querystring.Ean" = true
  }
}

// demande_travaux
module "demande_travaux" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "demande_travaux"
}
module "GET_demande_travaux" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
}
module "POST_demande_travaux" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}

module "demande_travaux_documents" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.demande_travaux.resource.id
  path   = "documents"
}
module "GET_demande_travaux_documents" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux_documents.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.NumDossier" = true
  }
}

module "demande_travaux_dossier" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.demande_travaux.resource.id
  path   = "dossier"
}
module "POST_demande_travaux_dossier" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux_dossier.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}

module "demande_travaux_planification" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.demande_travaux.resource.id
  path   = "planification"
}
module "POST_demande_travaux_planification" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux_planification.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}

module "demande_travaux_puissance" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.demande_travaux.resource.id
  path   = "puissance"
}

module "demande_travaux_puissance_forains" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.demande_travaux_puissance.resource.id
  path   = "forains"
}

module "GET_demande_travaux_puissance_forains" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux_puissance_forains.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}

module "demande_travaux_demande" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.demande_travaux.resource.id
  path   = "demande"
}
module "POST_demande_travaux_demande" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux_demande.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}

module "demande_travaux_devis" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.demande_travaux.resource.id
  path   = "devis"
}
module "GET_demande_travaux_devis" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux_devis.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.header.Langue"            = false
    "method.request.querystring.Ordre"        = true
    "method.request.querystring.PartenaireId" = false
  }
}

module "demande_travaux_confirmation" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.demande_travaux.resource.id
  path   = "confirmation"
}
module "GET_demande_travaux_confirmation" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.demande_travaux_confirmation.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.header.Langue"          = false
    "method.request.querystring.NumDossier" = false
    "method.request.querystring.DateAccord" = false
    "method.request.querystring.TypeAction" = false
  }
}

// /sms
module "sms" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "sms"
}
module "GET_sms" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.sms.resource
  lambda               = module.passThroughLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Sandbox"   = true,
    "method.request.querystring.MessageId" = true
  }
}
module "POST_sms" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.sms.resource
  lambda               = module.passThroughLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Sandbox" = true
  }
}

# /sms/incoming
module "sms_incoming" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sms.resource.id
  path   = "incoming"
}
module "POST_sms_incoming" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.sms_incoming.resource
  lambda               = module.IncomingSmsLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

// token (ppp)
module "token" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "token"
}
module "POST_token" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.token.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.SessionId"  = false,
    "method.request.querystring.Token" = true
  }
}

module "token_invalider" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.token.resource.id
  path   = "invalider"
}
module "POST_token_invalider" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.token_invalider.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Token" = true
  }
}

// formulaire/demande
module "formulaire" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "formulaire"
}
module "formulaire_demande" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.formulaire.resource.id
  path   = "demande"
}
module "POST_formulaire_demande" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.formulaire_demande.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

// fichiers uploads
module "fichiers" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "fichiers"
}
module "fichiers_upload" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.fichiers.resource.id
  path   = "upload"
}
module "GET_fichiers_upload" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.documentUpload.lambda_alias
  resource             = module.fichiers_upload.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.DocumentName" = true
    "method.request.querystring.IdDossier"    = true
  }
}
module "fichiers_temp_upload" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.fichiers.resource.id
  path   = "temp_upload"
}
module "GET_fichiers_temp_upload" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.tempFileUpload.lambda_alias
  resource             = module.fichiers_temp_upload.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.FileName" = true
  }
}
module "fichiers_liste" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.fichiers.resource.id
  path   = "liste"
}
module "GET_fichiers_liste" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.documentUpload.lambda_alias
  resource             = module.fichiers_liste.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.IdDossier" = false
  }
}

module "sms_validation_send_code" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sms.resource.id
  path   = "send_code"
}
module "POST_sms_validation_send_code" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.sms_validation_send_code.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_parameters = {
    "method.request.querystring.Sandbox" = false
  }
}

module "sms_validation_check_code" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sms.resource.id
  path   = "check_code"
}
module "GET_sms_validation_check_code" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.sms_validation_check_code.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  lambda               = module.getDynamoDBLambda.lambda_alias
}

// /entrepreneurs/account

module "powalco" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "powalco"
}

module "chantiers" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.powalco.resource.id
  path   = "chantiers"
}

module "GET_chantiers" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.chantiers.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}
module "chantiers_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.chantiers.resource.id
  path   = "{Id}"
}
module "GET_chantiers_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.chantiers_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
  request_parameters = {
  }
}

module "entrepreneurs" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.powalco.resource.id
  path   = "entrepreneurs"
}

module "entrepreneurs_account" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.entrepreneurs.resource.id
  path   = "account"
}
module "POST_entrepreneurs_account" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.entrepreneurs_account.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}

module "entrepreneurs_account_enable" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.entrepreneurs_account.resource.id
  path   = "enable"
}
module "POST_entrepreneurs_account_enable" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "PATCH"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.entrepreneurs_account_enable.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}
module "entrepreneurs_chantiers" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.entrepreneurs.resource.id
  path   = "chantiers"
}

module "entrepreneurs_chantiers_upload" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.entrepreneurs_chantiers.resource.id
  path   = "upload"
}
module "GET_entrepreneurs_chantiers_upload" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.entrepreneurs_chantiers_upload.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}
module "POST_entrepreneurs_chantiers_upload" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.entrepreneurs_chantiers_upload.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}
module "PATCH_entrepreneurs_chantiers_upload" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "PATCH"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.entrepreneurs_chantiers_upload.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}
module "DELETE_entrepreneurs_chantiers_upload" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "DELETE"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.entrepreneurs_chantiers_upload.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
  }
}

module "GET_entrepreneurs" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.entrepreneurs.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
}
module "entrepreneurs_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.entrepreneurs.resource.id
  path   = "{EntrepreneurId}"
}
module "GET_entrepreneurs_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.entrepreneurs_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
}

module "partenaire" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "partenaire"
}

module "partenaire_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.partenaire.resource.id
  path   = "{PartenaireId}"
}
module "GET_partenaire_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.partenaire_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
}

module "referentiel" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "referentiel"
}
module "referentiel_civilite" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.referentiel.resource.id
  path   = "civilite"
}
module "GET_referentiel_civilite" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.referentiel_civilite.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}

// sap
module "sap" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "sap"
}
module "sap_utilisateur" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sap.resource.id
  path   = "utilisateur"
}
module "sap_utilisateur_creation" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sap_utilisateur.resource.id
  path   = "creation"
}
module "POST_sap_utilisateur_creation" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.sap_utilisateur_creation.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
}
module "sap_utilisateur_recherche" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sap_utilisateur.resource.id
  path   = "recherche"
}
module "GET_sap_utilisateur_recherche" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.sap_utilisateur_recherche.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
  request_parameters = {
    "method.request.querystring.BpNom"    = true,
    "method.request.querystring.BpPrenom" = true
  }
}
module "sap_utilisateur_modification" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sap_utilisateur.resource.id
  path   = "modification"
}
module "POST_sap_utilisateur_modification" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.sap_utilisateur_modification.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
}
module "sap_utilisateur_suppression" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sap_utilisateur.resource.id
  path   = "suppression"
}
module "DELETE_sap_utilisateur_suppression" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "DELETE"
  role                 = aws_iam_role.api_gateway
  resource             = module.sap_utilisateur_suppression.resource
  lambda               = module.passThroughLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
  request_parameters = {
    "method.request.querystring.Bp" = true
  }
}
module "monitoring" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "monitoring"
}

module "GET_monitoring" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.monitoring.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.basicAuthorizer.id
  request_parameters = {}
}

module "gaz_meter" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "gazMeter"
}

module "gaz_meter_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.gaz_meter.resource.id
  path   = "{sn}"
}

module "gaz_meter_x_pin" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.gaz_meter_x.resource.id
  path   = "pin"
}

module "GET_gaz_meter_x_pin" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.gaz_meter_x_pin.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

//region payements/mollie/update
module "payements" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "payements"
}
module "payements_mollie" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.payements.resource.id
  path   = "mollie"
}
module "payements_mollie_update" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.payements_mollie.resource.id
  path   = "update"
}
module "POST_payements_mollie_update" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.payements_mollie_update.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "payements_mollie_status" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.payements_mollie.resource.id
  path   = "status"
}
module "GET_payements_mollie_status" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.payements_mollie_status.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "POST_payements_mollie_status" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.payements_mollie_status.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
//endregion

//region digacert
module "digacert" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "digacert"
}
module "digacert_soap" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.digacert.resource.id
  path   = "soap"
}
module "POST_digacert_soap" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.passThroughLambda.lambda_alias
  resource             = module.digacert_soap.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
//endregion

//region UserRefundForm
module "formulaire_remboursement" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "formulaire_remboursement"
}
module "POST_formulaire_remboursement" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "POST"
  role     = aws_iam_role.api_gateway
  lambda   = module.UserRefundFormLambda.lambda_alias
  resource = module.formulaire_remboursement.resource
}
//endregion

//region pannes_subscription
module "pannes-subscription" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "panne_subscription"
}
module "POST_pannes-subscription" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes-subscription.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "pannes-subscription_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes-subscription.resource.id
  path   = "{PanneId}"
}
module "DELETE_pannes-subscription_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "DELETE"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes-subscription_x.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Adresse" = true
  }
}
module "pannes-subscription_x_remove" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.pannes-subscription_x.resource.id
  path   = "remove"
}
module "GET_pannes-subscription_x_remove" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.pannes-subscription_x_remove.resource
  lambda               = module.getDynamoDBLambda.lambda_alias
  authorizer           = aws_api_gateway_authorizer.signedUrlAuthorizer.id
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Adresse"   = true
    "method.request.querystring.Salt"      = true
    "method.request.querystring.Signature" = true
  }
}
//endregion

//region bornesRechargeDataHandler
module "bornes_recharge" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "bornes_recharge"
}
module "GET_bornes_recharge" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.passThroughLambda.lambda_alias
  resource             = module.bornes_recharge.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "POST_bornes_recharge" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.bornes_recharge.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
//endregion

//region ibanity
module "ibanity" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "ibanity"
}
module "POST_ibanity" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.genericPassThroughLambda.lambda_alias
  resource             = module.ibanity.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
//endregion

//region /raccordement/upload_plan/upload_plan
module "raccordement" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "raccordement"
}

module "POST_raccordement" {
  source   = "./HttpProxy"
  prod     = local.workspace["prod"]
  method   = "POST"
  role     = aws_iam_role.api_gateway
  lambda   = module.simpleComputeLambda.lambda_alias
  resource = module.raccordement.resource
}

module "raccordement_draft" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.raccordement.resource.id
  path   = "draft"
}

module "POST_raccordement_draft" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.simpleComputeLambda.lambda_alias
  resource             = module.raccordement_draft.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "GET_raccordement_draft" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.simpleComputeLambda.lambda_alias
  resource             = module.raccordement_draft.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "raccordement_upload-plan" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.raccordement.resource.id
  path   = "upload_plan"
}
module "POST_demande_travaux_upload-plan" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.passThroughLambda.lambda_alias
  resource             = module.raccordement_upload-plan.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
module "raccordement_tarifs" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.raccordement.resource.id
  path   = "tarifs"
}
module "GET_raccordement_tarifs" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.raccordement_tarifs.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
//endregion

//region /raccordement/cout
module "raccordement_cout" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.raccordement.resource.id
  path   = "cout"
}
module "GET_raccordement_cout" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.coutModifRaccordementLambda.lambda_alias
  resource             = module.raccordement_cout.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
//endregion

//region /raccordement/forfaits
module "raccordement_forfaits" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.raccordement.resource.id
  path   = "forfaits"
}
module "GET_raccordement_forfaits" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda = module.sqlDrivenLambda.lambda_alias
  resource             = module.raccordement_forfaits.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.header.Accept" = false
  }
}
//endregion

//region sharepoint
module "sharepoint" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "sharepoint"
}

module "sharepoint_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint.resource.id
  path   = "{valise}"
}

module "sharepoint_x_metadata" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint_x.resource.id
  path   = "metadata"
}

module "GET_sharepoint_x_metadata" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sharepointHandlerLambda.lambda_alias
  resource             = module.sharepoint_x_metadata.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters   = {}
}

module "sharepoint_passthrough" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint.resource.id
  path   = "passthrough"
}

module "sharepoint_passthrough_search" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint_passthrough.resource.id
  path   = "search"
}

module "POST_sharepoint_passthrough_search" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.passThroughLambda.lambda_alias
  resource             = module.sharepoint_passthrough_search.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "sharepoint_valise" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint.resource.id
  path   = "valise"
}

module "sharepoint_valise_copy" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint_valise.resource.id
  path   = "copy"
}

module "POST_sharepoint_valise_copy" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sharepointHandlerLambda.lambda_alias
  resource             = module.sharepoint_valise_copy.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "sharepoint_valise_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint_valise.resource.id
  path   = "{ValiseUid}"
}

module "GET_sharepoint_valise_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sharepointHandlerLambda.lambda_alias
  resource             = module.sharepoint_valise_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "POST_sharepoint_valise_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sharepointHandlerLambda.lambda_alias
  resource             = module.sharepoint_valise_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "sharepoint_valise_x_y" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint_valise_x.resource.id
  path   = "{DocumentId}"
}
module "GET_sharepoint_valise_x_y" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sharepointHandlerLambda.lambda_alias
  resource             = module.sharepoint_valise_x_y.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "PUT_sharepoint_valise_x_y" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "PUT"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sharepointHandlerLambda.lambda_alias
  resource             = module.sharepoint_valise_x_y.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "sharepoint_valise_x_all" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.sharepoint_valise_x.resource.id
  path   = "all"
}

module "GET_sharepoint_valise_x_all" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sharepointHandlerLambda.lambda_alias
  resource             = module.sharepoint_valise_x_all.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}
//endregion

module "communes" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "communes"
}

//region /communes/utilisateurs
module "communes_utilisateurs" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes.resource.id
  path   = "utilisateurs"
}
module "GET_communes_utilisateurs" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.communes_utilisateurs.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
module "POST_communes_utilisateurs" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.communes_utilisateurs.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/utilisateurs/{Uid}
module "communes_utilisateurs_uid" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes_utilisateurs.resource.id
  path   = "{Uid}"
}
module "GET_communes_utilisateurs_uid" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.communes_utilisateurs_uid.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
module "PATCH_communes_utilisateurs_uid" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "PATCH"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.communes_utilisateurs_uid.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
module "DELETE_communes_utilisateurs_uid" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "DELETE"
  role                 = aws_iam_role.api_gateway
  lambda               = module.getDynamoDBLambda.lambda_alias
  resource             = module.communes_utilisateurs_uid.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/roles
module "communes_roles" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes.resource.id
  path   = "roles"
}
module "GET_communes_roles" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_roles.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/index
module "communes_index" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes.resource.id
  path   = "index"
}
module "GET_communes_index" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_index.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
module "POST_communes_index" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "POST"
  role                 = aws_iam_role.api_gateway
  lambda               = module.CommuneUploadIndexlambda.lambda_alias
  resource             = module.communes_index.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/index/smart
module "communes_index_smart" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes_index.resource.id
  path   = "smart"
}
module "GET_communes_index_smart" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_index_smart.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {
    "method.request.querystring.EndDate"   = false
    "method.request.querystring.StartDate" = false
    "method.request.querystring.Type"      = false
    "method.request.querystring.Ean"       = false
    "method.request.querystring.MeterId"   = false
  }
}
//endregion

//region /communes/demande_travaux
module "communes_demande-travaux" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes.resource.id
  path   = "demande_travaux"
}
//endregion

//region /communes/demande_travaux/dossiers
module "communes_demande-travaux_dossiers" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes_demande-travaux.resource.id
  path   = "dossiers"
}
module "GET_communes_demande-travaux_dossiers" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_demande-travaux_dossiers.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/demande_travaux/dossiers/{Id}
module "communes_demande-travaux_dossiers_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes_demande-travaux_dossiers.resource.id
  path   = "{Id}"
}
module "GET_communes_demande-travaux_dossiers_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_demande-travaux_dossiers_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/ean
module "communes_ean" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes.resource.id
  path   = "ean"
}

module "GET_communes_ean" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_ean.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region communes/ean/cadrans
module "communes_ean_cadrans" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes_ean.resource.id
  path   = "cadrans"
}

module "GET_communes_ean_cadrans" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_ean_cadrans.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/ean/{Ean}
module "communes_ean_x" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes_ean.resource.id
  path   = "{Ean}"
}

module "GET_communes_ean_x" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_ean_x.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/ean/{Ean}/historique
module "communes_ean_x_historique" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes_ean_x.resource.id
  path   = "historique"
}

module "GET_communes_ean_x_historique" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_ean_x_historique.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}
//endregion

//region /communes/ep
module "communes_ep" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.communes.resource.id
  path   = "ep"
}
module "GET_communes_ep" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.sqlDrivenLambda.lambda_alias
  resource             = module.communes_ep.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  authorizer           = aws_api_gateway_authorizer.tokenAuthorizer.id
  request_parameters = {}
}

//endregion
module "Vehicules" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "vehicules"
}
module "GET_Vehicules" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  lambda               = module.evdbVehiclesExpositionLambda.lambda_alias
  resource             = module.Vehicules.resource
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

module "documentation" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "documentation"
}
module "GET_documentation" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.documentation.resource
  lambda               = module.getDocApilambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {}
}

//region /tad
module "tad" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "tad"
}

//region /tad/locality
module "tad_locality" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.tad.resource.id
  path = "locality"
}

module "GET_tad_locality" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource = module.tad_locality.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}
//endregion

//region /tad/redirection
module "tad_redirection" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = module.tad.resource.id
  path = "redirection"
}

module "GET_redirection" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource = module.tad_redirection.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
}
//endregion
//endregion

//region /installation
module "installation" {
  source = "./Endpoint"
  api    = aws_api_gateway_rest_api.MyResaAPI
  parent = aws_api_gateway_rest_api.MyResaAPI.root_resource_id
  path   = "installation"
}

module "GET_installation" {
  source               = "./HttpProxy"
  prod                 = local.workspace["prod"]
  method               = "GET"
  role                 = aws_iam_role.api_gateway
  resource             = module.installation.resource
  lambda               = module.sqlDrivenLambda.lambda_alias
  request_validator_id = aws_api_gateway_request_validator.validator.id
  request_parameters = {
    "method.request.querystring.Ean"     = true
    "method.request.querystring.MeterId" = true
  }
}
//endregion

locals {
  methods = [
    module.DELETE_sap_utilisateur_suppression,
    module.POST_sap_utilisateur_modification,
    module.POST_sap_utilisateur_creation,
    module.GET_sap_utilisateur_recherche,
    module.POST_formulaire_demande,
    module.POST_utilisateurs,
    module.DELETE_entrepreneurs_chantiers_upload,
    module.PATCH_utilisateurs,
    module.POST_utilisateurs_authenticate,
    module.GET_jobs,
    module.GET_entrepreneurs_x,
    module.GET_me_preferences,
    module.POST_me_preferences,
    module.DELETE_me_preferences_X,
    module.PATCH_me_preferences_X,
    module.GET_me_dashboard,
    module.GET_me_notifications,
    module.GET_adresse,
    module.POST_adresse_validate,
    module.GET_adresse_ean,
    module.GET_adresse_services,
    module.GET_ep,
    module.GET_pannes_x,
    module.POST_entrepreneurs_account,
    module.GET_ean,
    module.GET_ean_validation,
    module.GET_ean_x,
    module.GET_ean_cpt,
    module.GET_referentiel_civilite,
    module.POST_entrepreneurs_account_enable,
    module.GET_entrepreneurs,
    module.PATCH_entrepreneurs_chantiers_upload,
    module.GET_index,
    module.POST_index,
    module.GET_entrepreneurs_chantiers_upload,
    module.GET_index_historique_x,
    module.GET_cab_point_rechargement,
    module.GET_facturation_factures_hgz,
    module.GET_facturation_factures,
    module.GET_facturation_attestations,
    module.GET_facturation_contrats,
    module.GET_facturation_contrats_x,
    module.GET_monitoring,
    module.POST_asynchrone,
    module.GET_asynchrone_x,
    module.POST_asynchrone_callback,
    module.POST_utilisateurs_activate,
    module.POST_utilisateurs_activate_sendMail,
    module.GET_sms,
    module.POST_sms,
    module.POST_sms_incoming,
    module.GET_delestage_adresse,
    module.POST_utilisateurs_reset_sendMail,
    module.POST_utilisateurs_reset,
    module.POST_utilisateurs_updateMail_sendMail,
    module.POST_utilisateurs_updateMail,
    module.POST_utilisateurs_updatePassword,
    module.GET_facturation_balance,
    module.PATCH_me_notifications_status,
    module.POST_me_notifications_create,
    module.GET_facturation_devis,
    module.GET_index_passage,
    module.GET_utilisateurs_edit_uid_preferences,
    module.GET_pannes_planned,
    module.GET_pannes_unplanned,
    module.GET_ppp_master_x,
    module.GET_demande_travaux,
    module.POST_entrepreneurs_chantiers_upload,
    module.GET_chantiers,
    module.GET_facturation_pdf,
    module.POST_demande_travaux_demande,
    module.GET_demande_travaux_devis,
    module.POST_demande_travaux,
    module.POST_demande_travaux_dossier,
    module.POST_demande_travaux_planification,
    module.DELETE_me_objet_raccordement_x,
    module.GET_chantiers_x,
    module.POST_me_objet_raccordement,
    module.POST_tarifs,
    module.POST_adresse_match,
    module.POST_adresse_distCourte,
    module.GET_partenaire_x,
    module.GET_portP1,
    module.POST_portP1_demande,
    module.POST_token,
    module.GET_index_preparer_encodage,
    module.POST_pannes_ep,
    module.GET_pannes,
    module.GET_pannes_active,
    module.DELETE_me,
    module.GET_fichiers_upload,
    module.GET_fichiers_temp_upload,
    module.GET_fichiers_liste,
    module.POST_token_invalider,
    module.POST_sms_validation_send_code,
    module.GET_sms_validation_check_code,
    module.GET_gaz_meter_x_pin,
    module.GET_demande_travaux_confirmation,
    module.POST_payements_mollie_update,
    module.POST_me_consentements,
    module.GET_me_consentements,
    module.GET_adresse_pays,
    module.POST_digacert_soap,
    module.GET_demande_travaux_puissance_forains,
    module.GET_demande_travaux_documents,
    module.GET_ean_recherche,
    module.POST_formulaire_remboursement,
    module.POST_pannes-subscription,
    module.DELETE_pannes-subscription_x,
    module.GET_pannes-subscription_x_remove,
    module.POST_utilisateurs_activate_sendSms,
    module.POST_utilisateurs_activate_checkSms,
    module.POST_ibanity,
    module.GET_bornes_recharge,
    module.POST_bornes_recharge,
    module.POST_demande_travaux_upload-plan,
    module.GET_communes_utilisateurs,
    module.POST_communes_utilisateurs,
    module.GET_communes_utilisateurs_uid,
    module.PATCH_communes_utilisateurs_uid,
    module.DELETE_communes_utilisateurs_uid,
    module.GET_communes_roles,
    module.GET_communes_demande-travaux_dossiers,
    module.GET_communes_demande-travaux_dossiers_x,
    module.GET_communes_ean,
    module.GET_communes_ean_x,
    module.GET_communes_ean_x_historique,
    module.GET_communes_ep,
    module.POST_me_delete_sendMail,
  ]
}

resource "aws_api_gateway_base_path_mapping" "base_path_mapping" {
  depends_on = [aws_api_gateway_stage.MyResaAPI_Stage]
  count       = (local.workspace["domain_name"] == "" || length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0) ? 0 : 1
  api_id      = aws_api_gateway_rest_api.MyResaAPI.id
  stage_name  = local.workspace["api_stage"]
  base_path   = local.workspace["api_version"]
  domain_name = local.workspace["domain_name"]
}

resource "aws_api_gateway_base_path_mapping" "latest" {
  depends_on = [aws_api_gateway_stage.MyResaAPI_Stage]
  count       = (local.workspace["domain_name"] == "" || length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0) ? 0 : 1
  api_id      = aws_api_gateway_rest_api.MyResaAPI.id
  stage_name  = local.workspace["api_stage"]
  base_path   = "latest"
  domain_name = local.workspace["domain_name"]
}

resource "aws_cloudwatch_log_group" "logs" {
  name = "/api/logs/${local.workspace["api"]}"
}

resource "aws_api_gateway_stage" "MyResaAPI_Stage" {
  #aws_api_gateway_stage.MyResaAPI_Stage
  rest_api_id   = aws_api_gateway_rest_api.MyResaAPI.id
  stage_name    = local.workspace["api_stage"]
  deployment_id = aws_api_gateway_deployment.MyResaAPI_Deployment.id

  variables = {
    version = local.workspace["api_version"]
  }

  access_log_settings {
    format          = "{ \"requestId\":\"$context.requestId\", \"ip\": \"$context.identity.sourceIp\", \"caller\":\"$context.identity.caller\", \"user\":\"$context.identity.user\",\"requestTime\":\"$context.requestTime\", \"httpMethod\":\"$context.httpMethod\",\"resourcePath\":\"$context.resourcePath\", \"status\":\"$context.status\",\"protocol\":\"$context.protocol\", \"responseLength\":\"$context.responseLength\" }"
    destination_arn = aws_cloudwatch_log_group.logs.arn
  }

  cache_cluster_enabled = true
  cache_cluster_size    = 0.5
}

resource "aws_api_gateway_method_settings" "general_settings" {
  rest_api_id = aws_api_gateway_rest_api.MyResaAPI.id
  stage_name  = aws_api_gateway_stage.MyResaAPI_Stage.stage_name
  method_path = "*/*"

  settings {
    # Enable CloudWatch logging and metrics
    metrics_enabled                         = true
    data_trace_enabled                      = true
    logging_level                           = "INFO"
    require_authorization_for_cache_control = false
  }
}

resource "aws_api_gateway_deployment" "MyResaAPI_Deployment" {
  rest_api_id = aws_api_gateway_rest_api.MyResaAPI.id
  stage_name  = "dummy"
  lifecycle {
    create_before_destroy = true
  }
  variables = {
    deployed_at = timestamp()
  }

  depends_on = [aws_cloudwatch_dashboard.dashboard]
}

## web ACL association for the MyResaAPI
data "aws_wafv2_web_acl" "MyResaAPI_AWS_WAF" {
  name  = local.workspace["WAF_name"]
  scope = "REGIONAL"
}

resource "aws_wafv2_web_acl_association" "APIGatewayWAFAssociation" {
  resource_arn = aws_api_gateway_stage.MyResaAPI_Stage.arn
  web_acl_arn  = data.aws_wafv2_web_acl.MyResaAPI_AWS_WAF.arn
}

