resource "aws_dynamodb_table" "DossierConfirmationActions" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["DossierConfActionsTable"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Id"

  attribute {
    name = "Id"
    type = "N"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "DossierConfirmationActions"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "MyResaMD" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["dynamodb_md"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "document_id"

  attribute {
    name = "uid"
    type = "S"
  }

  attribute {
    name = "dossier_id"
    type = "S"
  }

  attribute {
    name = "document_id"
    type = "S"
  }

  global_secondary_index {
    name            = "dossier_id-index"
    hash_key        = "dossier_id"
    projection_type = "ALL"
  }
  global_secondary_index {
    name            = "uid-index"
    hash_key        = "uid"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "MyResaMD"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "MyResaSMSCodes" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["sms_codes"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "code"

  attribute {
    name = "code"
    type = "S"
  }

  attribute {
    name = "mobile"
    type = "S"
  }

  global_secondary_index {
    name            = "mobile-index"
    hash_key        = "mobile"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = false
  }

  tags = {
    Name        = "MyResaSMSCodes"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "MyResaUser" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["dynamodb"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "uid"

  attribute {
    name = "uid"
    type = "S"
  }

  attribute {
    name = "bp"
    type = "N"
  }

  attribute {
    name = "email"
    type = "S"
  }

  attribute {
    name = "phone"
    type = "S"
  }

  attribute {
    name = "entrepreneur_id"
    type = "S"
  }

  attribute {
    name = "commune_id"
    type = "S"
  }

  global_secondary_index {
    name            = "bp-index"
    hash_key        = "bp"
    projection_type = "ALL"
  }
  global_secondary_index {
    name            = "email-index"
    hash_key        = "email"
    projection_type = "ALL"
  }
  global_secondary_index {
    name            = "phone-index"
    hash_key        = "phone"
    projection_type = "ALL"
  }
  global_secondary_index {
    name            = "entrepreneur_id-index"
    hash_key        = "entrepreneur_id"
    projection_type = "ALL"
  }
  global_secondary_index {
    name            = "commune_id-index"
    hash_key        = "commune_id"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "MyResaUser"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "AsynchroneProcesses" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["AsynchroneProcesses_table"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  ttl {
    attribute_name = "expirationTime"
    enabled        = true
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "AsynchroneProcesses"
    Environment = local.workspace["stage"]
  }
}


resource "aws_dynamodb_table" "UserRefundForm" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["form_dynamo_table"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "contract"
  range_key    = "date"

  attribute {
    name = "contract"
    type = "S"
  }

  attribute {
    name = "date"
    type = "S"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "UserRefundForm"
    Environment = local.workspace["stage"]
  }
}


resource "aws_dynamodb_table" "UserPanneSubscription" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["panne_dynamodb_table"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "panne"
  range_key    = "adresse"

  attribute {
    name = "panne"
    type = "S"
  }

  attribute {
    name = "adresse"
    type = "S"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "UserPanneSubscription"
    Environment = local.workspace["stage"]
  }
}


resource "aws_dynamodb_table" "PanneLocation" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["panne_location_table"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "panne_id"

  attribute {
    name = "panne_id"
    type = "S"
  }

  attribute {
    name = "equi_id"
    type = "S"
  }

  global_secondary_index {
    name            = "equi_id-index"
    hash_key        = "equi_id"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "PanneLocation"
    Environment = local.workspace["stage"]
  }
}


resource "aws_dynamodb_table" "BornesRechargeTable" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["BornesRechargeTable"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Ean"
  range_key    = "DateCreation"

  attribute {
    name = "Ean"
    type = "S"
  }

  attribute {
    name = "DateCreation"
    type = "S"
  }

  attribute {
    name = "Uuid"
    type = "S"
  }

  global_secondary_index {
    name            = "uuid-index"
    hash_key        = "Uuid"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "BornesRechargeTable"
    Environment = local.workspace["stage"]
  }

}


resource "aws_dynamodb_table" "MiscTable" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = "MyResaAPI_MISC_${local.workspace["stage_tag"]}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Key"

  attribute {
    name = "Key"
    type = "S"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "MiscTable"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "FormulairesTable" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = local.workspace["FormulairesTable"]
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "DateCreation"

  attribute {
    name = "DateCreation"
    type = "S"
  }

  attribute {
    name = "Section"
    type = "S"
  }

  attribute {
    name = "SousSection"
    type = "S"
  }

  global_secondary_index {
    name            = "Section-index"
    hash_key        = "Section"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "SousSection-index"
    hash_key        = "SousSection"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "FormulairesTable"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "UserVehiclesTable" {
  count        = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = "UserVehicles_${local.workspace["stage_tag"]}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "UUID"

  attribute {
    name = "UUID"
    type = "S"
  }

  attribute {
    name = "UID"
    type = "S"
  }

  global_secondary_index {
    name            = "UID-index"
    hash_key        = "UID"
    projection_type = "ALL"
  }


  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "UserVehiclesTable"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "MollieStatus" {
  count = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1
  // do not create on sandbox (will use dev one)
  name         = "MollieStatus_${local.workspace["stage_tag"]}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "payment_id"

  attribute {
    name = "payment_id"
    type = "S"
  }

  attribute {
    name = "order_id"
    type = "S"
  }

  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  global_secondary_index {
    name            = "order_id-index"
    hash_key        = "order_id"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "MollieStatus"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "UserEnergyProfiles" {
  count = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = "UserEnergyProfiles_${local.workspace["stage_tag"]}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key  = "Uid"
  range_key = "HashAddress"

  attribute {
    name = "Uid"
    type = "S"
  }

  attribute {
    name = "HashAddress"
    type = "S"
  }

  global_secondary_index {
    name     = "HashAddress-index"
    hash_key = "HashAddress"
    projection_type = "ALL"
  }
  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "UserEnergyProfiles"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "SmartConsoAlerts" {
  count = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name      = "SmartConsoAlerts_${local.workspace["stage_tag"]}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Uid"
  range_key = "Meter"

  attribute {
    name = "Uid"
    type = "S"
  }

  attribute {
    name = "Meter"
    type = "S"
  }

  global_secondary_index {
    name     = "Meter-index"
    hash_key = "Meter"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "SmartConsoAlerts"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "HistoricAlerts" {
  count = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1 // do not create on sandbox (will use dev one)
  name         = "HistoricAlerts_${local.workspace["stage_tag"]}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "meter_id"
  range_key    = "date"

  attribute {
    name = "meter_id"
    type = "S"
  }

  attribute {
    name = "date"
    type = "S"
  }

  attribute {
    name = "address_hash"
    type = "S"
  }

  global_secondary_index {
    name            = "address_hash-index"
    hash_key        = "address_hash"
    projection_type = "ALL"
  }
  ttl {
    attribute_name = "expirationTime"
    enabled        = true
  }
  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name = "HistoricAlerts"
    Environment = local.workspace["stage"]
  }
}

resource "aws_dynamodb_table" "RaccordementDraftTable" {
  count = length(regexall("^sandbox-.*$", local.workspace["stage"])) > 0 ? 0 : 1
  // do not create on sandbox (will use dev one)
  name         = "RaccordementDraft_${local.workspace["stage_tag"]}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "UUID"

  attribute {
    name = "UUID"
    type = "S"
  }

  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  point_in_time_recovery {
    enabled = local.workspace["stage"] == "production"
  }

  tags = {
    Name        = "RaccordementDraftTable"
    Environment = local.workspace["stage"]
  }
}