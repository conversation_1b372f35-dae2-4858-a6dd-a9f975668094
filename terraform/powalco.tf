resource "aws_s3_bucket_notification" "bucket_notification" {
  bucket = aws_s3_bucket.PowalcoFiles.id

  topic {
    topic_arn = aws_sns_topic.on_upload.arn
    events    = ["s3:ObjectCreated:*"]
  }
}

resource "aws_sns_topic" "on_upload" {
  name   = "${local.workspace["PowalcoBucket"]}_upload"
  policy = <<EOF
{
  "Version": "2008-10-17",
  "Id": "__default_policy_ID",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "s3.amazonaws.com"
      },
      "Action": "SNS:Publish",
      "Resource": "*",
      "Condition": {
        "ArnLike": {
          "aws:SourceArn": "${aws_s3_bucket.PowalcoFiles.arn}"
        }
      }
    }
  ]
}
EOF
}

resource "aws_sns_topic_subscription" "bucket_notification_to_powalco_sharepoint_sqs" {
  raw_message_delivery = true
  topic_arn            = aws_sns_topic.on_upload.arn
  protocol             = "sqs"
  endpoint             = module.powalcoSharepointUploadQueue.queue.arn
}

resource "aws_sns_topic_subscription" "bucket_notification_to_powalco_sqs" {
  raw_message_delivery = true
  topic_arn            = aws_sns_topic.on_upload.arn
  protocol             = "sqs"
  endpoint             = module.powalcoUploadQueue.queue.arn
}

resource "aws_sqs_queue_policy" "sqs_access_policy" {
  queue_url = module.powalcoUploadQueue.queue.id

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Id": "sqspolicy",
  "Statement": [
    {
      "Action": "sqs:SendMessage",
      "Effect": "Allow",
      "Resource": "${module.powalcoUploadQueue.queue.arn}",
      "Principal": {
        "AWS": "*"
      },
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${aws_sns_topic.on_upload.arn}"
        }
      }
    }
  ]
}
EOF
}

resource "aws_sqs_queue_policy" "sqs_access_policy_sharepoint" {
  queue_url = module.powalcoSharepointUploadQueue.queue.id

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Id": "sqspolicy",
  "Statement": [
    {
      "Action": "sqs:SendMessage",
      "Effect": "Allow",
      "Resource": "${module.powalcoSharepointUploadQueue.queue.arn}",
      "Principal": {
        "AWS": "*"
      },
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${aws_sns_topic.on_upload.arn}"
        }
      }
    }
  ]
}
EOF
}


locals {
  powalco_prefix_url = local.workspace["PowalcoUrl"] == null ? "https://${aws_s3_bucket.PowalcoFiles.bucket_domain_name}" : "https://${local.workspace["PowalcoUrl"]}"
}
