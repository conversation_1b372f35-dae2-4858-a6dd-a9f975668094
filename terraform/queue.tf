module "userActivation" {
  source            = "./Queue"
  timeout           = 900 # 15min
  retry             = 200 # 15min * 200 = 50h
  lambda            = module.userActivationLambda
  prod              = local.workspace["prod"]
  deadletter_lambda = module.deadletterHandlerLambda
}

module "powalcoUploadQueue" {
  source            = "./Queue"
  timeout           = 3600 # 60min
  retry             = 50   # 60min * 50 = 50h
  lambda            = module.powalcoUploadLambda
  prod              = local.workspace["prod"]
  deadletter_lambda = null
}

module "powalcoSharepointUploadQueue" {
  source            = "./Queue"
  timeout           = 3600 # 60min
  retry             = 50   # 60min * 50 = 50h
  lambda            = module.powalcoSharepointUploadLambda
  prod              = local.workspace["prod"]
  deadletter_lambda = null
}

data "aws_sns_topic" "on_userActivation_failure" {
  name = "userActivation_failure"
}

resource "aws_cloudwatch_metric_alarm" "on_userActivation_failure" {
  alarm_name  = "userActivation_failure"
  namespace   = "AWS/SQS"
  metric_name = "NumberOfMessagesSent"
  dimensions = {
    QueueName = module.userActivation.deadletter.name
  }
  statistic           = "Sum"
  period              = 600
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0
  evaluation_periods  = 1
  treat_missing_data  = "notBreaching"

  alarm_actions = [data.aws_sns_topic.on_userActivation_failure.arn]
}

module "sendToSalesForceQueue" {
  source            = "./Queue"
  timeout           = 3600 # 1h
  retry             = 48   # 2d
  lambda            = module.sendToSalesForceLambda
  prod              = local.workspace["prod"]
  deadletter_lambda = module.deadletterHandlerLambda
}
