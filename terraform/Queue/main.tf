
variable "lambda" { description = "lambda module" }
variable "timeout" {
}
variable "retry" {
}
variable "prod" {}
variable "output" {
  default = null
}
variable "deadletter_lambda" {
}

locals {
  lambda_arn = var.lambda.lambda_alias.arn
}

resource "aws_iam_role_policy" "allow_sqs" {
  name = "allow_sqs${aws_sqs_queue.retry.name}"
  role = var.lambda.role.id

  policy = <<EOF
{
"Version": "2012-10-17",
"Statement": [
    {
    "Effect": "Allow",
    "Action": [
        "sqs:*"
    ],
    "Resource": ["${aws_sqs_queue.retry.arn}"]
    }
]
}
EOF
}

resource "aws_iam_role_policy" "allow_sqs-deadletter" {
  count = var.deadletter_lambda == null ? 0 : 1
  name  = "allow_sqs-deadletter${aws_sqs_queue.retry-deadletter.name}"
  role  = var.deadletter_lambda.role.id

  policy = <<EOF
{
"Version": "2012-10-17",
"Statement": [
    {
    "Effect": "Allow",
    "Action": [
        "sqs:*"
    ],
    "Resource": ["${aws_sqs_queue.retry-deadletter.arn}"]
    }
]
}
EOF
}

resource "aws_sqs_queue" "retry-deadletter" {
  name                      = "${var.lambda.lambda.function_name}_deadletter-queue"
  message_retention_seconds = 1209600
  #TODO : monitor this deadletter queue to easily handle errors
}
resource "aws_sqs_queue" "retry" {
  visibility_timeout_seconds = var.timeout
  name                       = "${var.lambda.lambda.function_name}_queue"
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.retry-deadletter.arn
    maxReceiveCount     = var.retry
  })
}

resource "aws_lambda_event_source_mapping" "executeRequest" {
  event_source_arn = aws_sqs_queue.retry.arn
  function_name    = local.lambda_arn
}

resource "aws_lambda_event_source_mapping" "deadletter_mapping" {
  count            = var.deadletter_lambda == null ? 0 : 1
  event_source_arn = aws_sqs_queue.retry-deadletter.arn
  function_name    = var.deadletter_lambda.lambda_alias.arn
}

resource "aws_lambda_function_event_invoke_config" "output" {
  count         = var.output == null ? 0 : 1
  function_name = var.lambda.lambda.function_name

  destination_config {
    on_success {
      destination = var.output.arn
    }
  }
}

output "queue" {
  value = aws_sqs_queue.retry
}

output "deadletter" {
  value = aws_sqs_queue.retry-deadletter
}
