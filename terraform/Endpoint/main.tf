variable "path" { type = string }
variable "api" {}
variable "parent" {}
variable "root" {
  default = false
}

module "api-gateway-enable-cors" {
  source          = "squidfunk/api-gateway-enable-cors/aws"
  version         = "0.3.3"
  api_id          = var.api.id
  api_resource_id = aws_api_gateway_resource.endpoint.id
}

resource "aws_api_gateway_resource" "endpoint" {
  rest_api_id = var.api.id
  parent_id   = var.parent
  path_part   = var.path
}



// FOR CORS
/* resource "aws_api_gateway_method" "method" {
  depends_on    = [aws_api_gateway_resource.endpoint]
  rest_api_id   = "${var.api.id}"
  resource_id   = "${aws_api_gateway_resource.endpoint.id}"
  http_method   = "OPTIONS"
  authorization = "NONE"
}
resource "aws_api_gateway_method_response" "method_response" {
  depends_on  = [aws_api_gateway_method.method]
  rest_api_id = "${var.api.id}"
  resource_id = "${aws_api_gateway_resource.endpoint.id}"
  http_method = "OPTIONS"

  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = true,
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true
  }
}
resource "aws_api_gateway_integration" "integration" {
  rest_api_id = "${var.api.id}"
  resource_id = "${aws_api_gateway_resource.endpoint.id}"
  http_method = "OPTIONS"
  type        = "MOCK"
  depends_on  = [aws_api_gateway_method_response.method_response]
  request_templates = {
    "application/json" = <<EOF
{
   "statusCode" : 200
}
EOF
  }
} */

/*data "aws_s3_bucket_object" "cors_conf" {
  bucket = "ourcorp-deploy-config"
  key    = "ec2-bootstrap-script.sh"
}*/

/*
resource "aws_api_gateway_integration_response" "integration_response_200" {
  rest_api_id = "${var.api.id}"
  resource_id = "${aws_api_gateway_resource.endpoint.id}"
  http_method = "OPTIONS"
  status_code = "200"
  depends_on  = [aws_api_gateway_integration.integration]

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = "'*'" #"'${data.aws_s3_bucket_object.cors_conf.body}'"
    "method.response.header.Access-Control-Allow-Headers" = "'*'"
  }
}
*/
output "resource" {
  value = aws_api_gateway_resource.endpoint
}
