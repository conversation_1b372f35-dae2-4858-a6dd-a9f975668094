# Ressource pour le chemin /health:
# Cette ressource crée un chemin "/health" sous votre API principale.
resource "aws_api_gateway_resource" "health_resource" {
  rest_api_id = aws_api_gateway_rest_api.MyResaAPI.id  # L'ID de votre API Gateway principal
  parent_id   = aws_api_gateway_rest_api.MyResaAPI.root_resource_id  # L'ID du chemin racine de l'API
  path_part   = "health"  # Nom du chemin
}

# Méthode HTTP GET pour /health:
# Cette ressource associe une méthode GET au chemin "/health" précédemment créé.
resource "aws_api_gateway_method" "health_get" {
  rest_api_id   = aws_api_gateway_rest_api.MyResaAPI.id
  resource_id   = aws_api_gateway_resource.health_resource.id
  http_method   = "GET"
  authorization = "NONE"
  request_parameters = {
    "method.request.path.id" = false
  }
}

# Réponse de la méthode pour le code HTTP 200:
# Cette ressource définit la réponse attendue pour un code HTTP 200.
resource "aws_api_gateway_method_response" "success_response" {
  rest_api_id = aws_api_gateway_rest_api.MyResaAPI.id
  resource_id = aws_api_gateway_resource.health_resource.id
  http_method = aws_api_gateway_method.health_get.http_method
  status_code      = "200"
  response_models = {
    "application/json" = "Empty"
  }
}

# Intégration Mock pour le chemin /health:
# Cette ressource crée une réponse mock pour la méthode GET du chemin "/health".
# Elle renverra directement un code HTTP 200 sans passer par une fonction Lambda ou une autre intégration.
resource "aws_api_gateway_integration" "health_mock" {
  rest_api_id = aws_api_gateway_rest_api.MyResaAPI.id
  resource_id = aws_api_gateway_resource.health_resource.id
  http_method = aws_api_gateway_method.health_get.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates       = {
    "application/json" = "{\"statusCode\": 200}"  # Cette ligne définie le code de statut 200 pour la réponse mock.
  }
}

# Réponse de l'intégration pour le code HTTP 200:
# Cette ressource définit la réponse de l'intégration pour un code HTTP 200.
resource "aws_api_gateway_integration_response" "mock_200_response" {
  depends_on  = [aws_api_gateway_integration.health_mock]
  rest_api_id = aws_api_gateway_rest_api.MyResaAPI.id
  resource_id = aws_api_gateway_resource.health_resource.id
  http_method = aws_api_gateway_method.health_get.http_method
  status_code      = aws_api_gateway_method_response.success_response.status_code
  response_templates = {
    "application/json" = ""
  }
}