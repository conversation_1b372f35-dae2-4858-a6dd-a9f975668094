//region gaz pin sms topic
resource "aws_sns_topic" "gaz_pin_sms" {
  name = "gaz_pin_sms_${local.workspace["stage"]}"
}

resource "aws_sns_topic_subscription" "gaz_pin_sms_to_gazMeterPinLambda" {
  topic_arn = aws_sns_topic.gaz_pin_sms.arn
  protocol  = "lambda"
  endpoint  = module.gazMeterPinLambda.lambda_alias.arn
}
//endregion

//region new api version codestar topic
resource "aws_sns_topic" "codestar_new_version" {
  name = "codestar-notifications-myresaapi-${local.workspace["stage"]}-new-version"
}

resource "aws_sns_topic_policy" "codestar_new_version" {
  arn    = aws_sns_topic.codestar_new_version.arn
  policy = data.aws_iam_policy_document.codestar_new_version_policy.json
}

data "aws_iam_policy_document" "codestar_new_version_policy" {
  statement {
    sid = "CodeNotification_publish"
    actions = [
      "SNS:Publish"
    ]
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["codestar-notifications.amazonaws.com"]
    }
    resources = [
      aws_sns_topic.codestar_new_version.arn,
    ]
  }
}

resource "aws_sns_topic_subscription" "codestar_new_version_to_sendEmailOnReleaseLambda" {
  topic_arn = aws_sns_topic.codestar_new_version.arn
  protocol  = "lambda"
  endpoint  = module.sendEmailOnReleaseLambda.lambda_alias.arn
}
//endregion

resource "aws_sns_topic" "basic_deadletter_notification" {
  name = "basic_deadletter_notification-${local.workspace["stage"]}"
}