variable "resource" {}
variable "lambda" {}
variable "authorizer" { default = null }
variable "method" { type = string }
variable "role" {}
variable "prod" {}
variable "request_validator_id" { default = null }
variable "request_parameters" { default = {} }
variable "response_parameters" { default = { "method.response.header.Access-Control-Allow-Origin" = true } }
variable "response_models" { default = null }