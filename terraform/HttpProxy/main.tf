

resource "aws_api_gateway_method" "method" {
  rest_api_id          = var.resource.rest_api_id
  resource_id          = var.resource.id
  http_method          = var.method
  authorization        = (var.authorizer == null) ? "NONE" : "CUSTOM"
  authorizer_id        = var.authorizer
  request_validator_id = var.request_validator_id
  request_parameters   = var.request_parameters
}

locals {
  status = [200, 400, 401, 404, 406, 409, 500, 501, 502]
}

resource "aws_api_gateway_method_response" "method_response" {
  rest_api_id = var.resource.rest_api_id
  resource_id = var.resource.id
  http_method = var.method

  for_each            = zipmap(local.status, local.status)
  status_code         = each.value
  response_parameters = var.response_parameters
  response_models     = var.response_models
  depends_on          = [aws_api_gateway_method.method]
}

resource "aws_api_gateway_integration" "integration" {
  rest_api_id             = var.resource.rest_api_id
  resource_id             = var.resource.id
  http_method             = var.method
  credentials             = var.role.arn
  passthrough_behavior    = "WHEN_NO_MATCH"
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = var.lambda.invoke_arn

  cache_key_parameters = keys(var.request_parameters)
  request_parameters = {
    "integration.request.header.X-Authorization" = "'static'",
  }
  depends_on = [aws_api_gateway_method_response.method_response]
}

output "integration" {
  value = aws_api_gateway_integration.integration
}

output "resource" {
  value = var.resource
}

output "method" {
  value = var.method
}
