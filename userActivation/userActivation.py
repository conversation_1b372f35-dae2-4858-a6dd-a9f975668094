import json
import os

from boto3.dynamodb.conditions import Key

from utils.api import api_caller, basic_auth_headers
from utils.auth_utils import loadUserByUID, load_env_file
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import send_sqs, get_dynamodb_table, write_s3_file, load_s3_file
from utils.dict_utils import get, merge
from utils.ldap_utils import LDAP
from utils.log_utils import log_info, Capturing
from utils.parallel import concurrently
from utils.sap_user import generate_or_find_bp
from utils.string_utils import randomString
from utils.token_utils import decode_simple_jwt
from utils.validation import alnum_only


@aws_lambda_handler
def handler(event, context):
    log_info(event)
    for record in event["Records"]:
        record_handler(json.loads(record["body"]), context)


def record_handler(payload, context):
    try:
        with Capturing() as output:
            log_info("lambda userActivation stated")
            log_info("payload " + str(payload))
            os.environ["API_URL"] = payload["API_URL"]
            os.environ["USER_ACTIVATION_SQS_URL"] = payload["USER_ACTIVATION_SQS_URL"]
            step = str(payload["step"]).upper()
            if step == "GET_BP":
                get_bp(payload)
            elif step == "HANDLE_TOKEN":
                handle_token(payload)
            elif step == "WRITE_AD":
                write_ad(payload)
            elif step == "UPDATE_UPLOADS":
                update_uploads(payload)
            elif step == "SEND_MAIL":
                send_mail(payload)
            else:
                raise Exception("unknown step `{}`".format(step))
    except Exception as e:
        print(e)
        logs = output.copy() + [str(e)]
        log_info("logs " + str(logs))
        key = "logs/" + str(get(payload, "log_id"))
        current = ""
        try:
            current = load_s3_file(os.environ["BUCKET"], key)
        except Exception as ex:
            log_info("error reading file (ignored) : " + str(ex))
        current += "\n".join(logs)
        current += "\n"
        write_s3_file(os.environ["BUCKET"], key, current)
        raise e


def get_bp(event):
    user_data = loadUserByUID(event["uid"])
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    bp = generate_or_find_bp(user_data)
    table.update_item(
        Key={"uid": event["uid"]},
        UpdateExpression="SET bp = :bp",
        ExpressionAttributeValues={
            ":bp": bp,
        },
    )
    send_sqs(os.environ["USER_ACTIVATION_SQS_URL"], merge(event, {"step": "HANDLE_TOKEN"}))


def handle_token(event):
    user_data = loadUserByUID(event["uid"])
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    if get(user_data, "token_ppp"):
        resp_token = api_caller(
            "post",
            "/token",
            {
                "Token": user_data["token_ppp"],
            },
            headers={"SessionId": event["uid"]},
        )
        del user_data["token_ppp"]
        table.update_item(
            Key={"uid": event["uid"]},
            UpdateExpression="SET token_ppp = :token_ppp",
            ExpressionAttributeValues={":token_ppp": None},
        )
    send_sqs(os.environ["USER_ACTIVATION_SQS_URL"], merge(event, {"step": "WRITE_AD"}))


def write_ad(event):
    user_data = loadUserByUID(event["uid"])
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    firstname = get(user_data, "firstname")
    lastname = get(user_data, "lastname")
    email = get(user_data, "email")
    phone = get(user_data, "phone")
    phonefixe = get(user_data, "phone_fixe")
    username = alnum_only(firstname[:3]) + "." + alnum_only(lastname[:9]) + "." + randomString(5)
    try:
        encryptedPassword = get(event, "encryptedPassword")
        password = decode_simple_jwt(encryptedPassword)["password"]
    except:
        password = get(event, "password")  # TODO: remove here (will cause version rollback issue)
    fullname = firstname + " " + lastname
    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    uid = ldap.addUser(
        {
            "fullname": fullname,
            "password": password,
            "firstname": firstname,
            "lastname": lastname,
            "email": email,
            "phone": phone,
            "phone_fixe": phonefixe,
            "username": username,
        }
    )
    del ldap
    new_user = merge(user_data, {"uid": uid, "valide": True})
    if "Commune" in new_user:
        new_user["Commune"]["Actif"] = True
    table.put_item(Item=new_user)
    table.delete_item(Key={"uid": user_data["uid"]})
    send_sqs(
        os.environ["USER_ACTIVATION_SQS_URL"],
        merge(event, {"step": "UPDATE_UPLOADS", "uid": uid, "ghost": event["uid"]}),
    )


def update_uploads(event):
    uid = event["uid"]
    ghost = event["ghost"]
    MD_TABLE = get_dynamodb_table(os.environ["MD_TABLE"])
    documents = MD_TABLE.query(IndexName="uid-index", KeyConditionExpression=Key("uid").eq(ghost)).get("Items", [])
    concurrently(
        *[
            lambda: MD_TABLE.update_item(
                Key={"document_id": document["document_id"]},
                UpdateExpression="SET uid=:uid",
                ExpressionAttributeValues={":uid": uid},
            )
            for document in documents
        ]
    )
    send_sqs(
        os.environ["USER_ACTIVATION_SQS_URL"],
        merge(event, {"step": "SEND_MAIL", "uid": uid}),
    )


def send_mail(event):
    user_data = loadUserByUID(event["uid"])
    preferences = get(user_data, "preferences", {}, default_on_empty=True)

    api_caller(
        method="post",
        path="/envMessage",
        body={
            "Langue": get(preferences, "Langue", "FR", default_on_empty=True),
            "Header": {
                "TEMPLATE_ID": ("MYRE_COMMUNE_ACCOUNT_CONFIRMATION" if user_data.get("commune_id") else "MYRE_ACCOUNT_CONFIRMATION"),
                "EMAIL": user_data["email"],
            },
        },
        headers=basic_auth_headers(load_env_file()["BASICAUTH_AWS"]),
    )
