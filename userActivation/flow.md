# User Activation lambda flow

this lambda handle messages in a queue  
this flow is divided in many steps to easily catch errors

## GET_BP

the user just asked to being activated  
**behaviour :** query WS73 to fetch Bp and attach it to the user

## WRITE_AD

create the user  
send a mail to the user to confirm user creation  
**behaviour :** Create the user in the active directory

## HANDLE_TOKEN

if user has called WS64 previously (entred a token), validate this token  
**behaviour :** query WS64 to attach token to Bp

## SEND_MAIL

onfirm user creation  
**behaviour :** send a mail to the user
