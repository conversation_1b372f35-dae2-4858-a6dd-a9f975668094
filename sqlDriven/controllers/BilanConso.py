import os
from collections import defaultdict
from datetime import datetime
from os import environ

import pytz
from boto3.dynamodb.conditions import Attr
from controllers.NoPagination import NoPagination
from dateutil.relativedelta import relativedelta

from utils.api import api_caller, basic_auth_headers
from utils.aws_utils import get_dynamodb_table
from utils.errors import NotFoundError
from utils.log_utils import log_info


class BilanConso(NoPagination):
    RED_TABLE_ELEC = "public.cumulativeactiveenergyp1d"
    RED_TABLE_GAZ = "public.cumulativestoredvolumep1d"
    TEMPLATE_MAIL = "BILAN_CONSO_V2"
    DYNAMO_BILAN_TABLE = get_dynamodb_table(os.environ["UserEnergyProfiles"])
    DYNAMO_USER_TABLE = get_dynamodb_table(os.environ["DYNAMODB"])

    def __init__(self, event, linking):
        super().__init__(event, linking)
        current_date = datetime.now()

        self.previous_month_month = current_date.month - 1 if current_date.month > 1 else 12
        self.previous_month_year = current_date.year if current_date.month > 1 else current_date.year - 1
        self.two_months_ago_month = self.previous_month_month - 1 if self.previous_month_month > 1 else 12
        self.two_months_ago_year = self.previous_month_year if self.previous_month_month > 1 else self.previous_month_year - 1

        self.end_date = current_date.replace(day=1)
        self.mid_date = self.end_date - relativedelta(months=1)
        self.start_date = self.end_date - relativedelta(months=2)
        self.belgium_tz = pytz.timezone("Europe/Brussels")

        self.items = {}
        self.eans = []
        self.uids = self.get_all_uids_with_bilan()

    def get_all_uids_with_bilan(self):
        filter_attr_bilan = "Bilan"
        response = self.DYNAMO_BILAN_TABLE.scan(FilterExpression=Attr(filter_attr_bilan).eq(True))
        items = response.get("Items", [])
        while "LastEvaluatedKey" in response:
            response = self.DYNAMO_BILAN_TABLE.scan(FilterExpression=Attr(filter_attr_bilan).eq(True), ExclusiveStartKey=response["LastEvaluatedKey"])
            items.extend(response.get("Items", []))

        uids = []
        if len(items) <= 0:
            raise NotFoundError("No data where found, no one is using this features", error_code="FEATURE_NOT_USED")
        for item in items:
            hash_address = item.get("HashAddress")
            uid = item.get("Uid")
            self.get_all_table_items(uid, hash_address)
            if item["Uid"] not in uids:
                uids.append(uid)
        if len(self.eans) <= 0:
            raise NotFoundError("No eans where found, no one is using this features", error_code="EANS_NOT_FOUND")
        log_info(f"evaluated eans = {self.eans}")

        return uids

    def get_all_table_items(self, uid, hash_address):
        response = self.DYNAMO_USER_TABLE.get_item(Key={"uid": uid})
        item = response.get("Item")
        for e in item["ean"]:
            e_add_hash = e.get("address_hash")
            if e_add_hash == hash_address:
                ean_key = e["ean"]
                if ean_key not in self.eans:
                    self.eans.append(ean_key)
                ean_address = e.get("address", {})
                address = {
                    "Cdpostal": ean_address.get("CdPostal"),
                    "CodePays": "BE",
                    "Localite": ean_address.get("Localite"),
                    "NumRue": ean_address.get("NumRue"),
                    "Rue": ean_address.get("Rue"),
                }
                self.items[ean_key] = {
                    "bp": item.get("bp"),
                    "email": item.get("email"),
                    "adresse": address,
                    "langue": item["preferences"].get("Langue", "FR"),
                    "firstname": item.get("firstname"),
                }

    def apply_hana_env(self, sql_statement):
        start_date = self.belgium_tz.localize(self.start_date).strftime("'%Y-%m-%d 00:00:00%z'")
        mid_date = self.belgium_tz.localize(self.mid_date).strftime("'%Y-%m-%d 00:00:00%z'")
        end_date = self.belgium_tz.localize(self.end_date).strftime("'%Y-%m-%d 00:00:00%z'")

        start_date_6 = self.belgium_tz.localize(self.start_date).strftime("'%Y-%m-%d 06:00:00%z'")
        mid_date_6 = self.belgium_tz.localize(self.mid_date).strftime("'%Y-%m-%d 06:00:00%z'")
        end_date_6 = self.belgium_tz.localize(self.end_date).strftime("'%Y-%m-%d 06:00:00%z'")

        sql_statement = sql_statement.format(
            red_table_elec=self.RED_TABLE_ELEC,
            red_table_gaz=self.RED_TABLE_GAZ,
            start_date=start_date,
            mid_date=mid_date,
            end_date=end_date,
            start_date_6=start_date_6,
            mid_date_6=mid_date_6,
            end_date_6=end_date_6,
            Ean="(" + ",".join([f"'{ean}'" for ean in self.eans]) + ")",
        )
        return sql_statement

    def group_consumption(self, data):
        data_grouped = defaultdict(lambda: {"consumption": 0, "injection": 0})
        for entry in data:
            key = (entry["ean"], entry["type_energie"], entry["measuredatetime"].date())
            if entry["readingtypeid"].startswith("1.8") or entry["readingtypeid"].startswith("3."):
                data_grouped[key]["consumption"] += entry["measurevalue"]
            elif entry["readingtypeid"].startswith("2.8"):
                data_grouped[key]["injection"] += entry["measurevalue"]

        return data_grouped

    def calcul_consumption(self, data_grouped):
        monthly_differences = defaultdict(
            lambda: {
                "previous_month": 0,
                "two_months_ago": 0,
                "previous_month_injection": 0,
                "two_months_ago_injection": 0,
            },
        )
        grouped_by_ean = defaultdict(list)
        for (ean, energy, date), values in data_grouped.items():
            grouped_by_ean[(ean, energy)].append((date, values))

        for key, entries in grouped_by_ean.items():
            entries.sort()
            if len(entries) >= 3:
                farest_date, mid_date, closest_date = entries[0][0], entries[1][0], entries[2][0]

                farest_values = data_grouped.get((key[0], key[1], farest_date), {"consumption": 0, "injection": 0})
                mid_values = data_grouped.get((key[0], key[1], mid_date), {"consumption": 0, "injection": 0})
                closest_values = data_grouped.get((key[0], key[1], closest_date), {"consumption": 0, "injection": 0})

                monthly_differences[key]["previous_month"] = closest_values["consumption"] - mid_values["consumption"]
                monthly_differences[key]["two_months_ago"] = mid_values["consumption"] - farest_values["consumption"]
                monthly_differences[key]["previous_month_injection"] = closest_values["injection"] - mid_values["injection"]
                monthly_differences[key]["two_months_ago_injection"] = mid_values["injection"] - farest_values["injection"]

        return monthly_differences

    def send_consumption(self, consumption):
        for key, values in consumption.items():
            ean = key[0]
            energy_type = key[1]
            item_details = self.items.get(ean, {})

            email = item_details.get("email", None)
            adresse = item_details.get("adresse", {})
            first_name = item_details.get("firstname")
            lang = item_details.get("langue")
            self.send_mail(lang=lang, energy=energy_type, values=values, email=email, adresse=adresse, first_name=first_name)

    def to_response(self, cursor, params=None):
        data: list = super().to_response(cursor, params)
        data_grouped = self.group_consumption(data)
        monthly_consumption = self.calcul_consumption(data_grouped)
        self.send_consumption(monthly_consumption)
        return {"statusCode": 204, "body": None}

    def send_mail(self, lang, energy, values, email, adresse, first_name):
        prelev_previous_month = round(values["previous_month"], 2)
        prelev_two_months_ago = round(values["two_months_ago"], 2)
        prelev_data = {"PRELEV_AMOUNT_M": f"{prelev_previous_month:.2f}".replace(".", ","), "PRELEV_AMOUNT_MPREC": f"{prelev_two_months_ago:.2f}".replace(".", ",")}

        mail_params = {}
        if energy == "elec":
            mail_params["ELEC"] = prelev_data
            if values["previous_month_injection"] != 0:
                mail_params["ELEC"]["INJEC_AMOUNT_M"] = f"{values['previous_month_injection']:.2f}".replace(".", ",")
                mail_params["ELEC"]["INJEC_AMOUNT_MPREC"] = f"{values['two_months_ago_injection']:.2f}".replace(".", ",")
        else:
            mail_params["GAZ"] = prelev_data
        body = {
            "Langue": lang,
            "Header": {
                "TEMPLATE_ID": self.TEMPLATE_MAIL,
                "EMAIL": email,
                "STREET": f"{adresse['Rue']} {adresse['NumRue']}",
                "POST_CODE": adresse["Cdpostal"],
                "CITY": adresse["Localite"],
                "FIRST_NAME": first_name,
                "CONCERN_TIME": f"{self.previous_month_month:02d}-{self.previous_month_year}",
                "CONCERN_TIME_MPREC": f"{self.two_months_ago_month:02d}-{self.two_months_ago_year}",
            },
        }
        body.update(mail_params)
        api_caller(
            method="post",
            path="/envMessage",
            body=body,
            headers=basic_auth_headers(environ["BASICAUTH_AWS"]),
            internal_url=False,
        )
