import itertools
from datetime import datetime
from enum import Enum

from controllers.parallel_controller import <PERSON><PERSON>or<PERSON>, ParallelController_Deprecated

from utils.aws_utils import get_secret
from utils.dict_utils import get, rename_nested_keys, snake_to_pascal
from utils.ean_utils import get_code_cadran
from utils.errors import INVALID_EAN_FOR_USER, BadRequestError, NotFoundError
from utils.models.user import User
from utils.type_utils import to_list
from utils.validation import is_number


class InputType(Enum):
    EAN = "Ean"
    EAN_CPT = "EanCpt"


class ws34(ParallelController_Deprecated):
    def __init__(self, event, linking):
        super().__init__(event, linking)

    def validate_request_params(self):
        """
        Validates request parameters and returns their string representation
        """
        input_params = super().validate_request_params()
        input_type = get_input_type(self.event["queryStringParameters"])

        if input_type == InputType.EAN:
            # If only EAN provided, the call should be authenticated and the user should own the given EAN
            user = User.from_event(self.event, allow_ghost=False)
            input_params["bp"] = user.bp
            if str(input_params.get("Ean")) not in user.ean_ids:
                raise INVALID_EAN_FOR_USER
        elif input_type == InputType.EAN_CPT:
            # If EAN + NumCompteur, the call can be made anonymously
            # (used by voicebot, where authentication is not possible)
            cpt = input_params["NumCompteur"]
            input_params["NumCompteur"] = cpt.zfill(18) if is_number(cpt) else cpt
        else:
            raise BadRequestError(
                "Les possibles inputs sont [Ean] ou [Ean, NumCompteur]",
                error_code="INVALID_INPUT",
            )

        return input_params

    def execute_query(self, sql_statement, request_params):
        # Parameters for all queries
        db_credentials = get_secret(super().get_secret())
        result_dict = {}
        inner_fetcher = self.fetch
        input_type = get_input_type(self.event["queryStringParameters"])

        num_compteur = str(request_params["NumCompteur"]) if input_type != InputType.EAN else None

        ean_from_cpt = None

        if input_type != InputType.EAN:
            preliminar_query = DatabaseWorker(
                "get_ean_from_counter",
                db_credentials,
                sql_statement["get_ean_from_counter"],
                {"NumCompteur": num_compteur},
                inner_fetcher,
                result_dict,
            )
            preliminar_query.start()
            preliminar_query.join()
            ean_from_cpt = get(result_dict["get_ean_from_counter"], "EAN")
            if not ean_from_cpt:
                BadRequestError("Le NumCpt fourni n'existe pas")

        ean = str(request_params["Ean"])

        if input_type == InputType.EAN_CPT and ean != ean_from_cpt:
            raise BadRequestError(
                "Le NumCpt fourni n'est pas associé à l'EAN fourni",
                error_code="EAN_CPT_NO_MATCH",
            )

        if input_type == InputType.EAN:
            full_query = DatabaseWorker(
                "get_historique_conso_from_ean",
                db_credentials,
                sql_statement["get_historique_conso_from_ean"],
                {"ean": ean, "bp": request_params["bp"]},
                inner_fetcher,
                result_dict,
            )
        else:
            full_query = DatabaseWorker(
                "get_historique_conso_from_ean",
                db_credentials,
                sql_statement["get_historique_conso_from_ean_noauth"],
                {"ean": ean},
                inner_fetcher,
                result_dict,
            )

        full_query.start()
        full_query.join()

        # Add category label
        for item in to_list(result_dict["get_historique_conso_from_ean"]):
            item["Cadran"] = get_code_cadran(item)

        result = process_result(to_list(result_dict["get_historique_conso_from_ean"]))

        if not result:
            raise NotFoundError("Pas de contrat actif")

        return result


def get_init_data(data, tarif_info, ignore_keys):
    return [{k: row[k] for k in row if k not in ignore_keys and k not in tarif_info} for row in data]


def groupby_level(data, level, levels, inner_info):
    extract_keygroup = lambda dct: tuple(dct[k] for k in levels if levels.index(k) <= levels.index(level))
    if level == "NumCadran":
        groupby_iter = itertools.groupby(data, extract_keygroup)
    else:
        inner_level = levels[levels.index(level) + 1]
        groupby_iter = itertools.groupby(groupby_level(data, inner_level, levels, inner_info), extract_keygroup)

    wrapper = [
        {
            **{l: t[levels.index(l)] for l in levels if levels.index(l) <= levels.index(level) and l != "NumCadran"},
            "Detail" + level: [{k: d[k] for k in d if k in inner_info or k.startswith("Detail") or levels.index(k) > levels.index(level)} for d in list(v)],
        }
        for t, v in groupby_iter
    ]
    return wrapper


def get_tarif_data(data, levels, tarif_info):
    flat_tarif_data = [{k: row[k] for k in row if k in levels or k in tarif_info} for row in data]
    tarifs_data_iter = itertools.groupby(flat_tarif_data, lambda dct: (dct["Contrat"], dct["DateRel"]))
    grouped_tarif_data = {t: {"Tarif": [{k: d[k] if k != "CatTarif" else d[k].split("_")[-1] for k in tarif_info} for d in v]} for t, v in tarifs_data_iter}
    return grouped_tarif_data


def join_tarif_data(data, tarif_data):
    join_tarif_data = []
    for contrat in data:
        contrat_with_tarif = {"NomContrat": contrat["NomContrat"], "DetailContrat": []}
        for releve in contrat["DetailContrat"]:
            compteur_info = {
                "DateRel": releve["DateRel"],
                "DetailCpt": releve["DetailCpt"],
            }
            relevant_tarifs = tarif_data[(contrat["NomContrat"], releve["DateRel"])]
            all_contrat_info = {**compteur_info, **relevant_tarifs}
            contrat_with_tarif["DetailContrat"].append(all_contrat_info)
        join_tarif_data.append(contrat_with_tarif)
    return join_tarif_data


def get_consumption_history(result):
    base_consumption_data = [
        {
            "Datecons": int(str(row["DateRel"])[0:6]),
            "QttAnn": row["QttAnn"],
            "DetailConsommation": {
                "CatCadran": row["CatCadran"],
                "Cadran": row["Cadran"],
            },
        }
        for row in result
        if row["DateQttAnn"] and int(str(row["DateRel"])[0:4]) >= (datetime.today().year - 5)
    ]

    grouped_consumption_data_iter = itertools.groupby(base_consumption_data, lambda dct: dct["Datecons"])

    grouped_consumption_data = []
    for k, v in grouped_consumption_data_iter:
        data_point = list(v)
        grouped_consumption_data.append(
            {
                "Datecons": k,
                "QttAnn": str(round(sum([row["QttAnn"] for row in data_point]), 2)),
                "DetailConsommation": [
                    {
                        "QttCadran": str(round(row["QttAnn"], 2)),
                        "CatCadran": row["DetailConsommation"]["CatCadran"],
                        "Cadran": row["DetailConsommation"]["Cadran"],
                    }
                    for row in data_point
                ],
            },
        )

    return grouped_consumption_data


def process_result(result):
    levels = ["Contrat", "DateRel", "NumCompteur", "NumCadran"]
    inner_info = ["CatCadran", "Cadran", "Index", "IndexUnit", "IndexQual"]
    tarif_info = ["CatTarif", "Cadran", "Qtt", "QttUnit"]
    ignore_keys = [
        "Ean",
        "FirstName",
        "LastName",
        "CurrentFirstName",
        "CurrentLastName",
        "DateQttAnn",
        "QttAnn",
        "CodeCadran",
        "ETDZ_ZWNUMMER",
        "MATNR",
        "EQUI_EQUNR",
        "SERNR",
        "lv_nb_lines",
        "ANZART",
        "ZWNUMMER",
        "EQUNR",
        "ATWRT",
        "OBJEK",
        "Smart",
    ]

    flat_releve_data = get_init_data(result, ["CatTarif", "Qtt", "QttUnit"], ignore_keys)
    releve_history = groupby_level(flat_releve_data, "Contrat", levels, inner_info)
    rename_keys = {
        "Contrat": "NomContrat",
        "DetailDateRel": "DetailCpt",
        "DetailNumCompteur": "Compteur",
        "DetailNumCadran": "Cadran",
    }
    clean_releve_history = rename_nested_keys(releve_history, rename_keys)

    tarif_history = get_tarif_data(result, levels, tarif_info)

    consumption_history = get_consumption_history(result)

    releve_with_tarifs = join_tarif_data(clean_releve_history, tarif_history)

    return {"Contrat": releve_with_tarifs, "Consommation": consumption_history}


def row_processor(cursor, row):
    """
    Return the row as key-value dictionnary
    """
    resource = {}
    for i in range(len(cursor.description)):
        column = cursor.description[i][0]
        resource[snake_to_pascal(column)] = row[i]
    return resource


def get_input_type(query_string_params):
    if get(query_string_params, "Ean") and not get(query_string_params, "NumCompteur"):
        return InputType.EAN
    elif get(query_string_params, "Ean") and get(query_string_params, "NumCompteur"):
        return InputType.EAN_CPT
    else:
        return None
