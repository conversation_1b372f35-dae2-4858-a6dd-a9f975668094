from datetime import date

from controllers.parallel_controller import <PERSON>Worker, ParallelController_Deprecated
from dateutil.relativedelta import relativedelta

from utils.aws_utils import get_secret
from utils.dict_utils import get
from utils.errors import BadRequestError, NotFoundError
from utils.string_utils import prettify_int
from utils.time_utils import add_business_days, last_day_of_month
from utils.validation import is_number


class ws33(ParallelController_Deprecated):
    def __init__(self, event, linking):
        super().__init__(event, linking)

    def validate_request_params(self):
        """
        Validates request parameters and returns their string representation
        """
        query_string_params = get(self.event, "queryStringParameters", default={})
        if "Ean" not in query_string_params and "NumCompteur" not in query_string_params:
            raise BadRequestError("Les possibles inputs sont [<PERSON><PERSON>] ou bien [NumCompteur] ou bien [<PERSON><PERSON>, <PERSON>um<PERSON>ompteur]")
        if "NumCompteur" in query_string_params:
            cpt = query_string_params["NumCompteur"]
            query_string_params["NumCompteur"] = cpt.zfill(18) if is_number(cpt) else cpt
        return super().validate_request_params()

    def execute_query(self, sql_statement, request_params):
        # Parameters for all queries
        db_credentials = get_secret(super().get_secret())
        result_dict = {}
        inner_fetcher = self.fetch
        input_type = get_input_type(self.event["queryStringParameters"])

        # Queries that depend on input type
        core_sql_statement = "get_installation_from_ean" if input_type == "Ean" else "get_ean_from_counter"
        core_params = {"Ean": request_params["Ean"]} if input_type == "Ean" else {"NumCompteur": request_params["NumCompteur"]}
        core_query = DatabaseWorker(
            core_sql_statement,
            db_credentials,
            sql_statement[core_sql_statement],
            core_params,
            inner_fetcher,
            result_dict,
        )

        core_query.start()
        core_query.join()
        core_result = result_dict.get(core_sql_statement)

        # Compatibilite de Ean et Compteurs
        if input_type == "EanCpt" and isinstance(core_result, list):
            core_result_list = [record for record in core_result if str(record["EAN"]) == str(request_params["Ean"])]
            core_result = core_result_list[0] if core_result_list else None
            if not core_result or str(core_result["EAN"]) != str(request_params["Ean"]):
                raise BadRequestError("Le NumCompteur fourni ne correspond pas à l'Ean fourni")
        if input_type != "EanCpt" and isinstance(core_result, list) and core_result:
            ean_list = ",".join([str(record["EAN"]) for record in core_result])
            raise BadRequestError("Le compteur fourni est associé à plusieurs Ean: " + ean_list + " Spécifier un seul Ean")

        # Autres conditions
        if not core_result:
            raise (NotFoundError("EAN non trouvé") if input_type != "NumCompteur" else NotFoundError("NumCompteur non trouvé"))
        if get(core_result, "AKLASSE", "").strip() not in ["YMMR", "MMMR"]:
            raise BadRequestError("Installation n'est ni mensuelle ni annuelle")

        # Points d entree
        installation = str(core_result["ANLAGE"])
        type_tarif = core_result["TARIFTYP"].strip() if "TARIFTYP" in core_result else BadRequestError("Le type d'énérgie n'est ni éléc ni gaz")
        ableinh = str(core_result["ABLEINH"])

        result = from_core_result(
            type_tarif,
            ableinh,
            installation,
            input_type,
            db_credentials,
            sql_statement,
            inner_fetcher,
            result_dict,
            core_result,
        )

        return result


# This functions is also used from WS27.
def from_core_result(
    type_tarif,
    ableinh,
    installation,
    input_type,
    db_credentials,
    sql_statement,
    inner_fetcher,
    result_dict,
    core_result,
):
    energy_type = type_tarif[0]

    # Second batch of links
    independent_queries = [
        DatabaseWorker(
            "get_periodes_passage_" + installation,
            db_credentials,
            sql_statement["get_periodes_passage"],
            {"TERMSCHL": ableinh},
            inner_fetcher,
            result_dict,
        ),
        DatabaseWorker(
            "get_installation_property_" + installation,
            db_credentials,
            sql_statement["get_installation_property"],
            {"ANLAGE": installation, "OPERAND": (energy_type + "_STAT_EAN")},
            inner_fetcher,
            result_dict,
        ),
    ]

    if input_type in ["Ean", "ws27"]:
        independent_queries.append(
            DatabaseWorker(
                "get_counters_from_installation_" + installation,
                db_credentials,
                sql_statement["get_counters_from_installation"],
                {"ANLAGE": installation, "IncludeControlCpt": 1},
                inner_fetcher,
                result_dict,
            ),
        )
    if input_type != "Ean":
        independent_queries.append(
            DatabaseWorker(
                "get_periodes_encodage_" + installation,
                db_credentials,
                sql_statement["get_periodes_encodage"],
                {"ABLEINH": ableinh},
                inner_fetcher,
                result_dict,
            ),
        )

    for t in independent_queries:
        t.start()

    for t in independent_queries:
        t.join()

    result = join_partial_results(result_dict, type_tarif, energy_type, input_type, installation, core_result)

    return result


def get_input_type(query_string_params):
    if "Ean" in query_string_params and "NumCompteur" not in query_string_params:
        return "Ean"
    if "NumCompteur" in query_string_params and "Ean" not in query_string_params:
        return "Cpt"
    # Inoput already validated, at least one of the inputs must be provided
    else:
        return "EanCpt"


def join_partial_results(partial_results, type_tarif, energy_type, input_type, anlage, core_result):
    if get(get(partial_results, "get_installation_property_" + anlage), "STRING1") == "DETRUIT":
        raise BadRequestError("Cet EAN ne peut pas être relevé car son statut est DETRUIT")

    statut_ean = get(partial_results.get("get_installation_property_" + anlage), "STRING1")
    counter_info = partial_results.get("get_counters_from_installation_" + anlage) if input_type in ["Ean", "ws27"] else core_result
    passage_periods = partial_results.get("get_periodes_passage_" + anlage)
    encodage_periods = partial_results.get("get_periodes_encodage_" + anlage) if input_type != "Ean" else core_result

    is_compteur_smart = False
    is_compteur_smart_comm = False
    compteurs = []
    if isinstance(counter_info, dict):
        is_compteur_smart = counter_info["IS_SMART"]
        is_compteur_smart_comm = counter_info["IS_SMART_COMM"]
        if input_type == "ws27":
            compteurs.append(prettify_int(counter_info["SERNR"]))
    if isinstance(counter_info, list):
        is_compteur_smart = True in [counter["IS_SMART"] for counter in counter_info]
        is_compteur_smart_comm = True in [counter["IS_SMART_COMM"] for counter in counter_info]
        if input_type == "ws27":
            compteurs = [prettify_int(counter["SERNR"]) for counter in counter_info]

    next_passage_period = get_next_passage_period(passage_periods, type_tarif, is_compteur_smart_comm)
    if get(core_result, "AKLASSE", "").strip() not in ["YMMR", "MMMR"]:
        all_periods = []
    else:
        all_periods = add_encodage_period(encodage_periods, next_passage_period, type_tarif)

    # All info into result dict
    result = {k: int(all_periods[k].strftime("%Y%m%d")) if all_periods[k] else "" for k in all_periods}
    result["CptSmart"] = bool(is_compteur_smart)
    result["StatutEan"] = statut_ean
    if input_type == "ws27":
        compteurs_result = {"NumCpt" + ("" if i == 0 else str(i + 1)): compteurs[i] for i in range(len(compteurs))}
        result = {**result, **compteurs_result}

    return order_keys(result)


def get_next_passage_period(passage_periods, type_tarif, is_compteur_smart_comm):
    if not passage_periods:
        return {}

    adatsoll_str = passage_periods["ADATSOLL"].strip()
    adatsoll = date(int(adatsoll_str[0:4]), int(adatsoll_str[4:6]), int(adatsoll_str[6:8]))
    zuorddat_str = passage_periods["ZUORDDAT"].strip()
    zuorddat = date(int(zuorddat_str[0:4]), int(zuorddat_str[4:6]), int(zuorddat_str[6:8]))
    last_day_of_zuordat_m = last_day_of_month(zuorddat)

    if is_compteur_smart_comm:
        date_fin = date_debut = adatsoll
    elif type_tarif in ["EM", "GM"]:
        date_fin = add_business_days(last_day_of_zuordat_m, 5)
        date_debut = add_business_days(zuorddat, -7)
    else:
        date_fin = add_business_days(last_day_of_zuordat_m, -7)
        date_debut = date(adatsoll.year, zuorddat.month, 1)

    return {"DateDebut": date_debut, "DateFin": date_fin}


def add_encodage_period(encodage_periods, next_passage_period, type_tarif):
    if not next_passage_period or not encodage_periods:
        return {
            "DateDebut": None,
            "DateFin": None,
            "DtLimiDebut": None,
            "DtLimiFin": None,
        }

    portion = encodage_periods["PORTION"].strip()

    if type_tarif in ["EM", "GM"]:
        next_passage_period["DtLimiDebut"] = next_passage_period["DateDebut"]
        next_passage_period["DtLimiFin"] = next_passage_period["DateFin"]
    else:
        date_aux = date(next_passage_period["DateDebut"].year, int(portion[2:4]), 1)
        next_passage_period["DtLimiDebut"] = add_business_days(date_aux, -10)
        next_passage_period["DtLimiFin"] = add_business_days(date_aux + relativedelta(date_aux, months=1), 10)

    return next_passage_period


def order_keys(result):
    attribute_order = [
        "StatutEan",
        "DateDebut",
        "DateFin",
        "DtLimiDebut",
        "DtLimiFin",
        "CptSmart",
    ]

    ordered_result = {k: result[k] for k in attribute_order if k in result and k != "Compteurs"}
    missing_attributes = [k for k in result if k not in ordered_result]
    for k in missing_attributes:
        ordered_result[k] = result[k]

    return ordered_result
