from controllers.CommuneController import CommuneController
from controllers.parallel_controller import DatabaseWorker
from utils.aws_utils import get_resource_key, get_secret
from utils.ean_utils import get_code_cadran
from utils.errors import NotFoundError


class ws132(CommuneController):
    inflate = False

    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.ean = None
        self.result_dict = {}
        self.eans_thread = DatabaseWorker(
            "eans",
            get_secret(self.get_secret()),
            self.apply_hana_env(self.load_sql_file(get_resource_key(self.linking["sql_template"]["get_eans"]))),
            {"IdCommune": self.commune_id, "Smart": None},
            self.fetch,
            self.result_dict,
        )

    def sql_file(self):
        """
        return the sql filename to load
        """
        return get_resource_key(self.linking["sql_template"]["get_historique"])

    def validate_request_params(self):
        params = super().validate_request_params()
        self.ean = params["Ean"]
        self.event["queryStringParameters"] = {"Ean": self.ean}
        return params

    def execute_query(self, sql_statement, request_params):
        self.eans_thread.start()
        return super().execute_query(sql_statement, request_params)

    def to_response(self, cursor, params=None):
        self.eans_thread.join()
        eans = {item["Ean"] for item in self.result_dict["eans"]}
        if self.ean not in eans:
            raise NotFoundError

        result = super().to_response(cursor, params)
        return [
            {
                "Date": f"{item['DateRel'][:4]}-{item['DateRel'][4:6]}-{item['DateRel'][6:]}",
                "Index": item["Index"],
                "Unite": item["IndexUnit"],
                "NumCpt": item["NumCompteur"].lstrip("0"),
                "Cadran": get_code_cadran(item),
            }
            for item in result
        ]
