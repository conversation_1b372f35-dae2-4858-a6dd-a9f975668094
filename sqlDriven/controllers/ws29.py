import json
import os
from math import atan, cos, floor

from controllers.Controller import validate_type_int
from controllers.parallel_controller import <PERSON>Wor<PERSON>, ParallelController_Deprecated

from utils.aws_utils import get_resource_key, get_secret, load_s3_file
from utils.dict_utils import filter_keys, first, get
from utils.errors import (
    INVALID_EAN_FOR_USER,
    BadRequestError,
    ForbiddenError,
    NotFoundError,
    UnauthorizedError,
)
from utils.log_utils import log_info
from utils.models.user import User
from utils.string_utils import ifnempty, ifpreffix, ifstrip, strOrNone
from utils.type_utils import int_format, int_or_none
from utils.u_codes import UCode


class ws29(ParallelController_Deprecated):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.auth = False

    def validate_request_params(self):
        input_params = super().validate_request_params()

        try:
            user = User.from_event(self.event, allow_ghost=False)
            self.auth = True
            if input_params.get("Ean"):
                if str(input_params.get("Ean")) not in user.ean_ids:
                    raise INVALID_EAN_FOR_USER
        except UnauthorizedError:
            self.auth = False
            if not input_params.get("NumCpt"):
                raise ForbiddenError("Missing required request parameters: [NumCpt]")

        return input_params

    def load_single_sql_file(self, file_key):
        result = load_s3_file(
            os.environ["MAPPING_BUCKET_NAME"],
            get_resource_key(self.linking["sql_template"][file_key]),
        )
        return result

    def load_sql_file(self, filename_dict):
        # Load separately protection query because it cannot be paramterized in the same way. The HANA cursor
        # does not accept parameterized arguments
        standard_filename_dict = {k: filename_dict[k] for k in filename_dict if k != "get_protection_info"}
        result = super().load_sql_file(standard_filename_dict)
        result["get_protection_info"] = self.load_single_sql_file("get_protection_info")
        return result

    def execute_query(self, sql_statement, request_params):
        # Parameters for all queries
        db_credentials = get_secret(super().get_secret())
        result_dict = {}
        inner_fetcher = self.fetch

        # From EAN to energy type, installation number and consumption point
        core_query = DatabaseWorker(
            "get_energy_type",
            db_credentials,
            sql_statement["get_energy_type"],
            {"Ean": request_params["Ean"]},
            inner_fetcher,
            result_dict,
        )
        core_query.start()
        core_query.join()
        core_result = result_dict["get_energy_type"]
        if isinstance(core_result, list):
            core_result = first(core_result)
        if not core_result:
            raise NotFoundError("EAN non trouvé")
        energy_type = "E" if core_result["SPARTE"] == "01" else "G"

        # From existing inputs to all outputs excepting counter characteristics and gas related attributes
        independent_queries = [
            DatabaseWorker(
                "get_installation_properties",
                db_credentials,
                sql_statement["get_installation_properties"],
                {"ANLAGE": core_result["ANLAGE"]},
                inner_fetcher,
                result_dict,
            ),
            DatabaseWorker(
                "get_energy_measures",
                db_credentials,
                sql_statement["get_energy_measures"],
                {"ANLAGE": core_result["ANLAGE"]},
                inner_fetcher,
                result_dict,
            ),
            DatabaseWorker(
                "get_business_info",
                db_credentials,
                sql_statement["get_business_info"],
                {"ANLAGE": core_result["ANLAGE"]},
                inner_fetcher,
                result_dict,
            ),
        ]

        for t in independent_queries:
            t.start()

        first_order_queries = [
            DatabaseWorker(
                "get_counter_info",
                db_credentials,
                sql_statement["get_counter_info"],
                {"ANLAGE": core_result["ANLAGE"], "IncludeControlCpt": 1},
                self.fetch,
                result_dict,
            ),
            DatabaseWorker(
                "get_adress_info",
                db_credentials,
                sql_statement["get_adress_info"],
                {"VSTELLE": core_result["VSTELLE"]},
                inner_fetcher,
                result_dict,
            ),
            DatabaseWorker(
                "get_hgz_info",
                db_credentials,
                sql_statement["get_hgz_info"],
                {"Ean": request_params["Ean"]},
                inner_fetcher,
                result_dict,
            ),
            DatabaseWorker(
                "get_label_horaire",
                db_credentials,
                sql_statement["get_label_horaire"],
                {},
                inner_fetcher,
                result_dict,
            ),
        ]

        for t in first_order_queries:
            t.start()

        for t in first_order_queries:
            t.join()

        # Counter characteristics and gas related attributes
        counters_aux = get(result_dict, "get_counter_info", {})
        counters = counters_aux if isinstance(counters_aux, list) else [counters_aux]
        result_dict["get_counter_info"] = counters
        counter_ids = [row["SERNR"].zfill(18) for row in counters]
        if not self.auth and request_params["NumCpt"].zfill(18) not in counter_ids and not self.auth:
            raise BadRequestError("L'EAN n' est pas associé au compteur fourni")
        counter_threads = [self.get_counter_characteristics(db_credentials, sql_statement, result_dict, energy_type, counter) for counter in counters]
        flat_counter_threads = [t for threads in counter_threads for t in threads]

        other_threads = [
            DatabaseWorker(
                "get_liaison_info",
                db_credentials,
                sql_statement["get_liaison_info_" + energy_type],
                {
                    "SPARTE_LIT": energy_type,
                    "TPLNR": result_dict["get_adress_info"]["TPLNR"],
                },
                self.fetch,
                result_dict,
            ),
        ]

        if energy_type == "E":
            other_threads.extend(
                (
                    DatabaseWorker(
                        "get_characteristic_installation",
                        db_credentials,
                        sql_statement["get_characteristic_by_names_list"],
                        {
                            "OBJEK": core_result["ETINS_EQUNR"],
                            "ATNAM": "IE_IT_TYPE_CLIENT",
                        },
                        self.fetch,
                        result_dict,
                    ),
                    DatabaseWorker(
                        "get_characteristic_meter",
                        db_credentials,
                        sql_statement["get_characteristic_by_names_list"],
                        {
                            "OBJEK": result_dict["get_counter_info"][0]["EGERH_EQUNR"],
                            "ATNAM": "IE_IT_TYPE_CLIENT",
                        },
                        self.fetch,
                        result_dict,
                    ),
                ),
            )

        second_order_queries = flat_counter_threads + other_threads
        for t in second_order_queries:
            t.start()

        for t in second_order_queries:
            t.join()

        if energy_type == "E":
            counter_eqs = [get(row, "EQUNR", "").zfill(18) for row in counters]
            liste_equnr_txt = ",".join(["'" + counter_eq + "'" for counter_eq in counter_eqs])
            env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
            prt_query = sql_statement["get_protection_info"].format(HanaSchema=env_file["HANA_SCHEMA"], ListeEquipements=liste_equnr_txt)
            log_info(prt_query)
            third_order_query = [
                DatabaseWorker(
                    "IE_OL_TYPE_RACC",
                    db_credentials,
                    sql_statement["get_characteristic_by_name"],
                    {
                        "OBJEK": get(result_dict["get_liaison_info"], "VINET_TPLNR"),
                        "ATNAM": "IE_OL_TYPE_RACC",
                    },
                    self.fetch,
                    result_dict,
                ),
                DatabaseWorker(
                    "PRT_characteristics",
                    db_credentials,
                    prt_query,
                    {"LISTE_EQUI_CPT": liste_equnr_txt},
                    self.fetch,
                    result_dict,
                ),
            ]

            for t in third_order_query:
                t.start()

            for t in third_order_query:
                t.join()

            for t in independent_queries:
                t.join()

        result = self.join_partial_results(result_dict)

        if not self.auth:
            keys_to_keep = (
                "SectActivite",
                "PContPrel",
                "ReglPrel",
                "NbPhases",
                "NbPhasesLibelle",
                "UCodeLibelle",
                "Compteurs",
                "CptControle",
                "NumCpt",
                "NRegistres",
                "PTotProd",
                "GrpTarif",
                "GrpTarifLibelle",
                "ReglInje",
            )
            result = filter_keys(result, keep_keys=keys_to_keep)
        return result

    def get_counter_characteristics(self, db_credentials, sql_statement, result_dict, energy_type, counter):
        characteristics_threads = []
        counter_characteristics = DatabaseWorker(
            "get_counter_characteristics_" + str(get(counter, "EGERH_EQUNR")),
            db_credentials,
            sql_statement["get_characteristic_by_names_list"],
            {"OBJEK": get(counter, "EGERH_EQUNR")},
            self.fetch,
            result_dict,
        )
        characteristics_threads.append(counter_characteristics)

        if energy_type == "G":
            get_ig_ta_cap_nom = DatabaseWorker(
                "IG_TA_CAP_NOM",
                db_credentials,
                sql_statement["get_direct_characteristic"],
                {"OBJEK": get(counter, "MATNR"), "ATNAM": "IG_TA_CAP_NOM"},
                self.fetch,
                result_dict,
            )
            characteristics_threads.append(get_ig_ta_cap_nom)

        return characteristics_threads

    def join_partial_results(self, partial_results):
        print("partial_results")
        print(partial_results)

        core_result = self.get_core_result(partial_results)
        energy_type = "E" if core_result["SectActivite"] == "01" else "G"

        counters_count = self.get_counters_count(partial_results, energy_type)
        energy_and_power = self.process_installation_operands(energy_type, partial_results["get_installation_properties"])

        result = {**core_result, **counters_count, **energy_and_power}

        # Remove switch hour for meter without switching
        if result.get("Ucode") in (
            "U00",
            "U10",
            "U13",
            "U26",
            "U30",
            "U31",
            "U33",
            "Gaz",
        ):
            result.pop("HeureSwJour", None)
            result.pop("HeureSwNuit", None)

        return self.order_keys(result)

    def get_core_result(self, partial_results):
        energy_types = partial_results["get_energy_type"]
        adress_info = partial_results["get_adress_info"]
        business_info = get(partial_results, "get_business_info")
        hgz_info = partial_results["get_hgz_info"]
        energy_measures = get(partial_results, "get_energy_measures", {})

        if not business_info:
            raise NotFoundError("Pas de contrat actif pour cet EAN")

        core_result = {
            "Langue": ifstrip(get(business_info, "LANGU_CORR")),
            "SectActivite": energy_types["SPARTE"] if isinstance(energy_types, dict) else energy_types[0]["SPARTE"],
            "Contrat": int_or_none(get(business_info, "VERTRAG")),
            "Mi": int_or_none(get(business_info, "EINZDAT")),
            "Mo": int_or_none(get(business_info, "AUSZDAT")),
            "Email": None,
            "Tel": None,
            "PartenaireId": int_or_none(get(business_info, "GPART")),
            "BpHgz": int_or_none(get(hgz_info, "GPART")),
            "CcHgz": int_or_none(get(hgz_info, "VKONT")),
            "Bp": None,
            "BpAddress": " ".join((ifstrip(get(business_info, "PARTNER_STREET", "")) + " " + ifstrip(get(business_info, "PARTNER_HOUSE_NUM1", ""))).split()),
            "BpLocalite": ifstrip(get(business_info, "PARTNER_CITY1")),
            "BpCdpostal": validate_type_int(ifstrip(get(business_info, "PARTNER_POSTCODE_1")), "BpCdpostal"),
            "OrAddress": " ".join((ifstrip(get(adress_info, "INSTALL_STREET", "")) + " " + ifstrip(get(adress_info, "INSTALL_HOUSENUM_1", ""))).split()),
            "OrLocalite": ifstrip(get(adress_info, "INSTALL_CITY1")),
            "OrCdpostal": validate_type_int(ifstrip(get(adress_info, "INSTALL_POSTCODE_1")), "OrCdpostal"),
            "OrNumCmpt": ifnempty(
                " ".join(
                    (
                        ifstrip(get(adress_info, "HAUS_NUM2", ""))
                        + " "
                        + ifpreffix(ifstrip(get(adress_info, "FLOOR", "")), "Etage")
                        + " "
                        + ifstrip(get(adress_info, "ROOMNUMBER", ""))
                    ).split(),
                ),
            ),
            "OrIdRadRue": ifstrip(get(adress_info, "IDRAD_RUE")),
            "OrIdRadLocalite": ifstrip(get(adress_info, "IDRAD_LOCALITE")),
            "Nace": int_format(get(business_info, "NACE")),
            "Frns": ifstrip(get(business_info, "INVOICING_PARTY")),
            "FrnsLibelle": ifstrip(get(business_info, "SP_NAME")),
            "TypInstln": ifstrip(get(energy_types, "ANLART")),
            "Frequence": ifstrip(get(energy_measures, "AKLASSE")),
            "Slp": ifstrip(get(energy_measures, "TEMP_AREA")),
            "CodeCommune": get(adress_info, "DESCRIPT"),
            "Usage": get(business_info, "CATEG"),
        }

        return core_result

    @staticmethod
    def get_business_partner_name(business_info):
        if str(business_info["PARTNER_TYPE"]) == "1":
            phrase = business_info["NAME_FIRST"].strip() + " " + business_info["NAME_LAST"].strip()
            bp = " ".join(phrase.split())
        elif str(business_info["PARTNER_TYPE"]) == "2":
            phrase = (
                business_info["NAME_ORG1"].strip() + " " + business_info["NAME_ORG2"].strip() + " " + business_info["NAME_ORG3"].strip() + " " + business_info["NAME_ORG4"].strip()
            )
            bp = " ".join(phrase.split())
        elif str(business_info["PARTNER_TYPE"]) == "3":
            phrase = business_info["NAME_GRP1"].strip() + " " + business_info["NAME_GRP2"].strip()
            bp = " ".join(phrase.split())
        else:
            bn = None

        return bp

    def get_counters_count(self, partial_results, energy_type):
        n_billing_act = [0, 0, 0]
        n_billing_inact = [0, 0, 0]
        counter_info = partial_results["get_counter_info"]
        counter_characteristics_results = [partial_results[key] for key in partial_results if "get_counter_characteristics_" in key and partial_results[key] is not None]
        counter_characteristics = [row for table in counter_characteristics_results for row in table]
        counter_status_list = [row for row in counter_characteristics if get(row, "ATNAM", "") == "IC_CPT_STATUT_GRD"]

        for counter_attrib in counter_status_list:
            # Get the nombre de registres billing relevants pour les compteurs actifs
            nb_registres_act_aux = [
                counter["N_BILLING"]
                for counter in counter_info
                if get(counter_attrib, "INPUT_OBJEK") == get(counter, "EGERH_EQUNR") and get(counter_attrib, "ATWRT") == "O" and counter["N_BILLING"]
            ]
            nb_registres_act = nb_registres_act_aux[0] if nb_registres_act_aux else -1
            billing_index_act = 2 if nb_registres_act > 2 else (nb_registres_act - 1)
            if billing_index_act >= 0:
                n_billing_act[billing_index_act] = n_billing_act[billing_index_act] + 1
            nb_registres_inact_aux = [
                counter["N_BILLING"]
                for counter in counter_info
                if get(counter_attrib, "INPUT_OBJEK") == get(counter, "EGERH_EQUNR") and get(counter_attrib, "ATWRT") != "O" and counter["N_BILLING"]
            ]
            nb_registres_inact = nb_registres_inact_aux[0] if nb_registres_inact_aux else -1
            billing_index_inact = 2 if nb_registres_inact > 2 else (nb_registres_inact - 1)
            if billing_index_inact >= 0:
                n_billing_inact[billing_index_inact] = n_billing_inact[billing_index_inact] + 1

        result = {
            "ComptActi1Re": str(n_billing_act[0]),
            "TotalComptAct": str(sum(n_billing_act)),
            "ComptInac1Re": str(n_billing_inact[0]),
            "TotalComptInac": str(sum(n_billing_inact)),
        }

        def get_counter_result(counter):
            counter_type_cab = [row for row in counter_characteristics if get(row, "ATNAM", "") == "IC_CPT_STATUT_CAB" and get(row, "INPUT_OBJEK") == get(counter, "EGERH_EQUNR")]
            elec_only = {}
            if energy_type == "E":
                fonct_compt = [row for row in counter_characteristics if get(row, "ATNAM", "") == "IE_CPT_FONCTION" and get(row, "INPUT_OBJEK") == get(counter, "EGERH_EQUNR")]
                log_info(fonct_compt)
                elec_only["FctCompt"] = get(fonct_compt[0], "ATWTB") if fonct_compt else None
                elec_only["ReglPrel"] = counter["IE_PRT_REGL_PREL"]
                elec_only["ReglInje"] = counter["IE_PRT_REGL_INJE"]

            all_energies = {
                "NumCpt": counter["SERNR"],
                "CptCab": counter["EQART"] == "IE_CAB",
                "CptControle": counter["CPT_CONTROL"],
                "CptSmart": str(counter["CAP_ACT_GRP"]) in ["9000", "9001", "9002"],
                "CptPrepaiement": (get(counter_type_cab[0], "ATWRT") == "A" if counter_type_cab else False),
                "NRegistres": counter["N_BILLING"],
            }

            result_counter = {**all_energies, **elec_only}

            return result_counter

        result["Compteurs"] = [get_counter_result(counter) for counter in counter_info]

        if energy_type == "G":
            liaison_info = partial_results["get_liaison_info"]
            result["VanneExte"] = "Oui" if get(liaison_info, "HAS_VANNE_EXT", 0) > 0 else "Non"
            result["PressionCompt"] = str(get(counter_info[0], "MESSDRCK")) if counter_info else None
            ig_ta_cap_nom = get(partial_results, "IG_TA_CAP_NOM")
            result["ComptCali"] = ifstrip(get(ig_ta_cap_nom, "ATWRT"))
        if energy_type == "E":
            # region DMC-791 : Set empty ReglPrel / ReglInje with the normal meter one
            regl_prel = None
            regl_inje = None

            # get ReglPrel / ReglInje from first possible meter
            for cpt in result["Compteurs"]:
                if cpt["ReglPrel"]:
                    regl_prel = cpt["ReglPrel"]
                if cpt["ReglInje"]:
                    regl_inje = cpt["ReglInje"]
                if regl_prel and regl_inje:
                    break

            # set ReglPrel / ReglInje to empty meters
            for cpt in result["Compteurs"]:
                if not cpt["ReglPrel"]:
                    cpt["ReglPrel"] = regl_prel
                if not cpt["ReglInje"]:
                    cpt["ReglInje"] = regl_inje
            # endregion

            result["TypRacc"] = get(get(partial_results, "IE_OL_TYPE_RACC", {}), "ATWRT", None)
            result["ComptActi2Re"] = str(n_billing_act[1])
            result["ComptActi2PlusRe"] = str(n_billing_act[2])
            result["ComptInac2Re"] = str(n_billing_inact[1])
            result["ComptInac2PlusRe"] = str(n_billing_inact[2])
            prop_names = {
                "IE_IT_RECP_PROG_1": "ATWRT",
                "IE_PRT_TENSION": "ATFLV",
                "IE_PRT_NB_PHASES": "ATWTB",
                "IE_PRT_NB_PHASES_CODE": "ATWRT",
                "IE_IT_TYPE_CLIENT": "ATWRT",
                "IE_CPT_SMART_CALENDAR": "ATWTB2",
            }
            install_props = get(partial_results, "get_characteristic_installation", [])
            install_props_list = install_props if isinstance(install_props, list) else [install_props]
            install_props_values = {entry["ATNAM"]: entry[prop_names[entry["ATNAM"]]] for entry in install_props_list if entry["ATNAM"] in prop_names}
            meter_props_values = {
                entry["ATNAM"]: entry[prop_names[entry["ATNAM"]]]
                for entry in get(
                    partial_results,
                    "get_characteristic_meter",
                    [],
                    default_on_empty=True,
                )
                if entry["ATNAM"] in prop_names
            }
            result["GrpTarif"] = get(install_props_values, "IE_IT_TYPE_CLIENT", None)
            grp_tarif_map = {
                "BT": "Basse tension",
                "TBT": "Trans-Basse tension",
                "MT": "Moyenne tension",
                "TMT": "Trans-Moyenne tension",
                "HT": "Haute tension",
            }
            result["GrpTarifLibelle"] = get(grp_tarif_map, result["GrpTarif"], "")
            result["HoraireTarif"] = get(meter_props_values, "IE_CPT_SMART_CALENDAR", None) or get(install_props_values, "IE_IT_RECP_PROG_1", None)
            label_horaire = [row for row in partial_results["get_label_horaire"] if row["CodeHoraire"] == (result["HoraireTarif"] if result["HoraireTarif"] != "06" else "6")]
            label_horaire = label_horaire[0] if label_horaire else {}
            heure_sw_jour = get(label_horaire, "HeureSwJour", default_on_empty=True)
            if heure_sw_jour:
                result["HeureSwJour"] = f"{heure_sw_jour[:2]}:{heure_sw_jour[2:]}"

            heure_sw_nuit = get(label_horaire, "HeureSwNuit", default_on_empty=True)
            if heure_sw_nuit:
                result["HeureSwNuit"] = f"{heure_sw_nuit[:2]}:{heure_sw_nuit[2:]}"

            prt_info = get(partial_results, "PRT_characteristics", [])
            result["UMesu"] = get(prt_info, "IE_PRT_NB_PHASES_CODE", "") + "N" + str(get(prt_info, "IE_PRT_TENSION", "") or "")
            result["NbPhases"] = get(prt_info, "IE_PRT_NB_PHASES", None)
            result["ReglPrel"] = get(prt_info, "IE_PRT_REGL_PREL", "") if result["GrpTarif"] == "BT" else None
            result["ReglInje"] = get(prt_info, "IE_PRT_REGL_INJE", "") if result["GrpTarif"] == "BT" else None

            # map NbPhases NbPhasesLibelle
            nb_phases_map = {
                "3": "TRI",
                "3N": "TRI_N",
                "1N": "MONO_N",
                "2": "MONO",
            }
            result["NbPhasesLibelle"] = get(nb_phases_map, result["NbPhases"])

        return result

    def process_installation_operands(self, energy_type, operands):
        result = {}

        literal_operands = [
            energy_type + "_STAT_EAN",
            energy_type + "_UCODE",
            energy_type + "_GRPTARIF",
        ]

        install_properties = {}
        for row in operands:
            if row["OPERAND"] in literal_operands:
                install_properties[row["OPERAND"]] = row["STRING1"]
            else:
                install_properties[row["OPERAND"]] = row["WERT1"]

        u_code = install_properties[energy_type + "_UCODE"]
        u_code_info = UCode(u_code if energy_type == "E" else "Gaz")

        result["Ucode"] = u_code
        result["UCodeLibelle"] = u_code_info.desc
        result["StatutEan"] = ifstrip(get(install_properties, energy_type + "_STAT_EAN"))
        result["CodeTarif"] = int_or_none(ifstrip(get(install_properties, energy_type + "_GRPTARIF")))
        energy_operands = u_code_info.operands
        energy_values = {entry["output"]: round(float(get(install_properties, entry["input"], 0.00)), 2) for entry in energy_operands}
        energy_values["EnergieTotAct"] = round(sum([energy_values[k] for k in energy_values]), 2)

        if energy_type == "E":
            non_active_energies_base = ["HI", "LO", "PE", "TH"]
            capa_energy = ["ENERGY_CAPA_" + s for s in non_active_energies_base]
            inductive_energy = ["ENERGY_INDUCTIVE_" + s for s in non_active_energies_base]
            np_capa_energy = ["ENERGY_NP_CAPA_" + s for s in non_active_energies_base]
            np_inductive_energy = ["ENERGY_NP_INDUCTIVE_" + s for s in non_active_energies_base]
            energy_values["EnergieCap"] = round(sum([get(install_properties, s, 0.0) for s in capa_energy]), 2)
            energy_values["EnergieInd"] = round(sum([get(install_properties, s, 0.0) for s in inductive_energy]), 2)
            energy_values["EnergieCapSpert"] = round(sum([get(install_properties, s, 0.0) for s in np_capa_energy]), 2)
            energy_values["EnergieIndSpert"] = round(sum([get(install_properties, s, 0.0) for s in np_inductive_energy]), 2)
            energy_values["TotalEnerSpert"] = round(energy_values["EnergieCapSpert"] + energy_values["EnergieIndSpert"], 2)
            energy_values["TotalEnerReac"] = round(energy_values["EnergieCap"] + energy_values["EnergieInd"], 2)
            energy_values["PerteF"] = round(float(get(install_properties, "E_PERTES_F", 0.0)), 2)
            energy_values["PerteC"] = round(float(get(install_properties, "E_PERTES_C", 0.0)), 2)
            energy_values["PTechPrel"] = floor(float(get(install_properties, "E_PTP", 0.0)) * 10) / 10
            energy_values["PContPrel"] = floor(float(get(install_properties, "E_PCP", 0.0)) * 10) / 10
            energy_values["PIinstPrel"] = round(float(get(install_properties, "E_PIP", 0.0)), 2)
            energy_values["PContComp"] = round(float(get(install_properties, "E_PCC", 0.0)), 2)
            energy_values["PTotProd"] = round(float(get(install_properties, "E_PPR", 0.0)), 2)
            energy_values["PMaxSansPert"] = round(float(get(install_properties, "EM_MD_AMAX", 0.0)), 2)
            energy_values["PMaxAvecPert"] = round(float(get(install_properties, "EM_BD_AMAX", 0.0)), 2)
            energy_values["NewTarif"] = get(install_properties, "E_NEW_TARI", None)
            energy_values["CosPhi"] = cos(float(atan(energy_values["TotalEnerReac"]) / float(energy_values["EnergieTotAct"]))) if energy_values["EnergieTotAct"] != 0 else None

        energy_values = {k: strOrNone(energy_values[k]) for k in energy_values}

        return {**result, **energy_values}

    def order_keys(self, result):
        attribute_order = [
            "StatutEan",
            "SectActivite",
            "Contrat",
            "Langue",
            "Mi",
            "Mo",
            "PartenaireId",
            "Bp",
            "Tel",
            "Email",
            "BpHgz",
            "CcHgz",
            "BpAddress",
            "BpLocalite",
            "BpCdpostal",
            "OrAddress",
            "OrLocalite",
            "OrCdpostal",
            "OrNumCmpt",
            "OrIdRadRue",
            "OrIdRadLocalite",
            "CodeCommune",
            "Nace",
            "Frns",
            "FrnsLibelle",
            "TypInstln",
            "TypRacc",
            "Slp",
            "Frequence",
            "Usage",
            "GrpTarif",
            "GrpTarifLibelle",
            "HoraireTarif",
            "HeureSwJour",
            "HeureSwNuit",
            "CodeTarif",
            "NewTarif",
            "Ucode",
            "UCodeLibelle",
            "VanneExte",
            "ComptCali",
            "PressionCompt",
            "EnergieActHi",
            "EnergieActLo",
            "EnergieActPe",
            "EnergieTh",
            "EnergieActExn",
            "EnergieActEhp",
            "EnergieActPeHi",
            "EnergieActPeLo",
            "EnergieActNp",
            "EnergieActNplok",
            "EnergieTotAct",
            "EnergieIndPert",
            "EnergieIndSpert",
            "EnergieCapSpert",
            "TotalEnerSpert",
            "EnergieInd",
            "EnergieCap",
            "TotalEnerReac",
            "CosPhi",
            "UMesu",
            "NbPhases",
            "NbPhasesLibelle",
            "ReglPrel",
            "ReglInje",
            "PerteF",
            "PerteC",
            "PTechPrel",
            "PContPrel",
            "PIinstPrel",
            "PContComp",
            "PTotProd",
            "PMaxSansPert",
            "PMaxAvecPert",
            "TotalComptAct",
            "ComptActi1Re",
            "ComptActi2Re",
            "ComptActi2PlusRe",
            "TotalComptInac",
            "ComptInac1Re",
            "ComptInac2Re",
            "ComptInac2PlusRe",
            "Compteurs",
        ]

        ordered_result = {k: result[k] for k in attribute_order if k in result and k != "Compteurs"}
        missing_attributes = [k for k in result if k not in ordered_result]
        for k in missing_attributes:
            ordered_result[k] = result[k]
        ordered_result["Compteurs"] = result["Compteurs"]

        return ordered_result

    def to_response(self, fake_cursor, params=None):
        return fake_cursor
