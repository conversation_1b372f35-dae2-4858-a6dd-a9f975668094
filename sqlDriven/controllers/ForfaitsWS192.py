import json
import os
from os import environ

import boto3
from controllers.NoPagination import NoPagination
from jinja2 import Template

from utils.aws_utils import (
    get_resource_key,
    load_s3_file,
)
from utils.errors import BadRequestError
from utils.models.pydantic_utils import PascalModel


class _Forfait(PascalModel):
    label: str
    phases: int
    amperage: int | float
    power: int | float
    amperage_unit: str = "A"
    power_unit: str = "kVA"
    price: int | float


class ForfaitsWS192(NoPagination):
    """
    Generate a response based on forfaits information.

    This class is a specialized implementation of `NoPagination` that provides
    custom logic to fetch, process, and format forfaits data for a specific use
    case.
    It supports generating responses in various formats, including JSON and PDF.
    The class handles data transformation and invokes an external service
    for PDF generation when required.

    """

    def __init__(self, event, linking):
        super().__init__(event, linking)

    def to_response(self, cursor, params=None):
        """
        Process database response and convert it into the desired format.

        This function processes the SQL query results, converting them into a specific
        format.
        Additionally, it generates a PDF response if the `Accept` header specifies `application/pdf`.
        The output format of the response is contingent on the request's acceptance type.
        When the `application/pdf` format is requested, the function uses
        a specified HTML template to generate the PDF content.

        Parameters
        ----------
        cursor : Any
            The database cursor used to execute the SQL query.
        params : Optional[dict]
            Additional query parameters to customize the SQL query execution.

        Returns
        -------
        Union[list, str]
            Returns a list of processed forfait dictionaries or a base64-encoded string
            representing the generated PDF file.

        """
        sql_result = super().to_response(cursor, params)

        forfaits = [
            _Forfait(
                label=result.get("FORFAIT"),
                phases=result.get("NB_PHASE"),
                amperage=result.get("AMPERAGE"),
                power=result.get("PUISSANCE"),
                amperage_unit="A",
                power_unit="kVA",
                price=result.get("PRICE"),
            ).model_dump()
            for result in sql_result["Data"]
        ]

        headers = self.event.get("headers", {})
        accept_type = headers.get("Accept")

        if accept_type == "application/pdf":
            html_template = Template(load_s3_file(environ["BUCKET"], get_resource_key("templates/forfaits-ws192.html"))).render({"params": {"forfaits": forfaits}}, autoescape=True)
            response = boto3.client("lambda").invoke(
                FunctionName=os.environ["HTML_TO_PDF_LAMBDA"],
                InvocationType="RequestResponse",
                Payload=json.dumps({"body": html_template}),
            )
            response_dict = json.loads(response["Payload"].read())
            if response_dict.get("errorMessage"):
                raise BadRequestError(response_dict["errorMessage"], error_code="PAYLOAD_PROBLEMS")
            payload_str = response_dict["results"]

            self.base64 = True
            self.headers = {"Content-Type": "application/pdf", "content-disposition": "attachment; filename=TarifsForfaits.pdf"}
            return payload_str

        return forfaits
