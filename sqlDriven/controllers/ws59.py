import json
import os
import unicodedata
from builtins import list
from collections import ChainMap
from copy import deepcopy
from datetime import datetime
from decimal import Decimal
from threading import Thread

from controllers.parallel_controller import DatabaseWorker
from controllers.parallel_controller import Parallel<PERSON><PERSON>roller_Deprecated
from utils.api import api_caller
from utils.aws_utils import (
    get_secret,
    load_s3_file,
    get_resource_key,
    get_dynamodb_table,
)
from utils.dict_utils import get
from utils.errors import BadRequestError, NotFoundError, HttpError
from utils.log_utils import log_info, log_err
from utils.type_utils import int_format, assertType, date_format, to_list
from ws59_actions.ctrl_admc import ctrl_admc_post_query_action
from ws59_actions.ctrl_adrc import ctrl_adrc_post_query_action


class ws59(ParallelController_Deprecated):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.langue = os.environ["LANG"]
        self.quick = get(event, "queryStringParameters", {}).get("Quick", "").upper() == "TRUE"

    def fetch(self, cursor, params=None):
        """
        return the response body
        """

        data = []
        result = cursor.fetchall()
        for row in result:
            r = self.row_processor(cursor, row)
            data.append(r)
        if len(data) == 1:
            return data[0]
        else:
            return data

    def to_response(self, fake_cursor, params=None):
        return fake_cursor

    def validate_request_params(self):
        """
        Validates request parameters and returns their string representation
        """
        self.validate_required_params()
        body = self.validate_type()
        body = self.add_info_in_request(body)
        return body

    def validate_required_params(self):
        body = get(self.event, "body", {})
        req_params = set(list(body.keys())) & {
            "Ean",
            "Adresse",
            "NumDossier",
            "IdPartenaire",
            "UserInfo",
        }
        if not req_params or len(req_params) > 1:
            raise BadRequestError("Il faut et un seul input parmi ['Ean', 'Adresse', 'NumDossier', 'IdPartenaire', 'UserInfo'] dans la requête")
        req_key = list(req_params)[0]
        self.input_type = req_key
        list_values = body[req_key]
        if not isinstance(list_values, list):
            raise BadRequestError("L'attribut " + req_key + " doit être fourni sous forme de liste de valeurs")
        if not list_values:
            raise BadRequestError("Aucune valeur de " + req_key + " n\´a été fournie")

    def validate_type(self):
        body = get(self.event, "body", {})
        list_values = body[self.input_type]
        if self.input_type in ["Ean", "NumDossier", "IdPartenaire"]:
            [
                int_format(
                    v,
                    err=BadRequestError("Les valeurs de " + self.input_type + " doivent être des string représentant des entiers"),
                )
                for v in list_values
            ]
        if self.input_type == "NumDossier":
            body["NumDossier"] = [str(v).zfill(12) for v in list_values]
        if self.input_type == "Adresse":
            # En DB les valeurs des champs ne sont pas clean. Le cleaning via fonctions de SAP HANA est
            # obligatoirement via regex donc plus tedieux et moins performant
            def clean(s):
                return unicodedata.normalize("NFD", s).encode("ascii", "ignore").decode("utf-8")

            upper_trimed_list = [{k: str(assertType(k, str, adresse[k])).strip().upper() for k in adresse} for adresse in list_values]
            body["Adresse"] = [
                {
                    **adresse,
                    **{"CleanedLocalite": (clean(adresse["Localite"]) if "Localite" in adresse else {})},
                    **{"CleanedRue": clean(adresse["Rue"]) if "Rue" in adresse else {}},
                }
                for adresse in upper_trimed_list
            ]

        if "DateFrom" in body and "DateTo" in body:
            err = BadRequestError("DateFrom et DateTo doivent être fournis en format YYYYMMDD")
            body["DateFrom"] = date_format(
                body["DateFrom"],
                input_format="%Y%m%d",
                output_format="%Y%m%d",
                throw=True,
                err=err,
            )
            body["DateTo"] = date_format(
                body["DateTo"],
                input_format="%Y%m%d",
                output_format="%Y%m%d",
                throw=True,
                err=err,
            )

        return body

    def add_info_in_request(self, body):
        # We add in the body default values and relevant headers
        if "TypDoss" not in body:
            body["TypDoss"] = "TOUS"
        elif body["TypDoss"] not in ["TOUS", "INF", "SUP", "DIR"]:
            raise BadRequestError("Les valeurs admises pour TypDoss sont ['TOUS', 'INF', 'SUP', 'DIR']")

        # In DB values can be F or FR
        liste_langues_valide = ["FR", "EN", "DE", "NL", "F", "E", "D", "N"]
        langue = self.langue if self.langue in liste_langues_valide else "FR"
        liste_langues_db = [v for v in liste_langues_valide if v[0] == langue[0]]
        body["ListeCodeLangue"] = liste_langues_db

        return body

    def load_sql_file(self, filename_dict):
        # override this function as sql statements will require custom formatting for this WS
        return filename_dict

    def execute_query(self, sql_statement, request_params):
        # Parameters for all queries
        db_credentials = get_secret(super().get_secret())
        result_dict = {}
        inner_fetcher = self.fetch
        input_type = self.input_type
        input_values = request_params[input_type]
        sql_dict = self.sql_file()
        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))

        inputs_by_dossier = get_dossiers_from_input(
            input_type,
            input_values,
            sql_dict,
            env_file,
            result_dict,
            db_credentials,
            inner_fetcher,
        )

        dossiers_in_input = list(inputs_by_dossier.keys())

        if not dossiers_in_input:
            raise NotFoundError("Aucun résultat pour la séléction")

        log_info("Dossiers from user input")
        log_info(dossiers_in_input)

        mapping_linked_doss = get_linked_dossiers(
            request_params,
            dossiers_in_input,
            sql_dict,
            env_file,
            result_dict,
            db_credentials,
            inner_fetcher,
        )

        if self.input_type != "NumDossier":
            if request_params["TypDoss"] == "INF":
                all_dossiers = [doss for doss in mapping_linked_doss if mapping_linked_doss[doss]["NumDossierSup"]]
            elif request_params["TypDoss"] == "SUP":
                all_dossiers = [doss for doss in mapping_linked_doss if not mapping_linked_doss[doss]["NumDossierSup"]]
            elif request_params["TypDoss"] == "TOUS":
                all_dossiers = list(mapping_linked_doss)
            else:
                all_dossiers = list(mapping_linked_doss)
        else:
            # Keep dossiers in input that do indeed correspond do to dossiers number
            all_dossiers = list(mapping_linked_doss)

        log_info("All dossiers after getting linked dossiers and applying time filter")
        log_info(all_dossiers)

        dossiers_all_info = get_dossiers_info(
            self.langue,
            input_type,
            request_params,
            all_dossiers,
            inputs_by_dossier,
            mapping_linked_doss,
            sql_dict,
            env_file,
            result_dict,
            db_credentials,
            inner_fetcher,
            self.quick,
        )

        if not self.quick:
            # Get dossier inf data
            dossier_inf_threads = []
            for dossier_info in dossiers_all_info["Liste"]:
                if dossier_info["NumDossierInf"]:

                    def fill_dossier_inf(_dossier_info):
                        event = deepcopy(self.event)
                        event["body"] = {"NumDossier": _dossier_info["NumDossierInf"]}
                        instance = ws59(event, self.linking)
                        request_params = instance.validate_request_params()
                        sql_statement = instance.load_sql_file(instance.sql_file())
                        sql_statement = instance.apply_hana_env(sql_statement)
                        _dossier_info["DossierInf"] = instance.execute_query(sql_statement, request_params).get("Liste", [])

                    dossier_inf_threads.append(Thread(target=fill_dossier_inf, args=(dossier_info,)))
                else:
                    dossier_info["DossierInf"] = []

            for t in dossier_inf_threads:
                t.start()
            for t in dossier_inf_threads:
                t.join()

            # Set head dossier that is at step 9 to its higher sub dossier step
            for dossier_info in dossiers_all_info["Liste"]:
                if dossier_info["NumEtape"] == 9:
                    doss_inf = [doss for doss in dossier_info["DossierInf"] if doss["NumEtape"] < 9]
                    if doss_inf:
                        inf_steps = max(doss_inf, key=lambda x: x["CodeStatut"])
                        if inf_steps:
                            dossier_info["NumEtape"] = inf_steps["NumEtape"]
                            dossier_info["CodeStatut"] = inf_steps["CodeStatut"]
                            dossier_info["LibEtape"] = inf_steps["LibEtape"]
                            dossier_info["LibStatut"] = inf_steps["LibStatut"]
                            dossier_info["Liste"] = inf_steps["Liste"]

        return dossiers_all_info


def load_single_sql_file(sql_dict, file_key):
    return load_s3_file(os.environ["MAPPING_BUCKET_NAME"], get_resource_key(sql_dict[file_key]))


def get_dossiers_from_input(
    input_type,
    input_values,
    sql_dict,
    env_file,
    result_dict,
    db_credentials,
    inner_fetcher,
):
    """
    Retrieve the list of dossiers linked to the input provided by the user.
    Keep in mind user can provide different input types
    Keep in mind that for some input types, there are 2 possible queries to try to reach a result
    """

    if input_type == "NumDossier":
        return {dossier: {} for dossier in input_values}

    query_name_by_input_type = {
        "IdPartenaire": "partenaire_to_dossier",
        "Ean": "ean_to_dossier",
        "Adresse": "adresse_to_dossier",
        "UserInfo": "get_dossiers_from_user_info",
    }

    if input_type == "IdPartenaire":
        params_in_query = ",".join([("'" + str(v) + "'") for v in input_values])
        params_in_cursor = [{}]
        # SAP Hana cursor does not treat params that are lists, therefore we need to treat them separately
        queries = [load_single_sql_file(sql_dict, query_name_by_input_type[input_type]).format(HanaSchema=env_file["HANA_SCHEMA"], ParamsValue=params_in_query)]
    elif input_type == "Ean":
        params_in_query = ",".join([("'" + str(v) + "'") for v in input_values])
        params_in_cursor = [{}]
        # SAP Hana cursor does not treat params that are lists, therefore we need to treat them separately
        queries = [
            load_single_sql_file(sql_dict, query_name_by_input_type[input_type] + "_" + str(i)).format(HanaSchema=env_file["HANA_SCHEMA"], ParamsValue=params_in_query)
            for i in range(0, 2)
        ]
    elif input_type == "Adresse":
        queries = [load_single_sql_file(sql_dict, query_name_by_input_type[input_type] + "_" + str(i)).format(HanaSchema=env_file["HANA_SCHEMA"]) for i in range(0, 2)]
        params_in_cursor = input_values
    elif input_type == "UserInfo":
        queries = [load_single_sql_file(sql_dict, query_name_by_input_type[input_type]).format(HanaSchema=env_file["HANA_SCHEMA"])]
        params_in_cursor = input_values

    query_threads = [
        DatabaseWorker(
            query_name_by_input_type[input_type] + "_" + str(i) + "_" + str(j),
            db_credentials,
            queries[i],
            params_in_cursor[j],
            inner_fetcher,
            result_dict,
        )
        for i in range(len(queries))
        for j in range(len(params_in_cursor))
    ]

    for t in query_threads:
        t.start()

    for t in query_threads:
        t.join()

    if not result_dict or all(not v for v in list([result_dict.values()])):
        raise NotFoundError("Aucun résultat pour la séléction")

    output_by_dossier = {row["DossierId"]: {k: row[k] for k in row if k != "DossierId"} for q in result_dict for row in to_list(result_dict[q])}

    return output_by_dossier


def get_linked_dossiers(
    body,
    dossiers_in_input,
    sql_dict,
    env_file,
    result_dict,
    db_credentials,
    inner_fetcher,
):
    """
    Retrieve the list of dossiers superieurs eventually linked to the dossiers obtained directly from user input
    Filters the overall list of dossiers (inferieurs and superieurs) if a date range is provided
    Keeps only dossiers inferieurs or superieurs if filter provided
    """

    date_filter = """
          WHERE
          (
            (TO_VARCHAR({DateFrom}, 'YYYYMMDD') <= IDAT2 AND IDAT2 <= TO_VARCHAR({DateTo}, 'YYYYMMDD'))
            OR
            (IDAT2 = '00000000' OR IDAT2 = '' OR IDAT2 IS NULL)
          )
    """
    date_filter = date_filter.format(DateFrom="'" + body["DateFrom"] + "'", DateTo="'" + body["DateTo"] + "'") if all(k in body for k in ["DateFrom", "DateTo"]) else " "

    query = load_single_sql_file(sql_dict, "expand_dossier_list").format(
        HanaSchema=env_file["HANA_SCHEMA"],
        ListeDossiers=",".join(["'" + dossier_id + "'" for dossier_id in dossiers_in_input]),
        DateFilter=date_filter,
    )

    query_thread = DatabaseWorker("expand_dossier_list", db_credentials, query, {}, inner_fetcher, result_dict)

    query_thread.start()
    query_thread.join()

    query_result = to_list(result_dict.get("expand_dossier_list", []))

    if not query_result:
        raise NotFoundError("Aucun résultat pour la séléction")

    mapping = {}
    for row in query_result:
        if row["NumDossier"] not in mapping:
            mapping[row["NumDossier"]] = {"NumDossierSup": [], "NumDossierInf": []}
        if row["NumDossierSup"]:
            mapping[row["NumDossier"]]["NumDossierSup"].append(row["NumDossierSup"])
        if row["NumDossierInf"]:
            mapping[row["NumDossier"]]["NumDossierInf"].append(row["NumDossierInf"])

    # Filter sub-dossier already listed in a head-dossier
    for doss_id in list(mapping):
        # Check if we have the head dossier of this one,
        # and ensure that the head dossier list this one as one of its sub dossiers
        if any(sup_id in mapping and doss_id in mapping[sup_id]["NumDossierInf"] for sup_id in mapping[doss_id]["NumDossierSup"]):
            del mapping[doss_id]

    return mapping


def get_dossiers_info(
    langue,
    input_type,
    request_params,
    all_dossiers,
    inputs_by_dossier,
    mapping,
    sql_dict,
    env_file,
    result_dict,
    db_credentials,
    inner_fetcher,
    quick=False,
):
    """
    Retrieves a bunch of information for each dossier.
    Some infos are retrieved for all dossiers in one query
    Statut and action infos require one query per dossier (only if quick = False)
    """

    # Params
    # _________________________________________________________________________________________________

    dossiers_phrase = ",".join(["'" + dossier_id + "'" for dossier_id in all_dossiers])
    statut_query = load_single_sql_file(sql_dict, "get_dossier_statut").format(HanaSchema=env_file["HANA_SCHEMA"])
    get_coordinator_query = load_single_sql_file(sql_dict, "get_dossier_coordinator").format(HanaSchema=env_file["HANA_SCHEMA"])
    get_lib_status_query = load_single_sql_file(sql_dict, "get_lib_status").format(HanaSchema=env_file["HANA_SCHEMA"])
    get_info_auart_query = load_single_sql_file(sql_dict, "get_info_auart").format(HanaSchema=env_file["HANA_SCHEMA"])
    config_dosrac_query = load_single_sql_file(sql_dict, "get_config_dosrac").format(HanaSchema=env_file["HANA_SCHEMA"])
    info_action_queries = {
        "DATE_PLANIF": load_single_sql_file(sql_dict, "get_info_action_date_planif").format(HanaSchema=env_file["HANA_SCHEMA"]),
        "MONT_ETUD": load_single_sql_file(sql_dict, "get_info_action_mont").format(HanaSchema=env_file["HANA_SCHEMA"], Action="MONT_ETUD"),
        "MONT_DEVIS": load_single_sql_file(sql_dict, "get_info_action_mont").format(HanaSchema=env_file["HANA_SCHEMA"], Action="MONT_DEVIS"),
        "CTRL_CONC": load_single_sql_file(sql_dict, "get_info_action_ctrl_conc").format(HanaSchema=env_file["HANA_SCHEMA"]),
    }

    # Enrich with input like info (Ean, Partenaire, Adresse)
    # _________________________________________________________________________________________________

    def parameterize_sql(sql, key):
        return load_single_sql_file(sql, key).format(HanaSchema=env_file["HANA_SCHEMA"], ListeDossiers=dossiers_phrase)

    # Let's already fill in what was present in the given inputs
    output_by_dossier = {
        dossier: {
            # A dossier superieur or a linked dossier inferieur may not have inputs
            **(inputs_by_dossier[dossier] if dossier in inputs_by_dossier else {})
        }
        for dossier in all_dossiers
    }
    # Let's get what was not provided
    query_name_by_input_type = {
        "IdPartenaire": "dossier_to_partenaire",
        "Ean": "dossier_to_ean",
        "Adresse": "dossier_to_adresse",
    }

    all_threads = [
        DatabaseWorker(
            query_name_by_input_type[k],
            db_credentials,
            parameterize_sql(sql_dict, query_name_by_input_type[k]),
            {},
            inner_fetcher,
            result_dict,
        )
        # When the input is adresse we need to fetch other adress elements the user has maybe not provided
        for k in query_name_by_input_type
        if k != input_type or input_type == "Adresse"
    ]

    # Enrich with status info
    # ________________________________________________________________________________________________
    coordinators_map = {}

    all_threads.extend(
        [
            DatabaseWorker(
                f"get_dossier_statut_{dossier}",
                db_credentials,
                statut_query,
                {"DossierId": dossier},
                inner_fetcher,
                result_dict,
            )
            for dossier in all_dossiers
        ]
    )

    all_threads.extend(
        [
            DatabaseWorker(
                dossier,
                db_credentials,
                get_coordinator_query,
                {"DossierId": dossier},
                inner_fetcher,
                coordinators_map,
            )
            for dossier in all_dossiers
        ]
    )

    all_threads.extend(
        [
            DatabaseWorker(
                f"get_config_dosrac_{dossier}",
                db_credentials,
                config_dosrac_query,
                {"DossierId": dossier},
                inner_fetcher,
                result_dict,
            )
            for dossier in all_dossiers
        ]
    )

    all_threads.extend(
        [
            DatabaseWorker(
                "AUART",
                db_credentials,
                get_info_auart_query.format(DossierIds="('" + "','".join(all_dossiers) + "')"),
                {},
                inner_fetcher,
                result_dict,
            ),
            DatabaseWorker(
                "lib_status",
                db_credentials,
                get_lib_status_query,
                {},
                inner_fetcher,
                result_dict,
            ),
        ]
    )

    for t in all_threads:
        t.start()

    for t in all_threads:
        t.join()

    # Language filter for status
    for action_key in [k for k in result_dict if "get_dossier_statut_" in k]:
        statut = result_dict[action_key]
        if isinstance(statut, list):
            for item in statut:
                if item["Lang"] in request_params["ListeCodeLangue"]:
                    result_dict[action_key] = item

    query_names_in_result = list(set(query_name_by_input_type.values()) & set(list(result_dict.keys())))
    all_rows = {(q + "_" + str(row["NumDossier"])): {k: row[k] for k in row if k != "NumDossier"} for q in query_names_in_result for row in to_list(result_dict[q])}
    pre_merge_all_rows = {dossier_id: dict(ChainMap(*[all_rows[k] for k in all_rows if dossier_id in k])) for dossier_id in all_dossiers}
    all_statut_rows = {row["NumDossier"]: row for k in result_dict if "get_dossier_statut_" in k for row in to_list(result_dict[k])}

    # filter to remove incomplete dossiers data due to query filters
    all_dossiers = [dossier for dossier in all_dossiers if dossier in all_statut_rows]

    all_config_dosrac_rows = {
        dossier: {config_dosrac["ATNAM"]: config_dosrac[config_dosrac["VAL_TO_USE"]] for config_dosrac in result_dict[f"get_config_dosrac_{dossier}"]} for dossier in all_dossiers
    }

    output_by_dossier = {
        dossier: {
            **output_by_dossier.get(dossier, {}),
            **pre_merge_all_rows.get(dossier, {}),
            **all_statut_rows.get(dossier, {}),
            "Coordinateur": coordinators_map.get(dossier) or None,
            "NumDossierSup": next(iter(mapping.get(dossier, {}).get("NumDossierSup", [])), ""),
            "NumDossierInf": mapping.get(dossier, {}).get("NumDossierInf", []),
            # Set synergy info
            # "SynergieCile": all_config_dosrac_rows.get(dossier, {}).get("RAC_SYNERGIE") == "O",
            # "SynergieCmh": all_config_dosrac_rows.get(dossier, {}).get("RAC_SYNERGIE") == "OC",
            "SynergieCile": False,  # Always set to false, waiting for approval
            "SynergieCmh": False,  # Always set to false, waiting for approval
        }
        for dossier in all_dossiers
    }

    all_action_rows = {}
    if not quick:
        # Enrich with action info
        # To run the action we may need to know status information, it cannot be done in parallel
        # ________________________________________________________________________________________________

        action_threads = [
            get_action_thread(
                output_by_dossier[dossier_id],
                dossier_id,
                info_action_queries,
                result_dict,
                db_credentials,
                inner_fetcher,
            )
            for dossier_id in all_dossiers
        ]

        for t in action_threads:
            if t:
                t.start()

        for t in action_threads:
            if t:
                t.join()

        # Language filter for actions
        for action_key in [k for k in result_dict if "get_info_action_" in k]:
            lang_actions = {}
            if isinstance(result_dict[action_key], list):
                for action in result_dict[action_key]:
                    if action["Lang"] not in lang_actions:
                        lang_actions[action["Lang"]] = []
                    lang_actions[action["Lang"]].append(action)
                for lang in lang_actions:
                    if lang in request_params["ListeCodeLangue"]:
                        result_dict[action_key] = lang_actions[lang]
                        break
                for action in result_dict[action_key]:
                    del action["Lang"]
            elif isinstance(result_dict[action_key], dict):
                del result_dict[action_key]["Lang"]

        # Post actions queries processing
        # ________________________________________________________________________________________________
        lib_status = {}

        for item in result_dict["lib_status"]:
            if item["CodeInfo"] not in lib_status:
                lib_status[item["CodeInfo"]] = {}
            lib_status[item["CodeInfo"]][item["Lang"]] = item["LibInfo"]

        post_action_threads = [
            Thread(
                target=dossier_post_action,
                args=(
                    dossier_id,
                    result_dict,
                    output_by_dossier,
                    all_config_dosrac_rows,
                    lib_status,
                    all_action_rows,
                    langue,
                ),
            )
            for dossier_id in all_dossiers
        ]
        for thread in post_action_threads:
            thread.start()
        for thread in post_action_threads:
            thread.join()

    # Convert output types as in SAP Web service
    # ________________________________________________________________________________________________

    def to_str_int(txt, param_name):
        return str(
            int_format(
                txt,
                err=HttpError("Une valeur de " + param_name + " dans le résultat n'admet pas de représentation entière"),
            )
        )

    def to_int(txt, param_name):
        return int_format(
            txt,
            err=HttpError("Une valeur de " + param_name + " dans le résultat n'admet pas de représentation entière"),
        )

    attributes_to_keep = [
        "NumDossier",
        "Coordinateur",
        "NumDossierSup",
        "NumDossierInf",
        "TypeDossier",
        "DateDossier",
        "IdPartenaire",
        "Ean",
        "IdAdresse",
        "CdPostal",
        "IdLocalite",
        "IdRadLocalite",
        "Localite",
        "IdRue",
        "IdRadRue",
        "Rue",
        "NumRue",
        "NumComp",
        "NumEtape",
        "LibEtape",
        "CodeStatut",
        "LibStatut",
        "TypeFluide",
        "WS109Actions",
        "TypeDossierCode",
        "SynergieCile",
        "SynergieCmh",
    ]

    type_transfo = {
        "NumDossier": to_str_int,
        "IdLocalite": to_str_int,
        "IdRue": to_str_int,
        "IdPartenaire": to_int,
        "Ean": to_int,
        "CdPostal": to_int,
        "IdRadLocalite": to_int,
        "IdRadRue": to_int,
        "NumRue": to_int,
        "NumEtape": to_int,
        "CodeStatut": to_int,
    }

    output_by_dossier = {
        dossier: {
            **{
                k: (
                    type_transfo[k](output_by_dossier[dossier][k], k)
                    if k in type_transfo and k in output_by_dossier[dossier]
                    else (output_by_dossier[dossier][k] if k in output_by_dossier[dossier] else None)
                )
                for k in attributes_to_keep
            },
            **get(all_action_rows, dossier, {}),
        }
        for dossier in all_dossiers
    }

    # Wrapping up
    # ________________________________________________________________________________________________

    # We must respect convention of providing NumDossier in a list
    output_by_dossier = {
        dossier: {
            **{k: output_by_dossier[dossier][k] for k in output_by_dossier[dossier] if k != "NummDossier"},
            **{"NumDossier": [output_by_dossier[dossier]["NumDossier"]]},
        }
        for dossier in all_dossiers
    }

    result = {"Liste": list(output_by_dossier.values())}

    return result


def dossier_post_action(
    dossier_id: int,
    result_dict: dict,
    output_by_dossier: dict,
    all_config_dosrac_rows: dict,
    lib_status: dict,
    all_action_rows: dict,
    langue: str,
):
    dossier_auart = next(filter(lambda x: x["NumDossier"] == dossier_id, to_list(result_dict["AUART"])))["AUART"]
    dossier = output_by_dossier.get(dossier_id, {})
    config_dosrac = all_config_dosrac_rows.get(dossier_id, {})
    action = dossier.get("ACTION", "").strip()
    base_code_statut = dossier["CodeStatut"]

    # Special action processing
    if f"get_info_action_{dossier_id}_MONT_DEVIS" in result_dict:
        mont_devis_post_query_action(
            dossier,
            result_dict[f"get_info_action_{dossier_id}_MONT_DEVIS"],
            config_dosrac,
        )
    if f"get_info_action_{dossier_id}_MONT_ETUD" in result_dict:
        mont_etud_post_query_action(
            dossier,
            result_dict[f"get_info_action_{dossier_id}_MONT_ETUD"],
            config_dosrac,
        )
    if f"get_info_action_{dossier_id}_CTRL_ADRC" in result_dict:
        ctrl_adrc_post_query_action(
            dossier,
            result_dict[f"get_info_action_{dossier_id}_CTRL_ADRC"],
            config_dosrac,
            dossier_auart,
        )
    if f"get_info_action_{dossier_id}_CTRL_ADMC" in result_dict:
        ctrl_admc_post_query_action(
            result_dict[f"get_info_action_{dossier_id}_CTRL_ADMC"],
            config_dosrac,
            dossier_auart,
        )
    if action == "PLANIFIABLE":
        date_planif_post_query_action(dossier_id, dossier, config_dosrac)
    if base_code_statut == "81" and dossier_auart == "DRE1" and dossier.get("NumDossierSup"):
        # No planification for Elec
        dossier["CodeStatut"] = "85"

    # Set LibInfo if post process changed the CodeStatut
    if base_code_statut != dossier["CodeStatut"] and dossier["CodeStatut"] in lib_status:
        lib_stat = lib_status[dossier["CodeStatut"]]
        dossier["LibStatut"] = lib_stat.get(langue, lib_stat["FR"])  # get correct language, default to FR

    # Set energy type
    if dossier_auart == "DRG1":
        dossier["TypeFluide"] = "Gaz"
    elif dossier_auart == "DRE1":
        dossier["TypeFluide"] = "Elec"

    # R01 identified as RT1
    if (dossier["NumDossierSup"] or not dossier["NumDossierInf"]) and dossier["TypeDossier"] in ("R01", "R06"):
        dossier["TypeDossierCode"] = "NOUV"
    else:
        dossier["TypeDossierCode"] = "MODIF"

    # Set ws109 actions
    add_ws109_actions(dossier)

    # Group actions
    for k in result_dict:
        if f"get_info_action_{dossier_id}" in k:
            if dossier_id not in all_action_rows:
                all_action_rows[dossier_id] = {"Liste": []}
            all_action_rows[dossier_id]["Liste"].extend(to_list(result_dict[k]))

    # Split LibInfo
    for info in all_action_rows.get(dossier_id, {}).get("Liste", []):
        lib_info = info.get("LibInfo")
        info["LibInfoStatut"] = ""

        if lib_info and "-" in lib_info:
            try:
                parts = lib_info.split("-")
                info["LibInfo"], info["LibInfoStatut"] = (
                    parts[0].strip(),
                    "-".join(parts[1:]).strip(),
                )
            except:
                pass  # Do nothing in case of error


def add_ws109_actions(dossier: dict):
    table = get_dynamodb_table(os.environ["DossierConfActionsTable"])

    actions = table.get_item(Key={"Id": Decimal(dossier["NumDossier"])})

    dossier["WS109Actions"] = actions.get("Item", {}).get("Actions", [])


def get_action_thread(dossier, dossier_id, queries, result_dict, db_credentials, inner_fetcher):
    params_by_action = {
        "DATE_PLANIF": {
            "DossierId": dossier_id,
            "STATUT2": dossier["STATUT2"],
            "STATUT1": dossier["STATUT1"],
        },
        "MONT_ETUD": {"DossierId": dossier_id, "TypeMontant": "C"},
        "MONT_DEVIS": {"DossierId": dossier_id, "TypeMontant": "B"},
        "CTRL_CONC": {"DossierId": dossier_id},
        "PLANIFIABLE": {"DossierId": dossier_id},
    }

    action = dossier["ACTION"].strip()

    # Return CTRL_ADRC action also in steps 5,7
    if dossier["NumEtape"] in ("5", "7"):
        result_dict[f"get_info_action_{dossier_id}_CTRL_ADRC"] = [lib_stat.copy() for lib_stat in result_dict["lib_status"] if lib_stat["ACTION"].startswith("CTRL_ADRC")]

    # Return CTRL_ADMC action also in step 7
    if dossier["NumEtape"] == "7":
        result_dict[f"get_info_action_{dossier_id}_CTRL_ADMC"] = [lib_stat.copy() for lib_stat in result_dict["lib_status"] if lib_stat["ACTION"].startswith("CTRL_ADMC")]

    if action:
        # Add actions with no special query
        if action not in queries:
            result_dict[f"get_info_action_{dossier_id}_{action}"] = [lib_stat.copy() for lib_stat in result_dict["lib_status"] if lib_stat["ACTION"].startswith(action)]
        # Run thread to fetch action with special query
        else:
            action_query = queries[action]
            params_query = params_by_action.get(action, {})
            return DatabaseWorker(
                f"get_info_action_{dossier_id}_{action}",
                db_credentials,
                action_query,
                params_query,
                inner_fetcher,
                result_dict,
            )

    return None


def mont_devis_post_query_action(dossier, actions_infos, config_dosrac):
    if actions_infos is None:
        actions_infos = []
    if dossier["CodeStatut"] == "42":
        if actions_infos:
            if config_dosrac.get("RAC_PAIEMENT_MYRESA") and config_dosrac.get("RAC_PAIEMENT_AVANT") == "O":
                dossier["CodeStatut"] = "44"
            elif config_dosrac.get("RAC_PAIEMENT_AVANT") == "N":
                if config_dosrac.get("RAC_BON_CMD_DATE"):
                    dossier["CodeStatut"] = "43"
            elif config_dosrac.get("RAC_PAIEMENT_AVANT") == "O":
                if config_dosrac.get("RAC_PAIEMENT_DATE"):
                    dossier["CodeStatut"] = "43"
                else:
                    dossier["CodeStatut"] = "44"
        else:
            dossier["CodeStatut"] = "41"
    if dossier["CodeStatut"] == "44":
        if config_dosrac.get("RAC_PAIEMENT_MYRESA") and config_dosrac.get("RAC_PAIEMENT_AVANT") == "O":
            for action_info in actions_infos:
                if action_info["CodeInfo"] == "44_2" and action_info["ValeurInfo"] != 0:
                    dossier["CodeStatut"] = "45"
                    break

    # Filter element not in the current statut
    actions_infos[:] = filter(lambda x: x["CodeInfo"].startswith(dossier["CodeStatut"]), actions_infos)


def mont_etud_post_query_action(dossier, actions_infos, config_dosrac):
    if dossier["CodeStatut"] == "32":
        if config_dosrac.get("RAC_PAIEMENT_ETUD_MYRESA") and config_dosrac.get("RAC_PAIEMENT_AVANT") == "O":
            for action_info in actions_infos:
                if action_info["CodeInfo"] == "32_2" and action_info["ValeurInfo"] != 0:
                    dossier["CodeStatut"] = "33"
                    break

    # Filter element not in the current statut
    actions_infos[:] = filter(lambda x: x["CodeInfo"].startswith(dossier["CodeStatut"]), actions_infos)


def date_planif_post_query_action(dossier_id, dossier, config_dosrac):
    if dossier["CodeStatut"] == "21":
        if config_dosrac.get("RAC_VISITE_URD") == "Y":
            try:
                log_info("Call WS40")
                res_ws40 = api_caller(
                    "post",
                    "/demande_travaux/planification",
                    body=json.dumps(
                        {
                            "Liste": [
                                {
                                    "NumDossier": dossier_id,
                                    "DatePlanif": datetime.today().strftime("%d/%m/%Y"),
                                    "TypeAction": "DATE",
                                }
                            ]
                        }
                    ),
                    headers={"Content-Type": "application/json"},
                )
            except Exception as e:
                log_err(e)
                res_ws40 = {}
            res_liste = res_ws40.get("Liste", [])
            if not res_liste or not (next(iter(res_liste)) or {}).get("Liste"):
                dossier["CodeStatut"] = "11"
                dossier["NumEtape"] = "1"
        else:
            dossier["CodeStatut"] = "11"
            dossier["NumEtape"] = "1"

    elif dossier["CodeStatut"] == "81":
        try:
            log_info("Call WS40")
            res_ws40 = api_caller(
                "post",
                "/demande_travaux/planification",
                body=json.dumps(
                    {
                        "Liste": [
                            {
                                "NumDossier": dossier_id,
                                "DatePlanif": datetime.today().strftime("%d/%m/%Y"),
                                "TypeAction": "DATE",
                            }
                        ]
                    }
                ),
                headers={"Content-Type": "application/json"},
            )
        except Exception as e:
            log_err(e)
            res_ws40 = {}
        res_liste = res_ws40.get("Liste", [])
        if not res_liste or not (next(iter(res_liste)) or {}).get("Liste"):
            dossier["CodeStatut"] = "85"
