import json
from typing import Any

from controllers.CommuneController import CommuneController

from utils.ean_utils import get_code_cadran


class CommuneControllerEan(CommuneController):
    """
    Control the processing and transformation of database query results out of communes EANs.

    This class extends the functionality of CommuneController by overriding the `to_response`
    method to restructure raw database query results, manipulate addresses, and organize EAN
    entries for further processing or consumption.
    """

    def to_response(self, cursor: dict, params: dict | None = None) -> list[dict[str, Any]]:
        """
        Transform a cursor query result into a structured response.

        This method processes the query results obtained from the cursor and
        converts them into a list of dictionaries with structured information.
        During this process, it manipulates the "Adresse" and "Index" fields of
        the input data for standardization and creates a mapping based on
        unique addresses.

        Parameters
        ----------
        cursor : dict
            Database cursor object used for executing queries.
        params : dict, optional
            Additional parameters used for the query or processing.

        Return
        ------
        list of dict
            A list where each element is a dictionary containing structured
            information for a specific unique address.
            Each dictionary includes an "Adresse" field and associated EANs.

        """
        address_dict = {}
        tmp_result = super().to_response(cursor, params)

        for tmp_res in tmp_result:
            tmp_res["Adresse"].pop("NumComp", None)
            address = json.dumps(tmp_res["Adresse"])
            del tmp_res["Adresse"]
            if address not in address_dict:
                address_dict[address] = []
            address_dict[address].append(tmp_res)

            tmp_res["Index"] = (
                [
                    {
                        "Date": f"{item['DateRel'][:4]}-{item['DateRel'][4:6]}-{item['DateRel'][6:]}",
                        "Index": item["Index"],
                        "Unite": item["IndexUnit"],
                        "NumCpt": item["NumCompteur"],
                        "Cadran": get_code_cadran(item),
                    }
                    for item in json.loads(tmp_res["Index"])
                ]
                if tmp_res["Index"]
                else None
            )

        return [{"Adresse": json.loads(k), "Eans": v} for k, v in address_dict.items()]
