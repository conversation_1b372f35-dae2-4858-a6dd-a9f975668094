from controllers.NoPagination import NoPagination

from utils.aws_handler_decorator import commune_handler
from utils.dict_utils import inflate_flat_dict
from utils.errors import INVALID_USER_RIGHTS
from utils.models.user import User


class CommuneController(NoPagination):
    inflate = True

    @commune_handler(event_args_position=1)
    def __init__(self, event, linking, logged_user: User):
        super().__init__(event, linking)
        self.logged_user = logged_user
        self.commune_id = logged_user.commune.id

        if not logged_user.commune.admin and "roles" in linking and not any(role in logged_user.commune.roles for role in linking["roles"]):
            raise INVALID_USER_RIGHTS

    def validate_request_params(self):
        params = super().validate_request_params()
        params["IdCommune"] = self.commune_id
        return params

    def fetch(self, cursor, params=None):
        resp = super().fetch(cursor, params)
        return [inflate_flat_dict(item) for item in resp] if self.inflate else resp
