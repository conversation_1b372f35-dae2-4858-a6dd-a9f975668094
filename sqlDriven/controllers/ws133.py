import json
from os import environ
from threading import Thread

from controllers.CommuneController import CommuneController
from controllers.parallel_controller import DatabaseWorker
from controllers.ws59 import (
    mont_devis_post_query_action,
    mont_etud_post_query_action,
    date_planif_post_query_action,
)
from utils.aws_utils import get_resource_key, get_secret


class ws133(CommuneController):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.result_dict = {}
        self.lib_status_thread = DatabaseWorker(
            "lib_status",
            get_secret(self.get_secret()),
            self.apply_hana_env(self.load_sql_file(get_resource_key(self.linking["sql_template"]["get_lib_status"]))),
            {},
            super(CommuneController, self).fetch,
            self.result_dict,
        )

    def execute_query(self, sql_statement, request_params):
        self.lib_status_thread.start()
        return super().execute_query(sql_statement, request_params)

    def sql_file(self):
        return get_resource_key(self.linking["sql_template"]["get_dossiers"])

    def to_response(self, fake_cursor, params=None):
        response = super().to_response(fake_cursor, params)

        # Post actions queries processing
        # ________________________________________________________________________________________________
        self.lib_status_thread.join()
        lib_status = {}
        for item in self.result_dict["lib_status"]:
            if item["CodeInfo"] not in lib_status:
                lib_status[item["CodeInfo"]] = {}
            lib_status[item["CodeInfo"]][item["Lang"]] = item["LibInfo"]

        post_action_threads = [Thread(target=self.dossier_post_action, args=(dossier_id, lib_status)) for dossier_id in response]
        for thread in post_action_threads:
            thread.start()
        for thread in post_action_threads:
            thread.join()

        return response

    def dossier_post_action(self, dossier: dict, lib_status: dict):
        dossier_id = dossier["Reference"]
        dossier_auart = dossier["AUART"]
        action = dossier.get("ACTION", "").strip()
        base_code_statut = dossier["CodeStatut"]
        config_dosrac = {config["Name"]: config["Value"] for config in (json.loads(dossier["Config"]))}

        # Special action processing
        if action == "MONT_DEVIS":
            mont_devis_post_query_action(dossier, json.loads(dossier["MontDevis"]), config_dosrac)
        if action == "MONT_ETUD":
            mont_etud_post_query_action(dossier, json.loads(dossier["MontEtud"]), config_dosrac)
        if action == "PLANIFIABLE":
            date_planif_post_query_action(dossier_id, dossier, config_dosrac)
        if base_code_statut == "81" and dossier_auart == "DRE1" and dossier.get("NumDossierSup"):
            # No planification for Elec
            dossier["CodeStatut"] = "85"

        # Set LibInfo if post process changed the CodeStatut
        if base_code_statut != dossier["CodeStatut"] and dossier["CodeStatut"] in lib_status:
            lib_stat = lib_status[dossier["CodeStatut"]]
            dossier["Statut"] = lib_stat.get(environ["LANG"], lib_stat["FR"])  # get correct language, default to FR

        # Drop unwanted data
        dossier.pop("CodeStatut", None)
        dossier.pop("NumEtape", None)
        dossier.pop("AUART", None)
        dossier.pop("ACTION", None)
        dossier.pop("Config", None)
        dossier.pop("MontEtud", None)
        dossier.pop("MontDevis", None)
