import json
import os
from threading import Thread, Lock

from controllers.Controller import Controller
from utils.aws_utils import get_resource_key, load_s3_file
from utils.db_connector import make_connection
from utils.errors import NotFoundError
from utils.parallel import concurrently


class DatabaseWorker(Thread):
    __lock = Lock()

    def __init__(self, result_name, db_credentials, query, params, fetcher, result_dict):
        Thread.__init__(self)
        self.result_name = result_name
        self.db_credentials = db_credentials
        self.query = query
        self.params = params
        self.fetcher = fetcher
        self.result_dict = result_dict

    def run(self):
        result = None
        cnx = None
        curs = None
        print("Connecting to database to run " + self.result_name + " ...")
        try:
            cnx = make_connection(self.db_credentials)
            curs = cnx.cursor()
            curs.execute(self.query, self.params)
            result = self.fetcher(curs)
            curs.close()
            cnx.close()
        except Exception as e:
            if curs:
                curs.close()
            if cnx:
                cnx.close()
            print(e)
        self.result_dict[self.result_name] = result


class ParallelController(Controller):
    def __init__(self, event, linking):
        super().__init__(event, linking)

    def sql_file(self):
        return self.linking["sql_template"]

    def load_sql_file(self, filename_dict):
        """
        return a lst of sql statements
        """
        if not isinstance(filename_dict, dict):
            filename_dict = {"sql_statement": filename_dict}

        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        return {k: load_s3_file(os.environ["MAPPING_BUCKET_NAME"], get_resource_key(filename_dict[k])).format(HanaSchema=env_file["HANA_SCHEMA"]) for k in filename_dict}

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        return sql_statement

    def execute_query(self, sql_statements, request_params):
        exec = super().execute_query
        ret = dict(list(concurrently(*[(lambda k, v: lambda: [k, exec(v, request_params)])(k, v) for k, v in sql_statements.items()])))
        return ret

    def fetch(self, cursor, params=None):
        try:
            return super().fetch(cursor, params)
        except NotFoundError:
            return []

    def to_response(self, fake_cursor, params=None):
        fetch = self.fetch
        data = dict([[k, fetch(v, params)] for k, v in fake_cursor.items()])
        data = self.layer(data, None)
        return data


class ParallelController_Deprecated(Controller):
    def __init__(self, event, linking):
        super().__init__(event, linking)

    def sql_file(self):
        return self.linking["sql_template"]

    def load_sql_file(self, filename_dict):
        """
        return a lst of sql statements
        """
        if not isinstance(filename_dict, dict):
            filename_dict = {"sql_statement": filename_dict}

        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        return {k: load_s3_file(os.environ["MAPPING_BUCKET_NAME"], get_resource_key(filename_dict[k])).format(HanaSchema=env_file["HANA_SCHEMA"]) for k in filename_dict}

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        return sql_statement

    def fetch(self, cursor, params=None):
        """
        return the response body
        """

        data = []
        result = cursor.fetchall()
        for row in result:
            r = self.row_processor(cursor, row)
            data.append(r)
        if len(data) == 1:
            return data[0]
        else:
            return data

    def to_response(self, fake_cursor, params=None):
        return fake_cursor
