import os
from datetime import datetime, timedelta

from controllers.NoPagination import NoPagination
from controllers.parallel_controller import DatabaseWorker
from controllers.ws205 import ws205

from utils.aws_handler_decorator import commune_handler
from utils.aws_utils import get_resource_key, get_secret
from utils.errors import INVALID_EAN_FOR_USER, INVALID_USER_RIGHTS
from utils.models.user import User


class CommuneControllerIndexSmart(ws205):
    """
    Fetch commune-specific smart meter consumption.

    This class inherits from `ws205` and adds commune-specific handling
    for events and requests. It ensures that only authorized users are
    allowed to perform actions based on their roles within a commune.
    """

    @commune_handler(event_args_position=1)
    def __init__(self, event: dict, linking: dict, logged_user: User) -> None:
        super().__init__(event, linking)
        self.logged_user = logged_user
        self.commune_id = logged_user.commune.id
        self.eans = {}

        if not logged_user.commune.admin and "roles" in linking and not any(role in logged_user.commune.roles for role in linking["roles"]):
            raise INVALID_USER_RIGHTS

    def validate_request_params(self) -> dict:
        """Validate request parameters and inject IdCommune."""
        self.eans = self._get_ean_info()
        params = super().validate_request_params()
        params["IdCommune"] = self.commune_id

        return params

    def validate_user_ean(self) -> None:
        """
        Validate if the given EAN belongs to a user and update relevant parameters.

        Raises
        ------
        ForbiddenError
            If the provided EAN does not belong to the user.
        BadRequestError
            If the given EAN is not linked to an energy type.

        """
        if self.params.get("Ean") not in self.eans:
            raise INVALID_EAN_FOR_USER

        for ean, energy in self.eans.items():
            if ean == self.params["Ean"]:
                self.params["Energy"] = energy
                self.params["ContractDate"] = [
                    (
                        (datetime.now() - timedelta(days=5 * 365)).strftime("%Y%m%d"),
                        (datetime.now() + timedelta(days=1)).strftime("%Y%m%d"),
                    ),
                ]
                break

    def _get_ean_info(self) -> dict[str, str]:
        result = {}

        # Fetch ean data and insert it in result["eans"]
        DatabaseWorker(
            "eans",
            get_secret(os.environ["HANA_SECRET"]),
            super(NoPagination, self).load_sql_file(get_resource_key(self.linking["sql_template"]["get_eans"])).format(HanaSchema=os.environ["HANA_SCHEMA"]),
            {"IdCommune": self.commune_id, "Smart": True},
            self.fetch,
            result,
        ).run()

        return {elem["Ean"]: elem["Type"].lower() for elem in result["eans"]} if result.get("eans") else {}
