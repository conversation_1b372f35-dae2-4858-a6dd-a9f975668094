from controllers.Controller import Controller, get_param_value
from utils.errors import BadRequestError


class MasterIn<PERSON>(Controller):
    def __init__(self, event, linking):
        super().__init__(event, linking)

    def fetch(self, cursor, params=None):
        bp_param = self.validate_and_get_bp()
        sql_statement = self.prepare_sql_statement(bp_param)
        cursor = self.execute_query(sql_statement, {"BP": bp_param})
        return super().to_response(cursor, params)

    def validate_and_get_bp(self):
        bp_param_info = self.linking.get("params", {}).get("IdBp", {})
        bp_param = get_param_value(bp_param_info, self.event)
        if bp_param is None:
            raise BadRequestError("BP is missing", error_code="BP_MISSING")
        return bp_param

    def prepare_sql_statement(self, bp_param):
        sql_filename = self.sql_file()
        sql_statement = self.load_sql_file(sql_filename)
        sql_statement = self.apply_hana_env(sql_statement)
        sql_statement = sql_statement.format(bp=bp_param)
        return sql_statement
