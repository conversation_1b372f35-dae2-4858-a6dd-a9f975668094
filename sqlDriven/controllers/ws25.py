import json
import os
import time

from controllers.parallel_controller import Controller
from utils.aws_utils import get_resource_key, load_s3_file
from utils.dict_utils import snake_to_pascal, get
from utils.errors import BadRequestError
from utils.log_utils import LogTime
from utils.type_utils import date_format, int_or_none, time_format


class ws25(Controller):
    accepted_params = {
        "input_1": {"CdPostal", "Localite", "Rue"},
        "input_2": {"IdLocalite", "IdRue"},
        "input_3": {"CdPostal"},
        "input_4": {"IdLocalite", "Rue"},
        "input_5": set(),
        "input_6": {"Lat0", "Long0", "Lat1", "Long1"},
    }

    def __init__(self, event, linking):
        self.zip_code_query = "POST_CODE1 = (:CdPostal)"
        self.event = event
        self.linking = linking
        self.row_processor = self.default_row_processor
        self.result_dict = {}
        self.nb_items = 0

        query_string_params = set(get(event, "queryStringParameters", {}).keys()) - {
            "Page",
            "PageSize",
            "EnCours",
        }
        matched_params = [k for k in ws25.accepted_params if ws25.accepted_params[k] == query_string_params]
        if not matched_params or len(matched_params) > 1:
            raise BadRequestError("Input incorrect. Les possibles inputs à fournir sont:" + str(list(ws25.accepted_params.values())))
        self.input_type = matched_params[0]

    def validate_request_params(self):
        params = super().validate_request_params()

        if "," in get(params, "CdPostal", ""):
            zip_codes = ",".join(f"'{zip_code.strip()}'" for zip_code in params["CdPostal"].split(","))
            self.zip_code_query = f"POST_CODE1 in ({zip_codes})"

        return params

    def sql_file(self):
        return self.linking["sql_template"]

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        return sql_statement

    def load_sql_file(self, sql_file):
        """
        return the sql filename to load
        """

        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        base_statement = load_s3_file(
            os.environ["MAPPING_BUCKET_NAME"],
            get_resource_key(sql_file["base_statement"]),
        )

        print("WS25 Input Type")
        print(self.input_type)

        if self.input_type == "input_1":
            geo_filter = f" WHERE {self.zip_code_query} AND CITY1 = (:Localite) AND STREET = (:Rue) "
            gps_filter = " "
        if self.input_type == "input_2":
            geo_filter = " WHERE CITY_CODE = (:IdLocalite) AND STREETCODE = (:IdRue) "
            gps_filter = " "
        if self.input_type == "input_3":
            geo_filter = f" WHERE {self.zip_code_query}  "
            gps_filter = " "
        if self.input_type == "input_4":
            geo_filter = " WHERE CITY_CODE = (:IdLocalite) AND STREET = (:Rue) "
            gps_filter = " "
        if self.input_type == "input_5":
            geo_filter = " "
            gps_filter = " "
        if self.input_type == "input_6":
            geo_filter = " "
            gps_filter = " WHERE :Lat0 <= GPS_LAT AND GPS_LAT <= :Lat1 AND :Long0 <= GPS_LONG AND GPS_LONG <= :Long1 "

        base_statement = base_statement.format(
            HanaSchema=env_file["HANA_SCHEMA"],
            GisSchema=env_file["GIS_SCHEMA"],
            GpsFilter=gps_filter,
            GeoFilter=geo_filter,
        )

        self.base_statement = base_statement

        return base_statement

    def execute_query(self, sql_statement, request_params):
        page_items = super().getPaginationPageSize()
        page = super().getPaginationPage()
        page_offset = page * page_items
        request_params["PageSize"] = page_items
        request_params["PageOffset"] = page_offset

        return super().execute_query(sql_statement, request_params)

    def fetch(self, cursor, params):
        """
        return the response body
        """

        pageSize = self.getPaginationPageSize()
        data = []

        with LogTime("Fetch query"):
            result = cursor.fetchmany(pageSize)

        for row in result:
            r = self.row_processor(cursor, row)
            data.append(r)
        return data

    def default_row_processor(self, cursor, row):
        """
        return the row as key-value dictionnary
        """
        resource = {}
        for i in range(len(cursor.description)):
            column = cursor.description[i][0]
            resource[snake_to_pascal(column)] = row[i]
        resource["CdPostal"] = int_or_none(resource["CdPostal"])
        date_aux = date_format(resource["DatePanne"], input_format="%Y%m%d", output_format="%d-%m-%Y")
        heure_aux = resource["HeureFin"]
        resource["DatePanne"] = int(resource["DatePanne"])
        resource["HeurePriseCharge"] = time_format(resource["HeurePriseCharge"], input_format="%H%M%S")
        resource["DateFin"] = date_format(
            resource["DateFin"],
            input_format="%Y%m%d",
            output_format="%d-%m-%Y",
            throw=False,
        )
        resource["HeureFin"] = time_format(heure_aux, input_format="%H%M%S", throw=False)
        if resource["DateFin"]:
            start = time.mktime(time.strptime(date_aux + " " + resource["HeurePriseCharge"], "%d-%m-%Y %H:%M:%S"))
            end = time.mktime(
                time.strptime(
                    resource["DateFin"] + " " + resource["HeureFin"],
                    "%d-%m-%Y %H:%M:%S",
                )
            )
            hours, seconds = (end - start) // 3600, (end - start) % 3600
            minutes, seconds = seconds // 60, seconds % 60
            resource["DureePanne"] = str(int(hours)).zfill(2) + ":" + str(int(minutes)).zfill(2)

        resource["Cause"] = get(
            {"01": "Agression par tiers", "02": "Intempéries"},
            resource["CauseCode"],
            "Défaillance(s) technique(s)",
        )
        resource["StatutPanne"] = "RESOLU" if resource["Closed"] else ("DECLARE" if resource["HeurePriseCharge"] and resource["HeurePriseCharge"] == "00:00:00" else "EN_COURS")
        resource["GPS"] = {"Lat": resource["Lat"], "Long": resource["Long"]}
        resource.pop("CauseCode", None)
        resource.pop("Closed", None)
        resource.pop("Lat", None)
        resource.pop("Long", None)

        self.nb_items = resource["NbItems"]
        resource.pop("NbItems", None)

        return resource

    def to_response(self, cursor, params=None):
        data = self.fetch(cursor, params)
        data = self.layer(data, self.nb_items)
        return data
