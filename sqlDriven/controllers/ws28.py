from controllers.Controller import Controller
from utils.errors import BadRequestError
from utils.validation import is_number


class ws28(Controller):
    def __init__(self, event, linking):
        self.event = event
        self.linking = linking
        self.row_processor = self.ean_cpt_processor

    def validate_request_params(self):
        """
        Return NumCompteur with correct left padding
        """
        qs = self.event["queryStringParameters"]
        if "NumCompteur" not in qs:
            raise BadRequestError("Le paramètre NumCompteur est requis")
        num_cpt = qs["NumCompteur"]
        if is_number(num_cpt):
            num_cpt = num_cpt.zfill(18)
        return {"NumCompteur": num_cpt}

    def ean_cpt_processor(self, cursor, row):
        """
        return the row as key-value dictionnary
        Only return Ean
        """
        resource = {}
        for i in range(len(cursor.description)):
            column = cursor.description[i][0]
            if column == "EAN":
                resource["Ean"] = row[i]
        return resource
