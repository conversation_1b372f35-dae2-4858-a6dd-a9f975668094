import time
from threading import Thread, Lock

from controllers.Controller import Controller
from utils.api import basic_auth_headers, api_caller
from utils.auth_utils import getUserData
from utils.dict_utils import merge


def str_time_prop(start, end, format, prop):
    """Get a time at a proportion of a range of two formatted times.

    start and end should be strings specifying times formated in the
    given format (strftime-style), giving an interval [start, end].
    prop specifies how a proportion of the interval to be taken after
    start.  The returned time will be in the specified format.
    """

    stime = time.mktime(time.strptime(start, format))
    etime = time.mktime(time.strptime(end, format))

    ptime = stime + prop * (etime - stime)

    return time.strftime(format, time.localtime(ptime))


def random_date(start, end, prop):
    return str_time_prop(start, end, "%d/%m/%Y", prop)


mock = [
    # {
    #     "Id": '2IOPP0292',
    #     "Email": '<EMAIL>',
    #     "Name": "Romane Jetpack",
    #     "FournisseurId": "30004"
    # },
    # {
    #     "Id": 'UIOPP12312',
    #     "Email": '<EMAIL>',
    #     "Name": "Romane II Jetpack",
    #     "FournisseurId": "30005"
    # },
    # {
    #     "Id": '8SNJKC21802',
    #     "Email": '<EMAIL>',
    #     "Name": "Henri & Fils",
    #     "FournisseurId": "30006"
    # }, {
    #     "Id": 'UIOPHJDNFLP12',
    #     "Email": '<EMAIL>',
    #     "Name": "Pierre & Fils",
    #     "FournisseurId": "30007"
    # }, {
    #     "Id": 'T4JDI38SD5Z',
    #     "Email": '<EMAIL>',
    #     "Name": "NeverLogin&Co",
    #     "FournisseurId": "30009"
    # },
    {
        "Id": "0000023889",
        "Email": "<EMAIL>",
        "Name": "Test01",
        "FournisseurId": "30010",
    }
    # {
    #     "Id": 'TEST02',
    #     "Email": '<EMAIL>',
    #     "Name": "Test02",
    #     "FournisseurId": "30011"
    # }, {
    #     "Id": 'TEST03',
    #     "Email": '<EMAIL>',
    #     "Name": "Test03",
    #     "FournisseurId": "30012"
    # }, {
    #     "Id": 'TEST04',
    #     "Email": '<EMAIL>',
    #     "Name": "Test04",
    #     "FournisseurId": "30013"
    # }, {
    #     "Id": 'TEST05',
    #     "Email": '<EMAIL>',
    #     "Name": "Test05",
    #     "FournisseurId": "30014"
    # }, {
    #     "Id": 'TEST06',
    #     "Email": '<EMAIL>',
    #     "Name": "Test06",
    #     "FournisseurId": "30015"
    # }, {
    #     "Id": 'TEST07',
    #     "Email": '<EMAIL>',
    #     "Name": "Test07",
    #     "FournisseurId": "30016"
    # }, {
    #     "Id": 'TEST08',
    #     "Email": '<EMAIL>',
    #     "Name": "Test08",
    #     "FournisseurId": "30017"
    # }, {
    #     "Id": '3000316',
    #     "Email": '<EMAIL>',
    #     "Name": "Test3000316",
    #     "FournisseurId": "3000316"
    # }, {
    #     "Id": '3000050',
    #     "Email": '<EMAIL>',
    #     "Name": "Test3000050",
    #     "FournisseurId": "3000050"
    # }, {
    #     "Id": '3000178',
    #     "Email": '<EMAIL>',
    #     "Name": "Test3000178",
    #     "FournisseurId": "3000178"
    # }
]


class Entrepreneurs(Controller):
    def fetch(self, cursor, params):
        """
        return the response body
        """
        data = []
        result = super().fetch(cursor, params)
        threads = [AsyncProcessor(self, row, data) for row in (mock + result)]
        [thread.join() for thread in threads]
        data.sort(key=lambda x: x.get("Id"))
        return data

    def validate_request_params(self):
        ret = super().validate_request_params()
        user_data = getUserData(self.event, allow_ghost=False, require_permission="admin")
        # ret['EntrepreneurId'] = user_data['entrepreneur_id']
        return ret


class AsyncProcessor(Thread):
    __lock = Lock()

    def __init__(self, controller, row, ret):
        Thread.__init__(self)
        self.controller = controller
        self.row = row
        self.ret = ret
        self.start()

    def run(self):
        try:
            entrepreneur = api_caller(
                "get",
                "/powalco/entrepreneurs/{}".format(self.row["Id"]),
                {},
                None,
                AsyncProcessor.authHeaders,
            )
            entrepreneur = merge(self.row, entrepreneur)
        except Exception:
            entrepreneur = {
                "Id": self.row["Id"],
                "Email": self.row["Email"],
                "Status": "sans compte",  # self.row['Status'],
                "Name": self.row["Name"],
                "FournisseurId": self.row["FournisseurId"],
                "Firstname": None,
                "Lastname": None,
                "CreationDate": None,
                "LastActivityDate": None,
                "DeactivationDate": None,
            }
        self.ret.append(entrepreneur)


AsyncProcessor.authHeaders = basic_auth_headers()
