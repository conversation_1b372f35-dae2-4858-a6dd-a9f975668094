from os import environ
from typing import Optional, Self

from controllers.NoPagination import NoPagination

from utils.dict_utils import get
from utils.errors import BadRequestError, UnauthorizedError
from utils.models.address import ADDRESS_EXCLUDE, Address
from utils.models.ean_meter_info import EanAddressInfo, EanMeters, Meter
from utils.models.pydantic_utils import PascalModel
from utils.models.user import User
from utils.u_codes import UCode


class WS200Input(PascalModel):
    """
    Represent the input data for the WS200.

    Attributes
    ----------
    ean : Optional[str]
        The EAN associated with the meter, or None if not provided.
    meter_id : Optional[str]
        The unique identifier for the meter, or None if not provided.
    user : Optional[User]
        The user information object containing authentication details, or None
        if the user is not authenticated.

    """

    ean: Optional[str] = None
    meter_id: Optional[str] = None
    user: Optional[User] = None

    @classmethod
    def from_event(cls, event: dict) -> Self:
        """
        Create an instance of the class from an event dictionary.

        Parameters
        ----------
        event : dict
            The event payload.

        Returns
        -------
        WS200Input
            An initialized instance of the class.

        Raises
        ------
        UnauthorizedError
            If the user authentication token is invalid.

        """
        obj: Self = cls.model_validate(get(event, "queryStringParameters", {}))

        try:
            # Only set user if no ean + meter_id provided.
            if not (obj.ean or obj.meter_id):
                obj.user = User.from_event(event, allow_ghost=False)
        except UnauthorizedError as ex:
            if ex.error_code == "INVALID_TOKEN":
                raise

        return obj


class ws200(NoPagination):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.input = WS200Input.from_event(event)

    def validate_request_params(self):
        input_params = super().validate_request_params()

        if not self.input.user and (not self.input.ean or not self.input.meter_id):
            raise BadRequestError(
                "Missing mandatory query string when non auth: [Ean, MeterId]",
                error_code="MISSING_PARAMETERS",
            )

        return input_params

    def apply_hana_env(self, sql_statement):
        """Specifies the Hana schema for Hana SQL environments."""
        ean_list = "'" + "','".join(self.input.user.ean_ids) + "'" if self.input.user else "'" + self.input.ean + "'"
        return sql_statement.format(HanaSchema=environ["HANA_SCHEMA"], Ean=ean_list)

    def to_response(self, cursor, params=None):
        db_result = super().to_response(cursor, params)

        if not self.input.user:
            counter_ids = [row["Meter_Number"].zfill(18) for row in db_result]
            if self.input.meter_id.zfill(18) not in counter_ids:
                raise BadRequestError(
                    "The given Ean doesn't match the MeterId",
                    error_code="INCORRECT_PARAMETERS",
                )

        # from db_result, group data by adress then by ean
        grouped_db_result = {}
        for row in db_result:
            address = Address(
                street=row["Address_Street"],
                number=row["Address_Number"],
                postcode=row["Address_Poscode"],
                city=row["Address_City"],
                box=ADDRESS_EXCLUDE,
                country=ADDRESS_EXCLUDE,
            )
            meter = Meter(
                type="elec" if row["Meter_Type"] == "01" else "gaz",
                number=row["Meter_Number"],
                power=row["Meter_Power"],
                amper=row["Meter_Amper"],
                tarif=UCode(row["Meter_Tarif"] if row["Meter_Type"] == "01" else "Gaz").desc,
                production=row["Meter_Production"],
                smart=row["Meter_Smart"],
                rate=row["Meter_Rate"],
                phase=row["Meter_Phase"],
                phase_type=row["Meter_PhaseType"],
            )
            if address not in grouped_db_result:
                grouped_db_result[address] = {}
            if row["Ean"] not in grouped_db_result[address]:
                grouped_db_result[address][row["Ean"]] = EanMeters(row["Ean"])
            grouped_db_result[address][row["Ean"]].meters.append(meter)

        return [EanAddressInfo(address, eans.values()).to_dict() for address, eans in grouped_db_result.items()]
