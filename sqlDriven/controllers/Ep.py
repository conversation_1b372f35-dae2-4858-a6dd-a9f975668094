from threading import Thread, Lock

from controllers.Controller import Controller
from utils.dict_utils import snake_to_pascal, get
from utils.ep_utils import lambert_to_wgs86, wgs86_to_lambert
from utils.log_utils import LogTime
from utils.type_utils import date_format, int_format, time_format


class Ep(Controller):
    def __init__(self, event, linking):
        self.event = event
        self.linking = linking
        self.row_processor = self.ep_processor

    def headers(self):
        headers = super().headers()
        headers["Cache-Control"] = "no-cache"
        return headers

    def validate_request_params(self):
        ret = super().validate_request_params()

        page_items = self.getPaginationPageSize()
        page = self.getPaginationPage()
        page_offset = page * page_items

        if get(ret, "Lat0", default_on_empty=True) and get(ret, "Long0", default_on_empty=True):
            ret["Lat0"], ret["Long0"] = wgs86_to_lambert(ret["Lat0"], ret["Long0"])
        if get(ret, "Lat1", default_on_empty=True) and get(ret, "Long1", default_on_empty=True):
            ret["Lat1"], ret["Long1"] = wgs86_to_lambert(ret["Lat1"], ret["Long1"])

        ret["PageSize"] = page_items
        ret["Offset"] = page_offset
        return ret

    def load_sql_file(self, sql_file):
        """
        return the sql filename to load
        """

        base_statement = super().load_sql_file(sql_file)
        page_items = super().getPaginationPageSize()
        page = super().getPaginationPage()
        page_offset = page * page_items
        paginated_statement = base_statement.replace("(:PageItems)", str(page_items)).replace("(:PageOffset)", str(page_offset))

        filter_by_statut = "queryStringParameters" in self.event and "FiltreEnPanne" in get(self.event, "queryStringParameters", {})

        base_clause = """NbAvis = 0 
        OR (NbAvis > 0  AND date_creation = MaxDateAvis AND StatutTimestamp = MaxStatutTimestamp AND avis_id = MaxAvisId AND (id_statut = 'I0070' OR id_statut = 'I0001') AND order_date IS NULL) 
        OR (NbAvis > 0  AND date_creation = MaxDateAvis AND StatutTimestamp = MaxStatutTimestamp AND avis_id = MaxAvisId AND ((id_statut != 'I0070') AND (id_statut != 'I0001') OR order_date IS NOT NULL))"""

        only_panne_clause = "(NbAvis > 0  AND date_creation = MaxDateAvis AND StatutTimestamp = MaxStatutTimestamp AND avis_id = MaxAvisId AND (id_statut IS NOT NULL AND id_statut != '' AND id_statut != 'I0072') AND order_date IS NULL)"

        only_no_panne_clause = "(NbAvis > 0  AND date_creation = MaxDateAvis AND StatutTimestamp = MaxStatutTimestamp AND avis_id = MaxAvisId AND NOT(id_statut IS NOT NULL AND id_statut != '' AND id_statut != 'I0072') OR order_date IS NOT NULL))"

        sql_statement = ""
        if not filter_by_statut:
            sql_statement = paginated_statement.replace("(:FiltreEnPanneClause)", base_clause)
        elif filter_by_statut and self.event["queryStringParameters"]["FiltreEnPanne"] in ["true", "True", 1, "1"]:
            sql_statement = paginated_statement.replace("(:FiltreEnPanneClause)", only_panne_clause)
        elif filter_by_statut and self.event["queryStringParameters"]["FiltreEnPanne"] in ["false", "False", 0, "0"]:
            sql_statement = paginated_statement.replace("(:FiltreEnPanneClause)", only_no_panne_clause)

        return sql_statement

    def fetch(self, cursor, params):
        """
        return the response body
        """

        page = self.getPaginationPage()
        pageSize = self.getPaginationPageSize()
        data = []

        with LogTime("Fetch query"):
            result = cursor.fetchmany(pageSize)
            print(len(result), "results")

        with LogTime("Data processing"):
            threads = [AsyncProcessor(self, (cursor, row, params), data) for row in result]
            [thread.join() for thread in threads]
            data.sort(key=lambda x: x.get("Id"))

        return data

    def layer(self, data, nbItems):
        return super().layer(data, self.nb_items)

    def ep_processor(self, cursor, row, params):
        """
        return the row as key-value dictionnary
        processes belgian lambert coordinates into standard Srid
        """
        resource = {}
        for i in range(len(cursor.description)):
            column = cursor.description[i][0]
            resource[snake_to_pascal(column.lower())] = row[i]
        self.nb_items = resource["NbItems"]
        result = self.to_ep_schema(resource)
        return result

    def to_ep_schema(self, resource):
        if get(resource, "IdStatut") in ["I0072"]:
            date_statut = resource["DateStatut"]
            date_cloture = date_statut
        elif resource["OrderDate"]:
            date_statut = resource["OrderDate"]
            date_cloture = date_statut
            resource["IdStatut"] = "I0072"
        else:
            date_statut = resource["DateStatut"]
            date_cloture = None

        date_statut_formated = date_format(date_statut, "%Y%m%d")
        date_cloture_formated = date_format(date_cloture, "%Y%m%d") if date_cloture else None

        if resource["Lat"] and resource["Long"]:
            resource["Lat"], resource["Long"] = lambert_to_wgs86(resource["Lat"], resource["Long"])

        result = {
            "Id": resource["Id"],
            "SousType": resource["SousType"],
            "Type": resource["Type"],
            "IlluminationType": resource["IlluminationType"],
            "Adresse": {
                "Rue": resource["Rue"],
                "Zipcode": int_format(resource["Zipcode"]),
                "Ville": resource["Ville"],
                "Numero": resource["Numero"],
                "CityCode": resource["CityCode"],
                "Lat": resource["Lat"],
                "Long": resource["Long"],
            },
            "Panne": None,
        }

        if resource["IdStatut"] is not None:
            result["Panne"] = {
                "Id": str(get(resource, "Maxavisid", "")),
                "DateCreation": date_format(resource["DateCreation"], "%Y%m%d"),
                "DateCloture": date_cloture_formated,
                "DernierStatut": {
                    "Id": resource["IdStatut"],
                    "Descr": resource["Descr"],
                    "ShortDescr": resource["ShortDescr"],
                    "Date": date_statut_formated,
                    "Heure": time_format(resource["HeureStatut"], "%H%M%S"),
                },
            }

        return result


class AsyncProcessor(Thread):
    __lock = Lock()

    def __init__(self, controller, params, ret):
        Thread.__init__(self)
        self.controller = controller
        self.params = params
        self.ret = ret
        self.start()

    def run(self):
        result = self.controller.ep_processor(*self.params)
        self.ret.append(result)
