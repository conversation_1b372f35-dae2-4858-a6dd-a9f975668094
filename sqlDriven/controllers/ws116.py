import functools
from os import environ
from uuid import uuid4

from controllers.Controller import Controller
from utils.auth_utils import getUserData
from utils.aws_utils import upload_s3_file, create_s3_presigned_url
from utils.dict_utils import flatten_nested_list
from utils.errors import ForbiddenError
from utils.log_utils import log_err
from utils.parallel import concurrently
from utils.sharepoint import SharepointHandler
from utils.userdata import Dossier

TMP_BUCKET = f"{environ['BUCKET']}-temp-storage"


def cache_document(valise_url, document_name):
    try:
        file_data = SharepointHandler.download_valise_file_from_sharepoint(f"{valise_url}/{document_name}")

        if file_data:
            tmp_file = f"ws39/{uuid4()}.{document_name}"
            upload_s3_file(TMP_BUCKET, tmp_file, file_data)
            return create_s3_presigned_url(TMP_BUCKET, tmp_file)
    except Exception as e:
        log_err(e)
        return None


class ws116(Controller):
    def assert_user_own_dossier(self, numDossier):
        user = getUserData(self.event, allow_ghost=True)
        dossiers = [str(dossier["id"]) for dossier in Dossier(user).get()]
        if str(numDossier) not in dossiers:
            raise ForbiddenError("Vous n'avez pas accès à ce dossier")

    def validate_request_params(self):
        ret = super().validate_request_params()
        self.assert_user_own_dossier(ret["NumDossier"])
        ret["SharepointEnv"] = SharepointHandler.secret()["env"]
        return ret

    def layer(self, data, nbItems):
        typeFilter = self.event.get("queryStringParameters", {}).get("Type", None)

        def valise_worker(valise, valise_url):
            files = SharepointHandler.list_valise_file_from_sharepoint(valise, typeFilter)
            lambdas = []
            for file in files:
                lambdas.append(
                    functools.partial(
                        lambda _valise_url, _file: {
                            "Document": _file,
                            "Url": cache_document(_valise_url, _file),
                        },
                        valise_url,
                        file,
                    )
                )
            return list(concurrently(*lambdas))

        workers = list(concurrently(*[lambda: valise_worker(d["VALISE_UID"], d["URL"]) for d in data]))
        return flatten_nested_list(workers)
