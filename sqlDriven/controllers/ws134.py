from os import environ

from controllers.parallel_controller import (
    ParallelController_Deprecated,
    DatabaseWorker,
)
from controllers.ws59 import ws59
from utils.aws_handler_decorator import commune_handler
from utils.aws_utils import get_secret, get_resource_key
from utils.errors import NotFoundError
from utils.models.user import User
from utils.type_utils import to_list


@commune_handler(roles=["RACC_CONSU", "RACC_GERER"])
class ws134(ws59):
    def __init__(self, event, linking, logged_user: User):
        super().__init__(event, linking)
        self.input_type = "NumDossier"
        self.num_dossier = None
        self.result_dict = {}
        base_controller = super(ParallelController_Deprecated, self)
        self.dossiers_thread = DatabaseWorker(
            "dossiers",
            get_secret(self.get_secret()),
            base_controller.apply_hana_env(base_controller.load_sql_file(get_resource_key(self.linking["sql_template"]["get_dossiers"]))),
            {"IdCommune": logged_user.commune.id, "Langue": environ["LANG"]},
            self.fetch,
            self.result_dict,
        )

    def validate_request_params(self):
        params = super(ParallelController_Deprecated, self).validate_request_params()
        self.num_dossier = params["Id"].zfill(12)
        return {"NumDossier": [self.num_dossier], "ListeCodeLangue": [environ["LANG"]]}

    def execute_query(self, sql_statement, request_params):
        self.dossiers_thread.start()
        return super().execute_query(sql_statement, request_params)

    def to_response(self, fake_cursor, params=None):
        self.dossiers_thread.join()
        num_dossiers = {item["Reference"].zfill(12) for item in to_list(self.result_dict["dossiers"])}
        if self.num_dossier not in num_dossiers:
            raise NotFoundError

        response = super().to_response(fake_cursor, params)
        try:
            return response["Liste"][0]
        except IndexError:
            raise NotFoundError
