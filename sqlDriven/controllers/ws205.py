import os
from datetime import datetime, timedelta
from typing import ClassVar

import pytz
from controllers.NoPagination import NoPagination
from dateutil.relativedelta import relativedelta

from utils.aws_utils import get_resource_key
from utils.dict_utils import get, keydefaultdict
from utils.errors import INVALID_EAN_FOR_USER, BadRequestError, ForbiddenError
from utils.models.user import User


def get_valid_date_ranges_from_params(params: dict) -> list:
    energy = params.get("Energy")
    belgium_tz = pytz.timezone("Europe/Brussels")

    start_date = datetime.fromisoformat(params["StartDate"])
    end_date = datetime.fromisoformat(params["EndDate"])

    # In case of gaz, set the data date to 6h
    if energy == "gaz":
        start_date = start_date.replace(hour=6)
        end_date = end_date.replace(hour=6)

    valid_date_ranges = []

    for _contract_start, _contract_end in params["ContractDate"]:
        contract_start = datetime.strptime(_contract_start, "%Y%m%d")
        contract_end = datetime.strptime(_contract_end, "%Y%m%d")

        valid_start = max(start_date, contract_start)
        valid_end = min(end_date, contract_end)

        if valid_start <= valid_end:
            valid_date_ranges.append((belgium_tz.localize(valid_start), belgium_tz.localize(valid_end)))

    return valid_date_ranges if valid_date_ranges else []


class ws205(NoPagination):
    DICT_TYPE_TO_TABLE: ClassVar[dict] = {
        "elec": {
            "HOURLY": "public.cumulativeactiveenergyp15m",
            "DAILY": "public.cumulativeactiveenergyp1d",
            "MONTHLY": "public.cumulativeactiveenergyp1d",
        },
        "gaz": {
            "HOURLY": "public.cumulativestoredvolumep1h",
            "DAILY": "public.cumulativestoredvolumep1d",
            "MONTHLY": "public.cumulativestoredvolumep1d",
        },
    }

    CSV_METER_NAME: ClassVar[dict] = {
        "FR": "Compteur communicant",
        "DE": "Kommunikationsfähiger Zähler",
    }
    CSV_HEADERS: ClassVar[dict] = {
        "FR": ["Du (date)", "De (heure)", "Au (date)", "À (heure)", "Code EAN", "Compteur", "Type de compteur", "Registre", "Volume", "Unité", "Statut de validation"],
        "DE": ["Vom (Datum)", "Um (Uhrzeit)", "Bis zum (Datum)", "Um (Uhrzeit)", "EAN-Code", "Zähler", "Zählertyp", "Register", "Menge", "Einheit", "Validierungsstatus"],
    }
    CSV_REGISTRY_MAP: ClassVar[dict] = {
        "FR": keydefaultdict(
            lambda x: x,  # return key as value if missing
            {
                "1.8.0": "Prélèvement Total",
                "1.8.1": "Prélèvement Jour",
                "1.8.2": "Prélèvement Nuit",
                "2.8.0": "Injection Total",
                "2.8.1": "Injection Jour",
                "2.8.2": "Injection Nuit",
            },
        ),
        "DE": keydefaultdict(
            lambda x: x,  # return key as value if missing
            {
                "1.8.0": "Entnahme gesamt",
                "1.8.1": "Entnahme Tag",
                "1.8.2": "Entnahme Nacht",
                "2.8.0": "Einspeisung gesamt",
                "2.8.1": "Einspeisung Tag",
                "2.8.2": "Einspeisung Nacht",
            },
        ),
    }
    CSV_STATUS_MAP: ClassVar[dict] = {
        "FR": keydefaultdict(
            lambda x: x,  # return key as value if missing
            {
                "valide": "Lu",
                "estimated": "Estimé",
                "edited": "Corrigé",
                "": "Pas de consommation",
                None: "Pas de consommation",
            },
        ),
        "DE": keydefaultdict(
            lambda x: x,  # return key as value if missing
            {
                "valide": "Abgelesen",
                "estimated": "Geschätzt",
                "edited": "Berichtigt",
                "": "Kein Verbrauch",
                None: "Kein Verbrauch",
            },
        ),
    }

    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.params = super().validate_request_params()
        self.type_params = get(self.params, "Type", "MONTHLY")
        self.belgium_tz = pytz.timezone("Europe/Brussels")

    def headers(self):
        header = super().headers()
        accept = get(self.event, "headers", {}).get("Accept")
        if accept == "text/csv":
            header["Content-Type"] = accept
            header["Content-Disposition"] = "attachment; filename=data.csv"
        return header

    def process_data_response(self, data):
        previous_row = {}
        for row in data:
            localized_time = self.belgium_tz.localize(row["measuredatetime"]) if row["measuredatetime"].tzinfo is None else row["measuredatetime"].astimezone(self.belgium_tz)
            row["measuredatetime"] = localized_time.isoformat()

            group_key = (row["ean"], row["meterid"], row["readingfrequency"], row["readingtypeid"])
            if group_key in previous_row:
                row["consumption"] = round(row["measurevalue"] - previous_row[group_key]["measurevalue"], 3)
                row["measuredatetime_from"] = previous_row[group_key]["measuredatetime"]
            else:
                row["consumption"] = None
                row["measuredatetime_from"] = None
            previous_row[group_key] = row

        # Filter first value without consumption
        data = [elem for elem in data if elem["consumption"] is not None]

        # Generate all expected time slots
        completed_data = []
        reading_type_ids = sorted({x["readingtypeid"] for x in data})
        time_slots = self._get_time_slots()

        for reading_type_id in reading_type_ids:
            for slot_start, slot_end in time_slots:
                for elem in data:
                    if elem["readingtypeid"] == reading_type_id and slot_start <= elem["measuredatetime_from"] < slot_end:
                        completed_data.append(elem)
                        break
                else:
                    completed_data.append(self._create_empty_record(data[0], reading_type_id, slot_start, slot_end))

        return completed_data

    def _get_time_slots(self) -> list[tuple[str, str]]:
        """Generate all time slots between start and end dates based on record type."""
        start_date = datetime.fromisoformat(self.params["StartDate"])
        end_date = datetime.fromisoformat(self.params["EndDate"]) + relativedelta(days=1)

        if self.type_params == "HOURLY":
            delta = timedelta(minutes=15) if self.params.get("Energy") == "elec" else timedelta(hours=1)
        elif self.type_params == "DAILY":
            delta = timedelta(days=1)
        else:  # MONTHLY
            delta = relativedelta(months=1)

        slots = []
        current = start_date
        while current < end_date:
            next_slot = min((current + delta).replace(day=1), end_date) if self.type_params == "MONTHLY" else min(current + delta, end_date)
            slots.append((self.belgium_tz.localize(current).isoformat(), self.belgium_tz.localize(next_slot).isoformat()))
            current = next_slot

        return slots

    def _create_empty_record(self, template: dict, reading_type_id: str, start_time: str, end_time: str) -> dict:
        """Create an empty consumption record based on template."""
        return {
            "meterid": template["meterid"],
            "measuredatetime": end_time,
            "measuredatetime_from": start_time,
            "measurevalue": None,
            "readingtypeid": reading_type_id,
            "measurestate": None,
            "ean": template["ean"],
            "readingfrequency": template["readingfrequency"],
            "standardizationtimestamp": None,
            "measureunit": template["measureunit"],
            "consumption": None,
        }

    def data_to_csv(self, processed_data: list[dict]) -> str:
        """
        Converts processed data into a CSV-formatted string.

        Returns
        -------
        str
            A CSV-formatted string containing the processed data with semicolon (`;`) delimiter.
            Each row in the string corresponds to a data entry with headers included as the
            first row.

        """
        csv_rows = [self.CSV_HEADERS[os.environ["LANG"]]]
        for row in processed_data:
            from_date_str = from_time_str = to_date_str = to_time_str = ""
            if "measuredatetime_from" in row:
                from_date = datetime.fromisoformat(row["measuredatetime_from"])
                from_date_str = from_date.strftime("%d-%m-%y")
                from_time_str = from_date.strftime("%H:%M:%S")
            if "measuredatetime" in row:
                to_date = datetime.fromisoformat(row["measuredatetime"])
                to_date_str = to_date.strftime("%d-%m-%y")
                to_time_str = to_date.strftime("%H:%M:%S")
            csv_rows.append(
                [
                    from_date_str,
                    from_time_str,
                    to_date_str,
                    to_time_str,
                    row.get("ean", ""),
                    row.get("meterid", ""),
                    self.CSV_METER_NAME[os.environ["LANG"]],
                    self.CSV_REGISTRY_MAP[os.environ["LANG"]][row.get("readingtypeid", "")],
                    row.get("consumption", ""),
                    row.get("measureunit", ""),
                    self.CSV_STATUS_MAP[os.environ["LANG"]][row.get("measurestate", "")],
                ],
            )
        return "\n".join([";".join(map(str, row)) for row in csv_rows])

    def data_to_response(self, processed_data: list[dict]) -> list[dict]:
        """
        Converts a list of processed data dictionaries into a list of PascalCase formatted response.

        Returns
        -------
        list[dict]
            A list of dictionaries where the keys are in PascalCase format, containing processed data.

        """
        return [
            {
                "MeterId": data.get("meterid"),
                "MeasureDateTime": data.get("measuredatetime"),
                "ConsumptionStartFrame": data.get("measuredatetime_from"),
                "MeasureValue": data.get("measurevalue"),
                "ReadingTypeId": data.get("readingtypeid"),
                "MeasureState": data.get("measurestate"),
                "Ean": data.get("ean"),
                "ReadingFrequency": data.get("readingfrequency"),
                "StandardizationTimestamp": data.get("standardizationtimestamp"),
                "MeasureUnit": data.get("measureunit"),
                "Consumption": data.get("consumption"),
            }
            for data in processed_data
        ]

    def to_response(self, cursor, params=None):
        data: list = super().to_response(cursor, params)
        processed_data = self.process_data_response(data)
        header = self.headers()
        if header["Content-Type"] == "text/csv":
            self.raw_response = True
            return self.data_to_csv(processed_data)
        else:
            return self.data_to_response(processed_data)

    def validate_request_params(self):
        accepted_type_list = ["MONTHLY", "DAILY", "HOURLY"]
        start_date = self.params["StartDate"]
        end_date = self.params["EndDate"]

        if self.type_params not in accepted_type_list:
            raise BadRequestError(message=f"Type params should be in {accepted_type_list}", error_code="TYPE_NOT_RECOGNIZED")

        try:
            start_date = datetime.fromisoformat(start_date)
            end_date = datetime.fromisoformat(end_date)
        except ValueError:
            raise BadRequestError(message="Date must be in ISO 8601 format", error_code="DATE_NOT_ISO8601")

        if start_date > end_date:
            raise BadRequestError(message="StartDate must be earlier than EndDate", error_code="INVALID_DATE_RANGE")

        if self.type_params == "HOURLY" and start_date.date() != end_date.date():
            raise BadRequestError(message="For HOURLY type, StartDate and EndDate must be the same day", error_code="HOURLY_INVALID_DATE_RANGE")

        self.validate_user_ean()

        return self.params

    def validate_user_ean(self) -> None:
        """
        Validate if the given EAN belongs to a user and update relevant parameters.

        Raises
        ------
        ForbiddenError
            If the provided EAN does not belong to the user.
        BadRequestError
            If the given EAN is not linked to an energy type.

        """
        user = User.from_event(self.event, allow_ghost=False)
        if str(self.params.get("Ean")) not in user.ean_ids:
            raise INVALID_EAN_FOR_USER
        for ean_obj in user.ean:
            if ean_obj.ean == self.params["Ean"]:
                self.params["Energy"] = ean_obj.energy
                self.params["ContractDate"] = []
                for contract in ean_obj.contract:
                    self.params["ContractDate"].append((contract.ctr_from, contract.ctr_to))
                break
        else:
            raise BadRequestError(message="This EAN has no Energy type linked to it", error_code="ENERGY TYPE NOT FOUND")

    def sql_file(self):
        """Return the sql filename to load"""
        return {k: get_resource_key(v) for k, v in self.linking["sql_template"].items()}

    def load_sql_file(self, filename):
        if self.type_params == "MONTHLY":
            return super().load_sql_file(filename["monthly"])
        else:
            return super().load_sql_file(filename["daily"])

    def build_query(self, sql_statement):
        valid_ranges = get_valid_date_ranges_from_params(self.params)
        if not valid_ranges:
            raise ForbiddenError("The user's contract has no access to the asked date", error_code="CONTRACT_HAS_NO_ACCESS")

        if self.type_params == "MONTHLY":
            monthly_dates = set()
            for start, end in valid_ranges:
                current = start
                i = 1
                while current <= end:
                    monthly_dates.add(current)
                    current = start + relativedelta(months=i)
                    # The first start date is fixed by the valid_range (contracts) but the next one should be reset to the first date of the month
                    if current.day != 1:
                        current = current.replace(day=1)
                    # Preserve time zone when shifting date
                    current = self.belgium_tz.localize(current.replace(tzinfo=None))
                    i += 1
                    if current > end:
                        current = end + relativedelta(days=1)
                        monthly_dates.add(current)
                        break

            date_conditions = []
            for idx, date in enumerate(sorted(monthly_dates)):
                if idx + 1 < len(monthly_dates):
                    next_date = sorted(monthly_dates)[idx + 1]
                    date_conditions.append(
                        f"(SELECT min(measuredatetime) AS measure_date FROM ean_data WHERE measuredatetime BETWEEN '{date.strftime('%Y-%m-%d %H:%M:%S %z')}' "
                        f"AND '{next_date.strftime('%Y-%m-%d %H:%M:%S %z')}')",
                    )
                if idx == len(monthly_dates) - 1:
                    previous_date = sorted(monthly_dates)[idx - 1] if idx > 0 else sorted(monthly_dates)[0]
                    date_conditions.append(
                        f"(SELECT max(measuredatetime) AS measure_date FROM ean_data WHERE measuredatetime BETWEEN '{previous_date.strftime('%Y-%m-%d %H:%M:%S %z')}' "
                        f"AND '{date.strftime('%Y-%m-%d %H:%M:%S %z')}')",
                    )
            dynamic_where_clause = "\n  union".join(date_conditions)
        else:
            date_conditions = [
                (f"(measureDateTime BETWEEN '{start.strftime('%Y-%m-%d %H:%M:%S %z')}' AND '{(end + relativedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S %z')}')")
                for start, end in valid_ranges
            ]
            dynamic_where_clause = "\n  AND (" + " OR ".join(date_conditions) + ")"
        return sql_statement.format(dynamic_clause=dynamic_where_clause)

    def execute_query(self, sql_statement, request_params):
        return super().execute_query(self.build_query(sql_statement), request_params)

    def apply_hana_env(self, sql_statement):
        energy_params = get(self.params, "Energy", "elec")
        red_table = self.DICT_TYPE_TO_TABLE[energy_params][self.type_params]

        # Set the table, but keep the dynamic_clause
        return sql_statement.format(red_table=red_table, dynamic_clause="{dynamic_clause}")
