import json
import os
import traceback

from controllers.Controller import Controller
from utils.api import api_caller
from utils.aws_utils import get_secret, load_s3_file, get_resource_key
from utils.db_connector import make_connection
from utils.dict_utils import snake_to_pascal
from utils.errors import BadRequestError
from utils.log_utils import log_info
from utils.validation import validateAdresse


class DistCourte(Controller):
    def __init__(self, event, linking):
        self.event = event
        self.linking = linking
        self.row_processor = self.custom_row_processor

    def sql_file(self):
        """
        return the sql filename to load
        """
        return get_resource_key(self.linking["sql_template"]["get_dist_courte"])

    def validate_request_params(self):
        ret = super().validate_request_params()
        print(ret)
        ret["Source"] = json.dumps(self.verify_adress(json.loads(ret["Source"])))
        ret["Destinations"] = json.dumps([self.verify_adress(adress) for adress in json.loads(ret["Destinations"])])
        return ret

    def verify_adress(self, adress):
        try:
            validateAdresse(adress, check_localite=False)
        except BadRequestError as e:
            raise BadRequestError("Invalid adress format `{}` : {}".format(json.dumps(adress), e.message))
        call = api_caller(method="post", path="/adresse/match", body=json.dumps(adress))
        try:
            print(call)
            return call["Data"][0]["Adresse"]
        except Exception:
            raise BadRequestError('Invalid adress "{}"'.format(json.dumps(adress)))

    def custom_row_processor(self, cursor, row):
        """
        return the row as key-value dictionnary
        """
        resource = {}
        env = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        hana_cursor = open_hana_connection(env)
        sql_filename = get_resource_key(self.linking["sql_template"]["ws71_enrich_adress"])
        sql_statement = load_s3_file(os.environ["MAPPING_BUCKET_NAME"], sql_filename).format(HanaSchema=env["HANA_SCHEMA"])
        for i in range(len(cursor.description)):
            column = cursor.description[i][0]
            resource[column] = row[i]
            resource = enrich_resource(hana_cursor, resource, sql_statement)
        hana_cursor.close()
        return resource


def enrich_resource(hana_cursor, resource, sql_statement):
    if not resource or "Adresse" not in resource or not all(k in resource["Adresse"] for k in ["Rue", "Localite", "Cdpostal"]):
        return resource
    adresse = resource["Adresse"]
    try:
        hana_cursor.execute(
            sql_statement,
            {
                "InputStreet": adresse["Rue"],
                "InputCity": adresse["Localite"],
                "InputCdpostal": adresse["Cdpostal"],
            },
        )
    except Exception as e:
        log_info("The request failed")
        log_info(e)
        track = traceback.format_exc()
        print(track)
        raise e

    hana_result = hana_cursor.fetchone()
    if not hana_result:
        hana_resource = {"IdRadRue": None, "IdRadLocalite": None}
    else:
        hana_resource = {}
        for i in range(len(hana_cursor.description)):
            column = hana_cursor.description[i][0]
            hana_resource[snake_to_pascal(column)] = hana_result[i]
    resource["Adresse"] = {**adresse, **hana_resource}
    return resource


def open_hana_connection(env):
    """
    return a db connection cursor
    """
    db_credentials = get_secret(env["HANA_SECRET"])
    cnx = make_connection(db_credentials)
    cursor = cnx.cursor()
    return cursor
