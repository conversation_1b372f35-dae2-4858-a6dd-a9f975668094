import os
import re
from copy import copy
from datetime import datetime

import requests
from controllers.parallel_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from requests import request

from utils.api import api_caller
from utils.auth_utils import getUserData
from utils.aws_utils import get_dynamodb_table, get_secret
from utils.dict_utils import aggregate, capitalizeKeys, first, get
from utils.ean_utils import NIGHT_ONLY_CODES
from utils.errors import NotFoundError
from utils.ldap_utils import LDAP
from utils.log_utils import LogTime, log_err
from utils.models.address import Address
from utils.parallel import Parallel
from utils.token_utils import decode_token, getToken
from utils.validation import cast_bool


def extract_contract_date(list_history):
    contracts = []
    seen_contracts = set()

    for history_item in list_history:
        contract_key = (history_item["NumeroContrat"], history_item["DateFrom"], history_item["DateTo"])
        if contract_key not in seen_contracts:
            contracts.append({"num_ctr": history_item["NumeroContrat"], "ctr_from": history_item["DateFrom"], "ctr_to": history_item["DateTo"]})
            seen_contracts.add(contract_key)
    return contracts


class ws14(ParallelController):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.is_smart_portal_enabled = None
        self.master_request = None
        self.user = getUserData(self.event, allow_ghost=False)
        self.login_mail = decode_token(getToken(get(event, "headers", {})))["upn"]

    def getPaginationPage(self):
        return 0

    def getPaginationPageSize(self):
        return 10000

    def fetch_master(self):
        with LogTime("fetch_master"):
            try:
                return get(
                    api_caller(
                        "get",
                        "/ppp/master/{}".format(get(self.user, "bp")),
                        throw=False,
                    )
                    or {},
                    "Master",
                )
            except Exception:
                return None

    def fetch_is_smart_portal_enabled(self):
        with LogTime("fetch_is_smart_portal_enabled"):
            _smart = False
            api_info = get_secret(os.environ["MACONSO_API"])

            resp = request(
                "get",
                f"{api_info['url']}/search_bp?ref={get(self.user, 'bp')}&login={self.login_mail}",
                headers={"Authorization": api_info["auth"]},
                verify=False,
                timeout=5,
            )
            if resp.status_code == requests.codes.ok:
                users = get(resp.json(), "users", [])
                if users:
                    _smart = any(get(user, "invitation_date", False, default_on_empty=True) for user in users)

            return _smart

    def updateInDynamo(self, dashboard):
        with LogTime("updateInDynamo"):
            user_from_sap = {
                "firstname": dashboard["Firstname"],
                "lastname": dashboard["Lastname"],
                "email": dashboard["Email"],
                "contact_email": dashboard["Contact"]["ContactEmail"],
                "phone": dashboard["Contact"]["Phone"],
                "valid_phone": dashboard["ValidPhone"],
                "phone_fixe": dashboard["Contact"]["PhoneFixe"],
                "adresse": dashboard["Contact"]["Adresse"],
                "master": dashboard["Master"],
                "ppp": dashboard["Ppp"],
                "smart_portal": dashboard["SmartPortal"],
                "Commune": dashboard["Commune"],
                "ean": [
                    {
                        "ean": item["Ean"],
                        "meterid": item["Cpt"],
                        "energy": item["SectActivite"],
                        "contract": extract_contract_date(item["History"]),
                        "address_hash": item["AddressHash"],
                        "address": item["Adresse"],
                    }
                    for item in dashboard["ListeEan"]
                ],
                "dossiers": [{"id": item["Id"]} for item in dashboard["Dossiers"]],
                "preferences": dashboard["Preferences"],
            }
            merged_user = {
                **self.user,
                **user_from_sap,
                "last_login": datetime.now().isoformat(),
            }
            if not merged_user["phone"]:
                del merged_user["phone"]

            user_table = get_dynamodb_table(os.environ["DYNAMODB"])
            user_table.put_item(Item=merged_user)

    @staticmethod
    def map_and_hash(_dict: dict):
        address = Address(
            street=_dict.get("Rue"),
            number=_dict.get("NumRue"),
            postcode=_dict.get("CdPostal"),
            city=_dict.get("Localite"),
        )

        return address.stable_hash()

    def syncMaster(self, dashboard):
        with LogTime("syncMaster"):
            try:
                master = dashboard.get("Master")
                ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
                cn = self.user["uid"]
                ldap.setUserAttribute(cn, "rESAListeIDPPP", master or "null")
                langue = get(get(dashboard, "Preferences", {}), "Langue", "fr").lower()
                langue = "fr" if langue not in ["fr", "en", "nl"] else langue
                ldap.setUserAttribute(cn, "rESAPrefLangues", langue)
                del ldap
            except Exception as e:
                log_err("fail to sync Master")
                log_err(e)

    def validate_request_params(self):
        ret = super().validate_request_params()
        ret["bp"] = self.user["bp"]
        self.master_request = Parallel(self.fetch_master)
        self.is_smart_portal_enabled = Parallel(self.fetch_is_smart_portal_enabled)

        return ret

    def get_dossiers_detail(self, dossiers: list[str]):
        res = api_caller(
            "post",
            "/demande_travaux/dossier",
            body={"NumDossier": dossiers},
            throw=False,
        )
        return (res or {}).get("Liste", [])

    def extract_commune_data(self, preferences: dict) -> dict | None:
        commune_data = {}

        for k, v in {**preferences}.items():
            if k.startswith("commune_"):
                commune_data[k.replace("commune_", "")] = v
                preferences.pop(k)

        return capitalizeKeys(commune_data) if commune_data else None

    def rebuild_preference_list(self, _preferences: dict) -> dict:
        if not _preferences:
            return _preferences

        preferences = dict(sorted(_preferences.items(), key=lambda item: item[0]))

        for k in copy(preferences):
            match = re.match(r"^(.*)_list_\d+$", k)
            if match:
                new_key = match.group(1)
                if new_key not in preferences or not isinstance(preferences[new_key], list):
                    preferences[new_key] = []
                preferences[new_key].append(preferences.pop(k, None))

        return preferences

    def layer(self, data, nbItems):
        if not first(data["user_data"]):
            raise NotFoundError("Donnée utilisateur non existante", error_code="USER_DATA_NOT_FOUND")
        master = self.master_request.get()
        smart_portal_enabled = self.is_smart_portal_enabled.get()
        preferences = dict(
            aggregate(
                data["preferences"],
                "Preference_Key",
                lambda rows: [
                    rows[0]["Preference_Key"].lower(),
                    cast_bool(rows[0]["Preference_Value"]),
                ],
            ),
        )
        preferences = self.rebuild_preference_list(preferences)
        commune_data = self.extract_commune_data(preferences)

        ret = {
            "Uid": self.user["uid"],
            "Permissions": get(self.user, "permissions", []),
            "Email": self.login_mail.lower(),
            "Bp": data["user_data"][0]["Bp"],
            "Master": master,
            "Ppp": master is not None and master != "",
            "SmartPortal": any(row["Ean_Smart"] for row in data["eans"]) and smart_portal_enabled,
            "SmartPortalConsent": self.user.get("SmartPortalConsent"),
            "ValidPhone": preferences.pop("valid_phone", False),
            "ValidContactEmail": preferences.pop("valid_contact_email", False),
            "Contact": {
                "ContactEmail": data["user_data"][0]["ContactEmail"],
                "Phone": data["user_data"][0]["Phone"],
                "PhoneFixe": data["user_data"][0]["PhoneFixe"],
                "Adresse": {
                    "Localite": data["user_data"][0]["Localite"],
                    "NumRue": data["user_data"][0]["NumRue"],
                    "Cdpostal": data["user_data"][0]["Cdpostal"],
                    "Rue": data["user_data"][0]["Rue"],
                    "CodePays": data["user_data"][0]["CodePays"],
                },
            },
            "Commune": commune_data,
            "Firstname": data["user_data"][0]["Firstname"],
            "Lastname": data["user_data"][0]["Lastname"],
            "ListeEan": aggregate(
                data["eans"],
                "Ean",
                lambda rows: {
                    "Ean": rows[0]["Ean"],
                    "Adresse": (
                        adresse := {
                            "CdPostal": rows[0]["Ean_Cdpostal"],
                            "Localite": rows[0]["Ean_Localite"],
                            "Rue": rows[0]["Ean_Rue"],
                            "NumRue": rows[0]["Ean_NumRue"],
                        }
                    ),
                    "AddressHash": self.map_and_hash(adresse),
                    "Status": rows[0]["Ean_Status"],
                    "SectActivite": rows[0]["Ean_SectActivite"],
                    "NumeroContrat": list(dict.fromkeys(elem["Ean_NumeroContrat"] for elem in rows if elem["Ean_Status"] == "ACTIF")),
                    "HaugazelId": list(dict.fromkeys(elem["Ean_HaugazelId"] for elem in rows if elem["Ean_Status"] == "ACTIF")),
                    "DateFrom": min(elem["Ean_DateFrom"] for elem in rows),
                    "DateTo": max(elem["Ean_DateTo"] for elem in rows),
                    "Cpt": list(dict.fromkeys(elem["Ean_Cpt"] for elem in rows if elem["Ean_Status"] == "ACTIF")),
                    "Meters": [
                        {
                            "Id": elem["Ean_Cpt"],
                            "Rates": (rates := (elem["Ean_Rates"] or "").split(",")),
                            "NightOnly": any(rate in NIGHT_ONLY_CODES for rate in rates),
                        }
                        for elem in rows
                        if elem["Ean_Status"] == "ACTIF"
                    ],
                    "Profile": {
                        "SmartMeter": rows[0]["Ean_Smart"],
                        "Social": rows[0]["Ean_NumeroContrat"] is not None,
                    },
                    "History": [
                        {
                            "Ean": row["Ean"],
                            "Adresse": {
                                "CdPostal": row["Ean_Cdpostal"],
                                "Localite": row["Ean_Localite"],
                                "Rue": row["Ean_Rue"],
                                "NumRue": row["Ean_NumRue"],
                            },
                            "Status": row["Ean_Status"],
                            "SectActivite": row["Ean_SectActivite"],
                            "PartenaireId": row["Ean_PartenaireId"],
                            "NumeroContrat": row["Ean_NumeroContrat"],
                            "HaugazelId": row["Ean_HaugazelId"],
                            "DateFrom": row["Ean_DateFrom"],
                            "DateTo": row["Ean_DateTo"],
                            "Cpt": row["Ean_Cpt"],
                            "Rates": (rates := (row["Ean_Rates"] or "").split(",")),
                            "NightOnly": any(rate in NIGHT_ONLY_CODES for rate in rates),
                            "Profile": {
                                "SmartMeter": row["Ean_Smart"],
                                "Social": row["Ean_NumeroContrat"] is not None,
                            },
                        }
                        for row in rows
                    ],
                },
            ),
            "Dossiers": aggregate(
                data["dossiers"],
                "Dossier",
                lambda rows: {
                    "Id": rows[0]["Dossier"],
                    "NumDossier": [rows[0]["Dossier"]],
                },
            ),
            "Notifications": capitalizeKeys(get(self.user, "notifications", [])),
            "Preferences": preferences,
        }

        # Filter to keep only EAN with active contract on them.
        # TODO: remove this filter when other WS handle the filter on Status
        ret["ListeEan"] = [ean_info for ean_info in ret["ListeEan"] if ean_info["Status"] == "ACTIF"]

        self.updateInDynamo(ret)

        quick = get(self.event, "queryStringParameters", {}).get("Quick", "").upper() == "TRUE"
        if ret["Dossiers"] and not quick:
            with LogTime("getDossierDetails"):
                ret["Dossiers"] = self.get_dossiers_detail([doss["Id"] for doss in ret["Dossiers"]])
                for dossier in ret["Dossiers"]:
                    dossier["Id"] = dossier["NumDossier"][0]

        self.syncMaster(ret)

        return ret
