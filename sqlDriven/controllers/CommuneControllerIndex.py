import base64
import io
import json
from datetime import date

import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, PatternFill

from controllers.CommuneController import CommuneController
from utils.dict_utils import get
from utils.ean_utils import get_code_cadran
from utils.string_utils import format_number
from utils.time_utils import last_day_of_month, add_business_days


class CommuneControllerIndex(CommuneController):
    base64 = True

    def get_response(self, cursor, params=None):
        address_dict = {}
        tmp_result = super().to_response(cursor, params)
        # get data from tmp_result, create csv with those data
        # Wanna return the csv, put in b64

        for tmp_res in tmp_result:
            address = json.dumps(tmp_res["Adresse"])
            del tmp_res["Adresse"]
            if address not in address_dict:
                address_dict[address] = []
            address_dict[address].append(tmp_res)
            print(f"tmp_res as {tmp_res}")
            if tmp_res["Index"]:
                index_data = json.loads(tmp_res["Index"])
                tmp_res["Index"] = []

                for item in index_data:
                    item["CodeCadran"] = item["CodeCadran"].replace(".XX", "")  # remove .XX that is at the end of certain code
                    if get(tmp_res, "NOMBREGAUCHE", default_on_empty=True) is not None and get(tmp_res, "NOMBREDROITE", default_on_empty=True) is not None:
                        last_index = format_number(item["Index_Integer"], item["Index_Decimal"], tmp_res["NOMBREGAUCHE"], tmp_res["NOMBREDROITE"], ",")
                    else:
                        last_index = item["Index_Integer"] + "," + item["Index_Decimal"].split(".")[-1][:5]
                    tmp_res["Index"].append(
                        {
                            "DatePrec": f"{item['DateRel'][:4]}/{item['DateRel'][4:6]}/{item['DateRel'][6:]}",
                            "IndexPrec": last_index,
                            "Cadran": get_code_cadran(item),
                            "CodeCadran": item["CodeCadran"],
                            "Adresse": json.loads(address),
                            "numCadran": item["LOGIKZW"] if "LOGIKZW" in item else None,
                        }
                    )

        return [{"Eans": v} for k, v in address_dict.items()]

    def headers(self):
        header = super().headers()
        header["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        header["Content-Disposition"] = "attachment; filename=EAN_Index.xlsx"
        return header

    def to_response(self, cursor, params=None):
        header_fill = PatternFill(start_color="FF8517", end_color="FF8517", fill_type="solid")
        header_font = Font(name="DejaVu Sans", bold=True, color="000000")  # Texte en gras et noir
        alignment = Alignment(horizontal="center")

        # Obtenir la reponse de la base de donnees
        response = self.get_response(cursor)
        wb = openpyxl.Workbook()
        ws = wb.active
        # En-tête du fichier excel
        headers = [
            "Rue",
            "NumRue",
            "NumCompl",
            "CodePostal",
            "Localite",
            "Ean",
            "NumCpt",
            "Type",
            "Cadran",
            "CodeCadran",
            "NumCadran",
            "DatePrec",
            "IndexPrec",
            "NouvIndex",
            "DateNouvIndex",
            "DateDebutRel",
            "DateFinRel",
            "FondEchelle",
        ]
        ws.append(headers)
        for col in range(1, len(headers) + 1):
            cell = ws.cell(row=1, column=col)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = alignment
            if col in [1, 6]:
                ws.column_dimensions[cell.column_letter].width = 35
            else:
                ws.column_dimensions[cell.column_letter].width = 20
        # Parcourir les donnees de la reponse
        for item in response:
            for ean_info in item["Eans"]:
                if ean_info["Index"]:
                    periode_dict = json.loads(ean_info["Periode"])[0]
                    date_debut_fin_dict = self.get_next_passage_period(
                        periode_dict["ADATSOLL"],
                        periode_dict["ZUORDDAT"],
                        periode_dict["TARIFTYP"],
                        ean_info["Smart"],
                    )
                    date_debut_fin_dict = {k: v.strftime("%Y/%m/%d") if v else None for k, v in date_debut_fin_dict.items()}
                    # Verification que l'index n'est pas nul (Un compteur peut avoir un idex nul si jamais releve)
                    for i in range(len(ean_info["Index"])):
                        index_data = ean_info["Index"][i]
                        excel_row = [
                            index_data["Adresse"]["Rue"],
                            index_data["Adresse"]["NumRue"],
                            index_data["Adresse"]["NumComp"],
                            index_data["Adresse"]["CodePostal"],
                            index_data["Adresse"]["Localite"],
                            f"{ean_info['Ean']}",
                            ean_info["NumCpt"],
                            ean_info["Type"],
                            index_data["Cadran"],
                            # Filter incoherent codes
                            (index_data["CodeCadran"] if index_data["CodeCadran"].startswith("1.8.") or index_data["CodeCadran"].startswith("2.8.") else ""),
                            index_data["numCadran"],  # NumCadran
                            index_data["DatePrec"],
                            index_data["IndexPrec"],
                            "",  # Empty line for "NouvIndex" so it can be manually filled with the latest index value
                            "",  # Empty line for "DateNouvIndex" so it can be manually filled with the latest index value
                            date_debut_fin_dict["DateDebut"],
                            date_debut_fin_dict["DateFin"],
                            (
                                f" {int(ean_info['NOMBREGAUCHE'])}.{int(ean_info['NOMBREDROITE'])}"
                                if ean_info["NOMBREGAUCHE"] is not None and ean_info["NOMBREDROITE"] is not None
                                else ""
                            ),
                        ]
                        ws.append(excel_row)
                else:
                    # TODO : this case is temporary removed while waiting for a solution to obtain registry type even when there is no index
                    pass
                    # # Si aucun index, ajouter une ligne avec des valeurs vides
                    # csv_row = (
                    #     f'EAN-{ean_info["Ean"]}',
                    #     ean_info["NumCpt"],
                    #     ean_info["Type"],
                    #     "",
                    #     "",
                    #     "",
                    #     ""
                    # )
                    # csv_data += ";".join(map(str, csv_row)) + "\n"
        # Set all sheet as text
        for col in range(1, ws.max_column + 1):
            for row in range(1, ws.max_row + 1):
                ws.cell(column=col, row=row).number_format = "@"
        # Save in memory file
        excel_file = io.BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)
        # If doesn't work transform it into b64
        b64_excel_file = base64.b64encode(excel_file.getvalue())
        return b64_excel_file

    def get_next_passage_period(self, adatsoll, zuroddat, type_tarif, is_compteur_smart_comm):
        if not zuroddat or not adatsoll:
            return {}

        adatsoll_str = adatsoll.strip()
        adatsoll = date(int(adatsoll_str[0:4]), int(adatsoll_str[4:6]), int(adatsoll_str[6:8]))
        zuorddat_str = zuroddat.strip()
        zuorddat = date(int(zuorddat_str[0:4]), int(zuorddat_str[4:6]), int(zuorddat_str[6:8]))
        last_day_of_zuordat_m = last_day_of_month(zuorddat)

        if type_tarif in ["EM", "GM"]:
            date_aux = add_business_days(last_day_of_zuordat_m, 5)
            date_debut = add_business_days(zuorddat, -7)
        else:
            date_aux = add_business_days(last_day_of_zuordat_m, -7)
            date_debut = date(adatsoll.year, zuorddat.month, 1)
        date_fin = date_debut if is_compteur_smart_comm else date_aux

        return {"DateDebut": date_debut, "DateFin": date_fin}
