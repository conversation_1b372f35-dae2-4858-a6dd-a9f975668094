from controllers.HanaPagination import HanaPagination
from utils.auth_utils import getUserData


class Chantiers(HanaPagination):
    def validate_request_params(self):
        ret = super().validate_request_params()
        user_data = getUserData(self.event, allow_ghost=False, require_permission="entrepreneur")
        ret["EntrepreneurId"] = user_data["entrepreneur_id"]
        return ret

    def fetch(self, cursor, params):
        """
        return the response body
        """
        data = super().fetch(cursor, params)
        for i, c in enumerate(data):
            if not c["Id"]:
                c["Id"] = "fakeid_" + str(i)
        return data
