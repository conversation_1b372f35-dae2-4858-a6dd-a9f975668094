import json
import os
import time

from controllers.Controller import Controller
from utils.aws_utils import load_s3_file, get_secret
from utils.dict_utils import get
from utils.dict_utils import snake_to_pascal
from utils.type_utils import date_format, int_or_none, time_format


class ws25_1(Controller):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.row_processor = self.default_row_processor

    def validate_request_params(self):
        params = super().validate_request_params()

        params["Id"] = params["Id"].zfill(12)

        return params

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        db_credentials = get_secret(self.get_secret())
        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        if get(db_credentials, "RDBMS") == "hana":
            sql_statement = sql_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
            )
        return sql_statement

    def default_row_processor(self, cursor, row):
        """
        return the row as key-value dictionnary
        """
        resource = {}
        for i in range(len(cursor.description)):
            column = cursor.description[i][0]
            resource[snake_to_pascal(column)] = row[i]
        resource["CdPostal"] = int_or_none(resource["CdPostal"])
        date_aux = date_format(resource["DatePanne"], input_format="%Y%m%d", output_format="%d-%m-%Y")
        heure_aux = resource["HeureFin"]
        resource["DatePanne"] = int(resource["DatePanne"])
        resource["HeurePriseCharge"] = time_format(resource["HeurePriseCharge"], input_format="%H%M%S")
        resource["DateFin"] = date_format(
            resource["DateFin"],
            input_format="%Y%m%d",
            output_format="%d-%m-%Y",
            throw=False,
        )
        resource["HeureFin"] = time_format(heure_aux, input_format="%H%M%S", throw=False)
        if resource["DateFin"]:
            start = time.mktime(time.strptime(date_aux + " " + resource["HeurePriseCharge"], "%d-%m-%Y %H:%M:%S"))
            end = time.mktime(
                time.strptime(
                    resource["DateFin"] + " " + resource["HeureFin"],
                    "%d-%m-%Y %H:%M:%S",
                )
            )
            hours, seconds = (end - start) // 3600, (end - start) % 3600
            minutes, seconds = seconds // 60, seconds % 60
            resource["DureePanne"] = str(int(hours)).zfill(2) + ":" + str(int(minutes)).zfill(2)

        resource["Cause"] = get(
            {"01": "Agression par tiers", "02": "Intempéries"},
            resource["CauseCode"],
            "Défaillance(s) technique(s)",
        )
        resource["StatutPanne"] = "Panne résolue" if not heure_aux or heure_aux.strip() == "000000" else "Panne en cours"
        resource["GPS"] = {"Lat": resource["Lat"], "Long": resource["Long"]}
        resource.pop("CauseCode", None)
        resource.pop("HeureFin", None)
        resource.pop("DateFin", None)
        resource.pop("Lat", None)
        resource.pop("Long", None)

        resource.pop("NbItems", None)

        return resource
