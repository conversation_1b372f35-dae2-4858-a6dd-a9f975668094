from controllers.parallel_controller import (
    Parallel<PERSON><PERSON>roller_Deprecated,
    DatabaseWorker,
)
from controllers.ws29 import ws29
from utils.aws_handler_decorator import commune_handler
from utils.aws_utils import get_secret, get_resource_key
from utils.errors import NotFoundError
from utils.models.user import User


@commune_handler(roles=["INDEX_CPT_CONSU", "INDEX_CPT_GERER"])
class ws131(ws29):
    def __init__(self, event, linking, logged_user: User):
        super().__init__(event, linking)
        self.ean = None
        self.auth = True
        self.result_dict = {}
        base_controller = super(ParallelController_Deprecated, self)
        self.eans_thread = DatabaseWorker(
            "eans",
            get_secret(self.get_secret()),
            base_controller.apply_hana_env(base_controller.load_sql_file(get_resource_key(self.linking["sql_template"]["get_eans"]))),
            {"IdCommune": logged_user.commune.id, "Smart": None},
            self.fetch,
            self.result_dict,
        )

    def validate_request_params(self):
        params = super(ParallelController_Deprecated, self).validate_request_params()
        self.ean = params["Ean"]
        return {**params, "NumCpt": ""}

    def execute_query(self, sql_statement, request_params):
        self.eans_thread.start()
        return super().execute_query(sql_statement, request_params)

    def to_response(self, cursor, params=None):
        self.eans_thread.join()
        eans = {item["Ean"] for item in self.result_dict["eans"]}
        if self.ean not in eans:
            raise NotFoundError

        return super().to_response(cursor, params)
