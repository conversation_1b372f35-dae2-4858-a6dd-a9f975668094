from datetime import datetime

from controllers.NoPagination import NoPagination

from utils.ean_utils import get_code_cadran
from utils.errors import (
    INVALID_EAN_FOR_USER,
)
from utils.models.user import User


class ws219_histo_index(NoPagination):
    def __init__(self, event, linking):
        self.previous_values = {}
        super().__init__(event, linking)
        self.user = User.from_event(self.event, allow_ghost=False)

    def validate_request_params(self):
        input_params = super().validate_request_params()

        if str(input_params.get("Ean")) not in self.user.ean_ids:
            raise INVALID_EAN_FOR_USER

        bp = self.user.bp
        input_params["Bp"] = bp

        return input_params

    def to_response(self, cursor, params=None):
        db_result = super().to_response(cursor, params)
        sorted_data = sorted(db_result, key=lambda x: (x["EAN"], x["CODECADRAN"], x["DATERELEVE"]))
        processed_data = self.calculate_consumption(sorted_data)
        processed_data = sorted(processed_data, key=lambda x: x["Date"])
        return processed_data

    def calculate_consumption(self, data):
        returned_data = []
        previous_values = {}

        for elem in data:
            ean = elem["EAN"]
            register_code = elem["CODECADRAN"]
            index = elem["RELEVEINDEXROUND"] + elem["RELEVEINDEXDECIMAL"]
            key = (ean, register_code)

            previous_index = previous_values.get(key)
            consumption = index - previous_index if previous_index is not None else 0.0

            previous_values[key] = index

            returned_data.append(self.build_response(elem, index, consumption))

        return returned_data

    def build_response(self, elem, index, consumption):
        codecadran = elem.get("CODECADRAN")
        dict_code_cadran = {
            "Smart": False,
            "lv_nb_lines": elem.get("lv_nb_lines"),
            "CatTarif": elem.get("CATTARIF"),
            "ATWRT": elem.get("ATWRT"),
            "ETDZ_ZWNUMMER": elem.get("ZWNUMMER"),
            "ANZART": elem.get("ANZART"),
            "CodeCadran": codecadran,
            "CatCadran": elem.get("CATCADRAN"),
        }
        info = get_code_cadran(dict_code_cadran)

        return {
            "Date": datetime.strptime(elem["DATERELEVE"], "%Y%m%d").strftime("%Y-%m-%d"),
            "Index": index,
            "Consumption": consumption,
            "Register": info,
            "RegisterCode": codecadran,
            "ReadingReason": elem.get("MOTIFRELEVE"),
            "ReadingType": elem.get("TYPERELEVE"),
            "Supplier": elem.get("SUPPLIERNAME"),
        }
