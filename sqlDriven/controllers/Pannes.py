from os import environ

import boto3

from controllers.Controller import Controller
from utils.dict_utils import snake_to_pascal, get, first
from utils.ep_utils import lambert_to_wgs86
from utils.errors import NotFoundError
from utils.type_utils import to_list, int_format, date_format, time_format

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(environ["PANNE_LOCATION_TABLE"])


class Pannes(Controller):
    def __init__(self, event, linking):
        self.event = event
        self.linking = linking
        self.row_processor = self.pannes_processor

    def execute_query(self, sql_statement, request_params):
        if self.linking["return"]["type"] == "list":
            page_items = super().getPaginationPageSize()
            page = super().getPaginationPage()
            page_offset = page * page_items
            request_params["PageSize"] = page_items
            request_params["Offset"] = page_offset

        if "," in get(request_params, "Zipcode", ""):
            zip_codes = ",".join(f"'{zip_code.strip()}'" for zip_code in request_params["Zipcode"].split(","))
            zip_code_query = f"avis_zipcode in ({zip_codes})"
        else:
            zip_code_query = ":Zipcode is null or avis_zipcode = :Zipcode"

        return super().execute_query(sql_statement.format(ZipcodeQuery=zip_code_query), request_params)

    def fetch(self, cursor, params=None):
        data = []
        ids = set()
        pannes_location = {}

        result = cursor.fetchall()

        # Fetch interruption location from DynamoDB
        for i in range(len(result)):
            ids.add(result[i].column_values[0])

        ids = list(ids)
        start = 0
        while start < len(ids):
            end = start + 100
            response = dynamodb.batch_get_item(RequestItems={environ["PANNE_LOCATION_TABLE"]: {"Keys": [{"panne_id": id} for id in ids[start:end]]}})
            for res in response["Responses"][environ["PANNE_LOCATION_TABLE"]]:
                pannes_location[res["panne_id"]] = res

            start = end

        # Format data from HANA
        for row in result:
            r = self.row_processor(cursor, row)
            data.append(r)

        # Merge interruption location from HANA and from DynamoDB
        for elem in data:
            location = pannes_location.get(elem["Id"])
            if location:
                address = elem["Adresse"]
                address["Rue"] = address["Rue"] or location.get("road")
                address["Zipcode"] = address["Zipcode"] or int_format(location.get("zip_code"))
                address["Numero"] = address["Numero"] or location.get("number")
                address["Ville"] = address["Ville"] or location.get("city")
                address["Lat"] = address["Lat"] or location.get("lat")
                address["Long"] = address["Long"] or location.get("lng")

        return data

    def pannes_processor(self, cursor, row):
        """
        return the row as key-value dictionnary
        processes belgian lambert coordinates into standard Srid
        """
        resource = {}
        for i in range(len(cursor.description)):
            column = cursor.description[i][0]
            resource[snake_to_pascal(column.lower())] = row[i]
        if resource["Long"] is not None and resource["Lat"] is not None:
            lat, long = lambert_to_wgs86(resource["Long"], resource["Lat"])
            resource["Long"] = long
            resource["Lat"] = lat
        result = self.to_pannes_schema(resource)
        return result

    def to_pannes_schema(self, resource):
        if resource["Stat"] == "I0072":
            date_cloture = resource["DateStatut"]
            date_statut = resource["DateStatut"]
            heure_statut = resource["HeureStatut"]
        elif resource["DateOrder"] is not None and resource["IsLastStatut"]:
            date_cloture = resource["DateOrder"]
            date_statut = resource["DateOrder"]
            heure_statut = resource["HeureOrder"]
            resource["Stat"] = "I0072"
        else:
            date_cloture = None
            date_statut = resource["DateStatut"]
            heure_statut = resource["HeureStatut"]

        result = {
            "Id": resource["Id"],
            "Ep": resource["Ep"],
            "DateCreation": date_format(resource["DateCreation"], "%Y%m%d"),
            "DateCloture": date_format(date_cloture, "%Y%m%d"),
            "Statut": {
                "Id": resource["Stat"],
                "Descr": resource["Descr"],
                "ShortDescr": resource["ShortDescr"],
                "DateHeure": resource["StatutTimestamp"],
                "Date": date_format(date_statut, "%Y%m%d"),
                "Heure": time_format(heure_statut, "%H%M%S"),
            },
            "SousType": resource["SousType"],
            "Type": resource["Type"],
            "IlluminationType": resource["IlluminationType"],
            "Adresse": {
                "Rue": resource["AvisRue"],
                "Zipcode": int_format(resource["AvisZipcode"]),
                "Numero": resource["AvisNum"],
                "Ville": resource["AvisVille"],
                "Lat": resource["Lat"],
                "Long": resource["Long"],
            },
            "Equipement": {
                "Id": resource["EquipementId"],
                "Type": resource["Type"],
                "SousType": resource["SousType"],
                "IlluminationType": resource["IlluminationType"],
            },
            "TotalElems": resource.get("TotalElems"),  # Only used when fetching multiple elements
        }
        return result

    def to_response(self, cursor, params=None):
        data = to_list(self.fetch(cursor, params))
        nb_items = len(data)

        result = []
        grouped_data = {}

        # get real total number of elements from the fist result (same number on each row)
        nb_items = get(first(data), "TotalElems", nb_items)

        for elem in data:
            if elem["Id"] not in grouped_data:
                grouped_data[elem["Id"]] = []
            grouped_data[elem["Id"]].append(elem)
        for elems in grouped_data.values():
            first_result = elems[0]
            result_elem = {k: first_result[k] for k in first_result if k != "Statut"}
            result_elem.pop("TotalElems", None)
            result_elem["HistoriqueStatut"] = sorted(
                [row["Statut"] for row in elems],
                key=lambda k: k["DateHeure"],
                reverse=True,
            )
            result.append(result_elem)
        if self.linking["return"]["type"] != "list":
            result = first(result)
            if not result:
                raise NotFoundError()

        result = self.layer(result, nb_items)
        return result
