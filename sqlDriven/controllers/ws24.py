import json
import os

from controllers.parallel_controller import Controller
from utils.aws_utils import get_resource_key, load_s3_file
from utils.dict_utils import snake_to_pascal, get
from utils.errors import BadRequestError
from utils.log_utils import LogTime
from utils.type_utils import date_format, int_or_none, time_format


class ws24(Controller):
    accepted_params = {
        "input_0": set(),
        "input_1": {"CdPostal", "Localite", "Rue"},
        "input_2": {"IdLocalite", "IdRue"},
        "input_3": {"CdPostal"},
        "input_4": {"IdLocalite", "Rue"},
        "input_5": {"IdRadLocalite", "IdRadRue"},
        "input_6": {"IdRue"},
        "input_7": {"Lat0", "Long0", "Lat1", "Long1"},
    }

    def __init__(self, event, linking):
        self.zip_code_query = "EQUI_CP = (:CdPostal)"
        self.event = event
        self.linking = linking
        self.row_processor = self.default_row_processor
        self.result_dict = {}
        self.nb_items = 0
        query_string_params = set(get(event, "queryStringParameters", {}).keys()) - {
            "Page",
            "PageSize",
        }
        matched_params = [k for k in ws24.accepted_params if ws24.accepted_params[k] == query_string_params]
        if not matched_params or len(matched_params) > 1:
            raise BadRequestError("Input incorrect. Les possibles inputs à fournir sont:" + str(list(ws24.accepted_params.values())))
        self.input_type = matched_params[0]

    def validate_request_params(self):
        params = super().validate_request_params()

        if "," in get(params, "CdPostal", ""):
            zip_codes = ",".join(f"'{zip_code.strip()}'" for zip_code in params["CdPostal"].split(","))
            self.zip_code_query = f"EQUI_CP in ({zip_codes})"

        return params

    def sql_file(self):
        return self.linking["sql_template"]

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        return sql_statement

    def load_sql_file(self, sql_file):
        """
        return the sql filename to load
        """

        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        base_statement = load_s3_file(
            os.environ["MAPPING_BUCKET_NAME"],
            get_resource_key(sql_file["base_statement"]),
        )
        street_filter = load_s3_file(
            os.environ["MAPPING_BUCKET_NAME"],
            get_resource_key(sql_file["street_filter"]),
        )
        city_filter = load_s3_file(os.environ["MAPPING_BUCKET_NAME"], get_resource_key(sql_file["city_filter"]))
        rad_filter = load_s3_file(os.environ["MAPPING_BUCKET_NAME"], get_resource_key(sql_file["rad_filter"]))

        print("WS24 Input Type")
        print(self.input_type)

        if self.input_type == "input_0":
            base_statement = base_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                StreetFilter=" ",
                CdPostalFilter=" ",
                CityFilter=" ",
                RadFilter=" ",
                GPSFilter=" ",
            )
        elif self.input_type == "input_1":
            street_filter = street_filter.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                StreetFilterField="STREET",
            )
            city_filter = city_filter.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                CityFilterField="CITY_NAME",
            )
            base_statement = base_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                StreetFilter=street_filter,
                CdPostalFilter=f" AND {self.zip_code_query} ",
                CityFilter=city_filter,
                RadFilter=" ",
                GPSFilter=" ",
            )
        elif self.input_type == "input_2":
            street_filter = street_filter.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                StreetFilterField="STRT_CODE",
            )
            city_filter = city_filter.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                CityFilterField="CITY_CODE",
            )
            base_statement = base_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                CdPostalFilter=" ",
                StreetFilter=street_filter,
                CityFilter=city_filter,
                RadFilter=" ",
                GPSFilter=" ",
            )
        elif self.input_type == "input_3":
            base_statement = base_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                StreetFilter=" ",
                CdPostalFilter=f" AND {self.zip_code_query} ",
                CityFilter=" ",
                RadFilter=" ",
                GPSFilter=" ",
            )
        elif self.input_type == "input_4":
            street_filter = street_filter.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                StreetFilterField="STREET",
            )
            city_filter = city_filter.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                CityFilterField="CITY_CODE",
            )
            base_statement = base_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                CdPostalFilter=" ",
                StreetFilter=street_filter,
                CityFilter=city_filter,
                RadFilter=" ",
                GPSFilter=" ",
            )
        elif self.input_type == "input_5":
            rad_filter = rad_filter.format(HanaSchema=env_file["HANA_SCHEMA"])
            base_statement = base_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                CdPostalFilter=" ",
                StreetFilter=" ",
                CityFilter=" ",
                RadFilter=rad_filter,
                GPSFilter=" ",
            )
        elif self.input_type == "input_6":
            street_filter = street_filter.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                StreetFilterField="STRT_CODE",
            )
            base_statement = base_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                CdPostalFilter=" ",
                StreetFilter=street_filter,
                CityFilter=" ",
                RadFilter=" ",
                GPSFilter=" ",
            )
        elif self.input_type == "input_7":
            gps_filter = " WHERE :Lat0 <= GPS_LAT AND GPS_LAT <= :Lat1 AND :Long0 <= GPS_LONG AND GPS_LONG <= :Long1 "
            base_statement = base_statement.format(
                HanaSchema=env_file["HANA_SCHEMA"],
                GisSchema=env_file["GIS_SCHEMA"],
                CdPostalFilter=" ",
                StreetFilter=" ",
                CityFilter=" ",
                RadFilter=" ",
                GPSFilter=gps_filter,
            )

        return base_statement

    def execute_query(self, sql_statement, request_params):
        if self.input_type in ["input_1", "input_4"]:
            request_params["StreetFilterValue"] = request_params["Rue"]
            request_params.pop("Rue", None)
        if self.input_type in ["input_2", "input_6"]:
            request_params["StreetFilterValue"] = request_params["IdRue"]
            request_params.pop("IdRue", None)
        if self.input_type in ["input_1"]:
            request_params["CityFilterValue"] = request_params["Localite"]
            request_params.pop("Localite", None)
        if self.input_type in ["input_2", "input_4"]:
            request_params["CityFilterValue"] = request_params["IdLocalite"]
            request_params.pop("IdLocalite", None)

        page_items = super().getPaginationPageSize()
        page = super().getPaginationPage()
        page_offset = page * page_items
        request_params["PageSize"] = page_items
        request_params["PageOffset"] = page_offset

        return super().execute_query(sql_statement, request_params)

    def fetch(self, cursor, params):
        """
        return the response body
        """

        pageSize = self.getPaginationPageSize()
        data = []

        with LogTime("Fetch query"):
            result = cursor.fetchmany(pageSize)

        for row in result:
            r = self.row_processor(cursor, row)
            data.append(r)
        return data

    def default_row_processor(self, cursor, row):
        """
        return the row as key-value dictionnary
        """
        resource = {}
        for i in range(len(cursor.description)):
            column = cursor.description[i][0]
            resource[snake_to_pascal(column)] = row[i]
        resource["CdPostal"] = int_or_none(resource["CdPostal"])
        resource["DateDebut"] = date_format(resource["DateDebut"], input_format="%Y%m%d", output_format="%d-%m-%Y")
        resource["DateFin"] = date_format(
            resource["DateFin"],
            input_format="%Y%m%d",
            output_format="%d-%m-%Y",
            throw=False,
        )
        resource["HeureDebut"] = time_format(resource["HeureDebut"], input_format="%H%M%S")
        resource["HeureFin"] = time_format(resource["HeureFin"], input_format="%H%M%S", throw=False)
        resource["CoupureOuGElectrogene"] = {
            "01": "Coupure",
            "02": "Electrogene",
            "GE": "Electrogene",
        }.get(resource["CoupureOuGElectrogene"])
        resource["GPS"] = {
            "AvgLat": resource["AvgLat"],
            "AvgLong": resource["AvgLong"],
            "PositionsObjRacc": ([{"Lat": float(p.split(" ")[0]), "Long": float(p.split(" ")[1])} for p in resource["Positions"].split(";")] if resource["Positions"] else None),
        }

        self.nb_items = resource["NbItems"]
        not_in_response = ["AvgLat", "AvgLong", "Long", "Lat", "Positions", "NbItems"]
        for elem in not_in_response:
            resource.pop(elem, None)

        return resource

    def to_response(self, cursor, params=None):
        data = self.fetch(cursor, params)
        data = self.layer(data, self.nb_items)
        return data
