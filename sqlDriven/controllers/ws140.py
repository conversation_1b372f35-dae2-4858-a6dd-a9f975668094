from datetime import datetime

from controllers.CommuneController import CommuneController
from controllers.parallel_controller import ParallelController


class ws140(ParallelController, CommuneController):
    def validate_request_params(self):
        params = super().validate_request_params()
        return {**params, "Commune": self.logged_user.commune.localite}

    def to_response(self, fake_cursor, params=None):
        response = super().to_response(fake_cursor, params)
        return {
            "NbrEpLed": response["led_count"][0]["NbrEpLed"] or 0,
            "NbrEpNonLed": response["led_count"][0]["NbrEpNonLed"] or 0,
            "ConsommationAnnee": sum((conso["CONSO"] for conso in response["ep_conso"] if conso["Year"] == datetime.now().year)),
            "Historique": [
                {
                    "Consommation": conso["CONSO"],
                    "Date": f"{conso['Year']}-{conso['Month']}-01",
                }
                for conso in response["ep_conso"]
            ],
        }
