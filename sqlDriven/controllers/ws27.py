from controllers.parallel_controller import DatabaseWorker
from controllers.parallel_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Deprecated
from controllers.ws33 import from_core_result
from utils.auth_utils import load_env_file, check_basic_credentials
from utils.aws_utils import get_secret
from utils.dict_utils import get
from utils.errors import BadRequestError, UnauthorizedError, NotFoundError
from utils.log_utils import log_info
from utils.string_utils import ifstrip, ifpreffix, ifnempty
from utils.type_utils import int_format, int_or_none


class ws27(ParallelController_Deprecated):
    def __init__(self, event, linking):
        super().__init__(event, linking)

    def validate_request_params(self):
        """
        Validates request parameters and returns their string representation
        """
        auth_header = get(self.event["headers"], "Authorization", {})
        log_info("Auth header")
        log_info(auth_header)
        self.authorized = check_basic_credentials(auth_header, secret_name=load_env_file()["BASICAUTH_MYRESAAPI"])
        log_info("Authorized")
        log_info(self.authorized)
        query_string_params = get(self.event, "queryStringParameters", default={})
        geo_params = ["Cdpostal", "Rue", "NumRue"]
        public_params = ["Ean", "NumCpt"]
        if any([param not in public_params for param in query_string_params]) and not self.authorized:
            raise UnauthorizedError("Utiliser cet endpoint avec d'autres paramètres que [Ean, NumCpt] nécessite des permissions")
        if not self.authorized and "Ean" not in query_string_params:
            raise BadRequestError("Les paramètres requis sont [Ean]")
        if self.authorized and "Ean" not in query_string_params and not all(elem in query_string_params for elem in geo_params):
            raise BadRequestError("Les possibles paramètres sont [Ean] ou bien ['Cdpostal', 'Localite', 'Rue', 'NumRue']")
        return super().validate_request_params()

    def list_fetcher(self, cursor, params=None):
        """
        return the response body
        """

        data = []
        result = cursor.fetchall()
        for row in result:
            r = self.row_processor(cursor, row)
            data.append(r)
        return data

    def execute_query(self, sql_statement, request_params):
        log_info("These are the request params")
        log_info(request_params)

        # Parameters for all queries
        db_credentials = get_secret(super().get_secret())
        result_dict = {}
        inner_fetcher = self.fetch
        input_type = self.get_input_type()

        # For every input get a list of (point de conso, ean, installation, adress)
        points_conso = get_point_conso(
            input_type,
            db_credentials,
            sql_statement,
            request_params,
            inner_fetcher,
            result_dict,
        )

        point_conso_list = [row["VSTELLE"] for row in points_conso] if isinstance(points_conso, list) else [points_conso["VSTELLE"]]

        get_ean_list_from_conso = [
            DatabaseWorker(
                "get_ean_from_conso_" + point_conso,
                db_credentials,
                sql_statement["get_ean_from_conso"],
                {"VSTELLE": point_conso},
                self.list_fetcher,
                result_dict,
            )
            for point_conso in point_conso_list
        ]

        get_adress_from_conso = [
            DatabaseWorker(
                "get_adress_from_conso_" + point_conso,
                db_credentials,
                sql_statement["get_adress_info"],
                {"VSTELLE": point_conso},
                inner_fetcher,
                result_dict,
            )
            for point_conso in point_conso_list
        ]

        all_conso_threads = get_ean_list_from_conso + get_adress_from_conso

        for t in all_conso_threads:
            t.start()

        for t in all_conso_threads:
            t.join()

        install_conso = {row["ANLAGE"]: row["VSTELLE"] for k in result_dict if "get_ean_from_conso_" in k for row in result_dict[k]}

        business_info_threads = [
            DatabaseWorker(
                "get_business_info_" + install,
                db_credentials,
                sql_statement["get_business_info"],
                {"ANLAGE": install},
                inner_fetcher,
                result_dict,
            )
            for install in install_conso.keys()
        ]

        for t in business_info_threads:
            t.start()

        for t in business_info_threads:
            t.join()

        for install in install_conso.keys():
            if not result_dict.get("get_business_info_" + install):
                result_dict["get_business_info_" + install] = {"STATUT": "INACTIF"}

        print("Final result dict")
        print(result_dict)

        def keep_relevant_install(table, install):
            return [elem for elem in table if elem["ANLAGE"] == install][0]

        output_list = [
            self.join_partial_results(
                keep_relevant_install(result_dict["get_ean_from_conso_" + install_conso[install]], install),
                result_dict["get_adress_from_conso_" + install_conso[install]],
                result_dict["get_business_info_" + install],
                db_credentials,
                sql_statement,
                inner_fetcher,
                result_dict,
                request_params,
            )
            for install in install_conso.keys()
        ]

        valid_outputs = [elem for elem in output_list if elem is not None]

        result = {"ListeEan": valid_outputs}

        return result

    def get_input_type(self):
        query_string_params = self.event["queryStringParameters"]
        if "Ean" in query_string_params:
            return "Ean"
        else:
            return "Geolocation"

    def join_partial_results(
        self,
        ean_info,
        adress_info,
        business_info,
        db_credentials,
        sql_statement,
        inner_fetcher,
        result_dict,
        request_params,
    ):
        # Contrat inactif
        if not business_info:
            return None

        result_ws_27 = {
            "Ean": ean_info["EAN"],
            "PartenaireId": int(get(business_info, "GPART", 0)),
            "BpHgz": (int(get(ean_info, "GPART")) if ean_info and get(ean_info, "GPART") else None),
            "CcHgz": (int(get(ean_info, "VKONT")) if ean_info and get(ean_info, "VKONT") else None),
            "SectActivite": ean_info["SPARTE"],
            "Nom": ifstrip(get(business_info, "NAME_LAST")),
            "Prenom": ifstrip(get(business_info, "NAME_FIRST", "")),
            "Email": ifstrip(get(business_info, "SMTP_ADDR")),
            "Tel": ifstrip(get(business_info, "TELNR_CALL")),
            "Langue": ifstrip(get(business_info, "LANGU_CORR")),
            "Contrat": int(get(business_info, "VERTRAG", 0)),
            "Mi": int(get(business_info, "EINZDAT", 0)),
            "Mo": int(get(business_info, "AUSZDAT", 0)),
            "BpAddress": " ".join((ifstrip(get(business_info, "PARTNER_STREET", "")) + " " + ifstrip(get(business_info, "PARTNER_HOUSE_NUM1", ""))).split()),
            "BpLocalite": ifstrip(get(business_info, "PARTNER_CITY1")),
            "BpCdpostal": (int_or_none(ifstrip(get(business_info, "PARTNER_POSTCODE_1"))) if get(business_info, "PARTNER_POSTCODE_1") else None),
            "OrAddress": " ".join((ifstrip(get(adress_info, "INSTALL_STREET", "")) + " " + ifstrip(get(adress_info, "INSTALL_HOUSENUM_1", ""))).split()),
            "OrLocalite": ifstrip(get(adress_info, "INSTALL_CITY1")),
            "OrCdpostal": int_or_none(ifstrip(get(adress_info, "INSTALL_POSTCODE_1"))),
            "OrNumCmpt": ifnempty(
                " ".join(
                    (
                        ifstrip(get(adress_info, "HAUS_NUM2", ""))
                        + " "
                        + ifpreffix(ifstrip(get(adress_info, "FLOOR", "")), "Etage")
                        + " "
                        + ifstrip(get(adress_info, "ROOMNUMBER", ""))
                    ).split()
                )
            ),
            "OrIdRadRue": ifstrip(get(adress_info, "IDRAD_RUE")),
            "OrIdRadLocalite": ifstrip(get(adress_info, "IDRAD_LOCALITE")),
            "CodeCommune": get(adress_info, "DESCRIPT"),
            "Nace": int_format(get(business_info, "NACE")),
            "Frns": ifstrip(get(business_info, "INVOICING_PARTY")),
            "TypInstln": ifstrip(get(ean_info, "ANLART")),
            "Frequence": ifstrip(get(ean_info, "AKLASSE")),
            "Slp": ifstrip(get(ean_info, "TEMP_AREA")),
            "Usage": get(business_info, "CATEG"),
            "Statut": get(business_info, "STATUT"),
        }

        result_ws_33 = from_core_result(
            type_tarif=ean_info["TARIFTYP"],
            ableinh=ean_info["ABLEINH"],
            installation=ean_info["ANLAGE"],
            input_type="ws27",
            db_credentials=db_credentials,
            sql_statement=sql_statement,
            inner_fetcher=inner_fetcher,
            result_dict=result_dict,
            core_result=ean_info,
        )

        full_result = {**result_ws_27, **result_ws_33}
        result = full_result

        if not self.authorized:
            outputs_to_keep = [
                "Ean",
                "SectActivite",
                "DateDebut",
                "DtLimiDebut",
                "DtLimiFin",
                "DateFin",
                "CptSmart",
                "Statut",
            ]
            cpts = {k: full_result[k].zfill(18) for k in full_result if k.startswith("NumCpt")}
            result = {k: full_result[k] for k in full_result if k in outputs_to_keep or k in cpts}
            if request_params.get("NumCpt") and ean_info["EAN"] == str(request_params["Ean"]) and request_params["NumCpt"].zfill(18) not in list(cpts.values()):
                raise BadRequestError("L'Ean n'est pas associé au compteur fourni")

        return result


def get_point_conso(
    input_type,
    db_credentials,
    sql_statement,
    request_params,
    inner_fetcher,
    result_dict,
):
    # Point de consommation
    if input_type == "Ean":
        db_job = DatabaseWorker(
            "get_point_conso",
            db_credentials,
            sql_statement["get_conso_from_ean"],
            {"Ean": str(request_params["Ean"])},
            inner_fetcher,
            result_dict,
        )
    else:
        db_job = DatabaseWorker(
            "get_point_conso",
            db_credentials,
            sql_statement["get_conso_from_geo"],
            {
                "RueNum": request_params["Rue"].strip() + ", " + str(request_params["NumRue"].strip()),
                "Rue": request_params["Rue"].strip().upper(),
                "NumRue": str(request_params["NumRue"].strip()),
                "Cdpostal": str(request_params["Cdpostal"].strip()),
                "Localite": get(request_params, "Localite", "").strip(),
            },
            inner_fetcher,
            result_dict,
        )

    db_job.start()
    db_job.join()

    if "get_point_conso" not in result_dict or not result_dict["get_point_conso"]:
        raise NotFoundError("Aucun point de consommation trouvé pour l'input fourni")

    return result_dict["get_point_conso"]
