from controllers.Controller import Controller
from utils.dict_utils import snake_to_pascal, get
from utils.ep import adapt_ep_equi_id
from utils.ep_utils import lambert_to_wgs86
from utils.type_utils import date_format, int_format, time_format


class ws120(Controller):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.row_processor = self.ep_processor

    def validate_request_params(self):
        ret = super().validate_request_params()

        ret["Id"] = adapt_ep_equi_id(ret["Id"], ret.get("IdCommune"))
        return ret

    def ep_processor(self, cursor, row):
        """
        return the row as key-value dictionnary
        processes belgian lambert coordinates into standard Srid
        """
        resource = {}
        for i in range(len(cursor.description)):
            column = cursor.description[i][0]
            resource[snake_to_pascal(column.lower())] = row[i]
        result = self.to_ep_schema(resource)
        return result

    def to_ep_schema(self, resource):
        if get(resource, "IdStatut") in ["I0072"]:
            date_statut = resource["DateStatut"]
            date_cloture = date_statut
        elif resource["OrderDate"]:
            date_statut = resource["OrderDate"]
            date_cloture = date_statut
            resource["IdStatut"] = "I0072"
        else:
            date_statut = resource["DateStatut"]
            date_cloture = None

        date_statut_formated = date_format(date_statut, "%Y%m%d")
        date_cloture_formated = date_format(date_cloture, "%Y%m%d") if date_cloture else None

        if resource["Lat"] and resource["Long"]:
            resource["Lat"], resource["Long"] = lambert_to_wgs86(resource["Lat"], resource["Long"])

        result = {
            "Id": resource["Id"],
            "SousType": resource["SousType"],
            "Type": resource["Type"],
            "IlluminationType": resource["IlluminationType"],
            "Adresse": {
                "Rue": resource["Rue"],
                "Zipcode": int_format(resource["Zipcode"]),
                "Ville": resource["Ville"],
                "Numero": resource["Numero"],
                "CityCode": resource["CityCode"],
                "Lat": resource["Lat"],
                "Long": resource["Long"],
            },
            "Panne": None,
        }

        if resource["IdStatut"] is not None:
            result["Panne"] = {
                "Id": str(get(resource, "Maxavisid", "")),
                "DateCreation": date_format(resource["DateCreation"], "%Y%m%d"),
                "DateCloture": date_cloture_formated,
                "DernierStatut": {
                    "Id": resource["IdStatut"],
                    "Descr": resource["Descr"],
                    "ShortDescr": resource["ShortDescr"],
                    "Date": date_statut_formated,
                    "Heure": time_format(resource["HeureStatut"], "%H%M%S"),
                },
            }

        return result
