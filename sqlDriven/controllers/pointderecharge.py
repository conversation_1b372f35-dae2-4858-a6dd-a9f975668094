from controllers.Controller import Controller
from utils.aws_utils import get_resource_key


class pointderecharge(Controller):
    def __init__(self, event, linking):
        super().__init__(event, linking)

    def sql_file(self):
        """
        return the sql filename to load
        """

        filter_fields = ["Lat0", "Long0", "Lat1", "Long1"]
        has_geo_filter = (
            "queryStringParameters" in self.event and self.event["queryStringParameters"] and len(set(self.event["queryStringParameters"]).intersection(filter_fields)) == 4
        )
        return get_resource_key(self.linking["sql_template"][1 if has_geo_filter else 0])

    def validate_request_params(self):
        general_validation = super(pointderecharge, self).validate_request_params()
        geo_fields = ["Lat0", "Long0", "Lat1", "Long1"]
        if "queryStringParameters" in self.event and self.event["queryStringParameters"]:
            intersection = self.event["queryStringParameters"].keys() & geo_fields
            if 0 < len(intersection) < 4:
                raise ValueError("La requête doit contenir soit zero paramètres de géolocalisation soit deux paramètres de longitude et deux paramètres de latitude")
        return general_validation
