from controllers.Controller import Controller


class NoPagination(Controller):
    def layer(self, data, nbItems):
        layer_ = super().layer(data, nbItems)
        if isinstance(layer_, dict):
            layer_.pop("Pagination", None)
        return layer_

    def fetch(self, cursor, params=None):
        """
        return the response body
        """
        data = []
        result = cursor.fetchall()
        for row in result:
            r = self.row_processor(cursor, row)
            data.append(r)
        return data

    def getPaginationPage(self):
        return 0

    def getPaginationPageSize(self):
        return 1000000
