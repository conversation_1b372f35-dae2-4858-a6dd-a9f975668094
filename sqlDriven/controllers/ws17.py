import utils.resa_api_constants as constants
from controllers.Controller import Controller, default_row_processor
from utils.errors import NotFoundError


class ws17(Controller):
    def __init__(self, event, linking):
        self.event = event
        self.linking = linking
        self.row_processor = default_row_processor

    def fetch(self, cursor, params=None):
        """
        return the response body
        """

        if cursor:
            data = []
            result = cursor.fetchall()
            if not result:
                raise NotFoundError(constants.errmsg_not_found, {})
            for row in result:
                r = self.row_processor(cursor, row)
                data.append(r)
            return data
        else:
            result = cursor.fetchone()
            if not result:
                raise NotFoundError(constants.errmsg_not_found, {})
            return self.row_processor(cursor, result)

    def to_response(self, cursor, params=None):
        data = self.fetch(cursor, params)
        data = self.layer(data, len(data))
        return data
