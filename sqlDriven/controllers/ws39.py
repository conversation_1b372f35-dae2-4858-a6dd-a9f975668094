import json
import os
from uuid import uuid4

from controllers.NoPagination import NoPagination
from utils.aws_utils import load_s3_file, upload_s3_file, create_s3_presigned_url
from utils.dict_utils import get
from utils.errors import NotFoundError, ForbiddenError
from utils.models.user import User
from utils.sharepoint import SharepointHandler

TMP_BUCKET = f"{os.environ['BUCKET']}-temp-storage"


class ws39(NoPagination):
    query_without_partner = """
         SELECT * FROM
        (SELECT QMEL.QMNUM AS QMEL_QMNUM, OBJNR, QMART, AUFNR
          FROM {HanaSchema}.QMEL
          WHERE LPAD (AUFNR, 12, '0') = LPAD (:Ordre, 12, '0')
        ) AS QMEL
    """

    query_with_partner_and_order = """
           SELECT * FROM
               (SELECT DISTINCT OBJNR AS IHPA_OBJNR 
               FROM {HanaSchema}.IHPA WHERE PARNR = :PartenaireId
               ) AS IHPA
           INNER JOIN
               (SELECT QMEL.QMNUM AS QMEL_QMNUM, OBJNR, QMART, AUFNR
                 FROM {HanaSchema}.QMEL
                 WHERE LPAD (AUFNR, 12, '0') = LPAD (:Ordre, 12, '0')
               ) AS QMEL
           ON IHPA.IHPA_OBJNR = QMEL.OBJNR
       """

    def validate_request_params(self):
        """
        Validates request parameters and return their string representation
        """
        user = User.from_event(self.event)
        input_params = super().validate_request_params()

        if input_params.get("Ordre"):
            if input_params.get("Ordre") not in user.dossiers_ids and not user.commune_id:
                raise ForbiddenError(
                    "This order doesn't belong to the given user.",
                    error_code="INVALID_ORDER_FOR_USER",
                )

        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        env_secret_name = env_file["HANA_SECRET"]
        if "PRD" in env_secret_name:
            env_name = "-PTE-"
        elif "QTA" in env_secret_name:
            env_name = "-QTA-"
        elif "QLA" in env_secret_name:
            env_name = "-QLA-"
        input_params["EnvName"] = env_name
        return input_params

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        return sql_statement

    def load_sql_file(self, sql_file):
        """
        return the sql filename to load
        """

        base_statement = super().load_sql_file(sql_file)
        input_type = self.get_input_type()
        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))

        if input_type == "Ordre":
            sql_statement = base_statement.format(
                DynamicQuery=self.query_without_partner.format(HanaSchema=env_file["HANA_SCHEMA"]),
                HanaSchema=env_file["HANA_SCHEMA"],
            )
        else:
            sql_statement = base_statement.format(
                DynamicQuery=self.query_with_partner_and_order.format(HanaSchema=env_file["HANA_SCHEMA"]),
                HanaSchema=env_file["HANA_SCHEMA"],
            )
        return sql_statement

    def get_input_type(self):
        query_string_params = get(self.event, "queryStringParameters", {})
        if "Ordre" in query_string_params and "PartenaireId" not in query_string_params:
            return "Ordre"
        else:
            return "OrdrePartenaireId"

    def add_s3_url(self, response):
        for item in get(response, "Liste", []):
            valise_url = item.get("Url")
            if valise_url and valise_url.startswith("http"):
                try:
                    valise_files = SharepointHandler.list_valise_file_from_sharepoint(valise_url.split("/")[-1], item.get("Type"))
                    if valise_files:
                        document_url = f"{valise_url}/{valise_files[0]}"
                        file_data = SharepointHandler.download_valise_file_from_sharepoint(document_url)

                        if file_data:
                            tmp_file = f"ws39/{uuid4()}/{valise_files[0]}"
                            upload_s3_file(TMP_BUCKET, tmp_file, file_data)
                            item["Url"] = create_s3_presigned_url(TMP_BUCKET, tmp_file)
                    else:
                        item["Url"] = None
                except NotFoundError:
                    item["Url"] = None

        return response

    def to_response(self, cursor, params=None):
        data = self.fetch(cursor, params)
        if not data:
            raise NotFoundError("Aucun devis/étude ne correspond à la sélection!")
        nb_items = len(data)
        data = self.layer(data, nb_items)
        data = self.add_s3_url(data)
        return data
