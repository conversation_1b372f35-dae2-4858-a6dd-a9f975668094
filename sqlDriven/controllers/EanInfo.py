from controllers.Controller import Controller


class EanInfo(Controller):
    def fetch(self, cursor, params):
        data = []
        result = cursor.fetchall()
        for row in result:
            r = self.row_processor(cursor, row)
            data.append(r)
        print(data)
        return {
            "PartenaireId": next(iter([x["PartenaireId"] for x in data if x["PartenaireId"]]), None),
            "Cpt": [x["Cpt"] for x in data if x["Cpt"]],
        }
