import pytz
from controllers.NoPagination import NoPagination

from utils.api import api_caller
from utils.auth_utils import getUserData
from utils.dict_utils import get
from utils.errors import BadRequestError, NotFoundError
from utils.token_utils import getToken

red_table = "public.cumulativeactiveenergyp1d"


class ws217(NoPagination):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.belgium_tz = pytz.timezone("Europe/Brussels")
        self.token = getToken(get(event, "headers", {}))
        self.query_params = event.get("queryStringParameters") or {}
        self.hashaddress = self.query_params.get("AddressHash")
        if not self.hashaddress:
            raise BadRequestError("AddressHash not found in query params", error_code="ADDRESSHASH_NOT_FOUND")
        self.user_data = getUserData(event)
        self.eans_by_address = self.get_eans_by_address_hash()

    def get_eans_by_address_hash(self):
        ean_tuple = tuple(ean_data["ean"] for ean_data in self.user_data.get("ean", []) if ean_data.get("address_hash") == self.hashaddress)
        if not ean_tuple:
            raise NotFoundError("Ean not found with the provided AddressHash", error_code="EAN_NOT_FOUND")
        return f"({', '.join(map(str, ean_tuple))})"

    def apply_hana_env(self, sql_statement):
        sql_statement = sql_statement.format(red_table=red_table, ean_tuple=self.eans_by_address)
        return sql_statement

    def to_response(self, cursor, params=None):
        data: list = super().to_response(cursor, params)
        conso_profil_energy = self.get_profile_conso()
        total_conso_previous_month = self.compute_last_month_conso(data)
        return self.group_data(conso_profil_energy, total_conso_previous_month)

    def get_profile_conso(self):
        response = api_caller("get", "/me/energy_profile", params={"AddressHash": self.hashaddress}, headers={"Authorization": "Bearer " + self.token})
        if response.get("TotalConsumption") in (None, 0):
            raise NotFoundError("There is no consumption target, maybe your profile is empty", error_code="NO_TARGET")
        return response["TotalConsumption"]

    def compute_last_month_conso(self, data: list[dict]) -> float | None:
        if data:
            return sum(conso["consumption_difference"] for conso in data)
        else:
            return None

    def group_data(self, conso_profil_energy, conso_previous_month):
        """
        ProfileTargetLow:
          type: number
          description: "Valeur basse de la cible de consommation d'énergie."
          example: 0
        ProfileTargetHigh:
          type: number
          description: "Valeur haute de la cible de consommation d'énergie (valeur idéale profil * 2)."
          example: 200
        RealConsumption:
          type: number
          description: "Consommation réelle du mois précédent (consommation - injection)."
          example: 100
        """
        return {
            "ProfileTargetLow": 0,
            "ProfileTargetHigh": conso_profil_energy / 12 * 2,  # /12 because the value is annual, *2 because our max is 2 time the target
            "RealConsumption": conso_previous_month,
        }
