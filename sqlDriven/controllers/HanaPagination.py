from controllers.Controller import Controller


class HanaPagination(Controller):
    def validate_request_params(self):
        ret = super().validate_request_params()

        page_items = self.getPaginationPageSize()
        page = self.getPaginationPage()
        page_offset = page * page_items

        ret["PageSize"] = page_items
        ret["Offset"] = page_offset
        return ret

    def fetch(self, cursor, params):
        data = []
        result = cursor.fetchall()
        self.nb_items = 0
        for row in result:
            r = self.row_processor(cursor, row)
            self.nb_items = r["NbItems"]
            del r["NbItems"]
            data.append(r)
        return data

    def load_sql_file(self, sql_file):
        """
        return the sql filename to load
        """

        base_statement = super().load_sql_file(sql_file)
        page_items = self.getPaginationPageSize()
        page = self.getPaginationPage()
        page_offset = page * page_items
        paginated_statement = base_statement.replace("(:PageItems)", str(page_items)).replace("(:PageOffset)", str(page_offset))

        return paginated_statement

    def layer(self, data, nbItems):
        return super().layer(data, self.nb_items)
