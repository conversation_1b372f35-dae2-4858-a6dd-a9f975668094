import json
import math
import os

import psycopg2

import utils.resa_api_constants as constants
from utils.aws_utils import get_secret, load_s3_file, get_resource_key
from utils.dict_utils import get
from utils.errors import NotFound<PERSON>rror, <PERSON>tt<PERSON><PERSON>rror, BadRequestError


def get_param_value(param_info, params_in_request):
    param_name = param_info["name"]
    if ("default" not in param_info) and param_info["name"] not in params_in_request:
        raise ValueError(constants.errmsg_dict["required"] % {"param_name": param_name})

    param_value = params_in_request[param_name] if param_name in params_in_request else param_info["default"]

    return param_value


def validate_request_param(param_value, param_info):
    if "validation" in param_info:
        validation_rules = param_info["validation"]
        param_name = param_info["name"]
        param_type = validation_rules["type"]
        if "sep" in validation_rules:
            try:
                param_value_list = param_value.split(validation_rules["sep"])
            except Exception:
                raise ValueError(constants.errmsg_dict["typeList"] % {"param_name": param_name, "sep": validation_rules["sep"]})
            typed_value = [validate_param_type(param_type, value_elem, param_info) for value_elem in param_value_list]
            valid_value = [validate_param(param_info, value_elem) for value_elem in typed_value]
        else:
            typed_value = validate_param_type(param_type, param_value, param_info)
            valid_value = validate_param(param_info, typed_value)
        return valid_value
    else:
        return param_value


def validate_param_type(param_type, param_value, param_info):
    type_validation_operators = {
        "int": validate_type_int,
        "float": validate_type_float,
        "bool": validate_type_bool,
    }
    typed_result = type_validation_operators[param_type](param_value, param_info)
    return typed_result


def validate_type_int(param_value, param_info):
    try:
        return int(param_value)
    except Exception:
        raise ValueError(constants.errmsg_dict["type"] % {"param_name": param_info["name"], "param_type": "entier"})


def validate_type_float(param_value, param_info):
    try:
        return float(param_value)
    except Exception:
        raise ValueError(constants.errmsg_dict["type"] % {"param_name": param_info["name"], "param_type": "réel"})


def validate_type_bool(param_value, param_info):
    if param_value not in ["true", "false", True, False]:
        raise ValueError(constants.errmsg_dict["type"] % {"param_name": param_info["name"], "param_type": "booléen"})
    else:
        return True if param_value in ["true", True] else False


def validate_param_range_min(rule_value, param_value, param_info):
    if param_value >= rule_value:
        return param_value
    else:
        raise ValueError(constants.errmsg_dict["rangeMin"] % {"param_name": param_info["name"], "range_min": str(rule_value)})


def validate_param_range_max(rule_value, param_value, param_info):
    if param_value <= rule_value:
        return param_value
    else:
        raise ValueError(constants.errmsg_dict["rangeMax"] % {"param_name": param_info["name"], "range_max": str(rule_value)})


def validate_param_range_list(rule_value, param_value, param_info):
    if param_value in rule_value:
        return param_value
    else:
        raise ValueError(constants.errmsg_dict["rangeList"] % {"param_name": param_info["name"], "param_value_list": str(rule_value)})


def validate_param(param_info, param_value):
    validation_rules = param_info["validation"]
    validation_operators = {
        "rangeMin": validate_param_range_min,
        "rangeMax": validate_param_range_max,
        "rangeList": validate_param_range_list,
    }
    validation_results = {
        rule_name: validation_operators[rule_name](rule_value, param_value, param_info) for rule_name, rule_value in validation_rules.items() if rule_name not in ["type", "sep"]
    }

    return validation_results


def default_row_processor(cursor, row):
    """
    return the row as key-value dictionnary
    """
    resource = {}
    for i in range(len(cursor.description)):
        column = cursor.description[i][0]
        resource[column] = row[i]
    return resource


class Controller:
    base64 = False
    raw_response = False

    def __init__(self, event, linking):
        self.event = event
        self.linking = linking
        self.row_processor = default_row_processor

    def headers(self):
        return {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": True,
        }

    def validate_request_params(self):
        """
        Validates request parameters and returns their string representation
        """
        params = get(self.linking, "params", {})
        ret = {}
        for param_id, param_info in params.items():
            params_in_request = self.event[param_info["source"]] if param_info["source"] in self.event else dict()

            if params_in_request:
                diff_request_expected = list(set(params_in_request) - set(params.keys()))
                """
                # thow an error if some parameters are not expected
                if len(diff_request_expected) > 0:
                    raise ValueError(constants.errmsg_dict['wrongParam'] % {
                                     'param_name': diff_request_expected[0]})
                """
                param_value = get_param_value(param_info, params_in_request)
                if param_value is not None:
                    validate_request_param(param_value, param_info)
                ret[param_id] = param_value

            elif "default" not in param_info:
                raise ValueError(constants.errmsg_dict["required"] % {"param_name": param_id})
            else:
                ret[param_id] = param_info["default"]
        for key, value in ret.items():
            ret[key] = self.transform_type(value)
        return ret

    def transform_type(self, var):
        """
        transorm the value to a sql readable format
        """
        ret = var
        if isinstance(var, list):
            ret = json.dumps(var)
        elif isinstance(var, dict):
            ret = json.dumps(var)
        return ret

    def sql_file(self):
        """
        return the sql filename to load
        """
        return get_resource_key(self.linking["sql_template"])

    def load_sql_file(self, filename):
        """
        return the sql statement
        """
        return load_s3_file(os.environ["MAPPING_BUCKET_NAME"], filename)

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        db_credentials = get_secret(self.get_secret())
        env_file = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        if get(db_credentials, "RDBMS") == "hana":
            sql_statement = sql_statement.format(HanaSchema=env_file["HANA_SCHEMA"])
        return sql_statement

    def get_secret(self):
        env = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
        return self.linking["secret"].format(**env)

    def db_connection(self):
        """
        return a db connection cursor
        """
        from utils.db_connector import make_connection

        db_credentials = get_secret(self.get_secret())
        cnx = make_connection(db_credentials)
        cursor = cnx.cursor()
        return cursor

    def fetch(self, cursor, params=None):
        """
        return the response body
        """
        if get(self.linking["return"], "type") == "list":
            data = []
            page = self.getPaginationPage()
            pageSize = self.getPaginationPageSize()
            try:
                cursor.scroll(page * pageSize)
            except Exception:
                if page * pageSize != 0:
                    raise NotFoundError("Pagination error")
            result = cursor.fetchmany(pageSize)
            if not result:
                raise NotFoundError(constants.errmsg_not_found, {})
            for row in result:
                r = self.row_processor(cursor, row)
                data.append(r)
            return data
        else:
            result = cursor.fetchone()
            if not result:
                raise NotFoundError(constants.errmsg_not_found, {})
            return self.row_processor(cursor, result)

    def execute_query(self, sql_statement, request_params):
        try:
            cursor = self.db_connection()
        except Exception:
            raise HttpError(500, constants.errmsg_dict["connection"], self.headers())
        try:
            cursor.execute(sql_statement, request_params)
        except psycopg2.errors.DataException as e:
            raise BadRequestError(str(e.diag.message_primary))
        return cursor

    def to_response(self, cursor, params=None):
        nb_items = cursor.rowcount
        data = self.fetch(cursor, params)
        data = self.layer(data, nb_items)
        return data

    def layer(self, data, nbItems):
        """
        add a layer upfront the data
        """
        container = get(get(self.linking, "return", {}), "container", {})
        for k in container:
            container[k] = data if container[k] == "{LAMBDA_RESPONSE}" else container[k]
        if get(get(self.linking, "return", {}), "type") == "list":
            if get(self.linking, "return", {}).get("pagination") is None or get(self.linking, "return", {}).get("pagination"):
                container["Pagination"] = {
                    "PageSize": self.getPaginationPageSize(),
                    "CurrentPage": self.getPaginationPage(),
                    "NbPage": math.ceil(nbItems / self.getPaginationPageSize()),
                }
        else:
            if not container:
                container = data
        return container

    def getPaginationPage(self):
        queryStringParameters = get(self.event, "queryStringParameters", {})
        page = int(get(queryStringParameters, "Page", "0"))
        return page

    def getPaginationPageSize(self):
        queryStringParameters = get(self.event, "queryStringParameters", {})
        pageSize = int(get(queryStringParameters, "PageSize", "20"))
        return pageSize
