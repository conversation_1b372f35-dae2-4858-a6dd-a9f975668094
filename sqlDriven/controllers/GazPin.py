from controllers.parallel_controller import Controller
from utils.aws_utils import get_secret_string
from utils.errors import UnauthorizedError


class GazPin(Controller):
    def validate_request_params(self):
        """
        Validates request parameters and returns their string representation
        + Check if correct APIKey
        """
        ret = super(Gaz<PERSON>in, self).validate_request_params()
        ret["sn"] = ret.get("sn", "").upper()

        # Check for APIKey
        if ret["apikey"] != get_secret_string("MyResaAPI/GazPinAPIKey"):
            raise UnauthorizedError("The given APIKey is not authorized to perform this request.")

        return ret
