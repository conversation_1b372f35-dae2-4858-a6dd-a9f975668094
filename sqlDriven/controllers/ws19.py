from controllers.parallel_controller import DatabaseWorker
from controllers.parallel_controller import <PERSON><PERSON>l<PERSON><PERSON><PERSON><PERSON>_Deprecated
from utils.aws_utils import get_secret
from utils.dict_utils import get, first
from utils.errors import NotFoundError
from utils.log_utils import log_info
from utils.string_utils import ifstrip
from utils.type_utils import int_or_none


class ws19(ParallelController_Deprecated):
    def execute_query(self, sql_statement, request_params):
        # Parameters for all queries
        db_credentials = get_secret(super().get_secret())
        result_dict = {}
        inner_fetcher = self.fetch

        # From EAN to energy type, installation number and consumption point
        get_installation = DatabaseWorker(
            "get_installation",
            db_credentials,
            sql_statement["get_installation"],
            {"Ean": request_params["Ean"]},
            inner_fetcher,
            result_dict,
        )
        get_installation.start()
        get_installation.join()
        install_result = result_dict["get_installation"]
        if not install_result:
            raise NotFoundError("EAN non trouvé")
        if isinstance(install_result, list):
            install_result = first(install_result)

        get_adresse = DatabaseWorker(
            "get_adress_info",
            db_credentials,
            sql_statement["get_adress_info"],
            {"VSTELLE": install_result["VSTELLE"]},
            inner_fetcher,
            result_dict,
        )

        get_adresse.start()
        get_adresse.join()
        adress_info = result_dict["get_adress_info"]
        log_info("WS19 result")
        log_info(adress_info)

        full_adress = " ".join(
            [
                (ifstrip(get(adress_info, "INSTALL_CITY1", "")) + "," if get(adress_info, "INSTALL_CITY1", None) else ""),
                ifstrip(get(adress_info, "INSTALL_STREET", "")),
                ifstrip(get(adress_info, "INSTALL_HOUSENUM_1", "")),
                ifstrip(get(adress_info, "INSTALL_HOUSENUM_2", "")),
            ]
        )

        result = {
            "Adresse": full_adress.strip(),
            "Localite": ifstrip(get(adress_info, "INSTALL_CITY1", "")),
            "Cdpostal": ifstrip(get(adress_info, "INSTALL_POSTCODE_1", "")),
            "Rue": ifstrip(get(adress_info, "INSTALL_STREET", "")),
            "NumRue": int_or_none(ifstrip(get(adress_info, "INSTALL_HOUSENUM_1", "").replace("\n", "")) or None),
            "NumCompl": ifstrip(get(adress_info, "HAUS_NUM2", "").replace("\n", "")) or None,
            "IdRue": ifstrip(get(adress_info, "STRT_CODE", "")),
            "IdLocalite": ifstrip(get(adress_info, "INSTALL_CITYCODE", "")),
            "IdAdresse": ifstrip(get(adress_info, "INSTALL_ADDRNUMBER", "")),
            "IdRadRue": int_or_none(ifstrip(get(adress_info, "IDRAD_RUE", "").replace("\n", "")) or None),
            "IdRadLocalite": int_or_none(ifstrip(get(adress_info, "IDRAD_LOCALITE", "").replace("\n", "")) or None),
        }

        return result

    def to_response(self, fake_cursor, params=None):
        return fake_cursor
