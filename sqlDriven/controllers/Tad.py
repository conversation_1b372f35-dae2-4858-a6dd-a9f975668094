import json
from os import environ

from controllers.HanaPagination import HanaPagination
from utils.aws_utils import load_s3_file, get_resource_key
from utils.errors import NotFoundError


class Tad(HanaPagination):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        cp_zones = json.loads(load_s3_file(environ["BUCKET"], get_resource_key("SURFACES_CP.json")))
        self.cp_zones = {zone["name"]: zone for zone in cp_zones}

    def fetch(self, cursor, params=None):
        try:
            result = super().fetch(cursor, params)
            self.add_zones(result)
            return result
        except NotFoundError:
            return []

    def add_zones(self, results):
        for result in results:
            result["Zone"] = self.cp_zones.get(f"CP-{result['Zipcode']}")
