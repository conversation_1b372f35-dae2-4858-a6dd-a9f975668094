from controllers.Controller import Controller


class Pascal<PERSON>ase<PERSON><PERSON>roller(Controller):
    def to_response(self, cursor, params=None):
        response = super().to_response(cursor, params)
        response = self.convert_case(response)
        return response

    @classmethod
    def convert_case(cls, response: any) -> any:
        if isinstance(response, dict):
            return {cls.to_pascal_case(key): cls.convert_case(item) for key, item in response.items()}
        elif isinstance(response, list):
            return [cls.convert_case(item) for item in response]
        else:
            return response

    @staticmethod
    def to_pascal_case(snake_str: str) -> str:
        return "".join(x.title() for x in snake_str.split("_"))
