import os

from controllers.NoPagination import NoPagination
from utils.dict_utils import first
from utils.errors import NotFoundError


class ws43(NoPagination):
    def __init__(self, event, linking):
        super().__init__(event, linking)

    def fetch(self, cursor, params=None):
        resp = super().fetch(cursor, params)
        if len(resp) == 0:
            if os.environ["LANG"] == "DE":
                raise NotFoundError("Fehler keine Entlastungsscheibe gefunden!")
            else:
                raise NotFoundError("Erreur aucune tranche de délestage trouvée !")
        return first(resp)
