from math import ceil

from controllers.NoPagination import NoPagination

from utils.api import api_caller
from utils.dict_utils import get
from utils.models.pydantic_utils import PascalModel


class CptForfait(PascalModel):
    """
    Represent a forfait metadata.

    Attributes
    ----------
    label : str
        The label or name of the forfait.
    phases : int
        The number of electrical phases included in the forfait.
    amperage : float
        The amperage value of the forfait.
    power : float
        The power rating of the forfait in watts or kilowatts.
    price : float
        The cost of the forfait.

    """

    label: str
    phases: int
    amperage: float
    power: float
    price: float


class CptSpec(PascalModel):
    """
    Represent a meter specification given as an input element in the WS63.

    Attributes
    ----------
    id_cpt : int
        Identifier for the component.
    nb_phase : int
        Number of phases in the system.
    amperage : float
        Amperage value of the system.
    puissance : float
        Power (in watts) associated with the system.

    """

    id_cpt: int
    nb_phase: int
    amperage: float
    puissance: float


class Ws63Input(PascalModel):
    """
    Represent input data for the Ws63 model.

    Attributes
    ----------
    liste : list of Ws63InputElement
        Contains a list of `Ws63InputElement` instances required as input
        for the Ws63 model.

    """

    liste: list[CptSpec]


class ws63(NoPagination):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.cpt_specs: list[CptSpec] = []

    def validate_request_params(self):
        body = Ws63Input.model_validate(self.event["body"])
        self.cpt_specs = body.liste
        return {}

    def to_response(self, data, params=None):
        # Convert all Decimal to float
        tarifs = {elem["Id"]: float(elem["Price"]) for elem in super().to_response(data)}

        result = [_get_racc_price(tarifs, elem) for elem in self.cpt_specs]

        # *100 each price then /100 total to avoid sum imprecision on float
        total_amount = sum(round(get(elem, "PrixHtva", 0) * 100) for elem in result) / 100
        return {"MontantTotal": total_amount, "Liste": result}


def _get_racc_price(tarifs: dict[str, float], cpt_spec: CptSpec) -> dict:
    total_price = 0

    forfaits_sap = api_caller(
        method="GET",
        path="/raccordement/forfaits",
    )
    forfait_choice = [CptForfait.recursive_construct(**forfait) for forfait in forfaits_sap]
    max_forfait = forfait_choice[-1]

    # Find a fitting forfait
    selected_forfait = None
    if cpt_spec.puissance <= max_forfait.power:
        for forfait in forfait_choice:
            if cpt_spec.nb_phase == forfait.phases and cpt_spec.amperage <= forfait.amperage and cpt_spec.puissance <= forfait.power:
                selected_forfait = forfait
                break
    elif cpt_spec.nb_phase == max_forfait.phases:
        selected_forfait = max_forfait

    if selected_forfait:
        total_price += selected_forfait.price
        if selected_forfait == max_forfait and cpt_spec.puissance > max_forfait.power:
            # Custom forfait, EK001 * (kva - 69.3 => rounded at 1/10)
            total_price += tarifs["EK001"] * ceil(cpt_spec.puissance * 10 - max_forfait.power * 10) / 10
    else:
        # If no forfait found that match
        return {"IdCpt": cpt_spec.id_cpt}

    return {
        "IdCpt": cpt_spec.id_cpt,
        "PrixHtva": round(total_price, 2),
        "LibIdCpt": selected_forfait.label,
    }
