from ws59_actions.util import map_valeur_info


def ctrl_admc_post_query_action(actions_infos, config_dosrac, dossier_auart):
    # Handles admc actions
    del_actions_infos = []
    for action_info in actions_infos:
        valeur_info = ctrl_admc_actions[action_info["ACTION"]](dossier_auart, config_dosrac)

        if valeur_info:
            action_info["ValeurInfo"] = valeur_info
            del action_info["ACTION"]
            del action_info["ACTION_DOSSIER"]
        else:
            del_actions_infos.append(action_info)

    for del_action_info in del_actions_infos:
        actions_infos.remove(del_action_info)


def _ctrl_admc1_post_query_action(auart, dosrac):
    if auart == "DRG1":
        return map_valeur_info(dosrac.get("RAC_G_PREQ_MES_ATT_CONF"))


def _ctrl_admc2_post_query_action(auart, dosrac):
    if dosrac.get("RAC_PREQ_MES_MVI") == "O":
        if dosrac.get("RAC_MOVE_IN_DATE"):
            return "RECU"
        else:
            return "NECESSAIRE"


def _ctrl_admc3_post_query_action(auart, dosrac):
    if auart == "DRE1":
        return map_valeur_info(dosrac.get("RAC_E_PREQ_MES_PV"), False)
    elif auart == "DRG1":
        return map_valeur_info(dosrac.get("RAC_G_PREQ_MES_PV_RECEP"))


def _ctrl_admc4_post_query_action(auart, dosrac):
    if auart == "DRE1":
        return map_valeur_info(dosrac.get("RAC_E_PREQ_MES_PV_DEC"))


ctrl_admc_actions = {
    "CTRL_ADMC1": _ctrl_admc1_post_query_action,
    "CTRL_ADMC2": _ctrl_admc2_post_query_action,
    "CTRL_ADMC3": _ctrl_admc3_post_query_action,
    "CTRL_ADMC4": _ctrl_admc4_post_query_action,
}
