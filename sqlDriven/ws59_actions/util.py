VALEUR_INFO_RECU = "RECU"
VALEUR_INFO_NECESSAIRE = "NECESSAIRE"
VALEUR_INFO_NON_NECESSAIRE = "NON_NECESSAIRE"
VALEUR_INFO_NECESSAIRE_SUR_PLACE = "NECESSAIRE_SUR_PLACE"


def map_valeur_info(val, op_mandatory=True):
    if val == "O" or (val == "OP" and not op_mandatory):
        return VALEUR_INFO_RECU
    elif val == "N" or (val == "OP" and op_mandatory):
        return VALEUR_INFO_NECESSAIRE
    elif val == "P":
        return VALEUR_INFO_NON_NECESSAIRE
    elif val == "OS":
        return VALEUR_INFO_NECESSAIRE_SUR_PLACE
