from ws59_actions.util import VALEUR_INFO_NECESSAIRE, VALEUR_INFO_RECU, map_valeur_info

DOSS_SUP = "SUP"  # Dossier supérieur (de tête)
DOSS_INF = "INF"  # Dossier inférieur (sous-dossier)
DOSS_IND = "IND"  # Dossier indépendant

ACTION_DOSSIER_SUP = "S"  # Action on dossier supérieur
ACTION_DOSSIER_INF = "I"  # Action on dossier inférieur


def ctrl_adrc_post_query_action(dossier, actions_infos, config_dosrac, dossier_auart):
    if dossier["NumDossierInf"]:
        type_doss = DOSS_SUP
    elif dossier["NumDossierSup"]:
        type_doss = DOSS_INF
    else:
        type_doss = DOSS_IND

    # Handles ADRC actions
    del_actions_infos = []
    for action_info in actions_infos:
        valeur_info = None
        if type_doss == DOSS_SUP and action_info["ACTION_DOSSIER"] == ACTION_DOSSIER_SUP:
            valeur_info = ctrl_adrc_actions[action_info["ACTION"]](dossier_auart, config_dosrac)
        elif type_doss == DOSS_INF and action_info["ACTION_DOSSIER"] == ACTION_DOSSIER_INF:
            valeur_info = ctrl_adrc_actions[action_info["ACTION"]](dossier_auart, config_dosrac)
        elif type_doss == DOSS_IND or not action_info["ACTION_DOSSIER"]:
            valeur_info = ctrl_adrc_actions[action_info["ACTION"]](dossier_auart, config_dosrac)

        if valeur_info:
            action_info["ValeurInfo"] = valeur_info
            del action_info["ACTION"]
            del action_info["ACTION_DOSSIER"]
        else:
            del_actions_infos.append(action_info)

    for del_action_info in del_actions_infos:
        actions_infos.remove(del_action_info)


def _ctrl_adrc1_post_query_action(auart, dosrac):
    if auart == "DRE1":
        if dosrac.get("RAC_E_RACC_MES") == "1":
            return map_valeur_info(dosrac.get("RAC_E_PREQ_MES_PV"), False)
    elif auart == "DRG1":
        if dosrac.get("RAC_G_RACC_MES") == "1":
            return map_valeur_info(dosrac.get("RAC_G_PREQ_MES_PV_RECEP"))


def _ctrl_adrc2_post_query_action(auart, dosrac):
    if auart == "DRE1":
        if dosrac.get("RAC_E_RACC_MES") == "1" and dosrac.get("RAC_PREQ_MES_MVI") == "O":
            if dosrac.get("RAC_MOVE_IN_DATE"):
                return VALEUR_INFO_RECU
            else:
                return VALEUR_INFO_NECESSAIRE
    if auart == "DRG1":
        if dosrac.get("RAC_G_RACC_MES") == "1" and dosrac.get("RAC_PREQ_MES_MVI") == "O":
            if dosrac.get("RAC_MOVE_IN_DATE"):
                return VALEUR_INFO_RECU
            else:
                return VALEUR_INFO_NECESSAIRE


def _ctrl_adrc3_post_query_action(auart, dosrac):
    return map_valeur_info(dosrac.get("RAC_PREQ_RAC_CLI_PRET"))


def _ctrl_adrc4_post_query_action(auart, dosrac):
    return map_valeur_info(dosrac.get("RAC_PREQ_EAN"))


def _ctrl_adrc5_post_query_action(auart, dosrac):
    return map_valeur_info(dosrac.get("RAC_PREQ_RAC_CONV"))


def _ctrl_adrc6_post_query_action(auart, dosrac):
    return map_valeur_info(dosrac.get("RAC_PREQ_RAC_CONT"))


def _ctrl_adrc7_post_query_action(auart, dosrac):
    return map_valeur_info(dosrac.get("RAC_PREQ_RAC_MAT_AGREA"))


def _ctrl_adrc8_post_query_action(auart, dosrac):
    return map_valeur_info(dosrac.get("RAC_PREQ_RAC_FORF_AGREA"))


def _ctrl_adrc9_post_query_action(auart, dosrac):
    if auart == "DRG1":
        if dosrac.get("RAC_G_RACC_MES") == "1":
            return map_valeur_info(dosrac.get("RAC_G_PREQ_MES_ATT_CONF"))


def _ctrl_adrc10_post_query_action(auart, dosrac):
    if auart == "DRE1":
        if dosrac.get("RAC_E_RACC_MES") == "1":
            return map_valeur_info(dosrac.get("RAC_E_PREQ_MES_PV_DEC"))


ctrl_adrc_actions = {
    "CTRL_ADRC1": _ctrl_adrc1_post_query_action,
    "CTRL_ADRC2": _ctrl_adrc2_post_query_action,
    "CTRL_ADRC3": _ctrl_adrc3_post_query_action,
    "CTRL_ADRC4": _ctrl_adrc4_post_query_action,
    "CTRL_ADRC5": _ctrl_adrc5_post_query_action,
    "CTRL_ADRC6": _ctrl_adrc6_post_query_action,
    "CTRL_ADRC7": _ctrl_adrc7_post_query_action,
    "CTRL_ADRC8": _ctrl_adrc8_post_query_action,
    "CTRL_ADRC9": _ctrl_adrc9_post_query_action,
    "CTRL_ADRC10": _ctrl_adrc10_post_query_action,
}
