import unittest
from unittest.mock import Mock, patch

from utils.api import api_caller
from utils.mocking import get_mock_commune_user_token
from utils.mocking import mock_api


@patch(
    "controllers.Controller.Controller.to_response",
    new=Mock(
        return_value=[
            {
                "ANLAGE": "4100746852",
                "ANZART": "DD",
                "ATWRT": "E",
                "CatCadran": "LO",
                "CatTarif": "EDA_LO",
                "CodeCadran": "1.6.1",
                "EQUNR": "000000000501356427",
                "ETDZ_ZWNUMMER": "001",
                "Ean": "541449011000143560",
                "LOGIKZW": "000000000002456657",
                "MATNR": "000000000000100940",
                "NumCpt": "000000000064285947",
                "STANZNAC": "03",
                "STANZVOR": "01",
                "ZWGRUPPE": "00000097",
                "ZWZUART": None,
                "lv_nb_lines": 6,
            },
            {
                "ANLAGE": "4100746852",
                "ANZART": "DD",
                "ATWRT": "E",
                "CatCadran": "HI",
                "CatTarif": "EDA_HI",
                "CodeCadran": "1.6.2",
                "EQUNR": "000000000501356427",
                "ETDZ_ZWNUMMER": "002",
                "Ean": "541449011000143560",
                "LOGIKZW": "000000000002456658",
                "MATNR": "000000000000100940",
                "NumCpt": "000000000064285947",
                "STANZNAC": "03",
                "STANZVOR": "01",
                "ZWGRUPPE": "00000097",
                "ZWZUART": None,
                "lv_nb_lines": 6,
            },
        ]
    ),
)
@mock_api
class TestWS197(unittest.TestCase):
    test_response = [
        {
            "Ean": "541449011000143560",
            "NumCpt": "000000000064285947",
            "EQUNR": "000000000501356427",
            "LOGIKZW": "000000000002456657",
            "CatCadran": "LO",
            "STANZVOR": "01",
            "STANZNAC": "03",
            "ZWZUART": None,
            "ETDZ_ZWNUMMER": "001",
            "MATNR": "000000000000100940",
            "ZWGRUPPE": "00000097",
            "CodeCadran": "1.6.1",
            "ANZART": "DD",
            "ANLAGE": "4100746852",
            "CatTarif": "EDA_LO",
            "lv_nb_lines": 6,
            "ATWRT": "E",
            "Cadran": "NUIT",
        },
        {
            "Ean": "541449011000143560",
            "NumCpt": "000000000064285947",
            "EQUNR": "000000000501356427",
            "LOGIKZW": "000000000002456658",
            "CatCadran": "HI",
            "STANZVOR": "01",
            "STANZNAC": "03",
            "ZWZUART": None,
            "ETDZ_ZWNUMMER": "002",
            "MATNR": "000000000000100940",
            "ZWGRUPPE": "00000097",
            "CodeCadran": "1.6.2",
            "ANZART": "DD",
            "ANLAGE": "4100746852",
            "CatTarif": "EDA_HI",
            "lv_nb_lines": 6,
            "ATWRT": "E",
            "Cadran": "JOUR",
        },
    ]

    @classmethod
    def setUpClass(cls):
        cls.token = get_mock_commune_user_token()

    def test_success(self):
        response = api_caller(
            "GET",
            "/communes/ean/cadrans",
            headers={"Authorization": "Bearer " + self.token},
            raw=True,
        )
        self.assertEqual(response.status_code, 200)
        self.assertListEqual(response.json(), self.test_response)


if __name__ == "__main__":
    unittest.main()
