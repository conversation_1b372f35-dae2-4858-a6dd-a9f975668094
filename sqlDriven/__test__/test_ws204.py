import unittest
from unittest.mock import Mock, patch

from utils.api import api_caller
from utils.mocking import mock_api


@patch(
    "controllers.Controller.Controller.to_response",
    new=Mock(
        return_value={
            "TotalPowerElec": 27.721,
        },
    ),
)
@mock_api
class TestWS204Call(unittest.TestCase):
    def test_success(self):
        response = api_caller(
            "GET",
            "/installation",
            params={
                "Ean": "541449020710302969",
                "MeterId": "34175932",
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertDictEqual(
            response_data,
            {
                "TotalPowerElec": 27.721,
            },
        )

    def test_success_strip_meter_id(self):
        response = api_caller(
            "GET",
            "/installation",
            params={
                "Ean": "541449020710302969",
                "MeterId": "00000000034175932",
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertDictEqual(
            response_data,
            {
                "TotalPowerElec": 27.721,
            },
        )

    def test_missing_ean(self):
        response = api_caller(
            "GET",
            "/installation",
            params={
                "MeterId": "34175932",
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertEqual(response_data.get("Error"), 400)
        self.assertEqual(response_data.get("Message"), "Le paramètre Ean est requis")

    def test_missing_meter_id(self):
        response = api_caller(
            "GET",
            "/installation",
            params={
                "Ean": "541449020710302969",
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertEqual(response_data.get("Error"), 400)
        self.assertEqual(response_data.get("Message"), "Le paramètre MeterId est requis")


if __name__ == "__main__":
    unittest.main()
