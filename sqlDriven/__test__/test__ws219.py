import unittest
from datetime import datetime
from unittest.mock import Mock, patch

from utils.api import api_caller
from utils.errors import HttpError
from utils.mocking import get_mock_user_token, mock_api

tmp_result = [
    {
        "MOTIFRELEVE": "Relevé périodique",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20220904",
        "RELEVEINDEXROUND": 0,
        "RELEVEINDEXDECIMAL": 0,
        "CATTARIF": "ECA_PL",
        "CATCADRAN": "PL",
        "CODECADRAN": "",
        "ZWNUMMER": "004",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé périodique",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20220904",
        "RELEVEINDEXROUND": 13321,
        "RELEVEINDEXDECIMAL": 0.70000000000000,
        "CATTARIF": "ECA_PH",
        "CATCADRAN": "PH",
        "CODECADRAN": "",
        "ZWNUMMER": "003",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé périodique",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20220904",
        "RELEVEINDEXROUND": 168637,
        "RELEVEINDEXDECIMAL": 0.20000000000000,
        "CATTARIF": "ECA_NL",
        "CATCADRAN": "NL",
        "CODECADRAN": "",
        "ZWNUMMER": "002",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé périodique",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20220904",
        "RELEVEINDEXROUND": 105070,
        "RELEVEINDEXDECIMAL": 0.10000000000000,
        "CATTARIF": "ECA_NH",
        "CATCADRAN": "NH",
        "CODECADRAN": "",
        "ZWNUMMER": "001",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé intermédiaire avec calcul fact.",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20230808",
        "RELEVEINDEXROUND": 108225,
        "RELEVEINDEXDECIMAL": 0.60000000000000,
        "CATTARIF": "ECA_NH",
        "CATCADRAN": "NH",
        "CODECADRAN": "",
        "ZWNUMMER": "001",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé intermédiaire avec calcul fact.",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20230808",
        "RELEVEINDEXROUND": 174652,
        "RELEVEINDEXDECIMAL": 0.20000000000000,
        "CATTARIF": "ECA_NL",
        "CATCADRAN": "NL",
        "CODECADRAN": "",
        "ZWNUMMER": "002",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé intermédiaire avec calcul fact.",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20230808",
        "RELEVEINDEXROUND": 14339,
        "RELEVEINDEXDECIMAL": 0.60000000000000,
        "CATTARIF": "ECA_PH",
        "CATCADRAN": "PH",
        "CODECADRAN": "",
        "ZWNUMMER": "003",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé intermédiaire avec calcul fact.",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20230808",
        "RELEVEINDEXROUND": 0,
        "RELEVEINDEXDECIMAL": 0,
        "CATTARIF": "ECA_PL",
        "CATCADRAN": "PL",
        "CODECADRAN": "",
        "ZWNUMMER": "004",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé périodique",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20230903",
        "RELEVEINDEXROUND": 0,
        "RELEVEINDEXDECIMAL": 0,
        "CATTARIF": "ECA_PL",
        "CATCADRAN": "PL",
        "CODECADRAN": "",
        "ZWNUMMER": "004",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé périodique",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20230903",
        "RELEVEINDEXROUND": 14402,
        "RELEVEINDEXDECIMAL": 0.50000000000000,
        "CATTARIF": "ECA_PH",
        "CATCADRAN": "PH",
        "CODECADRAN": "",
        "ZWNUMMER": "003",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé périodique",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20230903",
        "RELEVEINDEXROUND": 174856,
        "RELEVEINDEXDECIMAL": 0.80000000000000,
        "CATTARIF": "ECA_NL",
        "CATCADRAN": "NL",
        "CODECADRAN": "",
        "ZWNUMMER": "002",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé périodique",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par le client",
        "DATERELEVE": "20230903",
        "RELEVEINDEXROUND": 108338,
        "RELEVEINDEXDECIMAL": 0.20000000000000,
        "CATTARIF": "ECA_NH",
        "CATCADRAN": "NH",
        "CODECADRAN": "",
        "ZWNUMMER": "001",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé intermédiaire avec calcul fact.",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par l'EPTD",
        "DATERELEVE": "20230920",
        "RELEVEINDEXROUND": 0,
        "RELEVEINDEXDECIMAL": 0,
        "CATTARIF": "ECA_PL",
        "CATCADRAN": "PL",
        "CODECADRAN": "",
        "ZWNUMMER": "004",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
    {
        "MOTIFRELEVE": "Relevé pr dépose pr raisons calc. fact.",
        "EAN": "541456700002569958",
        "SUPPLIERNAME": "ENGIE (anc. Electrabel) - Elec",
        "TYPERELEVE": "Relevé par l'EPTD",
        "DATERELEVE": "20230920",
        "RELEVEINDEXROUND": 0,
        "RELEVEINDEXDECIMAL": 0,
        "CATTARIF": "ECA_PL",
        "CATCADRAN": "PL",
        "CODECADRAN": "",
        "ZWNUMMER": "004",
        "ATWRT": "I",
        "ANZART": "ME",
        "lv_nb_lines": 4,
    },
]


@mock_api
@patch("controllers.Controller.Controller.to_response", new=Mock(return_value=tmp_result))
class TestIndexHistory(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.token = get_mock_user_token()
        cls.user_ean = "541456700002569958"

    def test_get_wrong_ean(self):
        with self.assertRaises(HttpError) as context:
            api_caller(
                "GET",
                "/ean/w12345/history",
                headers={"Authorization": "Bearer " + self.token},
            )

        e = context.exception
        self.assertEqual(e.status, 403)
        self.assertEqual(e.message, "This EAN doesn't belong to the given user.")
        self.assertEqual(e.error_code, "INVALID_EAN_FOR_USER")

    def test_get_index_history(self):
        response = api_caller(
            "GET",
            f"/ean/{self.user_ean}/history",
            headers={"Authorization": "Bearer " + self.token},
            raw=True,
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIsInstance(data, list)

        expected_keys = {"Date", "Index", "Consumption", "Register", "RegisterCode", "ReadingReason", "ReadingType", "Supplier"}

        for i, item in enumerate(data):
            self.assertIsInstance(item, dict, f"L'élément {i} n'est pas un dictionnaire.")
            self.assertTrue(expected_keys.issubset(item.keys()), f"L'élément {i} ne contient pas toutes les clés requises : {expected_keys - item.keys()}")
            try:
                datetime.strptime(item["Date"], "%Y-%m-%d")
            except ValueError:
                self.fail(f"L'élément {i} a un format de date invalide : {item['Date']}")
