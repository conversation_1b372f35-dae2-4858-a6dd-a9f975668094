import unittest
from unittest.mock import patch, Mock

from utils.api import api_caller
from utils.errors import NotFoundError
from utils.mocking import mock_api


def _fetch_mock_data(cursor, query):
    if query["BP"] == "4100157967":
        return {
            "Bp": "31123456789",
        }
    else:
        raise NotFoundError


@mock_api
@patch(
    "controllers.Controller.Controller.to_response",
    new=Mock(side_effect=_fetch_mock_data),
)
class Testws188(unittest.TestCase):
    def testGetId(self):
        id_hgz = 4100157967
        call_get_bp = api_caller("get", f"/bp/{id_hgz}/myre", body=None)
        self.assertTrue(call_get_bp["Bp"].startswith("31"))

    def testWrongId(self):
        wrong_id = 123456
        response = api_caller("get", f"/bp/{wrong_id}/myre", raw=True)
        self.assertEqual(response.status_code, 404)
