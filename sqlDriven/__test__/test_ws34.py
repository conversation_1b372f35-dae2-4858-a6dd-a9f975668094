import unittest
from unittest.mock import Mock, patch

from utils.api import api_caller
from utils.mocking import get_mock_user_token
from utils.mocking import mock_api


def _mock_db_run(self):
    if self.result_name == "get_ean_from_counter" and self.params["NumCompteur"] == "000000000009837377":
        self.result_dict[self.result_name] = {"EAN": "541460900001145396"}
    else:
        self.result_dict[self.result_name] = None


@patch(
    "controllers.parallel_controller.DatabaseWorker.run",
    new=Mock(side_effect=_mock_db_run),
)
@mock_api
class TestWS34(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.token = get_mock_user_token()

    def test_ean_cpt_success(self):
        response = api_caller(
            "GET",
            "/index/historique?Ean=541460900001145396&NumCompteur=000000000009837377",
            raw=True,
        )
        self.assertEqual(response.status_code, 200)
        self.assertDictEqual(response.json(), {"Contrat": [], "Consommation": []})

    def test_ean_cpt_error_no_match(self):
        response = api_caller(
            "GET",
            "/index/historique?Ean=541460900001145396&NumCompteur=000000000009837376",
            raw=True,
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["ErrorCode"], "EAN_CPT_NO_MATCH")

    def test_ean_success(self):
        response = api_caller(
            "GET",
            "/index/historique?Ean=541460900001145396",
            headers={"Authorization": "Bearer " + self.token},
            raw=True,
        )
        self.assertEqual(response.status_code, 200)
        self.assertDictEqual(response.json(), {"Contrat": [], "Consommation": []})

    def test_ean_error_ean_not_allowed(self):
        response = api_caller(
            "GET",
            "/index/historique?Ean=541460900001145397",
            headers={"Authorization": "Bearer " + self.token},
            raw=True,
        )
        self.assertEqual(response.status_code, 403)
        self.assertEqual(response.json()["ErrorCode"], "INVALID_EAN_FOR_USER")

    def test_ean_error_auth_missing(self):
        response = api_caller("GET", "/index/historique?Ean=541460900001145396", raw=True)
        self.assertEqual(response.status_code, 401)
        self.assertEqual(response.json()["ErrorCode"], "MISSING_TOKEN")


if __name__ == "__main__":
    unittest.main()
