import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from utils.errors import BadRequestError, ForbiddenError
from utils.mocking import get_mock_commune_user_token, mock_api

linking_dict = {
    "controller": "CommuneControllerIndexSmart",
    "id": "WS220",
    "type": "sqlDriven",
    "secret": "{REDSHIFT_SECRET}",
    "sql_template": {
        "monthly": "sqlStatements/ws205_get_monthly_conso.sql",
        "daily": "sqlStatements/ws205_get_daily_conso.sql",
        "get_eans": "sqlStatements/ws130_get_communes_eans.sql",
    },
    "params": {
        "Type": {"default": None, "name": "Type", "source": "queryStringParameters"},
        "StartDate": {"default": None, "name": "StartDate", "source": "queryStringParameters"},
        "EndDate": {"default": None, "name": "EndDate", "source": "queryStringParameters"},
        "Ean": {"default": None, "name": "Ean", "source": "queryStringParameters"},
        "MeterId": {"default": None, "name": "MeterId", "source": "queryStringParameters"},
    },
    "roles": ["INDEX_CPT_CONSU", "INDEX_CPT_GERER"],
}

event_dict = {
    "body": {},
    "headers": {
        "Accept": "application/json",
        "Accept-Encoding": "gzip, deflate, br",
        "Authorization": "",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Host": "127.0.0.1:8011",
        "Postman-Token": "9188aecc-93d4-4441-84f5-ce9bf2ee26c6",
        "User-Agent": "PostmanRuntime/7.43.0",
        "X-Api-Key": "nope",
    },
    "httpMethod": "GET",
    "path": "/communes/index/smart",
    "pathParameters": {},
    "queryStringParameters": {"Ean": "541456700000626783", "EndDate": "2024-03-01", "MeterId": "1SAG3105225187", "StartDate": "2024-01-01", "Type": "MONTHLY"},
    "requestContext": {"domainName": "127.0.0.1:8011", "path": "/communes/index/smart", "resourcePath": "/communes/index/smart", "stage": "v0"},
    "resource": "/communes/index/smart",
}


@mock_api
@patch("controllers.CommuneControllerIndexSmart.CommuneControllerIndexSmart._get_ean_info", new=Mock(return_value={"541456700000626783": "elec", "541449060005835802": "elec"}))
class TestWS220Call(unittest.TestCase):
    flask = None

    @classmethod
    def setUpClass(cls):
        from controllers.CommuneControllerIndexSmart import CommuneControllerIndexSmart

        cls.token = get_mock_commune_user_token()
        event_dict["headers"]["Authorization"] = "Bearer " + cls.token
        cls.instance = CommuneControllerIndexSmart(event_dict, linking=linking_dict)
        cls.base_params = cls.instance.params
        cls.base_type_params = cls.instance.type_params

    def setUp(self):
        # restore original params
        self.instance.params = self.base_params
        self.instance.type_params = self.base_type_params

    def test_build_query(self):
        """Test build_query logic."""
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-03-01",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)

    def test_build_query_monthly(self):
        """Test build_query logic for MONTHLY type."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-03-01",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01 00:00:00 +0100' AND '2024-03-01 00:00:00 +0100'", result)

    def test_build_query_monthly_2(self):
        """Test build_query logic for MONTHLY type."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-18",
            "EndDate": "2024-03-21",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-18", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-03-01 00:00:00 +0100' AND '2024-03-22 00:00:00 +0100'", result)

    def test_build_query_monthly_3(self):
        """Test build_query logic for MONTHLY type."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-31",
            "EndDate": "2024-03-30",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-31", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01 00:00:00 +0100' AND '2024-03-01 00:00:00 +0100'", result)
        self.assertIn("measuredatetime BETWEEN '2024-03-01 00:00:00 +0100' AND '2024-03-31 00:00:00 +0100'", result)

    def test_build_query_daily(self):
        """Test build_query logic for DAILY type."""
        self.instance.type_params = "DAILY"
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-01-02",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("BETWEEN '2024-01-01 00:00:00 +0100' AND '2024-01-03 00:00:00 +0100'", result)

    def test_build_query_hourly(self):
        """Test build_query logic for HOURLY type."""
        self.instance.type_params = "HOURLY"
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-01-01",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp15m", result)
        self.assertIn("BETWEEN '2024-01-01 00:00:00 +0100' AND '2024-01-02 00:00:00 +0100'", result)

    def test_build_query_error(self):
        """Test build_query error handling."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-03-01",
            "ContractDate": [
                ("20000101", "20000201"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"

        with self.assertRaises(ForbiddenError) as cm:
            result = self.instance.apply_hana_env(sql_template)
            self.instance.build_query(result)
        self.assertEqual(cm.exception.error_code, "CONTRACT_HAS_NO_ACCESS")

    def test_validate_request_params_error_unknown_type(self):
        self.instance.type_params = "Unknown params"
        with self.assertRaises(BadRequestError) as cm:
            self.instance.validate_request_params()
        self.assertEqual(cm.exception.error_code, "TYPE_NOT_RECOGNIZED")

    def test_validate_request_params_error_date_format(self):
        self.instance.params["StartDate"] = "Error"
        self.instance.params["EndDate"] = "Error"
        with self.assertRaises(BadRequestError) as cm:
            self.instance.validate_request_params()
        self.assertEqual(cm.exception.error_code, "DATE_NOT_ISO8601")

    def test_validate_request_params_error_date_start(self):
        self.instance.type_params = "MONTHLY"
        self.instance.params["StartDate"] = "2024-01-01"
        self.instance.params["EndDate"] = "2023-01-01"
        with self.assertRaises(BadRequestError) as cm:
            self.instance.validate_request_params()
        self.assertEqual(cm.exception.error_code, "INVALID_DATE_RANGE")

    def test_validate_request_params_error_date_hourly(self):
        self.instance.type_params = "HOURLY"
        self.instance.params["StartDate"] = "2024-01-01"
        self.instance.params["EndDate"] = "2024-01-02"
        with self.assertRaises(BadRequestError) as cm:
            self.instance.validate_request_params()
        self.assertEqual(cm.exception.error_code, "HOURLY_INVALID_DATE_RANGE")

    def test_validate_request_params_invalid_ean(self):
        self.instance.type_params = "HOURLY"
        self.instance.params["StartDate"] = "2024-01-01"
        self.instance.params["EndDate"] = "2024-01-01"
        self.instance.params["Ean"] = "541456700000264718"
        with self.assertRaises(ForbiddenError) as ex:
            self.instance.validate_request_params()
        self.assertEqual(ex.exception.error_code, "INVALID_EAN_FOR_USER")

    def test_validate_request_params(self):
        self.instance.type_params = "HOURLY"
        self.instance.params["StartDate"] = "2024-01-01"
        self.instance.params["EndDate"] = "2024-01-01"
        result = self.instance.validate_request_params()
        self.assertIn("Energy", result)
        self.assertEqual(result["Energy"], "elec")
        self.assertIn("ContractDate", result)
        self.assertEqual(
            result["ContractDate"],
            [
                (
                    (datetime.now() - timedelta(days=5 * 365)).strftime("%Y%m%d"),
                    (datetime.now() + timedelta(days=1)).strftime("%Y%m%d"),
                ),
            ],
        )

    def test_process_data_response(self):
        data = [
            {
                "measuredatetime": datetime(2024, 1, 1, 12, 0),
                "ean": "541456700000626783",
                "meterid": "1SAG3105225187",
                "readingfrequency": "HOURLY",
                "readingtypeid": "ACTIVE_ENERGY",
                "measurestate": "VALID",
                "measureunit": "KWH",
                "measurevalue": 100.0,
            },
            {
                "measuredatetime": datetime(2024, 1, 1, 13, 0),
                "ean": "541456700000626783",
                "meterid": "1SAG3105225187",
                "readingfrequency": "HOURLY",
                "readingtypeid": "ACTIVE_ENERGY",
                "measurestate": "VALID",
                "measureunit": "KWH",
                "measurevalue": 150.0,
            },
            {
                "measuredatetime": datetime(2024, 1, 1, 14, 0),
                "ean": "541456700000626783",
                "meterid": "1SAG3105225187",
                "readingfrequency": "HOURLY",
                "readingtypeid": "ACTIVE_ENERGY",
                "measurestate": "VALID",
                "measureunit": "KWH",
                "measurevalue": 200.0,
            },
        ]
        self.instance.params = {"Ean": "541456700002609883", "EndDate": "2024-01-01", "MeterId": "1SAG1105183360", "StartDate": "2024-01-01", "Type": "HOURLY"}
        self.instance.type_params = "HOURLY"

        result = self.instance.process_data_response(data)

        first_non_null_result_index = 0
        for i, res in enumerate(result):
            if res["consumption"] is not None:
                first_non_null_result_index = i
                break

        self.assertEqual(result[first_non_null_result_index]["measuredatetime"], "2024-01-01T13:00:00+01:00")
        self.assertEqual(result[first_non_null_result_index + 1]["measuredatetime"], "2024-01-01T14:00:00+01:00")

        self.assertEqual(result[first_non_null_result_index]["consumption"], 50.0)
        self.assertEqual(result[first_non_null_result_index + 1]["consumption"], 50.0)

    def test_data_to_csv_empty_data(self):
        # Mock empty data
        processed_data = []

        # Call the method
        result = self.instance.data_to_csv(processed_data)

        # Expected output with only headers
        expected_output = "Du (date);De (heure);Au (date);À (heure);Code EAN;Compteur;Type de compteur;Registre;Volume;Unité;Statut de validation"

        # Assert the result
        self.assertEqual(result, expected_output)

    def test_data_to_csv_multiple_rows(self):
        # Mock input data
        processed_data = [
            {
                "consumption": 0.28,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:15:00+01:00",
                "measuredatetime_from": "2025-01-01T00:00:00+01:00",
                "measurestate": "valide",
                "measureunit": "KWH",
                "measurevalue": 35280.906,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "1.8.0",
            },
            {
                "consumption": 0.17,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:30:00+01:00",
                "measuredatetime_from": "2025-01-01T00:15:00+01:00",
                "measurestate": "valide",
                "measureunit": "KWH",
                "measurevalue": 35281.08,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "1.8.0",
            },
            {
                "consumption": 0.29,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:45:00+01:00",
                "measuredatetime_from": "2025-01-01T00:30:00+01:00",
                "measurestate": "valide",
                "measureunit": "KWH",
                "measurevalue": 35281.37,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "1.8.0",
            },
            {
                "consumption": 0.29,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:45:00+01:00",
                "measuredatetime_from": "2025-01-01T00:30:00+01:00",
                "measurestate": "edited",
                "measureunit": "KWH",
                "measurevalue": 35281.37,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "2.8.1",
            },
            {
                "consumption": 0.29,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:45:00+01:00",
                "measuredatetime_from": "2025-01-01T00:30:00+01:00",
                "measurestate": "estimated",
                "measureunit": "KWH",
                "measurevalue": 35281.37,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "whut",
            },
        ]

        # Call the method
        result = self.instance.data_to_csv(processed_data)

        # Expected CSV output
        expected_output = (
            "Du (date);De (heure);Au (date);À (heure);Code EAN;Compteur;Type de compteur;Registre;Volume;Unité;Statut de validation\n"
            "01-01-25;00:00:00;01-01-25;00:15:00;541456700003935950;1SAG1100245885;Compteur communicant;Prélèvement Total;0.28;KWH;Lu\n"
            "01-01-25;00:15:00;01-01-25;00:30:00;541456700003935950;1SAG1100245885;Compteur communicant;Prélèvement Total;0.17;KWH;Lu\n"
            "01-01-25;00:30:00;01-01-25;00:45:00;541456700003935950;1SAG1100245885;Compteur communicant;Prélèvement Total;0.29;KWH;Lu\n"
            "01-01-25;00:30:00;01-01-25;00:45:00;541456700003935950;1SAG1100245885;Compteur communicant;Injection Jour;0.29;KWH;Corrigé\n"
            "01-01-25;00:30:00;01-01-25;00:45:00;541456700003935950;1SAG1100245885;Compteur communicant;whut;0.29;KWH;Estimé"
        )

        # Assert the result
        self.assertEqual(result, expected_output)

    def test_data_to_csv_missing_keys(self):
        # Mock input data with missing keys
        processed_data = [
            {
                "measuredatetime_from": "2025-01-01T00:45:00+01:00",
            },
            {
                "ean": "541456700003935950",
            },
        ]

        # Call the method
        result = self.instance.data_to_csv(processed_data)

        # Expected CSV output
        expected_output = (
            "Du (date);De (heure);Au (date);À (heure);Code EAN;Compteur;Type de compteur;Registre;Volume;Unité;Statut de validation\n"
            "01-01-25;00:45:00;;;;;Compteur communicant;;;;Pas de consommation\n"
            ";;;;541456700003935950;;Compteur communicant;;;;Pas de consommation"
        )

        # Assert the result
        self.assertEqual(result, expected_output)


if __name__ == "__main__":
    unittest.main()
