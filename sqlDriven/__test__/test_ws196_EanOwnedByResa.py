import unittest
from unittest.mock import Mock, patch

from utils.api import api_caller
from utils.errors import NotFoundError
from utils.mocking import mock_api


def _fetch_mock_data(cursor, query):
    if query["Ean"] == "541449011000007794":
        return {
            "ResaIsGRD": True,
            "EnergyType": "ELEC",
        }
    elif query["Ean"] == "541460900002867679":
        return {
            "ResaIsGRD": True,
            "EnergyType": "GAZ",
        }
    else:
        raise NotFoundError


@mock_api
@patch(
    "controllers.Controller.Controller.to_response",
    new=Mock(side_effect=_fetch_mock_data),
)
class TestWS196EanOwnedByResa(unittest.TestCase):
    def test_ean_owned_by_resa_elec(self):
        valid_ean_elec = "541449011000007794"
        response = api_caller("GET", f"/ean/{valid_ean_elec}/grd", raw=True)
        self.assertTrue(response.json()["ResaIsGRD"])
        self.assertEqual(response.json()["EnergyType"], "ELEC")

    def test_ean_owned_by_resa_gaz(self):
        valid_ean_elec = "541460900002867679"
        response = api_caller("GET", f"/ean/{valid_ean_elec}/grd", raw=True)
        self.assertTrue(response.json()["ResaIsGRD"])
        self.assertEqual(response.json()["EnergyType"], "GAZ")

    def test_ean_not_owned_by_resa(self):
        invalid_ean = "541449020714489383"
        response = api_caller("GET", f"/ean/{invalid_ean}/grd", raw=True)
        self.assertEqual(response.status_code, 404)

    def test_invalid_ean_format(self):
        invalid_format_ean = "INVALID_EAN"  # EAN mal formaté
        response = api_caller("GET", f"/ean/{invalid_format_ean}/grd", raw=True)
        self.assertEqual(response.status_code, 404)
