import unittest
from datetime import datetime

import pytz

from utils.errors import BadRequestError, ForbiddenError
from utils.mocking import get_mock_user_token, mock_api

linking_dict = {
    "controller": "ws205",
    "id": "WS205",
    "params": {
        "Ean": {"default": None, "name": "Ean", "source": "queryStringParameters"},
        "EndDate": {"default": None, "name": "EndDate", "source": "queryStringParameters"},
        "MeterId": {"default": None, "name": "MeterId", "source": "queryStringParameters"},
        "StartDate": {"default": None, "name": "StartDate", "source": "queryStringParameters"},
        "Type": {"default": None, "name": "Type", "source": "queryStringParameters"},
    },
    "secret": "{REDSHIFT_SECRET}",
    "sql_template": "sqlStatements/ws205_get_daily_conso.sql",
    "type": "sqlDriven",
}

event_dict = {
    "body": {},
    "headers": {
        "Accept": "application/json",
        "Accept-Encoding": "gzip, deflate, br",
        "Authorization": "",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Host": "127.0.0.1:8011",
        "Postman-Token": "9188aecc-93d4-4441-84f5-ce9bf2ee26c6",
        "User-Agent": "PostmanRuntime/7.43.0",
        "X-Api-Key": "nope",
    },
    "httpMethod": "GET",
    "path": "/index/smart",
    "pathParameters": {},
    "queryStringParameters": {"Ean": "541456700002609883", "EndDate": "2024-09-30", "MeterId": "1SAG1105183360", "StartDate": "2024-08-29", "Type": "MONTHLY"},
    "requestContext": {"domainName": "127.0.0.1:8011", "path": "/index/smart", "resourcePath": "/index/smart", "stage": "v0"},
    "resource": "/index/smart",
}


@mock_api
class TestWS205Call(unittest.TestCase):
    flask = None

    @classmethod
    def setUpClass(cls):
        from controllers.ws205 import ws205

        cls.token = get_mock_user_token()
        event_dict["headers"]["Authorization"] = "Bearer " + cls.token
        cls.instance = ws205(event_dict, linking=linking_dict)
        cls.base_params = cls.instance.params
        cls.base_type_params = cls.instance.type_params

    def setUp(self):
        # restore original params
        self.instance.params = self.base_params
        self.instance.type_params = self.base_type_params

    def test_method_exists(self):
        self.assertTrue(hasattr(self.instance, "to_response"), "The instance should have a method named 'to_response'.")
        self.assertTrue(hasattr(self.instance, "validate_request_params"), "The instance should have a method named 'validate_request_params'.")
        self.assertTrue(hasattr(self.instance, "apply_hana_env"), "The instance should have a method named 'apply_hana_env'.")

    def test_build_query(self):
        """Test build_query logic."""
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-03-01",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)

    def test_build_query_monthly(self):
        """Test build_query logic for MONTHLY type."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-03-01",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01 00:00:00 +0100' AND '2024-03-01 00:00:00 +0100'", result)

    def test_build_query_monthly_2(self):
        """Test build_query logic for MONTHLY type."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-18",
            "EndDate": "2024-03-21",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-18", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-03-01 00:00:00 +0100' AND '2024-03-22 00:00:00 +0100'", result)

    def test_build_query_monthly_3(self):
        """Test build_query logic for MONTHLY type."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-31",
            "EndDate": "2024-03-30",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-31", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01 00:00:00 +0100' AND '2024-03-01 00:00:00 +0100'", result)
        self.assertIn("measuredatetime BETWEEN '2024-03-01 00:00:00 +0100' AND '2024-03-31 00:00:00 +0100'", result)

    def test_build_query_monthly_switch_tz(self):
        """Test build_query logic for MONTHLY type."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-31",
            "EndDate": "2024-05-15",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("measuredatetime BETWEEN '2024-01-31", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01", result)
        self.assertIn("measuredatetime BETWEEN '2024-02-01 00:00:00 +0100' AND '2024-03-01 00:00:00 +0100'", result)
        self.assertIn("measuredatetime BETWEEN '2024-03-01 00:00:00 +0100' AND '2024-04-01 00:00:00 +0200'", result)
        self.assertIn("measuredatetime BETWEEN '2024-04-01 00:00:00 +0200' AND '2024-05-01 00:00:00 +0200'", result)
        self.assertIn("measuredatetime BETWEEN '2024-05-01 00:00:00 +0200' AND '2024-05-16 00:00:00 +0200'", result)

    def test_build_query_daily(self):
        """Test build_query logic for DAILY type."""
        self.instance.type_params = "DAILY"
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-01-02",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp1d", result)
        self.assertIn("BETWEEN '2024-01-01 00:00:00 +0100' AND '2024-01-03 00:00:00 +0100'", result)

    def test_build_query_hourly(self):
        """Test build_query logic for HOURLY type."""
        self.instance.type_params = "HOURLY"
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-01-01",
            "ContractDate": [
                ("20000101", "99991231"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"
        result = self.instance.apply_hana_env(sql_template)
        result = self.instance.build_query(result)

        self.assertIn("public.cumulativeactiveenergyp15m", result)
        self.assertIn("BETWEEN '2024-01-01 00:00:00 +0100' AND '2024-01-02 00:00:00 +0100'", result)

    def test_build_query_error(self):
        """Test build_query error handling."""
        self.instance.type_params = "MONTHLY"
        self.instance.params = {
            "StartDate": "2024-01-01",
            "EndDate": "2024-03-01",
            "ContractDate": [
                ("20000101", "20000201"),
            ],
        }

        sql_template = "SELECT * FROM {red_table} WHERE meterid = :MeterId AND ean = :Ean {dynamic_clause}"

        with self.assertRaises(ForbiddenError) as cm:
            result = self.instance.apply_hana_env(sql_template)
            self.instance.build_query(result)
        self.assertEqual(cm.exception.error_code, "CONTRACT_HAS_NO_ACCESS")

    def test_validate_request_params_error_unknown_type(self):
        self.instance.type_params = "Unknown params"
        with self.assertRaises(BadRequestError) as cm:
            self.instance.validate_request_params()
        self.assertEqual(cm.exception.error_code, "TYPE_NOT_RECOGNIZED")

    def test_validate_request_params_error_date_format(self):
        self.instance.params["StartDate"] = "Error"
        self.instance.params["EndDate"] = "Error"
        with self.assertRaises(BadRequestError) as cm:
            self.instance.validate_request_params()
        self.assertEqual(cm.exception.error_code, "DATE_NOT_ISO8601")

    def test_validate_request_params_error_date_start(self):
        self.instance.type_params = "MONTHLY"
        self.instance.params["StartDate"] = "2024-01-01"
        self.instance.params["EndDate"] = "2023-01-01"
        with self.assertRaises(BadRequestError) as cm:
            self.instance.validate_request_params()
        self.assertEqual(cm.exception.error_code, "INVALID_DATE_RANGE")

    def test_validate_request_params_error_date_hourly(self):
        self.instance.type_params = "HOURLY"
        self.instance.params["StartDate"] = "2024-01-01"
        self.instance.params["EndDate"] = "2024-01-02"
        with self.assertRaises(BadRequestError) as cm:
            self.instance.validate_request_params()
        self.assertEqual(cm.exception.error_code, "HOURLY_INVALID_DATE_RANGE")

    def test_validate_request_params(self):
        self.instance.type_params = "HOURLY"
        self.instance.params["StartDate"] = "2024-01-01"
        self.instance.params["EndDate"] = "2024-01-01"
        result = self.instance.validate_request_params()
        self.assertIn("Energy", result)
        self.assertIn("ContractDate", result)

    def test_process_data_response(self):
        data = [
            {
                "measuredatetime": datetime(2024, 1, 1, 12, 0),
                "ean": "541456700002609883",
                "meterid": "1SAG1105183360",
                "readingfrequency": "HOURLY",
                "readingtypeid": "ACTIVE_ENERGY",
                "measurestate": "VALID",
                "measureunit": "KWH",
                "measurevalue": 100.0,
            },
            {
                "measuredatetime": datetime(2024, 1, 1, 13, 0),
                "ean": "541456700002609883",
                "meterid": "1SAG1105183360",
                "readingfrequency": "HOURLY",
                "readingtypeid": "ACTIVE_ENERGY",
                "measurestate": "VALID",
                "measureunit": "KWH",
                "measurevalue": 150.0,
            },
            {
                "measuredatetime": datetime(2024, 1, 1, 14, 0),
                "ean": "541456700002609883",
                "meterid": "1SAG1105183360",
                "readingfrequency": "HOURLY",
                "readingtypeid": "ACTIVE_ENERGY",
                "measurestate": "VALID",
                "measureunit": "KWH",
                "measurevalue": 200.0,
            },
        ]
        self.instance.params = {"Ean": "541456700002609883", "EndDate": "2024-01-01", "MeterId": "1SAG1105183360", "StartDate": "2024-01-01", "Type": "HOURLY"}
        self.instance.type_params = "HOURLY"

        result = self.instance.process_data_response(data)

        first_non_null_result_index = 0
        for i, res in enumerate(result):
            if res["consumption"] is not None:
                first_non_null_result_index = i
                break

        self.assertEqual(result[first_non_null_result_index]["measuredatetime"], "2024-01-01T13:00:00+01:00")
        self.assertEqual(result[first_non_null_result_index + 1]["measuredatetime"], "2024-01-01T14:00:00+01:00")

        self.assertEqual(result[first_non_null_result_index]["consumption"], 50.0)
        self.assertEqual(result[first_non_null_result_index + 1]["consumption"], 50.0)

    def test_get_valid_date_ranges_from_params(self):
        from controllers.ws205 import get_valid_date_ranges_from_params

        result = get_valid_date_ranges_from_params(
            {
                "StartDate": "2024-01-01",
                "EndDate": "2024-12-31",
                "ContractDate": [
                    ("20240101", "20240630"),
                    ("20240701", "20241231"),
                    ("20230101", "20231231"),  # Overlapping contract
                ],
            },
        )

        # Check valid ranges
        belgium_tz = pytz.timezone("Europe/Brussels")
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], (belgium_tz.localize(datetime(2024, 1, 1)), belgium_tz.localize(datetime(2024, 6, 30))))
        self.assertEqual(result[1], (belgium_tz.localize(datetime(2024, 7, 1)), belgium_tz.localize(datetime(2024, 12, 31))))

        # Check when no valid ranges
        params_invalid = {"StartDate": "2025-01-01", "EndDate": "2025-12-31", "ContractDate": [("20240101", "20240630"), ("20240701", "20241231")]}

        result_invalid = get_valid_date_ranges_from_params(params_invalid)
        self.assertEqual(result_invalid, [])

    def test_data_to_csv_empty_data(self):
        # Mock empty data
        processed_data = []

        # Call the method
        result = self.instance.data_to_csv(processed_data)

        # Expected output with only headers
        expected_output = "Du (date);De (heure);Au (date);À (heure);Code EAN;Compteur;Type de compteur;Registre;Volume;Unité;Statut de validation"

        # Assert the result
        self.assertEqual(result, expected_output)

    def test_data_to_csv_multiple_rows(self):
        # Mock input data
        processed_data = [
            {
                "consumption": 0.28,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:15:00+01:00",
                "measuredatetime_from": "2025-01-01T00:00:00+01:00",
                "measurestate": "valide",
                "measureunit": "KWH",
                "measurevalue": 35280.906,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "1.8.0",
            },
            {
                "consumption": 0.17,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:30:00+01:00",
                "measuredatetime_from": "2025-01-01T00:15:00+01:00",
                "measurestate": "valide",
                "measureunit": "KWH",
                "measurevalue": 35281.08,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "1.8.0",
            },
            {
                "consumption": 0.29,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:45:00+01:00",
                "measuredatetime_from": "2025-01-01T00:30:00+01:00",
                "measurestate": "valide",
                "measureunit": "KWH",
                "measurevalue": 35281.37,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "1.8.0",
            },
            {
                "consumption": 0.29,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:45:00+01:00",
                "measuredatetime_from": "2025-01-01T00:30:00+01:00",
                "measurestate": "edited",
                "measureunit": "KWH",
                "measurevalue": 35281.37,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "2.8.1",
            },
            {
                "consumption": 0.29,
                "ean": "541456700003935950",
                "measuredatetime": "2025-01-01T00:45:00+01:00",
                "measuredatetime_from": "2025-01-01T00:30:00+01:00",
                "measurestate": "estimated",
                "measureunit": "KWH",
                "measurevalue": 35281.37,
                "meterid": "1SAG1100245885",
                "readingfrequency": "15M",
                "readingtypeid": "whut",
            },
        ]

        # Call the method
        result = self.instance.data_to_csv(processed_data)

        # Expected CSV output
        expected_output = (
            "Du (date);De (heure);Au (date);À (heure);Code EAN;Compteur;Type de compteur;Registre;Volume;Unité;Statut de validation\n"
            "01-01-25;00:00:00;01-01-25;00:15:00;541456700003935950;1SAG1100245885;Compteur communicant;Prélèvement Total;0.28;KWH;Lu\n"
            "01-01-25;00:15:00;01-01-25;00:30:00;541456700003935950;1SAG1100245885;Compteur communicant;Prélèvement Total;0.17;KWH;Lu\n"
            "01-01-25;00:30:00;01-01-25;00:45:00;541456700003935950;1SAG1100245885;Compteur communicant;Prélèvement Total;0.29;KWH;Lu\n"
            "01-01-25;00:30:00;01-01-25;00:45:00;541456700003935950;1SAG1100245885;Compteur communicant;Injection Jour;0.29;KWH;Corrigé\n"
            "01-01-25;00:30:00;01-01-25;00:45:00;541456700003935950;1SAG1100245885;Compteur communicant;whut;0.29;KWH;Estimé"
        )

        # Assert the result
        self.assertEqual(result, expected_output)

    def test_data_to_csv_missing_keys(self):
        # Mock input data with missing keys
        processed_data = [
            {
                "measuredatetime_from": "2025-01-01T00:45:00+01:00",
            },
            {
                "ean": "541456700003935950",
            },
        ]

        # Call the method
        result = self.instance.data_to_csv(processed_data)

        # Expected CSV output
        expected_output = (
            "Du (date);De (heure);Au (date);À (heure);Code EAN;Compteur;Type de compteur;Registre;Volume;Unité;Statut de validation\n"
            "01-01-25;00:45:00;;;;;Compteur communicant;;;;Pas de consommation\n"
            ";;;;541456700003935950;;Compteur communicant;;;;Pas de consommation"
        )

        # Assert the result
        self.assertEqual(result, expected_output)


if __name__ == "__main__":
    unittest.main()
