import base64
import io
import unittest
from unittest.mock import Mock, patch

from openpyxl import load_workbook

from utils.api import api_caller
from utils.mocking import get_mock_commune_user_token, mock_api

tmp_result = [
    {
        "Ean": "541449020710372856",
        "Type": "Elec",
        "NumCpt": "11003946",
        "Smart": False,
        "EQUNR": "000000000501020901",
        "TRIGSTAT": "1",
        "EAN": "541449020710372856",
        "NOMBREGAUCHE": "05",
        "NOMBREDROITE": "00",
        "Index": '[{"CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"","DateRel":"20240607","EQUI_EQUNR":"000000000501020901","ETDZ_ZWNUMMER":"002","Ean":"541449020710372856","IndexQual":"02","IndexUnit":"KWH","Index_Decimal":"0.00000000000000","Index_Integer":"15235","LOGIKZW":"000000000001383511","MATNR":"000000000000120129","NumCompteur":"11003946","Smart":false,"lv_nb_lines":2},{"CatCadran":"HI","CatTarif":"ECA_HI","CodeCadran":"","DateRel":"20240607","EQUI_EQUNR":"000000000501020901","ETDZ_ZWNUMMER":"001","Ean":"541449020710372856","IndexQual":"02","IndexUnit":"KWH","Index_Decimal":"0.00000000000000","Index_Integer":"14553","LOGIKZW":"000000000001383510","MATNR":"000000000000120129","NumCompteur":"11003946","Smart":false,"lv_nb_lines":2}]',
        "CadranInfo": '[{"ANLAGE":"4100687391","ANZART":"DD","CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"SU/L/N","EQUNR":"000000000501020901","ETDZ_ZWNUMMER":"002","Ean":"541449020710372856","LOGIKZW":"000000000001383511","LV_NB_LINES":2,"MATNR":"000000000000120129","NumCpt":"11003946","STANZNAC":"00","STANZVOR":"05","ZWGRUPPE":"00000189","ZWZUART":"04"},{"ANLAGE":"4100687391","ANZART":"DD","CatCadran":"HI","CatTarif":"ECA_HI","CodeCadran":"NU/H/D","EQUNR":"000000000501020901","ETDZ_ZWNUMMER":"001","Ean":"541449020710372856","LOGIKZW":"000000000001383510","LV_NB_LINES":2,"MATNR":"000000000000120129","NumCpt":"11003946","STANZNAC":"00","STANZVOR":"05","ZWGRUPPE":"00000189","ZWZUART":"04"}]',
        "Periode": '[{"ADATSOLL":"20241031","TARIFTYP":"EY","ZUORDDAT":"20241103"}]',
        "Adresse": {"Rue": "Rue des Lilas", "NumRue": "12A", "NumComp": "Box 3", "CodePostal": "1000", "Localite": "Bruxelles"},
    },
    {
        "Ean": "541449020710372857",
        "Type": "Gaz",
        "NumCpt": "21003947",
        "Smart": True,
        "EQUNR": "000000000501020902",
        "TRIGSTAT": "0",
        "EAN": "541449020710372857",
        "NOMBREGAUCHE": "03",
        "NOMBREDROITE": "01",
        "Index": '[{"CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"","DateRel":"20240608","EQUI_EQUNR":"000000000501020902","ETDZ_ZWNUMMER":"003","Ean":"541449020710372857","IndexQual":"03","IndexUnit":"M3","Index_Decimal":"0.00000000000000","Index_Integer":"25235","LOGIKZW":"000000000001383611","MATNR":"000000000000120229","NumCompteur":"21003947","Smart":true,"lv_nb_lines":1}]',
        "CadranInfo": '[{"ANLAGE":"4100687392","ANZART":"DD","CatCadran":"LO","CatTarif":"GAS_LO","CodeCadran":"DU/G","EQUNR":"000000000501020902","ETDZ_ZWNUMMER":"003","Ean":"541449020710372857","LOGIKZW":"000000000001383611","LV_NB_LINES":1,"MATNR":"000000000000120229","NumCpt":"21003947","STANZNAC":"01","STANZVOR":"03","ZWGRUPPE":"00000289","ZWZUART":"05"}]',
        "Periode": '[{"ADATSOLL":"20251031","TARIFTYP":"GY","ZUORDDAT":"20251103"}]',
        "Adresse": {"Rue": "Avenue Louise", "NumRue": "46", "NumComp": "Etage 1", "CodePostal": "1050", "Localite": "Ixelles"},
    },
    {
        "Ean": "541449020710372858",
        "Type": "Elec",
        "NumCpt": "11003948",
        "Smart": True,
        "EQUNR": "000000000501020903",
        "TRIGSTAT": "0",
        "EAN": "541449020710372858",
        "NOMBREGAUCHE": "04",
        "NOMBREDROITE": "02",
        "Index": '[{"CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"","DateRel":"20240609","EQUI_EQUNR":"000000000501020903","ETDZ_ZWNUMMER":"004","Ean":"541449020710372858","IndexQual":"02","IndexUnit":"KWH","Index_Decimal":"0.00000000000000","Index_Integer":"12000","LOGIKZW":"000000000001383612","MATNR":"000000000000120130","NumCompteur":"11003948","Smart":true,"lv_nb_lines":1}]',
        "CadranInfo": '[{"ANLAGE":"4100687393","ANZART":"DD","CatCadran":"LO","CatTarif":"ECA_LO","CodeCadran":"SU/L/N","EQUNR":"000000000501020903","ETDZ_ZWNUMMER":"004","Ean":"541449020710372858","LOGIKZW":"000000000001383612","LV_NB_LINES":1,"MATNR":"000000000000120130","NumCpt":"11003948","STANZNAC":"02","STANZVOR":"04","ZWGRUPPE":"00000190","ZWZUART":"04"}]',
        "Periode": '[{"ADATSOLL":"20261031","TARIFTYP":"EZ","ZUORDDAT":"20261103"}]',
        "Adresse": {"Rue": "Chaussée de Gand", "NumRue": "55", "NumComp": "", "CodePostal": "1080", "Localite": "Molenbeek-Saint-Jean"},
    },
    {
        "Ean": "541449020710372859",
        "Type": "Gaz",
        "NumCpt": "21003949",
        "Smart": False,
        "EQUNR": "000000000501020904",
        "TRIGSTAT": "1",
        "EAN": "541449020710372859",
        "NOMBREGAUCHE": "02",
        "NOMBREDROITE": "02",
        "Index": '[{"CatCadran":"HI","CatTarif":"GAS_HI","CodeCadran":"","DateRel":"20240610","EQUI_EQUNR":"000000000501020904","ETDZ_ZWNUMMER":"005","Ean":"541449020710372859","IndexQual":"03","IndexUnit":"M3","Index_Decimal":"0.00000000000000","Index_Integer":"15000","LOGIKZW":"000000000001383613","MATNR":"000000000000120231","NumCompteur":"21003949","Smart":false,"lv_nb_lines":1}]',
        "CadranInfo": '[{"ANLAGE":"4100687394","ANZART":"DD","CatCadran":"HI","CatTarif":"GAS_HI","CodeCadran":"NU/H","EQUNR":"000000000501020904","ETDZ_ZWNUMMER":"005","Ean":"541449020710372859","LOGIKZW":"000000000001383613","LV_NB_LINES":1,"MATNR":"000000000000120231","NumCpt":"21003949","STANZNAC":"02","STANZVOR":"02","ZWGRUPPE":"00000291","ZWZUART":"05"}]',
        "Periode": '[{"ADATSOLL":"20271031","TARIFTYP":"GZ","ZUORDDAT":"20271103"}]',
        "Adresse": {"Rue": "Boulevard du Midi", "NumRue": "14B", "NumComp": "2e Etage", "CodePostal": "1060", "Localite": "Saint-Gilles"},
    },
]


@mock_api
@patch("controllers.Controller.Controller.to_response", new=Mock(return_value=tmp_result))
class TestCommuneDownloadIndex(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.token = get_mock_commune_user_token()

    def test_get_excel_commune_index(self):
        response = api_caller(
            "GET",
            "/communes/index?FullEan=false",
            headers={"Authorization": "Bearer " + self.token},
            raw=True,
        )
        self.assertEqual(response.status_code, 200)

        decoded_content = base64.b64decode(response.text)
        file = io.BytesIO(decoded_content)
        workbook = load_workbook(file)
        sheet = workbook.active

        self.assertGreater(sheet.max_row, 1)
        self.assertGreater(sheet.max_column, 0)

        expected_titles = [
            "Rue",
            "NumRue",
            "NumCompl",
            "CodePostal",
            "Localite",
            "Ean",
            "NumCpt",
            "Type",
            "Cadran",
            "CodeCadran",
            "NumCadran",
            "DatePrec",
            "IndexPrec",
            "NouvIndex",
            "DateNouvIndex",
            "DateDebutRel",
            "DateFinRel",
            "FondEchelle",
        ]
        actual_titles = [cell.value for cell in sheet[1]]

        self.assertEqual(expected_titles, actual_titles, "The column titles do not match the expected values.")
