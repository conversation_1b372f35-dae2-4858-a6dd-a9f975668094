import unittest
from decimal import Decimal
from unittest.mock import Mock, patch

from utils.api import api_caller
from utils.mocking import mock_api


@patch(
    "controllers.Controller.Controller.to_response",
    new=Mock(
        return_value=[
            {
                "Id": "EB121",
                "Price": Decimal("741.88"),
            },
            {
                "Id": "EB120",
                "Price": Decimal("741.88"),
            },
            {
                "Id": "EB111",
                "Price": Decimal("-363.70"),
            },
            {
                "Id": "EB110",
                "Price": Decimal("14922.00"),
            },
            {
                "Id": "EB109",
                "Price": Decimal("11871.76"),
            },
            {
                "Id": "EB108",
                "Price": Decimal("9282.36"),
            },
            {
                "Id": "EB107",
                "Price": Decimal("7307.39"),
            },
            {
                "Id": "EB106",
                "Price": Decimal("5793.24"),
            },
            {
                "Id": "EB105",
                "Price": Decimal("4619.24"),
            },
            {
                "Id": "EB104",
                "Price": Decimal("1623.86"),
            },
            {
                "Id": "EB103",
                "Price": Decimal("899.71"),
            },
            {
                "Id": "EB102",
                "Price": Decimal("741.88"),
            },
            {
                "Id": "EB101",
                "Price": Decimal("3144.13"),
            },
            {
                "Id": "EB100",
                "Price": Decimal("247.60"),
            },
            {
                "Id": "EK001",
                "Price": Decimal("219.44"),
            },
        ],
    ),
)
@patch(
    "controllers.ws63.api_caller",
    new=Mock(
        return_value=[
            {
                "Label": "Essentiel",
                "Phases": 1,
                "Amperage": 40,
                "Power": 9.2,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 971.98,
            },
            {
                "Label": "Confort",
                "Phases": 1,
                "Amperage": 63,
                "Power": 14.5,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 1855.78,
            },
            {
                "Label": "Essentiel",
                "Phases": 3,
                "Amperage": 25,
                "Power": 10,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 971.98,
            },
            {
                "Label": "Confort",
                "Phases": 3,
                "Amperage": 32,
                "Power": 12.7,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 1855.78,
            },
            {
                "Label": "Confort +",
                "Phases": 3,
                "Amperage": 40,
                "Power": 15.9,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 2567.13,
            },
            {
                "Label": "Power",
                "Phases": 3,
                "Amperage": 50,
                "Power": 19.9,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 5509.54,
            },
            {
                "Label": "Power +",
                "Phases": 3,
                "Amperage": 63,
                "Power": 25.1,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 6662.79,
            },
            {
                "Label": "Essentiel",
                "Phases": 4,
                "Amperage": 16,
                "Power": 11.1,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 971.98,
            },
            {
                "Label": "Confort",
                "Phases": 4,
                "Amperage": 20,
                "Power": 13.9,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 1855.78,
            },
            {
                "Label": "Confort +",
                "Phases": 4,
                "Amperage": 25,
                "Power": 17.3,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 2567.13,
            },
            {
                "Label": "Power",
                "Phases": 4,
                "Amperage": 32,
                "Power": 22.2,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 5509.54,
            },
            {
                "Label": "Power +",
                "Phases": 4,
                "Amperage": 40,
                "Power": 27.7,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 6662.79,
            },
            {
                "Label": "PRO 35",
                "Phases": 4,
                "Amperage": 50,
                "Power": 34.6,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 8150.16,
            },
            {
                "Label": "PRO 44",
                "Phases": 4,
                "Amperage": 63,
                "Power": 43.6,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 10090.21,
            },
            {
                "Label": "PRO 55",
                "Phases": 4,
                "Amperage": 80,
                "Power": 55.4,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 12633.83,
            },
            {
                "Label": "PRO 69",
                "Phases": 4,
                "Amperage": 100,
                "Power": 69.3,
                "AmperageUnit": "A",
                "PowerUnit": "kVA",
                "Price": 18475.45,
            },
        ],
    ),
)
@mock_api
class TestWS63(unittest.TestCase):
    test_body = {
        "Liste": [
            {
                "IdCpt": 1,
                "NbPhase": -1,
                "ExclNuit": 0,
                "Amperage": 10,
                "Puissance": 5.5,
            },
            {"IdCpt": 2, "NbPhase": 1, "ExclNuit": 0, "Amperage": 10, "Puissance": 5.5},
            {
                "IdCpt": 3,
                "NbPhase": 1,
                "ExclNuit": 0,
                "Amperage": 50,
                "Puissance": 11.5,
            },
            {
                "IdCpt": 4,
                "NbPhase": 3,
                "ExclNuit": 0,
                "Amperage": 40,
                "Puissance": 15.9,
            },
            {
                "IdCpt": 5,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 30,
                "Puissance": 10.7,
            },
            {
                "IdCpt": 6,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 33,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 7,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 45,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 8,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 51,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 9,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 63,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 10,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 80,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 11,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 100,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 12,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 100,
                "Puissance": 69.3,
            },
            {
                "IdCpt": 13,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 100,
                "Puissance": 69.4,
            },
            {
                "IdCpt": 14,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 100,
                "Puissance": 100,
            },
            {"IdCpt": 15, "NbPhase": 3, "ExclNuit": 0, "Amperage": 25, "Puissance": 10},
            {
                "IdCpt": 16,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 16,
                "Puissance": 11.1,
            },
        ],
    }
    test_response = {
        "MontantTotal": 141136.14,
        "Liste": [
            {"IdCpt": 1},
            {"IdCpt": 2, "PrixHtva": 971.98, "LibIdCpt": "Essentiel"},
            {"IdCpt": 3, "PrixHtva": 1855.78, "LibIdCpt": "Confort"},
            {"IdCpt": 4, "PrixHtva": 2567.13, "LibIdCpt": "Confort +"},
            {"IdCpt": 5, "PrixHtva": 5509.54, "LibIdCpt": "Power"},
            {"IdCpt": 6, "PrixHtva": 6662.79, "LibIdCpt": "Power +"},
            {"IdCpt": 7, "PrixHtva": 8150.16, "LibIdCpt": "PRO 35"},
            {"IdCpt": 8, "PrixHtva": 10090.21, "LibIdCpt": "PRO 44"},
            {"IdCpt": 9, "PrixHtva": 10090.21, "LibIdCpt": "PRO 44"},
            {"IdCpt": 10, "PrixHtva": 12633.83, "LibIdCpt": "PRO 55"},
            {"IdCpt": 11, "PrixHtva": 18475.45, "LibIdCpt": "PRO 69"},
            {"IdCpt": 12, "PrixHtva": 18475.45, "LibIdCpt": "PRO 69"},
            {"IdCpt": 13, "PrixHtva": 18497.39, "LibIdCpt": "PRO 69"},
            {"IdCpt": 14, "PrixHtva": 25212.26, "LibIdCpt": "PRO 69"},
            {"IdCpt": 15, "PrixHtva": 971.98, "LibIdCpt": "Essentiel"},
            {"IdCpt": 16, "PrixHtva": 971.98, "LibIdCpt": "Essentiel"},
        ],
    }

    def test_success(self):
        response = api_caller("POST", "/tarifs", raw=True, body=self.test_body)
        self.assertEqual(response.status_code, 200)
        self.assertDictEqual(response.json(), self.test_response)


if __name__ == "__main__":
    unittest.main()
