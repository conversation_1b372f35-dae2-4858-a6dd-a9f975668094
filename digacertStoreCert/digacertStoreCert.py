import base64
from datetime import datetime

import requests

from utils.aws_handler_decorator import aws_lambda_handler
from utils.log_utils import log_info_json
from utils.sharepoint import SharepointHandler


@aws_lambda_handler
def handler(event, context):
    log_info_json({"event": event})
    cert_response = requests.get(event["cert_url"])
    valise = f"Dossier EAN-{event['ean']}"

    SharepointHandler.create_sharepoint_valise_if_missing(valise, timeout=(60, 60 * 2))
    SharepointHandler.send_valise_file_to_sharepoint(
        f"Certificat_ELGACERT_{event['cert_number']}_{event['type']}",
        "pdf",
        base64.b64encode(cert_response.content).decode(),
        valise,
        prepend_date=False,
        metadata={
            "ContentType": "correspondance_in",
            "Type_Autres": "Document",
            "Origine_Document": "Plateforme ELGACERT",
            "Date_Reception": datetime.now().strftime("%Y%m%d"),
            "Date_Document": datetime.fromisoformat(event["creation_date"]).strftime("%Y%m%d"),
            "VALISE_OPERATIONNELLE": "1",
            "Type_Correspondance_In": "Pv reception",
        },
        timeout=(60, 60 * 8),
    )
