from os import environ

import boto3


def create_user_for_email_release(email):
    client = boto3.client("cognito-idp")
    response = client.admin_create_user(
        UserPoolId=environ["COGNITO_POOL_ID"],
        Username=f"new-version-mail_{email.lower()}",
        MessageAction="SUPPRESS",
        UserAttributes=[
            {"Name": "email", "Value": email.lower()},
            {"Name": "custom:receiveNotification", "Value": "1"},
        ],
    )
    print(response)


if __name__ == "__main__":
    mails = []
    for mail in mails:
        create_user_for_email_release(mail)
