import base64
import re
import zipfile
from io import BytesIO
from os import environ
from zipfile import ZIP_DEFLATED

import boto3
import sib_api_v3_sdk
from markdown import markdown
from sib_api_v3_sdk.rest import ApiException

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file, get_resource_key, get_secret_string
from utils.errors import BadRequestError, BadGatewayError
from utils.log_utils import log_info_json, log_err, log_err_json


@aws_lambda_handler
def handler(event, context):
    # Get emails for governance portal users that want the new version email
    emails = tuple(set(get_users_mails()))
    print(emails)

    if emails:
        changelog = load_s3_file(environ["BUCKET"], get_resource_key("CHANGELOG.md"))
        swagger_doc_html = load_s3_file(environ["BUCKET"], "resources/documentation.html")
        swagger_doc_json = load_s3_file(environ["BUCKET"], get_resource_key("myresaapi_openapi_doc.json"))

        changelog = changelog.replace("# MyResaAPI ChangeLog", f"# MyResaAPI {environ['STAGE']} ChangeLog")

        send_in_blue_mail(
            emails,
            {
                "changelog": markdown(get_latest_version_only(changelog, 5)),
                "env": environ["STAGE"],
                "version": environ["API_VERSION"],
            },
            [
                {
                    "name": f"swagger_documentation-{environ['STAGE']}_{environ['API_VERSION']}.html.txt",
                    "content": base64.b64encode(swagger_doc_html.encode()).decode(),
                },
                {
                    "name": f"swagger_documentation-{environ['STAGE']}_{environ['API_VERSION']}.json.txt",
                    "content": base64.b64encode(swagger_doc_json.encode()).decode(),
                },
                {
                    "name": f"changelog-{environ['STAGE']}_{environ['API_VERSION']}.md.txt",
                    "content": base64.b64encode(changelog.encode()).decode(),
                },
            ],
        )


def get_latest_version_only(changelog: str, count: int) -> str:
    matches = re.findall(r"^## *(?:[0-9\.]+) *\/ *(?:[0-9\-]+) *$", changelog, re.M)

    if matches and len(matches) >= count:
        latest_versions_pos = changelog.find(matches[count])
        latest_versions = changelog[:latest_versions_pos]
    else:
        latest_versions = changelog

    return latest_versions


def get_users():
    client = boto3.client("cognito-idp")
    return [user for user in client.list_users(UserPoolId=environ["COGNITO_POOL_ID"])["Users"] if get_user_attribute(user, environ["ATTRIBUTE"]) == "1"]


def get_user_attribute(user, attribute):
    return next(
        iter([attr["Value"] for attr in user["Attributes"] if attr["Name"] == attribute]),
        None,
    )


def get_users_mails():
    users = get_users()
    return [get_user_attribute(user, "email") for user in users if get_user_attribute(user, "email")]


def send_mail_with_zip_attachments(dest_list, data, attachments):
    with BytesIO() as zip_data:
        with zipfile.ZipFile(zip_data, "w", ZIP_DEFLATED) as zip_handler:
            for attachment in attachments:
                zip_handler.writestr(attachment.get("name"), attachment.get("content"))

        return send_in_blue_mail(
            dest_list,
            data,
            [
                {
                    "name": "documentation.zip",
                    "content": base64.b64encode(zip_data.getvalue()).decode(),
                }
            ],
        )


def send_in_blue_mail(emails: [str], template_data: dict, attachments: list = None):
    """
    Send a mail using sendinblue services.
    """
    configuration = sib_api_v3_sdk.Configuration()
    configuration.api_key["api-key"] = get_secret_string("MyResaApi/sendinblueApiKey")

    api_instance = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))
    send_email = sib_api_v3_sdk.SendSmtpEmail(
        bcc=[{"email": email} for email in emails],
        template_id=307,
        to=[{"email": "<EMAIL>"}],
        params=template_data,
        attachment=attachments or None,
    )

    try:
        log_info_json({"SendInBlueData": template_data})
        resp = api_instance.send_transac_email(send_email)
        log_info_json({"SendInBlueResponse": resp})
    except (ApiException, BadRequestError, BadGatewayError) as e:
        log_err_json({"SendInBlueError": e})
    except Exception as e:
        log_err(e)


if __name__ == "__main__":
    handler({}, {})
