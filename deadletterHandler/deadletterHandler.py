import json
import os

from utils.auth_utils import loadUserByUID
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_sns_subscriptions, publishSNS, load_s3_file
from utils.dict_utils import get
from utils.email_utils import Email


@aws_lambda_handler
def handler(event, context):
    for record in event["Records"]:
        source = record.get("eventSourceARN", "")
        if "userActivation" in source:
            handle_user_activation_deadletter(json.loads(record["body"]), context)
        else:
            handle_general_deadletter(source, json.loads(record["body"]), context)


def handle_general_deadletter(source, payload, context):
    mails = [sub["Endpoint"] for sub in get_sns_subscriptions(os.environ["BASIC_DEADLETTER_SNS"]) if sub["Protocol"] == "email"]

    emailUtils = Email()
    for mail in mails:
        emailUtils.send(
            f"Erreur de traitement sur la SQS {source}",
            mail,
            "general-sqs-error.html",
            {"payload": json.dumps(payload), "source": source},
        )
    del emailUtils


def handle_user_activation_deadletter(payload, context):
    print(payload)
    uid = get(payload, "uid")
    step = str(get(payload, "step")).upper()
    log_id = get(payload, "log_id")
    user_data = {}
    content = ""
    try:
        user_data = loadUserByUID(uid) or {}
    except Exception as e:
        content += str(e) + "<br>"

    if step == "GET_BP":
        content += """
            Détails de l'étape 'GET_BP'<br>
            L'echec vient probablement du call du WS73 vers SAP (voir logs)<br>
        """
    elif step == "HANDLE_TOKEN":
        content += """
            Détails de l'étape 'HANDLE_TOKEN'<br>
            L'echec vient probablement du call du WS64 (mode 2) vers SAP (voir logs)<br>
        """
    elif step == "WRITE_AD":
        content += """
            Détails de l'étape 'WRITE_AD'<br>
            L'echec vient probablement de l'ecriture sur l'Active Directory (voir logs)<br>
        """

    attachments = []
    try:
        logs = load_s3_file(os.environ["BUCKET"], "logs/" + log_id)
        local_file_name = "/tmp/{}.txt".format(log_id)
        with open(local_file_name, "wb") as f:
            f.write(str.encode(logs))
        attachments.append({"FilePath": local_file_name, "Extension": "txt", "DocumentName": "logs"})
    except Exception as e:
        print("no log file :", e)

    publishSNS(
        os.environ["ErrorSNS"],
        json.dumps(
            {
                "Topic": "UserActivation",
                "Payload": payload,
                "Environnement": get(os.environ, "STAGE"),
                "Logs": f"s3://{os.environ['BUCKET']}/logs/{log_id}",
            }
        ),
        subject="UserActivation Error",
    )

    mails = [sub["Endpoint"] for sub in get_sns_subscriptions(os.environ["ACTIVATION_FAILURE_SNS"]) if sub["Protocol"] == "email"]

    emailUtils = Email()
    for mail in mails:
        emailUtils.send(
            "Erreur dans le flux d'activation d'un utilisateur",
            mail,
            "userActivation-error.html",
            {
                "payload": json.dumps(payload),
                "step": step,
                "uid": uid,
                "email": get(user_data, "email"),
                "bp": get(user_data, "bp"),
                "env": get(os.environ, "STAGE"),
                "content": content,
                "receivers": "<ul>" + ("".join(["<li>" + str(mail) + "</li>" for mail in mails])) + "</ul>",
            },
            attachments,
        )
    del emailUtils
