import traceback

from utils.aws_handler_decorator import aws_lambda_handler
from utils.dict_utils import get
from utils.errors import BadRequestError
from utils.log_utils import log_info_json
from utils.url_sign_utils import check_signed_url


@aws_lambda_handler
def handler(event, context):
    log_info_json(event)
    try:
        params = get(event, "queryStringParameters", {}, default_on_empty=True)
        headers = event["headers"]
        signature = get(params, "Signature", err=BadRequestError(message="MissingSignature"))

        method = event["httpMethod"]
        protocol = headers["X-Forwarded-Proto"]
        host = headers["Host"]
        path = event["requestContext"]["path"]
        params.pop("Signature")

        # Remove unwanted "utm_" params added by SendInBlue
        params = {k: v for k, v in params.items() if not k.startswith("utm_")}

        allow = check_signed_url(method, f"{protocol}://{host}{path}", params, signature)
    except Exception as e:
        print("signedUrl<PERSON>uthorizer failed with an error ", e)
        traceback.print_exc()
        allow = False
    return {
        "principalId": "user",
        "policyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Action": "execute-api:Invoke",
                    "Effect": ("Allow" if allow else "Deny"),
                    "Resource": "*",
                }
            ],
        },
    }


if __name__ == "__main__":
    print(
        handler(
            {
                "resource": "/utilisateurs/envMessage",
                "httpMethod": "POST",
                "headers": {"Authorization": "Basic "},  # +  token
            },
            None,
        )
    )
