import io
from urllib.parse import urlparse

import requests

from utils.errors import InternalServerError
from utils.log_utils import log_err
from utils.sentry.sentry_utils import capture_message_in_sentry


def get_file_extension(url: str) -> str:
    """
    Parameters
    ----------
    url : str
        The URL from which to extract the file extension.

    Returns
    -------
    str
        The file extension extracted from the URL.

    Raises
    ------
    ValueError
        If the URL does not contain a file extension.

    """
    if not isinstance(url, str):
        url = str(url)

    parsed_url = urlparse(url)
    file_path = parsed_url.path

    if not file_path:
        raise ValueError("Invalid file URL, the path is missing")

    file_name = file_path.rsplit("/", 1)[-1]
    file_parts = file_name.split(".", 1)
    if len(file_parts) < 2 or not file_parts[1]:
        raise ValueError("Invalid file URL, the extension is missing")

    return file_parts[-1]


def download_file_in_memory(get_url: str) -> io.BytesIO:
    try:
        response = requests.get(get_url)
        response.raise_for_status()  # Raises an exception for HTTP errors
        file_content = io.BytesIO(response.content)
        return file_content
    except requests.exceptions.RequestException as e:
        error_message = f"Erreur lors du téléchargement du fichier, code : {response.status_code if response else 'N/A'}"
        extra_info = {"url": get_url, "status_code": response.status_code if response else "N/A", "response_text": response.text if response else str(e)}
        capture_message_in_sentry(msg=error_message, extra=extra_info, level="error")

        raise InternalServerError(message=error_message, error_code="DOWNLOAD_ERROR") from e


def upload_file_in_memory(put_url: str, file_in_memory: io.BytesIO, content_type="*"):
    try:
        file_in_memory.seek(0)
        response = requests.request("PUT", put_url, headers={"Content-Type": content_type}, data=file_in_memory)
        if not response.ok:
            error_message = f"Erreur lors de l'upload du fichier, code : {response.status_code}"
            extra_info = {"url": put_url, "status_code": response.status_code, "response_text": response.text}
            capture_message_in_sentry(msg=error_message, extra=extra_info, level="error")

            # Log de l'erreur
            log_err(f"Upload error: {error_message}, Extra: {extra_info}")

            raise InternalServerError(message=error_message, error_code="UPLOAD_ERROR")

    except Exception as e:
        log_err(f"Unexpected error during file upload: {str(e)}")
        capture_message_in_sentry(msg="Unexpected error during file upload", extra={"exception": str(e)}, level="error")
        raise InternalServerError(message="Erreur interne lors de l'upload", error_code="UPLOAD_ERROR") from e
