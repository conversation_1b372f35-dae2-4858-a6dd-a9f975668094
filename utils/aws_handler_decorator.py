import json
from contextlib import suppress
from functools import wraps
from os import environ
from typing import Optional

import requests
from pydantic import ValidationError
from unidecode import unidecode

from utils import NonameSecurity
from utils.api import set_api_url
from utils.aws_utils import load_env
from utils.cors import apply_cors
from utils.dict_utils import get
from utils.errors import INVALID_USER_RIGHTS, BadRequestError, HttpError
from utils.JsonEncoder import ResponseEncoder
from utils.log_utils import LogTime, log_err_json, log_info_json
from utils.sentry.sentry_utils import (
    capture_exception_in_sentry,
    init_sentry,
    set_user_in_sentry,
)

init_sentry()


def _get_json_data(request_or_response: dict) -> Optional[dict]:
    body = get(request_or_response, "body", default_on_empty=True)
    if body:
        with suppress(Exception):
            return json.loads(body)
    return None


def set_language(event: dict) -> None:
    """
    Set the language for the given environment based on input event data.

    Parameters
    ----------
    event : dict
        Dictionary containing event data from AWS Gateway.

    """
    headers = get(event, "headers", {}, default_on_empty=True)
    environ["LANG"] = get(headers, "Accept-Language", "fr", default_on_empty=True)[:2].upper()


def aws_lambda_handler(func):
    """
    Decorate an AWS Lambda handler function to handle common tasks and error handling.

    This decorator wraps an AWS Lambda handler function to manage utility tasks such as logging,
    input validation, error processing, and configuration setup. The function ensures input
    data formatting, catches various types of exceptions, adds CORS headers, and optionally
    formats the response body for API Gateway responses.

    Parameters
    ----------
    func : Callable
        The AWS Lambda handler function to be decorated.

    Returns
    -------
    Callable
        A wrapped Lambda handler function with added functionality as described.

    Examples
    --------
    >>> @aws_lambda_handler
    >>> def handler(event, context):

    """

    @wraps(func)
    def wrapper(event, context, **kwargs):
        call_log_info = {
            "context": context,
            "request": event,
            "request_body": _get_json_data(event),
        }
        log_info_json(call_log_info)
        # if no httpMethod or if methodArn is present, then it's a direct lambda call
        # or an Authorizers lambda and not an API call
        api = "httpMethod" in event and "methodArn" not in event
        with LogTime("Handler duration"):
            try:
                load_env()
                set_user_in_sentry(event)
                set_api_url(event)
                set_language(event)
                for param in get(event, "queryStringParameters", {}):
                    # unicode trim non printable char
                    event["queryStringParameters"][param] = unidecode(event["queryStringParameters"][param]).strip()
                response = func(event, context, **(kwargs or {}))
            except HttpError as e:
                log_err_json(e)
                if e.status >= requests.codes.internal_server_error and not (isinstance(e.error_code, str) and "SAP-" in e.error_code):
                    # exception to not log SAP error that always return 500 instead of 4xx
                    capture_exception_in_sentry(e)
                if api:
                    response = e.response()
                else:
                    raise
            except ValidationError as e:
                # handle Pydantic model error on input data
                extra = [
                    {"Field": ".".join(str(x) for x in error.get("loc", [])), "Error": error.get("msg"), "ErrorType": error.get("type"), "InputGiven": error.get("input")}
                    for error in e.errors()
                ]

                response = BadRequestError("Some required fields are missing or have an incorrect type", error_code="INVALID_FIELDS", extra=extra).response()
            except Exception as e:
                # Unhandled error
                log_err_json(e)
                capture_exception_in_sentry(e)
                if api:
                    response = HttpError().response()
                else:
                    raise

            try:
                if api and response:
                    response["headers"] = response.get("headers", {})
                    apply_cors(response["headers"])
            except Exception as e:
                print("applyCORS failed : ", e)

            try:
                if api and "body" in response and not isinstance(response["body"], str):
                    response["body"] = json.dumps(response["body"], cls=ResponseEncoder)
            except TypeError:
                pass

            call_log_info["response"] = response
            call_log_info["response_body"] = _get_json_data(response)
            log_info_json(call_log_info)
            try:
                if api and response and context is not None:  # Filter to not log localhost testing call
                    # force host to be the API domain name
                    if environ.get("DOMAIN_NAME"):
                        event["headers"]["Host"] = environ["DOMAIN_NAME"]
                        event["headers"].pop("X-Forwarded-For", None)
                    # send reuest event to noname
                    NonameSecurity.send_packet_pair(event, response, False)
            except Exception as e:
                capture_exception_in_sentry(e)
                print(e)
            return response

    return wrapper


def auth_handler(func):
    """
    Decorate a function to inject user data retrieved via authentication.

    Examples
    --------
    >>> @auth_handler
    >>> def handler(event, context):

    """
    from utils.auth_utils import getUserData

    @wraps(func)
    def wrapper(event, context, *args, **kwargs):
        user_data = getUserData(event)
        return func(event, context, user_data=user_data, *args, **kwargs)

    return wrapper


def commune_handler(admin: bool = False, roles: Optional[list] = None, event_args_position: int = 0):
    """
    Decorate a function to inject user data retrieved via authentication, assert the user is connected with a commune account.

    Examples
    --------
    >>> @commune_handler
    >>> def handler(event, context, logged_user: User):

    """

    def decorator(func):
        from utils.models.user import User

        @wraps(func)
        def wrapper(*args, **kwargs):
            user: User = User.from_event(args[event_args_position], allow_ghost=False)
            if user.commune and user.commune.actif and (not admin or user.commune.admin) and (user.commune.admin or not roles or any(role in user.commune.roles for role in roles)):
                return func(logged_user=user, *args, **kwargs)
            else:
                raise INVALID_USER_RIGHTS

        return wrapper

    return decorator
