import ipaddress
import json
import logging
import os
import ssl
import time
import urllib.request

import math

from utils.aws_utils import get_secret

NONAME_HOST = NONAME_PORT = NONAME_PATH = NONAME_TYPE = NONAME_INDEX = SOURCE_KEY = REQUEST_TIMEOUT = None


# -------------------------------- Custom code to load config --------------------------------


def load_config():
    global NONAME_HOST, NONAME_PORT, NONAME_PATH, NONAME_TYPE, NONAME_INDEX, SOURCE_KEY, REQUEST_TIMEOUT
    if NONAME_HOST is None:
        config = get_secret(f"NoName/LambdaEngine/{os.environ.get('STAGE_TAG')}", default={})
        NONAME_HOST = config.get("NONAME_HOST")
        NONAME_PORT = config.get("NONAME_PORT")
        NONAME_PATH = config.get("NONAME_PATH")
        NONAME_TYPE = config.get("NONAME_TYPE")
        NONAME_INDEX = config.get("NONAME_INDEX")
        SOURCE_KEY = config.get("SOURCE_KEY")
        REQUEST_TIMEOUT = config.get("REQUEST_TIMEOUT")


load_config()


# -------------------------------- Default NoName Lambda code --------------------------------


def request_post(data):
    url = f"https://{NONAME_HOST}:{NONAME_PORT}{NONAME_PATH}"
    data_json = json.dumps(data).encode("utf-8")

    req = urllib.request.Request(url, data=data_json, method="POST")
    req.add_header("Content-Type", "application/json")

    # ----- the "context=ssl._create_unverified_context()" is added to allow using self signed certificate -----
    with urllib.request.urlopen(req, timeout=REQUEST_TIMEOUT, context=ssl._create_unverified_context()) as response:
        return response.read()


def get_value(obj, chained_key):
    keys = chained_key.split(".")
    current_object = obj
    for key in keys:
        current_object = current_object.get(key)
        if current_object is None:
            return None
    return current_object


def get_source_ip(event):
    source_ip = get_value(event, "requestContext.identity.sourceIp") or get_value(event, "requestContext.http.sourceIp")
    if source_ip is not None:
        return source_ip
    return "0.0.0.0"


def get_ip_version(ip):
    try:
        ip_obj = ipaddress.ip_address(ip)
        if isinstance(ip_obj, ipaddress.IPv4Address):
            return "4"
        elif isinstance(ip_obj, ipaddress.IPv6Address):
            return "6"
    except ValueError:
        return "4"


def get_method(event):
    http_method = get_value(event, "requestContext.httpMethod") or get_value(event, "httpMethod") or get_value(event, "requestContext.http.method")
    if http_method is not None:
        return http_method
    return "GET"


def get_path(event):
    path = get_value(event, "requestContext.http.path") or get_value(event, "requestContext.path")
    return path


def get_response_code(response, errored):
    if response.get("statusCode") is not None:
        return int(response["statusCode"])

    if response.get("status") is not None:
        return int(response["status"])

    if errored:
        return 502
    return None


def send_packet_pair(event, result, errored):
    if event["requestContext"] is None:
        return

    source_ip = get_source_ip(event)
    ip_version = get_ip_version(source_ip)
    method = get_method(event)
    path = get_path(event)
    response_code = get_response_code(result, errored)

    if method is None or path is None or response_code is None:
        return

    result_headers = result.get("headers", {})

    url = event["requestContext"].get("path") or event.get("path") or event["requestContext"].get("http", {}).get("path") or "/"
    if event.get("queryStringParameters", {}):
        qp_count = 0
        for k, v in event["queryStringParameters"].items():
            url = "{}{}{}={}".format(url, "&" if qp_count > 0 else "?", k, v)
            qp_count += 1

    packet_pair = {
        "source": {
            "type": NONAME_TYPE,
            "index": NONAME_INDEX,
            "key": SOURCE_KEY,
        },
        "ip": {
            "src": source_ip,
            "dst": "0.0.0.0",
            "v": ip_version,
        },
        "tcp": {
            "v": "4",
            "src": int(event["headers"].get("x-forwarded-port") or event["headers"].get("X-Forwarded-Port") or "-1"),
            "dst": -1,
        },
        "http": {
            "v": event["requestContext"].get("protocol") or event["requestContext"].get("http", {}).get("protocol") or "1.1",
            "request": {
                "ts": event["requestContext"].get("requestTimeEpoch") or event["requestContext"].get("timeEpoch") or math.floor(time.time() * 1000),
                "headers": event["headers"],
                "url": url,
                "method": method,
                "body": event.get("body", ""),
            },
            "response": {
                "ts": math.floor(time.time() * 1000),
                "headers": result_headers,
                "body": result.get("body", ""),
                "status": result.get("statusCode", 200),
            },
        },
    }
    request_post(packet_pair)


def wrapper(func, event, context):
    errored = False
    result = None
    try:
        result = func(event, context)
    except Exception as e:
        logging.exception(e)
        errored = True
    try:
        send_packet_pair(event, result, errored)
    except Exception as e:
        logging.exception(e)
        return result
    return result
