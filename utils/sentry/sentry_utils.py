import logging
from os import environ
from typing import Optional

import sentry_sdk
from sentry_sdk import capture_exception
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

from utils.auth_utils import getUserId


def init_sentry():
    sentry_sdk.init(
        dsn="https://<EMAIL>:3443/4505278531371008",
        integrations=[
            AwsLambdaIntegration(timeout_warning=True),
            LoggingIntegration(
                level=logging.INFO,  # Capture info and above as breadcrumbs
                event_level=None,  # Never send errors logs as events
            ),
        ],
        traces_sample_rate=0.02,
        profiles_sample_rate=1,
        environment=environ.get("STAGE"),
        release=f"MyResaAPI@{environ.get('API_VERSION')}",
        max_request_body_size="always",
        attach_stacktrace=True,
    )


def set_user_in_sentry(event):
    try:
        sentry_sdk.set_user({"id": getUserId(event)})
    except:
        pass
    sentry_sdk.set_context(
        "application_trace",
        {
            "aws_trace_id": event.get("headers", {}).get("X-Amzn-Trace-Id"),
            "web_resa_trace_id": event.get("headers", {}).get("X-Web-Resa-Trace-Id"),
        },
    )


def capture_exception_in_sentry(e: Exception):
    if environ.get("LOCAL", "false") != "true" and environ.get("STAGE", "")[:8].lower() != "sandbox-":
        capture_exception(e)


def capture_message_in_sentry(msg: str, extra: Optional[dict] = None, level: Optional[str] = None):
    if environ.get("LOCAL", "false") != "true" and environ.get("STAGE", "")[:8].lower() != "sandbox-":
        with sentry_sdk.push_scope() as scope:
            if extra:
                for k, v in extra.items():
                    scope.set_extra(k, v)
            sentry_sdk.capture_message(msg, level)
