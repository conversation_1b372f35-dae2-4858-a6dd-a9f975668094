import json
from typing import Union, Any, Dict

from sentry_sdk.scrubber import EventScrubber
from sentry_sdk.utils import capture_internal_exceptions, AnnotatedValue

Event = Dict[str, Any]


class SentryEncoder(json.JSONEncoder):
    def default(self, obj):
        try:
            if isinstance(obj, AnnotatedValue):
                return obj.value
            return json.JSONEncoder.default(self, obj)
        except TypeError:
            if hasattr(obj, "__dict__"):
                return vars(obj)
            else:
                return repr(obj)


# Deprecated, not used anymore
class CustomEventScrubber(EventScrubber):
    def __init__(self, denylist=None):
        super().__init__(denylist)
        # For all entries with '_', add them again with '-' instead. To catch http headers
        self.denylist += [item.replace("_", "-") for item in self.denylist if "_" in item]
        # Add custom denied words
        self.denylist += [
            "nom",
            "name",
            "lastname",
            "username",
            "utilisateur",
            "password",
            "mot de passe",
            "prenom",
            "prénom",
            "firstname",
            "mail",
            "email",
            "tel",
            "telephone",
            "phone",
            "adresse",
            "address",
            "ean",
            "num_cpt",
            "NumCpt",
            "numCompteur",
        ]

    def scrub_dict(self, d: Event) -> None:
        if not isinstance(d, dict):
            return

        for k, v in d.items():
            if isinstance(k, str) and k.lower() in self.denylist:  # Filter full value if key is a denied word
                d[k] = AnnotatedValue.substituted_because_contains_sensitive_data()
            elif isinstance(v, dict):  # If the value is a dict, run again a dict filter on it
                self.scrub_dict(v)
            elif isinstance(v, list):  # If the value is a list, run a list filter on it
                self.scrub_list(v)
            elif isinstance(v, str):  # If value is a string, run a string filter on it
                d[k] = self.scrub_string(v)

    def scrub_list(self, l: list) -> None:
        if not isinstance(l, list):
            return

        for idx, v in enumerate(l):
            if isinstance(v, dict):  # If the value is a dict, run again a dict filter on it
                self.scrub_dict(v)
            elif isinstance(v, list):  # If the value is a dict, run again a dict filter on it
                self.scrub_list(v)
            elif isinstance(v, str):  # If value is a string, run a string filter on it
                l[idx] = self.scrub_string(v)

    def scrub_string(self, s: str) -> Union[str, AnnotatedValue]:
        if not isinstance(s, str):
            return s

        # If json string, convert to dict and filter it like one then serialize again to json string
        if len(s) > 2 and s[0] == "{" and s[-1] == "}":
            try:
                d = json.loads(s)
                self.scrub_dict(d)
                return json.dumps(d, cls=SentryEncoder)
            except:
                pass

        # Filter string if it contains any denied word
        lower_string = s.lower()
        if any(deny_item in lower_string for deny_item in self.denylist):
            return AnnotatedValue.substituted_because_contains_sensitive_data()
        else:
            return s

    def scrub_breadcrumbs(self, event: Event) -> None:
        with capture_internal_exceptions():
            if "breadcrumbs" in event and "values" in event["breadcrumbs"]:
                for value in event["breadcrumbs"]["values"]:
                    if "data" in value:
                        self.scrub_dict(value["data"])
                    if "message" in value:
                        value["message"] = self.scrub_string(value["message"])

    def scrub_exception(self, event: Event) -> None:
        with capture_internal_exceptions():
            if "exception" in event and "values" in event["exception"]:
                for value in event["exception"]["values"]:
                    if "value" in value:
                        value["value"] = self.scrub_string(value["value"])

    def scrub_event(self, event):
        super().scrub_event(event)
        self.scrub_exception(event)
