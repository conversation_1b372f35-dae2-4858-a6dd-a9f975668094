import unittest

from utils.file_utils import get_file_extension


class TestGetFileExtension(unittest.TestCase):
    def test_get_file_extension(self):
        self.assertEqual(get_file_extension("https://example.com/file.jpg"), "jpg")
        self.assertEqual(get_file_extension("https://example.com/file.tar.gz"), "tar.gz")
        self.assertEqual(get_file_extension("https://example.com/path/to/file.tar.gz"), "tar.gz")
        self.assertEqual(get_file_extension("https://example.com/path.file/file.tar.gz"), "tar.gz")
        self.assertEqual(get_file_extension("https://example.com/path.file/file.tar.gz?a=5&b=2"), "tar.gz")
        self.assertEqual(get_file_extension("file.jpg"), "jpg")
        self.assertEqual(get_file_extension("/file.jpg"), "jpg")
        self.assertEqual(get_file_extension("path.file/file.tar.gz"), "tar.gz")
        self.assertEqual(get_file_extension("/path.file/file.tar.gz"), "tar.gz")
        with self.assertRaises(ValueError):
            get_file_extension("https://example.com/path/file")
        with self.assertRaises(ValueError):
            get_file_extension("https://example.com/")


if __name__ == "__main__":
    unittest.main()
