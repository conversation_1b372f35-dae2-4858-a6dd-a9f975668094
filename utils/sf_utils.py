import sendToSalesForce.sendToSalesForce
from utils.models.sf_case_request_model import SFCaseRequest


def send_to_salesforce(case_request: SFCaseRequest):
    # sqs = boto3.client("sqs")
    # queue_url = os.environ["SALESFORCE_QUEUE_URL"]
    #
    # sqs.send_message(
    #     QueueUrl=queue_url,
    #     MessageBody=case_request.model_dump_json(),
    # )
    sendToSalesForce.sendToSalesForce.handler({"Records": {"body": case_request.model_dump_json()}}, None)
