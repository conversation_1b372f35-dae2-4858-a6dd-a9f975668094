import hashlib
import json
import re
from collections import OrderedDict, defaultdict
from copy import copy, deepcopy
from decimal import Decimal
from functools import reduce
from typing import Union, List, TypeVar, Callable, Iterable

from utils.string_utils import toPascalCase


def snake_to_pascal(word):
    split_word = word.split("_")
    result = "".join(x.capitalize() for x in split_word)
    return result


def pascal_to_snake(name):
    name = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", name).lower()


def get(dict_, key, default=None, err=None, default_on_empty=False, err_on_empty=False):
    """
    Get item from dict if exist
    If the value exist but is None, then default is returned instead
    But if the value contains False, then it doesn't use default
    """
    if dict_ is None or not isinstance(dict_, dict):
        return default
    if key not in dict_:
        if err:
            raise err
        else:
            return default
    else:
        val = dict_[key]
        if err_on_empty and (val is None or val == "") and err:
            raise err
        elif val is None or (default_on_empty and val == ""):
            return default
        else:
            return val


def safe_list_get(l, idx, default=None):
    try:
        return l[idx]
    except IndexError:
        return default


def capitalizeKeys(dict_, function=toPascalCase):
    """edit recursively keys form a dict"""
    if isinstance(dict_, OrderedDict) or isinstance(dict_, dict):
        elt_ = dict()
        for key, value in dict_.items():
            key = function(key)
            elt_[key] = capitalizeKeys(value, function)
        return elt_
    elif isinstance(dict_, list):
        elt = list(dict_)
        return list(map(lambda elt_: capitalizeKeys(elt_, function), elt))
    return dict_


def case_insensitive_dict(d):
    return {k.lower(): v for k, v in d.items()}


def remove_json_keys(elt, remove_keys):
    """
    @deprecated Use filter_keys(elt, remove_keys=[...]) instead
    """
    if isinstance(elt, OrderedDict) or isinstance(elt, dict):
        elt_ = dict()
        for key, value in elt.items():
            if key in (remove_keys or []):
                continue
            elt_[key] = remove_json_keys(value, remove_keys)
        elt = elt_
    elif isinstance(elt, list):
        elt = list(elt)
        elt = list(map(lambda elt_: remove_json_keys(elt_, remove_keys), elt))
    return elt


def filter_keys(
    elt,
    keep_keys: Union[list, tuple, set] = None,
    remove_keys: Union[list, tuple, set] = None,
):
    if remove_keys is None:
        remove_keys = []
    if keep_keys is None:
        keep_keys = []

    if isinstance(elt, (OrderedDict, dict)):
        elt_ = dict()
        for key, value in elt.items():
            if key in keep_keys and key not in remove_keys:
                elt_[key] = filter_keys(value, keep_keys, remove_keys)
        elt = elt_
    elif isinstance(elt, list):
        elt = list(elt)
        elt = list(map(lambda elt_: filter_keys(elt_, keep_keys, remove_keys), elt))
    return elt


def rename_nested_keys(obj, name_mapping):
    if isinstance(obj, dict):
        new_obj = dict()
        for k in obj:
            if k in name_mapping:
                new_obj[name_mapping[k]] = rename_nested_keys(obj[k], name_mapping)
            else:
                new_obj[k] = rename_nested_keys(obj[k], name_mapping)
    elif isinstance(obj, list):
        new_obj = [rename_nested_keys(dct, name_mapping) for dct in obj]
    else:
        new_obj = obj
    return new_obj


def merge(a, b):
    return {**a, **b}


def first(arr=None, default=None):
    if arr is None:
        arr = []
    return next(iter(arr or []), default)


def flatten_nested_list(nested_list: List[List]) -> List:
    """
    Parameters
    ----------
    nested_list : List[List]
        A list where each element is itself a list of elements to be flattened into a single list.

    Returns
    -------
    list
        A single flattened list containing all the elements from the sublists.
    """
    if not isinstance(nested_list, list):
        return nested_list

    flattened_list = []
    for sublist in nested_list:
        if isinstance(sublist, list):
            flattened_list.extend(sublist)
        else:
            flattened_list.append(sublist)

    return flattened_list


def aggregate(arr, key, mapping):
    keyBy = reduce(lambda acc, elt: {**acc, elt[key]: acc.get(elt[key], []) + [elt]}, arr, {})
    return [mapping(v) for k, v in keyBy.items() if k is not None]


def inflate_flat_dict(item: dict) -> dict:
    """
    Inflate a flat dict by splitting its key with '_' into sub-dict. Except if the key start with _.
    Example1 : {"a_b" : 5} => {"a": {"b": 5}}
    Example2 : {"_a_b": 5} => {"a_b" : 5}
    @param item: the source dict
    @return: the inflated_dict
    """
    inflated_dict = {}

    def inflate(elem: dict, key_part: list, value: any):
        if key_part[0] not in elem:
            elem[key_part[0]] = {}
        if len(key_part) == 2:
            elem[key_part[0]][key_part[1]] = value
        else:
            inflate(elem[key_part[0]], key_part[1:], v)

    for k, v in copy(item).items():
        if k[0] == "_":
            inflated_dict[k[1:]] = v
        elif "_" in k:
            inflate(inflated_dict, k.split("_"), v)
        else:
            inflated_dict[k] = v

    return inflated_dict


def convert_float_to_decimal(item: any) -> any:
    if isinstance(item, float):
        return Decimal(str(item))
    if isinstance(item, dict):
        item_copy = deepcopy(item)
        for k, v in item_copy.items():
            item_copy[k] = convert_float_to_decimal(v)
        return item_copy
    if isinstance(item, list):
        item_copy = deepcopy(item)
        for idx, v in enumerate(item_copy):
            item_copy[idx] = convert_float_to_decimal(v)
        return item_copy
    return item


def flatten_dict(d, parent_key="", sep="/"):
    """
    Fonction récursive pour aplatir un dictionnaire imbriqué.
    Les clés imbriquées seront concaténées avec sep servant de séparateur.
    """
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def flatten_dict_generator(d, parent_key="", sep="/"):
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            yield from flatten_dict_generator(v, new_key, sep=sep)
        else:
            yield (new_key, v)


def generate_stable_hash(data: dict) -> str:
    normalized_string = json.dumps(data, sort_keys=True, separators=(",", ":"))
    return hashlib.sha256(normalized_string.encode()).hexdigest()


K = TypeVar("K")
V = TypeVar("V")
_KT = TypeVar("_KT")
_VT = TypeVar("_VT")


# noinspection PyPep8Naming,SpellCheckingInspection
class keydefaultdict(defaultdict):
    """
    A dictionary subclass that provides default values for missing keys, with a user-defined
    function to dynamically generate those defaults.

    This class extends the `collections.defaultdict` to allow the `default_factory` to
    accept the missing key as an argument while generating the default value. This
    enables more complex dynamic behavior compared to the standard `defaultdict`.

    Attributes
    ----------
    default_factory : Callable[[Any], Any] or None
        A callable that takes the missing key as a parameter and returns a default value.
        If set to None, any attempt to access a missing key will raise a KeyError.
    """

    def __init__(self, default_factory: Callable[[K], V] | None, iterable: Iterable[tuple[_KT, _VT]] | dict) -> None:
        super().__init__(default_factory, iterable)
        self.default_factory = default_factory

    def __missing__(self, key: K) -> V:
        if self.default_factory is not None:
            ret = self[key] = self.default_factory(key)
            return ret
        raise KeyError(key)
