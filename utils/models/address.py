from dataclasses import dataclass, field
from typing import Optional

from dataclasses_json import dataclass_json, LetterCase, config

from utils.dict_utils import generate_stable_hash
from utils.models.pydantic_utils import PascalModel

ADDRESS_EXCLUDE = "ADDRESS_EXCLUDE"


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Address:
    """
    A class to represent an address.

    Attributes
    ----------
    street : str
        The street name of the address.
    number : str
        The street number of the address.
    postcode : str
        The postcode of the address.
    city : str
        The city of the address.
    country : str, optional
        The country of the address, by default "BE".
    box : str, optional
        The box number of the address, by default None.
    """

    street: str
    number: str
    postcode: str
    city: str
    box: Optional[str] = field(default=None, metadata=config(exclude=lambda x: x == ADDRESS_EXCLUDE))
    country: Optional[str] = field(default="BE", metadata=config(exclude=lambda x: x == ADDRESS_EXCLUDE))

    def __hash__(self):
        return hash((self.street, self.number, self.postcode, self.city, self.box, self.country))

    def stable_hash(self) -> str:
        """
        Generates a stable hash code for the object's internal dictionary.
        """
        return generate_stable_hash(self.__dict__)


class PydanticAddress(PascalModel, Address):
    pass
