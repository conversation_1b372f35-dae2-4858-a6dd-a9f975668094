from enum import Enum
from typing import Annotated, Optional

from annotated_types import Ge
from pydantic import model_validator
from pydantic_core.core_schema import ValidationInfo

from utils.models.pydantic_utils import NotEmpty, PascalModel
from utils.validation import must_be_true


class HabitationType(Enum):
    APARTMENT = "APARTMENT"
    HOUSE = "HOUSE"


class PrimaryHeatingSource(Enum):
    ELECTRICITY = "ELECTRICITY"
    PAC = "PAC"
    GAZ = "GAZ"
    OIL = "OIL"


class EnergyAsset(PascalModel):
    number_people: Annotated[int, Ge(0)]
    habitation_type: HabitationType
    domestic_hot_water: bool = False
    primary_heating_source: PrimaryHeatingSource
    swimming_pool: bool = False
    jacuzzi: bool = False
    electric_vehicles_charged_home: Annotated[int, Ge(0)]


class EnergyProfile(PascalModel):
    uid: NotEmpty[str]
    hash_address: NotEmpty[str]
    energy_asset: Optional[EnergyAsset] = None
    bilan: Optional[bool] = None
    consent_bilan: Optional[bool] = False
    consent_energy_asset: Optional[bool] = False

    @model_validator(mode="after")
    @classmethod
    def check_energy_asset_or_bilan(cls, values):
        if not values.energy_asset and values.bilan is None:
            raise ValueError("Either EnergyAsset or Bilan must be provided")
        return values

    @model_validator(mode="after")
    @classmethod
    def check_consent(cls, values, info: ValidationInfo):
        if values.bilan:
            must_be_true(values.consent_bilan, "ConsentBilan", "when 'Bilan' is set")
        if values.energy_asset:
            must_be_true(values.consent_energy_asset, "ConsentEnergyAsset", "when 'EnergyAsset' is set")
        return values
