from typing import Annotated

from annotated_types import Gt
from pydantic import AfterValidator

from utils.models.pydantic_utils import NotEmpty, PascalModel
from utils.validation import must_be_true


class SmartConsoAlertModel(PascalModel):
    uid: NotEmpty[str]
    ean: NotEmpty[str]
    meter: NotEmpty[str]
    daily: Annotated[float, Gt(0)] | None = None
    monthly: Annotated[float, Gt(0)] | None = None
    consent: Annotated[bool, AfterValidator(must_be_true)]

    def is_alert_empty(self) -> bool:
        return self.daily is None and self.monthly is None
