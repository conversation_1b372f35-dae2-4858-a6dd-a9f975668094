from typing import Any

from pydantic import AnyHttpUrl

from utils.models.pydantic_utils import PascalModel


class SFCaseAddress(PascalModel):
    """
    Model representing a contact address.

    This class defines the structure for address information used in contact requests,
    including street details, municipality, and postal code.

    Attributes
    ----------
    rue : str
        The street name of the address.
    numero : str
        The street number or building number.
    commune : str
        The municipality or city name.
    code_postal : str
        The postal code or ZIP code.

    """

    rue: str | None = None
    numero: str | None = None
    commune: str | None = None
    code_postal: str | None = None


class SFCaseFile(PascalModel):
    """
    Model representing a file attachment in a contact request.

    This class defines the structure for file attachments that can be included
    with contact requests, containing metadata about the file.

    Attributes
    ----------
    document_name : str
        The name or title of the document.
    file_url : AnyHttpUrl
        The URL where the file can be accessed or downloaded.
        Must be a valid HTTP/HTTPS URL.
    extension : str
        The file extension (e.g., 'pdf', 'docx', 'jpg').

    """

    document_name: str
    file_url: AnyHttpUrl | None = None
    file_data: str | None = None
    extension: str


class SFCaseData(PascalModel):
    """
    Model representing the main contact data and request information.

    This class contains all the essential information about a contact request,
    including personal details, address, preferences, and additional metadata
    for processing the request.

    Attributes
    ----------
    remarque : str
        Additional remarks or comments about the request.
    nom : str
        Last name of the contact person.
    prenom : str
        First name of the contact person.
    adresse : SFCaseAddress
        Complete address information of the contact person.
    phone : str
        Phone number of the contact person.
    email : str
        Email address of the contact person.
    preference : str
        Communication preference (e.g., 'email', 'phone', 'mail').
    destinataire : str
        The intended recipient or department for the request.
    section : str
        The main section or category of the request.
    sous_section : str
        The subsection or subcategory of the request.
    reference : int | str
        Reference number or identifier for the request.
        Can be either an integer or string.
    description : str
        Detailed description of the request or inquiry.
    ean : str
        European Article Number or barcode identifier.
    customs_data : dict[str, Any]
        Custom data fields as key-value pairs.
    customs_html : str
        Custom HTML content or formatting.
    status : str
        Status of the request.
    reason : str
        Reason or cause for the request.
    subject : str
        Subject or title of the request.

    """

    nom: str | None = None
    prenom: str | None = None
    adresse: SFCaseAddress | None = None
    phone: str | None = None
    email: str | None = None
    preference: str | None = None
    destinataire: str | None = None
    section: str = "default"
    sous_section: str = ""
    reference: int | str | None = None
    description: str | None = None
    ean: str | None = None
    remarque: str | None = None
    customs_data: dict[str, Any] | None = None
    customs_html: str | None = None
    status: str | None = None
    reason: str | None = None
    subject: str | None = None


class SFCaseRequest(PascalModel):
    """
    Model representing a complete contact request.

    This is the main model that encapsulates all information related to a contact
    request, including client information, request data, and any file attachments.

    Attributes
    ----------
    client_email : str
        Email address of the client making the request.
    data : SFCaseData
        The main contact data and request information.
    files : list[SFCaseFile], default=[]
        List of file attachments associated with the request.
        Defaults to an empty list if no files are provided.

    """

    client_email: str
    data: SFCaseData
    files: list[SFCaseFile] = []
