from contextlib import suppress
from decimal import Decimal
from enum import Enum
from typing import Annotated, Any, LiteralString, Self, TypeVar, Union, get_args, get_origin

from annotated_types import Len
from pydantic import BaseModel, ConfigDict, PlainSerializer, TypeAdapter, ValidationError
from pydantic.alias_generators import to_pascal
from pydantic_core import InitErrorDetails, PydanticCustomError, PydanticUseDefault

T = TypeVar("T")
NotEmpty = Annotated[T, Len(min_length=1)]

FloatAsDecimalAdapter = TypeAdapter(Annotated[float, PlainSerializer(lambda x: Decimal(x), return_type=Decimal)])


def default_if_none(value: Any) -> Any:
    if value is None:
        raise PydanticUseDefault
    return value


def custom_validation_error(error_type: LiteralString, message: LiteralString, loc: tuple[LiteralString], input_data: Any) -> ValidationError:
    error: InitErrorDetails = {
        "type": PydanticCustomError(error_type, message),
        "loc": loc,
        "input": input_data,
    }
    raise ValidationError.from_exception_data("Custom validation error", [error])


class PascalModel(BaseModel):
    """
    A model class that extends BaseModel and applies PascalCase aliasing to fields.

    Attributes
    ----------
    model_config : ConfigDict
        Configuration for the model, including alias generation and field population rules.

    Methods
    -------
    model_dump_json(self, **kwargs)
        Dumps the model instance to a JSON string, using aliases for field names by default.

    model_dump(self, **kwargs)
        Dumps the model instance to a dictionary, using aliases for field names by default.

    """

    model_config = ConfigDict(alias_generator=to_pascal, populate_by_name=True)

    def model_dump_json(self, **kwargs):
        kwargs.setdefault("by_alias", True)
        return super().model_dump_json(**kwargs)

    def model_dump(self, **kwargs):
        kwargs.setdefault("by_alias", True)
        return super().model_dump(**kwargs)

    def model_dump_dynamodb(self, **kwargs):
        kwargs.setdefault("by_alias", False)
        dump = super().model_dump(**kwargs)
        return _convert_values_for_dynamo(dump)

    @classmethod
    def recursive_construct(cls, **values: dict[str, Any]) -> Self:
        return _recursive_construct(cls, values)


def _convert_values_for_dynamo(data):
    if isinstance(data, float):
        return Decimal(str(data))
    if isinstance(data, Enum):
        return data.value
    if isinstance(data, dict):
        return {k: _convert_values_for_dynamo(v) for k, v in data.items()}
    if isinstance(data, (list, tuple)):
        return [_convert_values_for_dynamo(i) for i in data]
    return data


def _recursive_construct(model_cls: type[BaseModel], data: any) -> BaseModel | list[BaseModel]:
    # Handle lists/tuples of nested models
    if isinstance(data, (list, tuple)):
        # Handle lists or tuples
        inner_type = get_args(model_cls)[0]
        return [_recursive_construct(inner_type, item) for item in data]

    if isinstance(data, dict):
        constructed_data = {}
        for key, field_info in model_cls.model_fields.items():
            resolved_type = _resolve_type(field_info.annotation)
            field_value = data.get(field_info.alias, data.get(key))

            if isinstance(field_value, (dict, list, tuple)) and issubclass(resolved_type, BaseModel):
                field_value = _recursive_construct(resolved_type, field_value)
            elif field_value is not None and not isinstance(field_value, resolved_type):
                with suppress(ValidationError):
                    field_value = resolved_type(field_value)
            elif field_value is None:
                if field_info.default_factory is not None:
                    field_value = field_info.default_factory()
                elif field_info.default is not None:
                    field_value = field_info.default

            constructed_data[field_info.alias] = field_value

        return model_cls.model_construct(**constructed_data)

    return data


def _resolve_type(annotation: type) -> type:
    """Resolve Optional or Union types to their inner type."""
    origin = get_origin(annotation)
    args = get_args(annotation)

    if origin is Union:
        # Handle Optional[X] (Union[X, None])
        non_none_types = [arg for arg in args if arg is not type(None)]
        return non_none_types[0] if non_none_types else annotation

    return annotation
