import json
import os
import unittest

from pydantic import ValidationError

from utils.models.connection_model import (
    ActAs,
    Address,
    ApplicantPartner,
    ApplicantType,
    Company,
    Connection,
    ConnectionAddress,
    Document,
    EnergyType,
    Meter,
    Partner,
    PhaseType,
    Rate,
    Usage,
    WorkType,
)


class TestMeter(unittest.TestCase):
    def test_energy_type(self):
        meter = Meter(energy_type=EnergyType.ELEC, work_type=WorkType.NOUV_RACC, power=1000.0, amper=10.0, phase_type=PhaseType.TRI, rate=Rate.DT)
        self.assertEqual(meter.energy_type, EnergyType.ELEC)

    def test_work_type(self):
        meter = Meter(energy_type=EnergyType.ELEC, work_type=WorkType.MODI_INSTAL, ean="1234567890123", number="12345")
        self.assertEqual(meter.work_type, WorkType.MODI_INSTAL)

    def test_empty_ean(self):
        with self.assertRaises(ValidationError) as ex:
            meter = Meter(energy_type=EnergyType.ELEC, work_type=WorkType.MODI_INSTAL, ean="", number="12345")
        self.assertIn("String should have at least 1 character", ex.exception.errors()[0]["msg"])

    def test_validate_with_required_fields_and_workType_NOUV_RACC(self):
        try:
            Meter(energy_type=EnergyType.ELEC, work_type=WorkType.NOUV_RACC, power=1000.0, amper=10.0, phase_type=PhaseType.TRI, rate=Rate.DT)
        except ValidationError as ve:
            self.fail(f"ValidationError raised unexpectedly: {ve}")

    def test_validate_with_missing_required_fields_and_workType_NOUV_RACC_elec(self):
        with self.assertRaises(ValidationError) as ex:
            Meter(energy_type=EnergyType.ELEC, work_type=WorkType.NOUV_RACC)
        self.assertIn("[Power, PhaseType, Rate]", ex.exception.errors()[0]["msg"])

    def test_validate_with_missing_required_fields_and_workType_NOUV_RACC_gas(self):
        with self.assertRaises(ValidationError) as ex:
            Meter(energy_type=EnergyType.GAS, work_type=WorkType.NOUV_RACC)
        self.assertIn("[Power]", ex.exception.errors()[0]["msg"])

    def test_validate_with_required_fields_and_workType_MODI_INSTAL(self):
        try:
            Meter(energy_type=EnergyType.GAS, work_type=WorkType.MODI_INSTAL, ean="1234567890123", number="12345")
        except ValidationError as ve:
            self.fail(f"ValidationError raised unexpectedly: {ve}")

    def test_validate_with_missing_required_fields_and_workType_MODI_INSTAL(self):
        with self.assertRaises(ValueError):
            Meter(energy_type=EnergyType.GAS, work_type=WorkType.MODI_INSTAL)

    def test_validate_with_required_fields_and_workType_MODI_SMART(self):
        try:
            Meter(
                energy_type=EnergyType.GAS,
                work_type=WorkType.MODI_SMART,
                ean="1234567890123",
                number="12345",
                photo=Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg"),
            )
        except ValidationError as ve:
            self.fail(f"ValidationError raised unexpectedly: {ve}")

    def test_validate_with_missing_required_fields_and_workType_MODI_SMART(self):
        with self.assertRaises(ValueError):
            Meter(energy_type=EnergyType.GAS, work_type=WorkType.MODI_SMART, ean="1234567890123", number="12345")


class TestConnectionModel(unittest.TestCase):
    def setUp(self):
        os.environ["LANG"] = "FR"
        self.connection = Connection(
            applicant_type=ApplicantType.INDIVIDUAL,
            act_as=ActAs.OWNER,
            applicant=ApplicantPartner(
                name="name",
                firstname="firstname",
                email="email",
                phone="phone",
                address=Address(street="Street", number="1", postcode="12345", city="City", country="FR"),
            ),
            address=ConnectionAddress(street="Street", number="1", postcode="12345", city="City", country="FR"),
            meters=[
                Meter(
                    ean="ean",
                    number="number",
                    energy_type=EnergyType.ELEC,
                    work_type=WorkType.MODI_SMART,
                    photo=Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg"),
                ),
            ],
            confirm_gdpr=True,
            elec_note="ElecNote1",
            gas_note="GasNote1",
            general_note="GeneralNote1",
            desired_date="202512",
            usage=Usage.RESI,
            documents=[Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg")],
        )

    def test_empty_meter_list(self):
        with self.assertRaises(ValidationError) as ex:
            Connection(
                applicant_type=ApplicantType.INDIVIDUAL,
                act_as=ActAs.OWNER,
                applicant=ApplicantPartner(
                    name="name",
                    firstname="firstname",
                    email="email",
                    phone="phone",
                    address=Address(street="Street", number="1", postcode="12345", city="City", country="FR"),
                ),
                address=ConnectionAddress(street="Street", number="1", postcode="12345", city="City", country="FR"),
                meters=[],
                confirm_gdpr=True,
                elec_note="ElecNote1",
                gas_note="GasNote1",
                general_note="GeneralNote1",
                desired_date="202512",
                usage=Usage.RESI,
                documents=[Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg")],
            )
        self.assertIn("List should have at least 1 item after validation", ex.exception.errors()[0]["msg"])
        with self.assertRaises(ValidationError) as ex:
            Connection(
                applicant_type=ApplicantType.INDIVIDUAL,
                act_as=ActAs.OWNER,
                applicant=ApplicantPartner(
                    name="name",
                    firstname="firstname",
                    email="email",
                    phone="phone",
                    address=Address(street="Street", number="1", postcode="12345", city="City", country="FR"),
                ),
                address=ConnectionAddress(street="Street", number="1", postcode="12345", city="City", country="FR"),
                meters=None,
                confirm_gdpr=True,
                elec_note="ElecNote1",
                gas_note="GasNote1",
                general_note="GeneralNote1",
                desired_date="202512",
                usage=Usage.RESI,
                documents=[Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg")],
            )
        self.assertIn("Input should be a valid list", ex.exception.errors()[0]["msg"])
        with self.assertRaises(ValidationError) as ex:
            Connection(
                applicant_type=ApplicantType.INDIVIDUAL,
                act_as=ActAs.OWNER,
                applicant=ApplicantPartner(
                    name="name",
                    firstname="firstname",
                    email="email",
                    phone="phone",
                    address=Address(street="Street", number="1", postcode="12345", city="City", country="FR"),
                ),
                address=ConnectionAddress(street="Street", number="1", postcode="12345", city="City", country="FR"),
                confirm_gdpr=True,
                elec_note="ElecNote1",
                gas_note="GasNote1",
                general_note="GeneralNote1",
                desired_date="202512",
                usage=Usage.RESI,
                documents=[Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg")],
            )
        self.assertIn("Field required", ex.exception.errors()[0]["msg"])

    def test_no_meter_quantity_with_nouv_forf(self):
        with self.assertRaises(ValidationError) as ex:
            Connection(
                applicant_type=ApplicantType.INDIVIDUAL,
                act_as=ActAs.OWNER,
                applicant=ApplicantPartner(
                    name="name",
                    firstname="firstname",
                    email="email",
                    phone="phone",
                    address=Address(street="Street", number="1", postcode="12345", city="City", country="FR"),
                ),
                address=ConnectionAddress(street="Street", number="1", postcode="12345", city="City", country="FR"),
                meters=[
                    Meter(energy_type=EnergyType.ELEC, work_type=WorkType.NOUV_FORF, quantity=1)
                ],
                synergrid="12.34.56",
                confirm_gdpr=True,
                elec_note="ElecNote1",
                gas_note="GasNote1",
                general_note="GeneralNote1",
                desired_date="202512",
                usage=Usage.RESI,
                documents=[Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg")],
            )
        self.assertIn('[Quantity] must be 0 with WorkType : "WorkType.NOUV_FORF"', ex.exception.errors()[0]["msg"])
        Connection(
            applicant_type=ApplicantType.INDIVIDUAL,
            act_as=ActAs.OWNER,
            applicant=ApplicantPartner(
                name="name",
                firstname="firstname",
                email="email",
                phone="phone",
                address=Address(street="Street", number="1", postcode="12345", city="City", country="FR"),
            ),
            address=ConnectionAddress(street="Street", number="1", postcode="12345", city="City", country="FR"),
            meters=[
                Meter(energy_type=EnergyType.ELEC, work_type=WorkType.NOUV_FORF, quantity=0)
            ],
            synergrid="12.34.56",
            confirm_gdpr=True,
            elec_note="ElecNote1",
            gas_note="GasNote1",
            general_note="GeneralNote1",
            desired_date="202512",
            usage=Usage.RESI,
            documents=[Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg")],
        )

    def test_sect_activite_returns_01_with_energytype_ELEC(self):
        self.connection.meters[0].energy_type = EnergyType.ELEC
        self.assertEqual(self.connection.meters[0].energy_type.activity_sector, "01")

    def test_sect_activite_returns_02_with_energytype_GAZ(self):
        self.connection.meters[0].energy_type = EnergyType.GAS
        self.assertEqual(self.connection.meters[0].energy_type.activity_sector, "02")

    def test_to_sap_format_ws35_is_json_serializable(self):
        data = self.connection.format_for_sap_ws35()
        json.dumps(data)

    def test_to_sap_format_ws35(self):
        result = self.connection.format_for_sap_ws35()
        self.assertEqual(result["Partenaire"][0]["TypePartenaire"], "DEMANDEUR")
        self.assertEqual(result["Partenaire"][0]["Nom"], self.connection.applicant.name)
        self.assertEqual(result["Partenaire"][0]["Prenom"], self.connection.applicant.firstname)
        self.assertEqual(result["Partenaire"][0]["Langue"], os.environ["LANG"])
        self.assertEqual(result["Partenaire"][0]["Gsm"], self.connection.applicant.phone)
        self.assertEqual(result["Partenaire"][0]["Email"], self.connection.applicant.email)
        self.assertEqual(result["Partenaire"][0]["TypeURD"], self.connection.applicant_type.value)
        self.assertIsNone(result["Partenaire"][0]["NumTVA"])
        self.assertEqual(result["Partenaire"][0]["AssujetiTVA"], "N")
        self.assertIsNone(result["Partenaire"][0]["NomEntreprise"])
        self.assertIsNone(result["Partenaire"][0]["FormeJuridique"])
        self.assertEqual(result["Partenaire"][0]["Adresse"]["Rue"], self.connection.applicant.address.street)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["NumRue"], self.connection.applicant.address.number)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["NumCmpt"], self.connection.applicant.address.box)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["CdPostal"], self.connection.applicant.address.postcode)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["Localite"], self.connection.applicant.address.city)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["Pays"], self.connection.applicant.address.country)
        self.assertEqual(result["Adresse"]["Rue"], self.connection.address.street)
        self.assertEqual(result["Adresse"]["NumRue"], self.connection.address.number)
        self.assertEqual(result["Adresse"]["NumCmpt"], self.connection.address.box)
        self.assertEqual(result["Adresse"]["CdPostal"], self.connection.address.postcode)
        self.assertEqual(result["Adresse"]["Localite"], self.connection.address.city)
        self.assertEqual(result["Adresse"]["Pays"], self.connection.address.country)
        self.assertIsNone(result["Adresse"]["ParcelleSpw"])
        self.assertIsNone(result["Adresse"]["Emplacement"])
        self.assertEqual(result["Demande"][0]["SectActivite"], self.connection.meters[0].energy_type.activity_sector)
        self.assertEqual(result["Demande"][0]["Ean"], self.connection.meters[0].ean)
        self.assertEqual(result["Demande"][0]["NbCompteurs"], 1)
        self.assertEqual(result["Demande"][0]["Details"][0]["Valeur"], self.connection.meters[0].work_type.value)
        self.assertEqual(result["Demande"][0]["Compteur"][0]["Compteur"], 1)
        self.assertEqual(result["Demande"][0]["Compteur"][0]["DetailsCompteurs"][0]["ValeurDetailCpt"], self.connection.meters[0].number)
        self.assertEqual(result["Complement"][0]["Libelle"], "REMARQUE")
        self.assertEqual(result["Complement"][4]["Libelle"], "GDPR")
        self.assertEqual(result["Complement"][4]["Valeur"], "Y")
        self.assertEqual(result["Complement"][7]["Libelle"], "LOCATAIRE")
        self.assertEqual(result["Complement"][7]["Valeur"], "N")

    def test_to_sap_format_ws35_nearbystreet_user_if_present(self):
        result = self.connection.format_for_sap_ws35()
        self.assertEqual(result["Adresse"]["Rue"], self.connection.address.street)

        self.connection.near_by_street = "Near somewhere"
        result = self.connection.format_for_sap_ws35()
        self.assertNotEqual(result["Adresse"]["Rue"], self.connection.address.street)
        self.assertEqual(result["Adresse"]["Rue"], self.connection.near_by_street)

    def test_to_sap_format_ws35_act_as_tenant(self):
        self.connection.act_as = ActAs.TENANT
        result = self.connection.format_for_sap_ws35()
        self.assertEqual(result["Complement"][7]["Libelle"], "LOCATAIRE")
        self.assertEqual(result["Complement"][7]["Valeur"], "Y")

    def test_to_sap_format_ws35_grouping_r01(self):
        self.connection.meters = [
            Meter(
                energy_type=EnergyType.ELEC,
                work_type=WorkType.NOUV_RACC,
                quantity=3,
                power=9.2,
                amper=40,
                phase_type=PhaseType.MONO,
                rate=Rate.DT,
            ),
            Meter(
                energy_type=EnergyType.ELEC,
                work_type=WorkType.NOUV_RACC,
                quantity=1,
                power=13.9,
                amper=20,
                phase_type=PhaseType.TRI,
                rate=Rate.DT,
            ),
            Meter(
                energy_type=EnergyType.ELEC,
                work_type=WorkType.NOUV_CCOM,
                quantity=1,
                power=9.2,
                amper=40,
                phase_type=PhaseType.MONO,
                rate=Rate.DT,
            ),
            Meter(
                energy_type=EnergyType.ELEC,
                work_type=WorkType.NOUV_CCOM,
                quantity=1,
                power=13.9,
                amper=20,
                phase_type=PhaseType.TRI,
                rate=Rate.DT,
            ),
            Meter(
                energy_type=EnergyType.ELEC,
                work_type=WorkType.MODI_INSTAL,
                ean="ean_group",
                number="meter_group",
                power=9.2,
                amper=40,
            ),
            Meter(
                energy_type=EnergyType.ELEC,
                work_type=WorkType.MODI_INSTAL,
                ean="ean_group",
                number="meter_group",
                power=13.9,
                amper=20,
            ),
            Meter(
                energy_type=EnergyType.ELEC,
                work_type=WorkType.MODI_INSTAL,
                ean="ean1",
                number="meter1",
                power=9.2,
                amper=40,
            ),
            Meter(
                energy_type=EnergyType.ELEC,
                work_type=WorkType.MODI_INSTAL,
                ean="ean2",
                number="meter2",
                power=13.9,
                amper=20,
            ),
        ]
        result = self.connection.format_for_sap_ws35()
        self.assertEqual(next(c["Valeur"] for c in result["Demande"][0]["Details"] if c["CodeDetail"] == "CD_TYPE"), "NOUV_RACC")
        self.assertEqual(result["Demande"][0]["NbCompteurs"], 4)
        self.assertEqual(len(result["Demande"][0]["Compteur"]), 4)
        self.assertEqual(result["Demande"][0]["Compteur"][0]["Compteur"], 1)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][0]["Compteur"][0]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 9.2)
        self.assertEqual(result["Demande"][0]["Compteur"][1]["Compteur"], 2)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][0]["Compteur"][1]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 9.2)
        self.assertEqual(result["Demande"][0]["Compteur"][2]["Compteur"], 3)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][0]["Compteur"][2]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 9.2)
        self.assertEqual(result["Demande"][0]["Compteur"][3]["Compteur"], 4)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][0]["Compteur"][3]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 13.9)

        self.assertEqual(next(c["Valeur"] for c in result["Demande"][1]["Details"] if c["CodeDetail"] == "CD_TYPE"), "NOUV_CCOM")
        self.assertEqual(result["Demande"][1]["NbCompteurs"], 2)
        self.assertEqual(len(result["Demande"][1]["Compteur"]), 2)
        self.assertEqual(result["Demande"][1]["Compteur"][0]["Compteur"], 1)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][1]["Compteur"][0]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 9.2)
        self.assertEqual(result["Demande"][1]["Compteur"][1]["Compteur"], 2)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][1]["Compteur"][1]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 13.9)

        self.assertEqual(next(c["Valeur"] for c in result["Demande"][2]["Details"] if c["CodeDetail"] == "CD_TYPE"), "MODI_INSTAL")
        self.assertEqual(result["Demande"][2]["Ean"], "ean_group")
        self.assertEqual(result["Demande"][2]["NbCompteurs"], 2)
        self.assertEqual(len(result["Demande"][2]["Compteur"]), 2)
        self.assertEqual(result["Demande"][2]["Compteur"][0]["Compteur"], 1)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][2]["Compteur"][0]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 9.2)
        self.assertEqual(result["Demande"][2]["Compteur"][1]["Compteur"], 2)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][2]["Compteur"][1]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 13.9)

        self.assertEqual(next(c["Valeur"] for c in result["Demande"][3]["Details"] if c["CodeDetail"] == "CD_TYPE"), "MODI_INSTAL")
        self.assertEqual(result["Demande"][3]["Ean"], "ean1")
        self.assertEqual(result["Demande"][3]["NbCompteurs"], 1)
        self.assertEqual(len(result["Demande"][3]["Compteur"]), 1)
        self.assertEqual(result["Demande"][3]["Compteur"][0]["Compteur"], 1)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][3]["Compteur"][0]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 9.2)

        self.assertEqual(next(c["Valeur"] for c in result["Demande"][4]["Details"] if c["CodeDetail"] == "CD_TYPE"), "MODI_INSTAL")
        self.assertEqual(result["Demande"][4]["Ean"], "ean2")
        self.assertEqual(result["Demande"][4]["NbCompteurs"], 1)
        self.assertEqual(len(result["Demande"][4]["Compteur"]), 1)
        self.assertEqual(result["Demande"][4]["Compteur"][0]["Compteur"], 1)
        self.assertEqual(next(c["ValeurDetailCpt"] for c in result["Demande"][4]["Compteur"][0]["DetailsCompteurs"] if c["CodeDetailCpt"] == "PUISSANCE"), 13.9)

    def test_to_sap_format_ws35_with_address_spw_and_note(self):
        self.connection.address.spw_plot = "12345"
        self.connection.address.note = "Somewhere"
        result = self.connection.format_for_sap_ws35()

        self.assertEqual(result["Adresse"]["ParcelleSpw"], self.connection.address.spw_plot)
        self.assertEqual(result["Adresse"]["Emplacement"], self.connection.address.note)

    def test_to_sap_format_ws35_with_address_coordinates(self):
        self.connection.address.lat = 5.123
        self.connection.address.lon = 5.321
        result = self.connection.format_for_sap_ws35()

        self.assertEqual(result["Complement"][5]["Libelle"], "COORDGPSY")
        self.assertEqual(result["Complement"][5]["Valeur"], self.connection.address.lat)
        self.assertEqual(result["Complement"][6]["Libelle"], "COORDGPSX")
        self.assertEqual(result["Complement"][6]["Valeur"], self.connection.address.lon)

    def test_to_sap_format_ws35_with_company(self):
        # Check if company fields are properly handled
        self.connection.subject_to_vat = True
        self.connection.company = Company(name="Corp", legal_status="SA", vat="12345")

        result = self.connection.format_for_sap_ws35()
        self.assertEqual(result["Partenaire"][0]["NumTVA"], self.connection.company.vat)
        self.assertEqual(result["Partenaire"][0]["AssujetiTVA"], "Y")
        self.assertEqual(result["Partenaire"][0]["NomEntreprise"], self.connection.company.name)
        self.assertEqual(result["Partenaire"][0]["FormeJuridique"], self.connection.company.legal_status)

    def test_to_sap_format_ws35_with_contact(self):
        # Check if contact fields are properly handled
        self.connection.contact = Partner(
            name="name",
            firstname="firstname",
            email="email",
            phone="phone",
            address=Address(street="Street", number="1", box="A", postcode="12345", city="City", country="FR"),
        )
        result = self.connection.format_for_sap_ws35()

        self.assertEqual(result["Partenaire"][1]["TypePartenaire"], "CONTACT")
        self.assertEqual(result["Partenaire"][1]["Nom"], self.connection.applicant.name)
        self.assertEqual(result["Partenaire"][1]["Prenom"], self.connection.applicant.firstname)
        self.assertEqual(result["Partenaire"][1]["Langue"], os.environ["LANG"])
        self.assertEqual(result["Partenaire"][1]["Gsm"], self.connection.applicant.phone)
        self.assertEqual(result["Partenaire"][1]["Email"], self.connection.applicant.email)
        self.assertEqual(result["Partenaire"][1]["Adresse"]["Rue"], self.connection.contact.address.street)
        self.assertEqual(result["Partenaire"][1]["Adresse"]["NumRue"], self.connection.contact.address.number)
        self.assertEqual(result["Partenaire"][1]["Adresse"]["NumCmpt"], self.connection.contact.address.box)
        self.assertEqual(result["Partenaire"][1]["Adresse"]["CdPostal"], self.connection.contact.address.postcode)
        self.assertEqual(result["Partenaire"][1]["Adresse"]["Localite"], self.connection.contact.address.city)
        self.assertEqual(result["Partenaire"][1]["Adresse"]["Pays"], self.connection.contact.address.country)

    def test_to_sap_format_ws35_with_contact_without_address(self):
        # Check if contact fields are properly handled
        self.connection.contact = Partner(name="name", firstname="firstname", email="email", phone="phone")
        result = self.connection.format_for_sap_ws35()

        self.assertEqual(result["Partenaire"][1]["TypePartenaire"], "CONTACT")
        self.assertEqual(result["Partenaire"][1]["Nom"], self.connection.applicant.name)
        self.assertEqual(result["Partenaire"][1]["Prenom"], self.connection.applicant.firstname)
        self.assertEqual(result["Partenaire"][1]["Langue"], os.environ["LANG"])
        self.assertEqual(result["Partenaire"][1]["Gsm"], self.connection.applicant.phone)
        self.assertEqual(result["Partenaire"][1]["Email"], self.connection.applicant.email)
        self.assertEqual(result["Partenaire"][1]["Adresse"], self.connection.contact.address)

    def test_to_sap_format_ws35_with_category_note_text(self):
        # Check if contact fields are properly handled
        self.connection.meters.extend(
            (
                Meter(
                    ean="ean",
                    number="number",
                    energy_type=EnergyType.GAS,
                    work_type=WorkType.MODI_SMART,
                    photo=Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg"),
                ),
                Meter(
                    ean="ean",
                    number="number",
                    energy_type=EnergyType.ELEC,
                    work_type=WorkType.PULSE_DELAY,
                ),
                Meter.model_validate_json(
                    """{
                        "ean": "ean",
                        "number": "number",
                        "EnergyType": "ELEC",
                        "WorkType": "PULSE_DELAY"
                    }""",
                ),
            ),
        )
        result = self.connection.format_for_sap_ws35()

        self.assertEqual(self.connection.elec_note, result["Demande"][0]["Details"][6]["Valeur"])
        self.assertEqual(self.connection.gas_note, result["Demande"][1]["Details"][6]["Valeur"])
        self.assertEqual(WorkType.PULSE_DELAY.sap_value, result["Demande"][2]["Details"][0]["Valeur"])
        self.assertEqual(f"{WorkType.PULSE_DELAY.category_note_text}\n{self.connection.elec_note}", result["Demande"][2]["Details"][6]["Valeur"])

    def test_to_sap_format_ws35_with_meter_note(self):
        # Check if contact fields are properly handled
        self.connection.meters.extend(
            (
                Meter(ean="ean", number="number", energy_type=EnergyType.GAS, work_type=WorkType.DEPLA_BRAN, note="Something"),
                Meter(ean="ean", number="number", energy_type=EnergyType.ELEC, work_type=WorkType.PULSE_DELAY, note="Something else"),
            ),
        )
        result = self.connection.format_for_sap_ws35()

        self.assertEqual(self.connection.elec_note, result["Demande"][0]["Details"][6]["Valeur"])
        self.assertEqual(f"Something\n{self.connection.gas_note}", result["Demande"][1]["Details"][6]["Valeur"])
        self.assertEqual(f"{WorkType.PULSE_DELAY.category_note_text}\nSomething else\n{self.connection.elec_note}", result["Demande"][2]["Details"][6]["Valeur"])

    def test_format_for_str_ws79_is_json_serializable(self):
        data = self.connection.format_for_str_ws79()
        json.dumps(data)

    def test_format_for_str_ws79(self):
        # call the function to be tested
        self.connection.meters = [
            Meter(
                ean="ean",
                number="number",
                energy_type="ELEC",
                work_type="CHAN_PROVI",
                power=5,
                amper=5,
                phase_type="MONO",
                rate="ST",
            ),
        ]

        formatted_data = self.connection.format_for_str_ws79()
        str_content = json.loads(formatted_data["Description"])

        self.assertEqual(formatted_data["Section"], "NOUVEAU RACCORDEMENT")
        self.assertEqual(formatted_data["SousSection"], "DEMANDE RACCORDEMENT CHANTIER")
        self.assertEqual(str_content["Adresse"]["Rue"], self.connection.address.street)
        self.assertEqual(str_content["Adresse"]["NumRue"], self.connection.address.number)
        self.assertEqual(str_content["Adresse"]["NumCmpt"], self.connection.address.box)
        self.assertEqual(str_content["Adresse"]["CdPostal"], self.connection.address.postcode)
        self.assertEqual(str_content["Adresse"]["Localite"], self.connection.address.city)
        self.assertEqual(str_content["Adresse"]["Pays"], self.connection.address.country)
        self.assertIsNone(str_content["Adresse"]["ParcelleSpw"])
        self.assertIsNone(str_content["Adresse"]["Emplacement"])

        self.assertEqual(str_content["TypeBatiment"]["Type"], "Nouveau")
        self.assertEqual(str_content["TypeBatiment"]["Usage"], self.connection.usage.value)

        self.assertEqual(str_content["InformationCompteurs"]["Compteur"][0]["id"], 1)
        self.assertEqual(str_content["InformationCompteurs"]["Compteur"][0]["energy"], "electricity")
        self.assertEqual(str_content["InformationCompteurs"]["Compteur"][0]["phasesSlug"], self.connection.meters[0].phase_type.value)
        self.assertEqual(str_content["InformationCompteurs"]["Compteur"][0]["amperage"], self.connection.meters[0].amper)
        self.assertEqual(str_content["InformationCompteurs"]["Compteur"][0]["tarifSlug"], self.connection.meters[0].rate.value)

    def test_format_for_str_ws35_synergrid(self):
        # call the function to be tested
        self.connection.synergrid = "synergrid_id"
        self.connection.meters = [
            Meter(
                ean="ean",
                quantity=0,
                energy_type="ELEC",
                work_type="NOUV_FORF",
            ),
        ]

        result = self.connection.format_for_sap_ws35()

        synergrid_complement = next(c for c in result["Complement"] if c["Libelle"] == "SYNERGRID")
        self.assertEqual(synergrid_complement["Libelle"], "SYNERGRID")
        self.assertEqual(synergrid_complement["Valeur"], self.connection.synergrid)


class TestConnectionCorrectWsNeeded(unittest.TestCase):
    def setUp(self):
        self.connection = Connection(
            applicant_type=ApplicantType.INDIVIDUAL,
            act_as=ActAs.OWNER,
            applicant=ApplicantPartner(
                name="name",
                firstname="firstname",
                email="email",
                phone="phone",
                address=Address(street="Street", number="1", postcode="12345", city="City", country="FR"),
            ),
            address=ConnectionAddress(street="Street", number="1", postcode="12345", city="City", country="FR"),
            meters=[
                Meter(
                    ean="ean",
                    number="number",
                    energy_type=EnergyType.ELEC,
                    work_type=WorkType.MODI_SMART,
                    photo=Document(name="photo.jpg", url="https://photo.url.com/my_photo.jpg"),
                ),
            ],
            confirm_gdpr=True,
        )

    def test_ws35_needed(self):
        self.connection.meters = [Meter(quantity=1, work_type="NOUV_RTEC", energy_type=EnergyType.GAS, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed for work type 'NOUV_RTEC'")
        
        self.connection.meters = [Meter(quantity=1, work_type="NOUV_ARMO", energy_type=EnergyType.GAS, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed for work type 'NOUV_ARMO'")
        
        self.connection.meters = [Meter(quantity=1, work_type="NOUV_CCOM", energy_type=EnergyType.GAS, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed for work type 'NOUV_CCOM'")

        self.connection.meters = [Meter(quantity=1, work_type="NOUV_BRE", energy_type=EnergyType.GAS, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed for work type 'NOUV_CCOM'")

        self.connection.meters = [Meter(quantity=0, work_type="NOUV_FORF", energy_type=EnergyType.ELEC)]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed for work type 'NOUV_FORF'")

        self.connection.meters = [Meter(quantity=5, work_type="NOUV_RACC", energy_type=EnergyType.GAS, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed for more than 4 meters")

        self.connection.meters = [Meter(quantity=3, work_type="NOUV_RACC", energy_type=EnergyType.ELEC, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed for less than 4 meters")

        self.connection.meters = [Meter(quantity=1, work_type="NOUV_RACC", energy_type=EnergyType.GAS, power=70, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed when power is equal to 70kw")

        self.connection.meters = [Meter(quantity=1, work_type="NOUV_RACC", energy_type=EnergyType.GAS, power=69, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed when power is less than 70kw")

        self.connection.meters = [Meter(quantity=1, work_type="NOUV_RACC", energy_type=EnergyType.ELEC, power=43.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed when elec power is equal to 43.6kw")

        self.connection.meters = [Meter(quantity=1, work_type="NOUV_RACC", energy_type=EnergyType.ELEC, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertFalse(self.connection.ws79_needed, "WS79 should not be needed when elec power is less than 43.6kw")

    def test_ws79_needed(self):
        self.connection.meters = [Meter(quantity=1, work_type="RACC_FORAIN", energy_type=EnergyType.GAS, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertTrue(self.connection.ws79_needed, "WS79 should be needed for work type 'RACC_FORAIN'")

        self.connection.meters = [Meter(quantity=1, work_type="CHAN_PROVI", energy_type=EnergyType.GAS, power=27.6, amper=5, phase_type="MONO", rate="ST")]
        self.assertTrue(self.connection.ws79_needed, "WS79 should be needed for work type 'CHAN_PROVI'")


if __name__ == "__main__":
    unittest.main()
