import unittest

from utils.models.contact_request_model import (
    ContactRequest,
)


class TestContactRequestModel(unittest.TestCase):
    """Tests pour le modèle ContactRequest."""

    def setUp(self):
        """Configuration des données de test."""
        self.sample_json = {
            "client_email": "<EMAIL>",
            "data": {
                "Remarque": (
                    "Bonjour, \n\n"
                    "Le logement social de Grâce-Ho<PERSON>gne (SLGH) m'a demandé de vous transmettre ce document.\n\n"
                    "Et de prévoir éalement un RDV avec vous.\n\n"
                    "Merci d'avance, bonne journée."
                ),
                "Nom": "<PERSON>",
                "Prenom": "Caterina",
                "Adresse": {
                    "Rue": "Louis pasteur ",
                    "Numero": "14",
                    "Commune": "<PERSON>r<PERSON><PERSON>-<PERSON>",
                    "CodePostal": "4460",
                },
                "Phone": "498323649",
                "Email": "<EMAIL>",
                "Preference": "Email",
                "Destinataire": "<EMAIL>",
                "Section": "Raccordement et travaux",
                "SousSection": "Je n'ai pas de num\\u00e9ro de dossier",
                "Reference": 0,
                "Description": "",
            },
            "files": [
                {
                    "DocumentName": "Attestation conformit\\u00e9_edit_30-06-2025_21.39.03.pdf",
                    "FileUrl": "https://my-resa-api-production-temp-storage.s3.amazonaws.com/ws112/3e170d4d-dff0-4866-aa6a-b4de292c4fe9.pdf?AWSAccessKeyId=ASIAS7HADRZWAMM3HIIF&Signature=AZ1Fd6BjCEfJuHS93q50ZX1QwX4%3D&x-amz-security-token=IQoJb3JpZ2luX2VjEMz%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCWV1LXdlc3QtMSJHMEUCIQCN%2F2MbVWARrkkdnFzyw1znFbHsO9D6FMKGSVhP4%2BOH6AIgUF3unc3UeZINjv6e%2BO0W6WB6qEMcM1I4kefX36Zd7o4qjwMIxf%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARADGgwyMDQ0ODA5NDE2NzYiDFbV9ZOrkNyYag4uuCrjAhGA5De%2FOnV7zCqna8tMcC41ky0OVIWvCchS4NE4BMj4E0PecAYgkyO2wLQeeVP8LLjQCRGLKi5Gp39VM2P%2FHiwWcQ6vsPGyLKIUuQkfTLjy5li3d4peHXF41Mi5j65P0yZj5h0vP5KjR4UKK593HXdsAqfCTY0mpOhSu4SEm0VOXeE5b%2FOTXKQvO8XqkBdJanPiPEgBbD3%2BgLCe28DGlD5wMGRoWvuFrWcvaJSgtNRt2UHf3hW4hY3w9tpjhCmqS7ck8YwcgjVLnYqHLv%2Fz2txvz3KCJp7cpFNKOyimvR1CPYOMvhcj1PQgBqnQAisHF0dFfT9O%2Bu0F%2FY4czYV%2BLa8T2w0hKL2xjkX4gHlinXWwblJUxWDzM4DqHaTv6A0J1WNQiN6e%2F46kpy%2BGp3KMdQO4N2rtiaEJmR%2F7LajZpiX7GUarE3m1bxIssO8r8y26E4kHSa1DLfQpEYEqIntiL4nvcQow7NCLwwY6ngHPbqDlk1MzK9AWZRkvnME3I4dcQG7%2FhD8rgjwgrhHeWRRZqrsiUi4%2BK1YnAFycP7BI%2FIoZUJdai7MClXsf2n1EH5PaV3AtUWtXTpne0jzUq7a%2FUDXpbb3DL3NZ1KkRxutMYvH%2Bbe60nyzsR5q3G5i4YYmkMLFgkWYfhVFbT466eNpoUJaEjLLUd%2BhFT6TySWEu22NrPkZlS6ZCbBSvEQ%3D%3D&Expires=**********",
                    "Extension": "pdf",
                },
            ],
        }

    def test_parse_sample_json(self):
        """Test de parsing du JSON d'exemple."""
        contact_request = ContactRequest.model_validate(self.sample_json)

        # Vérification des données principales
        self.assertEqual(contact_request.client_email, "<EMAIL>")
        self.assertEqual(contact_request.data.nom, "Marino")
        self.assertEqual(contact_request.data.prenom, "Caterina")
        self.assertEqual(contact_request.data.email, "<EMAIL>")
        self.assertEqual(contact_request.data.phone, "498323649")
        self.assertEqual(contact_request.data.preference, "Email")
        self.assertEqual(contact_request.data.section, "Raccordement et travaux")

        # Vérification de l'adresse
        self.assertEqual(contact_request.data.adresse.rue, "Louis pasteur ")
        self.assertEqual(contact_request.data.adresse.numero, "14")
        self.assertEqual(contact_request.data.adresse.commune, "Grâce-Hollogne")
        self.assertEqual(contact_request.data.adresse.code_postal, "4460")

        # Vérification des fichiers
        self.assertEqual(len(contact_request.files), 1)
        self.assertEqual(contact_request.files[0].extension, "pdf")
        self.assertTrue(contact_request.files[0].document_name.startswith("Attestation"))

    def test_model_dump_with_aliases(self):
        """Test de sérialisation avec les alias PascalCase."""
        contact_request = ContactRequest.model_validate(self.sample_json)
        dumped = contact_request.model_dump()

        # Vérification que les alias PascalCase sont utilisés
        self.assertIn("ClientEmail", dumped)
        self.assertIn("Data", dumped)
        self.assertIn("Files", dumped)
        self.assertIn("Adresse", dumped["Data"])
        self.assertIn("CodePostal", dumped["Data"]["Adresse"])

    def test_empty_files_list(self):
        """Test avec une liste de fichiers vide."""
        data = self.sample_json.copy()
        data["files"] = []

        contact_request = ContactRequest.model_validate(data)
        self.assertEqual(len(contact_request.files), 0)

    def test_minimal_required_fields(self):
        """Test avec les champs minimaux requis."""
        minimal_data = {
            "client_email": "<EMAIL>",
            "data": {
                "Nom": "Test",
                "Prenom": "User",
                "Phone": "0123456789",
                "Email": "<EMAIL>",
                "Preference": "Email",
            },
        }

        contact_request = ContactRequest.model_validate(minimal_data)
        self.assertEqual(contact_request.data.section, "default")
        self.assertEqual(contact_request.data.sous_section, "")
        self.assertEqual(len(contact_request.files), 0)

        contact_request = ContactRequest.recursive_construct(**minimal_data)
        self.assertEqual(contact_request.data.section, "default")
        self.assertEqual(contact_request.data.sous_section, "")
        self.assertEqual(len(contact_request.files), 0)


if __name__ == "__main__":
    unittest.main()
