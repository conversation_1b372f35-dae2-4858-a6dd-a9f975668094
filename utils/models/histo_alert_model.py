from utils.models.pydantic_utils import PascalModel


class AlertAddress(PascalModel):
    cdpostal: str
    code_pays: str
    localite: str
    num_rue: str
    rue: str


class HistoricAlert(PascalModel):
    meter_id: str
    date: str
    check_date: str
    address: AlertAddress
    address_hash: str
    alert_type: str
    conso_value: float
    ean: str
    energy_type: str
    limit: float
    overhead: float
    uid: str
