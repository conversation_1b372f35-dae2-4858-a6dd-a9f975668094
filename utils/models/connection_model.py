from __future__ import annotations

import json
from enum import Enum
from itertools import groupby
from os import environ
from typing import Annotated, ClassVar

from annotated_types import G<PERSON>, Le
from pydantic import AfterValidator, AnyHttpUrl, BeforeValidator, Field, StringConstraints, field_validator, model_validator

from utils.file_utils import get_file_extension
from utils.models.pydantic_utils import NotEmpty, PascalModel, default_if_none
from utils.validation import must_be_true


# -- Enums
class ApplicantType(Enum):
    """
    Enumeration for types of applicants.

    Attributes
    ----------
    INDIVIDUAL : str
        Represents an individual applicant.
    SELF_EMPLOYED : str
        Represents a self-employed applicant.
    COMPANY : str
        Represents a corporate applicant.

    """

    INDIVIDUAL = "Particulier"
    SELF_EMPLOYED = "Independant"
    COMPANY = "Societe"


class ActAs(Enum):
    """
    Enumeration representing different actions.

    Attributes
    ----------
    OWNER : str
        Constant an owner.
    TENANT : str
        Constant a tenant.

    """

    OWNER = "PROP"
    TENANT = "LOC"

    @property
    def text(self) -> str | None:
        """
        Convert an enumeration value to its corresponding SAP text.

        Returns
        -------
        str
            The SAP text corresponding to the enumeration value.

        """
        return {"PROP": "Propriétaire", "LOC": "Locataire"}.get(self.value)


class EnergyType(Enum):
    """
    Enum representing types of energy.

    Attributes
    ----------
    ELEC : str
        Represents electrical energy type.
    GAS : str
        Represents gas energy type.

    """

    ELEC = "ELEC"
    GAS = "GAZ"

    @property
    def activity_sector(self) -> str | None:
        """
        Returns sector activity code based on the energy type.

        Returns
        -------
        str|None
            '01' if the energy type is 'ELEC'
            '02' if the energy type is 'GAZ'
            None otherwise.

        """
        return {self.ELEC: "01", self.GAS: "02"}.get(self)


class WorkType(Enum):
    """
    Enum representing the types of work.

    Attributes
    ----------
    NOUV_RACC : str
        New connection
    MODI_INSTAL : str
        Modify installation
    MODI_TARIF : str
        Modify tariff
    MODI_CABLE : str
        Modify link cable
    MODI_SMART : str
        Modify meter to smart
    DEPLA_BRAN : str
        Displace meter or connection
    ENLV_CPT : str
        Remove meter
    NOUV_FORF : str
        New flat rate (synergrid)
    NOUV_RTEC : str
        New technical connections
    NOUV_ARMO : str
        New cabinet
    NOUV_CCOM : str
        New commerce
    CHAN_PROVI : str
        Change provision
    SUPP_BRAN : str
        Remove connection
    RACC_FORAIN : str
        Fairground connection
    MODI_PROD : str
        Add/modify electricity production
    PULSE_DELAY : str
        Request for pulse delay
    NOUV_BRE : str
        Request for charging station

    """

    NOUV_RACC = "NOUV_RACC"
    MODI_INSTAL = "MODI_INSTAL"
    MODI_TARIF = "MODI_TARIF"
    MODI_CABLE = "MODI_CABLE"
    MODI_SMART = "MODI_SMART"
    DEPLA_BRAN = "DEPLA_BRAN"
    ENLV_CPT = "ENLV_CPT"
    NOUV_FORF = "NOUV_FORF"
    NOUV_RTEC = "NOUV_RTEC"
    NOUV_ARMO = "NOUV_ARMO"
    NOUV_CCOM = "NOUV_CCOM"
    CHAN_PROVI = "CHAN_PROVI"
    SUPP_BRAN = "SUPP_BRAN"
    RACC_FORAIN = "RACC_FORAIN"
    MODI_PROD = "MODI_PROD"
    PULSE_DELAY = "PULSE_DELAY"
    NOUV_BRE = "NOUV_BRE"

    @property
    def sap_value(self):
        """
        Return the expected value for SAP.

        For WorkType MODI_PROD and PULSE_DELAY, it returns MODI_INSTAL.
        For other WorkType, the value is returned as such.
        """
        if self in (self.MODI_PROD, self.PULSE_DELAY):
            return self.MODI_INSTAL.value
        return super().value

    @property
    def category_note_text(self) -> str | None:
        """
        Get the special category description for specific cases.

        This property evaluates the instance against predefined cases to determine
        a specific category description.
        The returned category will help identify
        the type of action associated with the instance, such as modifying electricity
        production or requesting a pulse delay.

        Returns
        -------
        str | None
            A specific string related to the case evaluated or None if no case matches.

        """
        match self:
            case self.MODI_PROD:
                return "Ajouter/modifier une production d'électricité"
            case self.PULSE_DELAY:
                return "Demander un report d'impulsion"
        return None

    @property
    def category_code(self) -> str | None:
        """
        Determines the category code based on the current instance type.

        - 'R01' corresponds to the creation types like: NOUV_RACC, NOUV_FORF, NOUV_RTEC, NOUV_ARMO, NOUV_CCOM, CHAN_PROVI, RACC_FORAIN, NOUV_BRE.
        - 'R02' corresponds to the modification types like: MODI_INSTAL, MODI_TARIF, MODI_CABLE, DEPLA_BRAN, ENLV_CPT, SUPP_BRAN.
        - 'S20' corresponds to the modification types like: MODI_SMART.

        Returns
        -------
        str | None
            The category code as a string if the instance matches any predefined types,
            otherwise returns None.

        """
        match self:
            case self.NOUV_RACC | self.NOUV_FORF | self.NOUV_RTEC | self.NOUV_ARMO | self.NOUV_CCOM | self.CHAN_PROVI | self.RACC_FORAIN | self.NOUV_BRE:
                return "R01"
            case self.MODI_INSTAL | self.MODI_TARIF | self.MODI_CABLE | self.DEPLA_BRAN | self.ENLV_CPT | self.SUPP_BRAN | self.MODI_PROD | self.PULSE_DELAY:
                return "R02"
            case self.MODI_SMART:
                return "S20"
        return None


class PhaseType(Enum):
    """
    Enum class for representing electrical phase types.

    Attributes
    ----------
    MONO : str
        Represents single-phase (Monophase) electrical configuration.
    TRI : str
        Represents three-phase (Triphase) electrical configuration.

    """

    MONO = "MONO"
    TRI = "TRI"


class Rate(Enum):
    """
    Enum class to represent different rates.

    Attributes
    ----------
    ST : Enum
        "Simple Tarif" rate.
    DT : Enum
        "Double Tarif" rate.
    EXN_ST : Enum
        "Simple Tarif EXclusif Nuit" rate.
    EXN_DT : Enum
        "Double Tarif EXclusif Nuit" rate.

    """

    ST = "ST"
    DT = "DT"
    EXN_ST = "EXN_ST"
    EXN_DT = "EXN_DT"


class Usage(Enum):
    """
    Enum class to represent different types of usages.

    Attributes
    ----------
    RESI : str
        Represents residential usage.
    PRO : str
        Represents professional usage.
    MIX : str
        Represents mixed usage.

    """

    RESI = "RESI"
    PRO = "PRO"
    MIX = "MIXTE"


class HouseType(Enum):
    """
    Enum class representing different house types.

    Attributes
    ----------
    PRIVATE_LESS : str
        Represents a private house less than 10 years old.
    DESTRUCTION : str
        Represents a house type marked for destruction.
    OTHER : str
        Represents other house type, with no specific categorization.
    PRIVATE_MORE : str
        Represents a private house more than 10 years old.
    REBUILT : str
        Represents a house type that after demolition will be reconstructed in the same operation.
    HANDICAP : str
        Represents a house type that is accessible for handicapped individuals.

    """

    PRIVATE_LESS = "PRIVE_MOINS"
    DESTRUCTION = "DESTRUCTION"
    OTHER = "AUCUNE"
    PRIVATE_MORE = "PRIVE_PLUS"
    REBUILT = "RECONSTRUIT"
    HANDICAP = "HANDICAP"


class VatType(Enum):
    """
    Enum class representing various types of VAT (Value Added Tax).

    Attributes
    ----------
    OWNER : str
        VAT type for owner, tenant, usufructuary, housing manager.
    COMMUNITY : str
        VAT type for community, SPW, Province, CPAS.
    SOCIETY : str
        VAT type for regional housing society and society approved for non-taxable social housing.
    DORM : str
        VAT type for retirement home, school dormitory, youth protection home;.
    NATO : str
        VAT type for NATO, embassy, ESA.
    SCHOOL : str
        VAT type for school building.
    AUTO : str
        VAT type for reverse-charge VAT.

    """

    OWNER = "PROP"
    COMMUNITY = "COMMU"
    SOCIETY = "SOCIETE"
    DORM = "INTERN"
    NATO = "OTAN"
    SCHOOL = "BATI"
    AUTO = "AUTO"


# -- Models


class ConnectionAddress(PascalModel):
    """
    Represents the address details of a connection.

    Attributes
    ----------
    street : str, optional
        The street name of the address (default is None).
    number : str, optional
        The street number of the address (default is None).
    postcode : str, optional
        The postal code of the address (default is None).
    city : str, optional
        The city name of the address (default is None).
    box : str, optional
        Additional box info of the address (default is None).
    country : str, optional
        The country code of the address (default is "BE").
    lat : float, optional
        The latitude of the address (default is None).
    lon : float, optional
        The longitude of the address (default is None).
    spw_plot : str, optional
        SPW plot information of the address (default is None).
    note : str, optional
        Additional notes regarding the address (default is None).

    """

    street: str | None = None
    number: str | None = None
    postcode: str | None = None
    city: str | None = None
    box: str | None = None
    country: str | None = "BE"
    lat: float | None = None
    lon: float | None = None
    spw_plot: str | None = None
    note: str | None = None

    @model_validator(mode="after")
    def _validate(self) -> ConnectionAddress:
        def has_complete_address():
            return all(
                [
                    self.street is not None,
                    self.number is not None,
                    self.postcode is not None,
                    self.city is not None,
                ],
            )

        def has_address_with_note():
            return all(
                [
                    self.street is not None,
                    self.postcode is not None,
                    self.city is not None,
                    self.note is not None,
                ],
            )

        def has_coordinates():
            return all(
                [
                    self.lat is not None,
                    self.lon is not None,
                ],
            )

        if not any([has_complete_address(), has_address_with_note(), has_coordinates(), self.spw_plot is not None]):
            raise ValueError("either [Street, Number, Postcode, City] or [Lat, Lon] or [SpwPlot] must be provided.")
        return self


class Address(PascalModel):
    """
    Represents the address details in general.

    Attributes
    ----------
    street : NotEmpty[str]
        The street name where the address is located.
    number : NotEmpty[str]
        The number associated with the dwelling on the street.
    box : str or None, optional
        An optional box number to further specify the address, by default None.
    postcode : NotEmpty[str]
        The postal code for the area where the address is located.
    city : NotEmpty[str]
        The city where the address is located.
    country : str or None, optional
        The country where the address is located, by default "BE" (Belgium).

    """

    street: NotEmpty[str]
    number: NotEmpty[str]
    box: str | None = None
    postcode: NotEmpty[str]
    city: NotEmpty[str]
    country: str | None = Field("BE")


class Partner(PascalModel):
    """
    Partner represents an individual applying for a connection request or set as contact.

    Attributes
    ----------
    name : NotEmpty[str]
        The last name or full name of the partner.
    firstname : NotEmpty[str]
        The first name of the partner.
    email : NotEmpty[str]
        The email address of the partner.
    phone : NotEmpty[str]
        The phone number of the partner.
    address : Address or None
        The physical address of the partner.

    """

    name: NotEmpty[str]
    firstname: NotEmpty[str]
    email: NotEmpty[str]
    phone: NotEmpty[str]
    address: Address | None = None


class ApplicantPartner(Partner):
    """
    ApplicantPartner class that inherits from Partner.

    This class represents an applicant.
    It includes Partner attributes with mandatory address.

    Attributes
    ----------
    address : Address
        The address of the applicant.

    """

    address: Address


class Company(PascalModel):
    """
    Company class represents a business entity with specific legal attributes.

    Attributes
    ----------
    name : NotEmpty[str]
        The name of the company.
    legal_status : NotEmpty[str]
        The legal status of the company.
    vat : str or None, optional
        VAT number of the company; default is None.

    """

    name: NotEmpty[str]
    legal_status: NotEmpty[str]
    vat: str | None = None


class Meter(PascalModel):
    """
    A class to represent a meter for electricity or gas consumption.

    Attributes
    ----------
    ean : NotEmpty[str], optional
        The EAN (European Article Number) of the meter.
    number : NotEmpty[str], optional
        The unique number associated with the meter.
    energy_type : EnergyType
        The type of energy the meter is measuring (e.g., electricity, gas).
    work_type : WorkType
        The type of work associated with the meter.
    quantity : int, optional
        The quantity of the meters needed, default is 1.
    power : float, optional
        The power rating of the meter, within the range of 0 to 25000.
    amper : float, optional
        The amperage rating of the meter.
    phase_type : PhaseType, optional
        The phase type of the meter.
    rate : Rate, optional
        The rate structure associated with the meter.
    allotment : bool, optional
        Indicator whether the meter is related to an allotment.
    moving : bool, optional
        Indicator whether the meter needs to be moved.
    photo : Document, optional
        The photo of the meter.
    note : str, optional
        A note about the meter.

    """

    ean: NotEmpty[str] | None = None
    number: NotEmpty[str] | None = None
    energy_type: EnergyType
    work_type: WorkType
    quantity: int | None = 1
    power: Annotated[float, Ge(0.0), Le(25000.0)] | None = None
    amper: float | None = None
    phase_type: PhaseType | None = None
    rate: Rate | None = None
    allotment: bool | None = None
    moving: bool | None = None
    photo: Document | None = None
    note: Annotated[str | None, BeforeValidator(default_if_none)] = ""

    @model_validator(mode="after")
    def _validate(self) -> Meter:
        match self.work_type:
            case WorkType.NOUV_RACC | WorkType.NOUV_RTEC | WorkType.NOUV_ARMO | WorkType.NOUV_CCOM | WorkType.CHAN_PROVI | WorkType.RACC_FORAIN | WorkType.NOUV_BRE:
                if self.energy_type == EnergyType.ELEC and not all(
                    [
                        self.power is not None,
                        self.phase_type is not None,
                        self.rate is not None,
                    ],
                ):
                    raise ValueError(f"[Power, PhaseType, Rate] must be provided with EnergyType : '{self.energy_type.value}' and WorkType : '{self.work_type.value}'.")
                if self.power is None:
                    raise ValueError(f"[Power] must be provided with EnergyType : '{self.energy_type.value}' and WorkType : '{self.work_type.value}'.")
            case (
                WorkType.MODI_INSTAL
                | WorkType.MODI_TARIF
                | WorkType.MODI_CABLE
                | WorkType.DEPLA_BRAN
                | WorkType.ENLV_CPT
                | WorkType.SUPP_BRAN
                | WorkType.MODI_PROD
                | WorkType.PULSE_DELAY
            ):
                if not all(
                    [
                        self.ean is not None,
                        self.number is not None,
                    ],
                ):
                    raise ValueError(f"[Ean, Number] must be provided with WorkType : '{self.work_type.value}'.")
            case WorkType.MODI_SMART:
                if not all(
                    [
                        self.ean is not None,
                        self.number is not None,
                        self.photo is not None,
                    ],
                ):
                    raise ValueError(f'[Ean, Number, Photo] must be provided with WorkType : "{self.work_type}".')
            case WorkType.NOUV_FORF:
                if self.quantity != 0:
                    raise ValueError(f'[Quantity] must be 0 with WorkType : "{self.work_type}".')
        return self


class Document(PascalModel):
    """
    Document class representing a file or attachment with a name and URL.

    Attributes
    ----------
    name : NotEmpty[str]
        The name of the document.
    url : AnyHttpUrl
        The URL of the document.

    """

    _extension_authorized: ClassVar[list[str]] = ["jpg", "png", "doc", "docx", "pdf"]
    name: NotEmpty[str]
    url: AnyHttpUrl

    @property
    def extension(self):
        return get_file_extension(self.url.path)

    @classmethod
    @field_validator("url")
    def _validate_url_extension(cls, field: AnyHttpUrl):
        extension = get_file_extension(field.path)
        if extension not in cls._extension_authorized:
            raise ValueError(f"extension '{extension}' not in authorized extension list {cls._extension_authorized}")
        return field


def build_meters_from_group_for_sap_ws35(group: list[Meter]) -> list[dict]:
    """
    Build a list of meter dictionaries formatted for SAP WS35 from a group of meters.

    This method processes a group of meters and creates a structured list where each meter
    is expanded according to its quantity. Each meter in the group generates multiple
    entries based on its quantity value, with sequential numbering starting from 1.

    Parameters
    ----------
    group : list[Meter]
        A list of Meter objects that share common characteristics (same EAN, number,
        energy type, work type, allotment status, moving status, and notes).
        These meters are typically grouped together in the SAP WS35 format.

    Returns
    -------
    list[dict]
        A list of dictionaries, each representing a meter in the group, expanded
        according to its quantity. Each dictionary contains the meter's details
        formatted for the SAP WS35 standard.

    """
    meter_list = []
    start_i = 1
    for meter in group:
        meter_list.extend(
            {
                "Compteur": i,
                "DetailsCompteurs": [
                    {"CodeDetailCpt": "NUMCPT", "ValeurDetailCpt": meter.number},
                    {"CodeDetailCpt": "PUISSANCE", "ValeurDetailCpt": meter.power},
                    {"CodeDetailCpt": "AMPERAGE", "ValeurDetailCpt": meter.amper},
                    {"CodeDetailCpt": "TYPECOMPTEUR", "ValeurDetailCpt": meter.phase_type.value if meter.phase_type else None},
                    {"CodeDetailCpt": "TARIF", "ValeurDetailCpt": meter.rate.value if meter.rate else None},
                ],
            }
            for i in range(start_i, meter.quantity + start_i)
        )
        start_i += meter.quantity
    return meter_list


class Connection(PascalModel):
    """
    Connection model to represent a connection request.

    Attributes
    ----------
    synergy : bool, optional
        Indicates if the connection is in synergy with CILE.
    synergrid : str, optional
        Synergrid reference identifier.
    applicant_type : ApplicantType
        Type of applicant.
    act_as : ActAs
        Role defining how the applicant acts in the connection request.
    address : ConnectionAddress
        Connection's address information.
    applicant : ApplicantPartner
        Detail of the client applying for the connection.
    contact : Partner, optional
        Detail of the contact (electrician, owner, etc...).
    billing : Address, optional
        Billing address information.
    company : Company, optional
        Information about the company if applicable.
    meters : list of Meter
        List of meters associated with the connection.
    product_more_than10_kva : bool, optional
        Indicates if the product capacity is more than 10 kVA.
    elec_note : str, optional
        Additional notes specific to electricity.
    gas_note : str, optional
        Additional notes specific to gas.
    general_note : str, optional
        General notes applicable to the connection.
    desired_date : str, optional
        Desired date for the connection in YYYYMM format.
    usage : Usage, optional
        The usage information of the meter.
    house_type : HouseType, optional
        Type of house related to the connection.
    subject_to_vat : bool, optional
        Indicates if the connection work is subject to VAT.
    vat_type : VatType, optional
        Type of VAT applicable.
    confirm_vat : bool, optional
        Confirmation of VAT applicability.
    confirm_gdpr : bool
        Confirmation of RGPD compliance.
    documents : list of Document, optional
        List of documents associated with the connection.
    near_by_street: str, optional.
        Nearby street if the real street is not know.
    extra_context: dict, optional.
        Context informations, usefull to the website when resuming the request.

    """

    _MAX_GAZ_POWER = 70
    _MAX_ELEC_POWER = 43.6
    _MAX_CPT_NUMBER = 4

    synergy: bool | None = None
    synergrid: str | None = None
    applicant_type: ApplicantType
    act_as: ActAs | None = None
    address: ConnectionAddress
    applicant: ApplicantPartner
    contact: Partner | None = None
    billing: Address | None = None
    company: Company | None = None
    meters: NotEmpty[list[Meter]]
    product_more_than10_kva: bool | None = None
    elec_note: Annotated[str | None, BeforeValidator(default_if_none)] = ""
    gas_note: Annotated[str | None, BeforeValidator(default_if_none)] = ""
    general_note: Annotated[str | None, BeforeValidator(default_if_none)] = ""
    desired_date: Annotated[str, StringConstraints(pattern=r"^\d{4}\d{2}$")] | None = None  # type:ignore
    usage: Usage | None = None
    house_type: HouseType | None = None
    subject_to_vat: bool | None = None
    vat_type: VatType | None = None
    confirm_vat: Annotated[bool, AfterValidator(must_be_true)] | None = None
    confirm_gdpr: Annotated[bool, AfterValidator(must_be_true)]
    documents: list[Document] = []
    near_by_street: str | None = None
    extra_context: dict | None = {}

    @property
    def ws79_needed(self) -> bool:
        """
        Checks if ws79 is needed based on specific conditions regarding the meters and work types.

        If in any specific type of work (RACC_FORAIN / CHAN_PROVI)
        Or if there is more than 4 meters
        Or if there is a gas meter with more than 70kW
        Or if there is a elec meter with more than 43.6kW.

        Returns
        -------
        bool
            True if ws79 is needed, False otherwise.

        """
        specific_work_types = (WorkType.RACC_FORAIN, WorkType.CHAN_PROVI)
        return any(meter.work_type in specific_work_types for meter in self.meters)

    @property
    def _ws79_building_type(self) -> str:
        if any(meter in (WorkType.RACC_FORAIN, WorkType.CHAN_PROVI) for meter in self.meters):
            return "Temporaire"

        if self.usage != Usage.RESI:
            return "Spécifique"

        if self.meters[0].work_type.category_code == "R01":
            return "Nouveau"
        else:
            return "Existant"

    @property
    def _ws79_subject(self) -> str:
        """
        Determines the subject of a request based on the type of work and meter details.

        This method analyzes the work type of the meters and their energy types,
        along with their power capacities to determine the subject of a connection request.

        Returns
        -------
        str
            The subject of the demand connection request.

        """
        demande_racc_autre = "DEMANDE RACCORDEMENT AUTRE"
        demande_racc_forain = "DEMANDE RACCORDEMENT FORAIN"
        demande_racc_chantier = "DEMANDE RACCORDEMENT CHANTIER"
        demande_racc_building = "DEMANDE RACCORDEMENT BUILDING"
        demande_racc_techni_bus = "DEMANDE RACCORDEMENT TECHNIQUE BUS"
        demande_racc_techni_cabinet = "DEMANDE RACCORDEMENT TECHNIQUE ARMOIRE"
        demande_racc_techni_spec = "DEMANDE RACCORDEMENT TECHNIQUE SPÉCIFIQUE"

        for meter in self.meters:
            match meter.work_type:
                case WorkType.RACC_FORAIN:
                    return demande_racc_forain
                case WorkType.CHAN_PROVI:
                    return demande_racc_chantier

        return demande_racc_autre

    def format_for_sap_ws35(self):
        """
        Convert the object's attributes into SAP format for WS35.

        Returns
        -------
        dict
            the data in SAP format.

        """
        language = environ["LANG"]

        partners = [
            {
                "TypePartenaire": "DEMANDEUR",
                # "IdPartenaire": "",  # Num BP, could maybe be useful if connected user
                "Nom": self.applicant.name,
                "Prenom": self.applicant.firstname,
                # "CodeCivilite": "9999",  # To review is it hard coded ?
                "Langue": language,
                "Gsm": self.applicant.phone,
                # "Tel": "",
                "Email": self.applicant.email,
                # "Fonction": "",
                "NumTVA": self.company.vat if self.company else None,
                "AssujetiTVA": "Y" if self.subject_to_vat else "N",
                "TypeURD": self.applicant_type.value if self.applicant_type else None,
                "NomEntreprise": self.company.name if self.company else None,
                "FormeJuridique": self.company.legal_status if self.company else None,
                "Adresse": {
                    "Rue": self.applicant.address.street,
                    "NumRue": self.applicant.address.number,
                    "NumCmpt": self.applicant.address.box,
                    "CdPostal": self.applicant.address.postcode,
                    "Localite": self.applicant.address.city,
                    "Pays": self.applicant.address.country,
                },
            },
        ]

        if self.contact:
            partners.append(
                {
                    "TypePartenaire": "CONTACT",
                    # "IdPartenaire": "",  # Num BP, could maybe be useful if connected user
                    "Nom": self.contact.name,
                    "Prenom": self.contact.firstname,
                    # "CodeCivilite": "9999",  # To review is it hard coded ?
                    "Langue": language,
                    "Gsm": self.contact.phone,
                    # "Tel": "",
                    "Email": self.contact.email,
                    # "Fonction": "",
                    # "NumTVA": self.company.vat if self.company else "",
                    # "AssujetiTVA": "Y" if self.company else "N",
                    # "TypeURD": self.applicant_type.value if self.applicant_type else None,
                    # "NomEntreprise": self.company.name if self.company else "",
                    # "FormeJuridique": self.company.legal_status if self.company else "",
                    "Adresse": {
                        "Rue": self.contact.address.street,
                        "NumRue": self.contact.address.number,
                        "NumCmpt": self.contact.address.box,
                        "CdPostal": self.contact.address.postcode,
                        "Localite": self.contact.address.city,
                        "Pays": self.contact.address.country,
                    }
                    if self.contact.address
                    else None,
                },
            )

        notes = self.general_note or ""
        if self.act_as or self.synergy or self.billing:
            notes += "\n---"
            if self.synergy:
                notes += f"\nSynergie CILE: {self.synergy}"
            if self.billing:
                notes += "\nAdresse de facturation:"
                notes += f"\n  Rue: {self.billing.street}"
                notes += f"\n  NumRue: {self.billing.number}"
                notes += f"\n  NumCmpt: {self.billing.box}"
                notes += f"\n  CdPostal: {self.billing.postcode}"
                notes += f"\n  Localite: {self.billing.city}"
                notes += f"\n  Pays: {self.billing.country}"

        grouped_meters = {
            k: list(v)
            for k, v in groupby(
                self.meters,
                key=lambda meter: (meter.ean, meter.number, meter.energy_type, meter.work_type, meter.allotment, meter.moving, meter.note),
            )
        }

        return {
            "Langue": language,
            "Partenaire": partners,
            "Adresse": {
                "Rue": self.near_by_street if self.near_by_street else self.address.street,
                "NumRue": self.address.number,
                "NumCmpt": self.address.box,
                "CdPostal": self.address.postcode,
                "Localite": self.address.city,
                "ParcelleSpw": self.address.spw_plot,
                "Pays": self.address.country,
                "Emplacement": self.address.note,
            },
            "Demande": [
                {
                    "SectActivite": group[0].energy_type.activity_sector,
                    "Ean": group[0].ean,
                    "TypeTravail": group[0].work_type.category_code,
                    "Details": [
                        {"CodeDetail": "CD_TYPE", "Valeur": group[0].work_type.sap_value if group[0].work_type else None},
                        {"CodeDetail": "TYPE_UTILISATION", "Valeur": self.usage.value if self.usage else None},
                        {"CodeDetail": "LOTISSEMENT", "Valeur": "Y" if group[0].allotment else "N"},
                        {"CodeDetail": "DEPLA_CPT", "Valeur": "Y" if group[0].moving else "N"},
                        {
                            "CodeDetail": "TYPE_RACC",
                            "Valeur": ("STANDARD" if group[0].power <= self._MAX_GAZ_POWER else "NON_STANDARD")
                            if group[0].power is not None and group[0].energy_type == EnergyType.GAS
                            else None,
                        },
                        {"CodeDetail": "PROD_SUP_10KVA", "Valeur": "Y" if self.product_more_than10_kva else "N"},
                        {
                            "CodeDetail": "REMARQUE",
                            "Valeur": (f"{group[0].work_type.category_note_text}\n" if group[0].work_type.category_note_text else "")
                            + (f"{group[0].note}\n" if group[0].note else "")
                            + (self.elec_note if group[0].energy_type == EnergyType.ELEC else self.gas_note),
                        },
                    ],
                    "NbCompteurs": sum(meter.quantity for meter in group),
                    "Compteur": build_meters_from_group_for_sap_ws35(group),
                }
                for group in grouped_meters.values()
            ],
            "Complement": [
                {"Libelle": "REMARQUE", "Valeur": notes, "Titre": "N"},
                {"Libelle": "DATESOUHAITEETRAV", "Valeur": self.desired_date, "Titre": "N"},
                {"Libelle": "TYPE_LOGEMENT", "Valeur": self.house_type.value if self.house_type else None, "Titre": "N"},
                {"Libelle": "TYPE_TVA", "Valeur": self.vat_type.value if self.vat_type else None, "Titre": "N"},
                {"Libelle": "GDPR", "Valeur": "Y" if self.confirm_gdpr else "N", "Titre": "N"},
                {"Libelle": "COORDGPSY", "Valeur": self.address.lat, "Titre": "N"},
                {"Libelle": "COORDGPSX", "Valeur": self.address.lon, "Titre": "N"},
                {"Libelle": "LOCATAIRE", "Valeur": "Y" if self.act_as == ActAs.TENANT else "N", "Titre": "N"},
                {"Libelle": "SYNERGRID", "Valeur": self.synergrid, "Titre": "N"},
            ],
        }

    def format_for_str_ws79(self):
        """
        Convert the object's attributes into format for WS79.

        Returns
        -------
        dict
            the data in WS79 format.

        """
        language = environ["LANG"]

        partners = [
            {
                "TypePartenaire": "DEMANDEUR",
                # "IdPartenaire": "",  # Num BP, could maybe be useful if connected user
                "Nom": self.applicant.name,
                "Prenom": self.applicant.firstname,
                # "CodeCivilite": "9999",  # To review is it hard coded ?
                "Langue": language,
                "Gsm": self.applicant.phone,
                # "Tel": "",
                "Email": self.applicant.email,
                # "Fonction": "",
                "NumTVA": self.company.vat if self.company else None,
                "AssujetiTVA": self.subject_to_vat,
                "TypeURD": self.applicant_type.value if self.applicant_type else None,
                "NomEntreprise": self.company.name if self.company else None,
                "FormeJuridique": self.company.legal_status if self.company else None,
                "Adresse": {
                    "Rue": self.applicant.address.street,
                    "NumRue": self.applicant.address.number,
                    "NumCmpt": self.applicant.address.box,
                    "CdPostal": self.applicant.address.postcode,
                    "Localite": self.applicant.address.city,
                    "Pays": self.applicant.address.country,
                },
            },
        ]

        if self.contact:
            partners.append(
                {
                    "TypePartenaire": "CONTACT",
                    # "IdPartenaire": "",  # Num BP, could maybe be useful if connected user
                    "Nom": self.contact.name,
                    "Prenom": self.contact.firstname,
                    # "CodeCivilite": "9999",  # To review is it hard coded ?
                    "Langue": language,
                    "Gsm": self.contact.phone,
                    # "Tel": "",
                    "Email": self.contact.email,
                    # "Fonction": "",
                    # "NumTVA": self.company.vat if self.company else "",
                    # "AssujetiTVA": self.company,
                    # "TypeURD": self.applicant_type.value if self.applicant_type else None,
                    # "NomEntreprise": self.company.name if self.company else "",
                    # "FormeJuridique": self.company.legal_status if self.company else "",
                    "Adresse": {
                        "Rue": self.contact.address.street,
                        "NumRue": self.contact.address.number,
                        "NumCmpt": self.contact.address.box,
                        "CdPostal": self.contact.address.postcode,
                        "Localite": self.contact.address.city,
                        "Pays": self.contact.address.country,
                    }
                    if self.contact.address
                    else None,
                },
            )

        notes = ""
        if self.act_as or self.synergy or self.synergrid or self.billing:
            notes += "<br>---"
            if self.act_as:
                notes += f"<br>Agit en tant que: {self.act_as.text}"
            if self.synergy:
                notes += f"<br>Synergie CILE: {self.synergy}"
            if self.synergrid:
                notes += f"<br>ID Synergrid: {self.synergrid}"
            if self.billing:
                notes += "<br>Adresse de facturation:"
                notes += f"<br>&nbsp;-&nbsp;Rue: {self.billing.street}"
                notes += f"<br>&nbsp;-&nbsp;NumRue: {self.billing.number}"
                notes += f"<br>&nbsp;-&nbsp;NumCmpt: {self.billing.box}"
                notes += f"<br>&nbsp;-&nbsp;CdPostal: {self.billing.postcode}"
                notes += f"<br>&nbsp;-&nbsp;Localite: {self.billing.city}"
                notes += f"<br>&nbsp;-&nbsp;Pays: {self.billing.country}"

        first_meter = self.meters[0]

        str_request = {
            "Partenaire": partners,
            "Societe": self.company.name if self.company else None,
            "Adresse": {
                "Rue": self.address.street,
                "NumRue": self.address.number,
                "NumCmpt": self.address.box,
                "CdPostal": self.address.postcode,
                "Localite": self.address.city,
                "Pays": self.address.country,
                "AdresseConnue": all([self.address.street, self.address.number, self.address.postcode, self.address.city]),
                "CoordGPSX": self.address.lon,
                "CoordGPSY": self.address.lat,
                "ParcelleSpw": self.address.spw_plot,
                "Emplacement": self.address.note,
            },
            "TypeBatiment": {
                "Type": self._ws79_building_type,
                "Usage": self.usage.value if self.usage else None,
            },
            "Lotissement": first_meter.allotment,
            "Is10KVA": self.product_more_than10_kva,
            "TVA": {
                "Type": self.vat_type.value if self.vat_type else None,
                "Logement": self.house_type.value if self.house_type else None,
                "AssujetiTVA": self.subject_to_vat,
            },
            "DateSouhaiteeTrav": self.desired_date,
            "InformationCompteurs": {
                "Compteur": [
                    {
                        # "uuid": "8e381540-185d-11ef-8200-c144ffb96a32",
                        "id": n,
                        "ean": meter.ean,
                        "numcpt": meter.number,
                        "energy": "electricity" if meter.energy_type == EnergyType.ELEC else "gaz",
                        "power": meter.power,
                        # "nightOption": false,
                        "phasesSlug": meter.phase_type.value if meter.phase_type else None,
                        # "samplingPower": null,
                        # "injectedPower": null,
                        "amperage": meter.amper,
                        # "phases": 1,
                        "tarifSlug": meter.rate.value if meter.rate else None,
                        # "tarif": true,
                        "modif": meter.work_type.value,
                    }
                    for n, meter in enumerate(self.meters, 1)
                ],
                # "InfomationRaccordement": "",
            },
            "Remarques": {
                # "date": None,
                "remarkCpts": {"electricity": self.elec_note, "gaz": self.gas_note},
                "remarkInfos": self.general_note,
                "remarkSupp": notes,
                "gdprConsent": self.confirm_gdpr,
                "tvaConfirm": self.confirm_vat,
                "deplaCpt": first_meter.moving,
                # "typeSmart": None,
            },
        }

        return {
            "Section": "NOUVEAU RACCORDEMENT" if first_meter.work_type.category_code == "R01" else "MODIFICATION RACCORDEMENT",
            "SousSection": self._ws79_subject,
            "Nom": self.applicant.name,
            "Prenom": self.applicant.firstname,
            "Email": self.applicant.email,
            "Phone": self.applicant.phone,
            "Description": json.dumps(str_request),
            "Files": [
                {
                    "FileUrl": str(document.url),
                    "DocumentName": document.name,
                    "Extension": get_file_extension(document.url),
                }
                for document in self.documents
            ],
        }
