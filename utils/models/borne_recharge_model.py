from dataclasses import dataclass
from decimal import Decimal
from typing import Optional, Union

from dataclasses_json import dataclass_json, LetterCase
from dateutil.parser import isoparse

from utils.errors import BadRequestError


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class FormField:
    libelle: Union[str, float, Decimal]
    valeur: Union[str, float, Decimal]


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Demandeur:
    nom: str
    prenom: str
    email: str
    telephone: str


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Installateur:
    nom: Optional[str] = None
    email: Optional[str] = None
    telephone: Optional[str] = None


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Entreprise:
    numero: Optional[str] = None
    nom: Optional[str] = None
    acronyme: Optional[str] = None
    forme_juridique: Optional[str] = None


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Adresse:
    rue: str
    numero: str
    code_postal: str
    commune: str
    pays: str


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Borne:
    adresse: Adresse
    ean: str
    date: str
    marque: str
    modele: str
    utilisation: FormField
    bidirectionnelle: bool
    serial: str
    puissance: Union[str, float, Decimal]
    photo: Optional[str] = None

    def __post_init__(self) -> None:
        self.puissance = Decimal(str(self.puissance).replace(",", "."))
        if not (0 < self.puissance < 1000):
            raise BadRequestError(
                "Invalid value for 'Puissance'. The value should be between 0 and 1000.",
                error_code="BORNE_PUISSANCE_BAD_VALUE",
            )
        try:
            isoparse(self.date)
        except (ValueError, TypeError):
            raise BadRequestError(
                "Invalid value for 'Date'. The value should be in ISO8601 format.",
                error_code="BORNE_DATE_BAD_VALUE",
            )


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class BorneRecharge:
    type_demande: FormField
    demandeur: Demandeur
    borne: Borne
    ean: Optional[str] = None
    uuid: Optional[str] = None
    installateur: Optional[Installateur] = None
    entreprise: Optional[Entreprise] = None
    date_creation: Optional[str] = None
    supprimer: Optional[bool] = False


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class BorneInstant:
    adresse: Adresse
    ean: str
    marque: str
    modele: str
    utilisation: FormField
    bidirectionnelle: bool
    serial: str
    puissance: Union[str, float, Decimal]
    active: bool = True
    date_desactivation: Optional[str] = None
    date_activation: Optional[str] = None
    photo: Optional[str] = None

    def __post_init__(self) -> None:
        self.puissance = Decimal(str(self.puissance).replace(",", "."))
        if not (0 < self.puissance < 1000):
            raise BadRequestError(
                "Invalid value for 'Puissance'. The value should be between 0 and 1000.",
                error_code="BORNE_PUISSANCE_BAD_VALUE",
            )
        if self.date_desactivation:
            try:
                isoparse(self.date_desactivation)
            except (ValueError, TypeError):
                raise BadRequestError(
                    "Invalid value for 'DateDesactivation'. The value should be in ISO8601 format.",
                    error_code="BORNE_DATE_DESACTIVATION_BAD_VALUE",
                )
        if self.date_activation:
            try:
                isoparse(self.date_activation)
            except (ValueError, TypeError):
                raise BadRequestError(
                    "Invalid value for 'DateActivation'. The value should be in ISO8601 format.",
                    error_code="BORNE_DATE_ACTIVATION_BAD_VALUE",
                )


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class BorneRechargeInstant:
    demandeur: Demandeur
    borne: BorneInstant
    ean: Optional[str] = None
    uuid: Optional[str] = None
    installateur: Optional[Installateur] = None
    entreprise: Optional[Entreprise] = None
    date_creation: Optional[str] = None
